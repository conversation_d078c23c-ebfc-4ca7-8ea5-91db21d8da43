/*! 
 Build based on gin-vue-admin 
 Time : 1754879545000 */
import s from"./index.54b02c0d.js";import{_ as a,h as e,a as c,b as n,d as o,j as p}from"./index.ab3e73c8.js";const t={class:"access-main"},d={class:"content-wrapper"},r={class:"access-app"};const m=a({name:"BowserAccess",components:{AppPage:s},data:()=>({}),async mounted(){},methods:{}},[["render",function(s,a,m,i,l,f){const u=e("AppPage");return c(),n("div",t,[o("ul",d,[o("li",r,[p(u,{class:"access-app-page"})])])])}],["__scopeId","data-v-550f2b74"]]);export{m as default};
