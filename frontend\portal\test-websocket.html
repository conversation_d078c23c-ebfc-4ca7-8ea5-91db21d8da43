<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket 登录消息测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .message-input {
            width: 100%;
            height: 100px;
            margin: 10px 0;
        }
        .log-output {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
        }
        button {
            padding: 8px 16px;
            margin: 5px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <h1>WebSocket 登录消息测试</h1>
    
    <div class="test-section">
        <h3>连接状态</h3>
        <div id="connection-status">未连接</div>
        <button onclick="connectWebSocket()">连接 WebSocket</button>
        <button onclick="disconnectWebSocket()">断开连接</button>
        <button onclick="testReconnect()">测试重连</button>
    </div>

    <div class="test-section">
        <h3>发送测试消息</h3>
        <textarea id="message-input" class="message-input" placeholder="输入要发送的JSON消息">
{
  "action": 0,
  "msg": {
    "realm": "asec",
    "refreshToken": "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "token": "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
  }
}
        </textarea>
        <br>
        <button onclick="sendMessage()">发送消息</button>
        <button onclick="sendLogoutMessage()">发送退出消息</button>
    </div>

    <div class="test-section">
        <h3>消息日志</h3>
        <div id="log-output" class="log-output"></div>
        <button onclick="clearLog()">清空日志</button>
    </div>

    <script>
        let ws = null;
        
        function log(message) {
            const logOutput = document.getElementById('log-output');
            const timestamp = new Date().toLocaleTimeString();
            logOutput.textContent += `[${timestamp}] ${message}\n`;
            logOutput.scrollTop = logOutput.scrollHeight;
        }
        
        function updateConnectionStatus(status) {
            document.getElementById('connection-status').textContent = status;
        }
        
        function connectWebSocket() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                log('WebSocket 已经连接');
                return;
            }
            
            try {
                ws = new WebSocket('ws://127.0.0.1:50001');
                
                ws.onopen = function(event) {
                    log('WebSocket 连接成功');
                    updateConnectionStatus('已连接');
                };
                
                ws.onmessage = function(event) {
                    log('收到消息: ' + event.data);
                    
                    try {
                        const message = JSON.parse(event.data);
                        if (message.action === 0 && message.msg && (message.msg.token || message.msg.refreshToken)) {
                            log('✅ 检测到登录消息格式正确');
                            log('Token: ' + (message.msg.token ? '存在' : '不存在'));
                            log('RefreshToken: ' + (message.msg.refreshToken ? '存在' : '不存在'));
                            log('Realm: ' + (message.msg.realm || '默认'));
                        }
                    } catch (e) {
                        log('消息解析失败: ' + e.message);
                    }
                };
                
                ws.onerror = function(error) {
                    log('WebSocket 错误: ' + error);
                    updateConnectionStatus('连接错误');
                };
                
                ws.onclose = function(event) {
                    log('WebSocket 连接关闭: ' + event.code + ' - ' + event.reason);
                    updateConnectionStatus('已断开');
                };
                
            } catch (error) {
                log('连接失败: ' + error.message);
                updateConnectionStatus('连接失败');
            }
        }
        
        function disconnectWebSocket() {
            if (ws) {
                ws.close();
                ws = null;
                updateConnectionStatus('已断开');
                log('主动断开 WebSocket 连接');
            }
        }
        
        function sendMessage() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('WebSocket 未连接，无法发送消息');
                return;
            }

            const messageText = document.getElementById('message-input').value;
            try {
                // 验证是否为有效的 JSON
                const messageObj = JSON.parse(messageText);
                ws.send(messageText);
                log('发送消息: ' + messageText);

                // 模拟处理逻辑验证
                if (messageObj.action === 0 && messageObj.msg && (messageObj.msg.token || messageObj.msg.refreshToken)) {
                    log('✅ 这是一个有效的登录消息格式');
                    log('应该触发登录同步处理');
                }
            } catch (error) {
                log('发送失败，消息格式错误: ' + error.message);
            }
        }
        
        function sendLogoutMessage() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('WebSocket 未连接，无法发送消息');
                return;
            }
            
            const logoutMessage = {
                action: 1,
                msg: {
                    reason: 'user_logout'
                }
            };
            
            ws.send(JSON.stringify(logoutMessage));
            log('发送退出消息: ' + JSON.stringify(logoutMessage));
        }
        
        function testReconnect() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                log('模拟重连：先断开连接');
                ws.close();

                // 等待2秒后重新连接
                setTimeout(() => {
                    log('开始重新连接...');
                    connectWebSocket();
                }, 2000);
            } else {
                log('WebSocket 未连接，无法测试重连');
            }
        }

        function clearLog() {
            document.getElementById('log-output').textContent = '';
        }

        // 页面加载时自动连接
        window.onload = function() {
            log('页面加载完成，可以开始测试');
            log('修复说明：现在WebSocket重连后会自动恢复登录同步处理器');
        };
    </script>
</body>
</html>
