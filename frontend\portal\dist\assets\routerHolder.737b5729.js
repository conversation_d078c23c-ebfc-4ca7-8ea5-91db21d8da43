/*! 
 Build based on gin-vue-admin 
 Time : 1754879545000 */
import{$ as e,h as a,a as s,b as n,j as t,w as o,W as u,k as l,a0 as r,y as i,z as d}from"./index.ab3e73c8.js";const c=Object.assign({name:"RouterHolder"},{setup(c){const m=e();return(e,c)=>{const f=a("router-view");return s(),n("div",null,[t(f,null,{default:o((({Component:e})=>[t(u,{mode:"out-in",name:"el-fade-in-linear"},{default:o((()=>[(s(),l(r,{include:i(m).keepAliveRouters},[(s(),l(d(e)))],1032,["include"]))])),_:2},1024)])),_:1})])}}});export{c as default};
