import {
  __commonJS,
  __toESM
} from "./chunk-BHN6OJC3.js";

// node_modules/@babel/runtime/helpers/OverloadYield.js
var require_OverloadYield = __commonJS({
  "node_modules/@babel/runtime/helpers/OverloadYield.js"(exports, module) {
    function _OverloadYield(e, d) {
      this.v = e, this.k = d;
    }
    module.exports = _OverloadYield, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@babel/runtime/helpers/regeneratorDefine.js
var require_regeneratorDefine = __commonJS({
  "node_modules/@babel/runtime/helpers/regeneratorDefine.js"(exports, module) {
    function _regeneratorDefine(e, r, n, t) {
      var i = Object.defineProperty;
      try {
        i({}, "", {});
      } catch (e2) {
        i = 0;
      }
      module.exports = _regeneratorDefine = function regeneratorDefine(e2, r2, n2, t2) {
        if (r2)
          i ? i(e2, r2, {
            value: n2,
            enumerable: !t2,
            configurable: !t2,
            writable: !t2
          }) : e2[r2] = n2;
        else {
          var o = function o2(r3, n3) {
            _regeneratorDefine(e2, r3, function(e3) {
              return this._invoke(r3, n3, e3);
            });
          };
          o("next", 0), o("throw", 1), o("return", 2);
        }
      }, module.exports.__esModule = true, module.exports["default"] = module.exports, _regeneratorDefine(e, r, n, t);
    }
    module.exports = _regeneratorDefine, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@babel/runtime/helpers/regenerator.js
var require_regenerator = __commonJS({
  "node_modules/@babel/runtime/helpers/regenerator.js"(exports, module) {
    var regeneratorDefine = require_regeneratorDefine();
    function _regenerator() {
      var e, t, r = "function" == typeof Symbol ? Symbol : {}, n = r.iterator || "@@iterator", o = r.toStringTag || "@@toStringTag";
      function i(r2, n2, o2, i2) {
        var c2 = n2 && n2.prototype instanceof Generator ? n2 : Generator, u3 = Object.create(c2.prototype);
        return regeneratorDefine(u3, "_invoke", function(r3, n3, o3) {
          var i3, c3, u4, f2 = 0, p = o3 || [], y = false, G = {
            p: 0,
            n: 0,
            v: e,
            a: d,
            f: d.bind(e, 4),
            d: function d2(t2, r4) {
              return i3 = t2, c3 = 0, u4 = e, G.n = r4, a;
            }
          };
          function d(r4, n4) {
            for (c3 = r4, u4 = n4, t = 0; !y && f2 && !o4 && t < p.length; t++) {
              var o4, i4 = p[t], d2 = G.p, l = i4[2];
              r4 > 3 ? (o4 = l === n4) && (u4 = i4[(c3 = i4[4]) ? 5 : (c3 = 3, 3)], i4[4] = i4[5] = e) : i4[0] <= d2 && ((o4 = r4 < 2 && d2 < i4[1]) ? (c3 = 0, G.v = n4, G.n = i4[1]) : d2 < l && (o4 = r4 < 3 || i4[0] > n4 || n4 > l) && (i4[4] = r4, i4[5] = n4, G.n = l, c3 = 0));
            }
            if (o4 || r4 > 1)
              return a;
            throw y = true, n4;
          }
          return function(o4, p2, l) {
            if (f2 > 1)
              throw TypeError("Generator is already running");
            for (y && 1 === p2 && d(p2, l), c3 = p2, u4 = l; (t = c3 < 2 ? e : u4) || !y; ) {
              i3 || (c3 ? c3 < 3 ? (c3 > 1 && (G.n = -1), d(c3, u4)) : G.n = u4 : G.v = u4);
              try {
                if (f2 = 2, i3) {
                  if (c3 || (o4 = "next"), t = i3[o4]) {
                    if (!(t = t.call(i3, u4)))
                      throw TypeError("iterator result is not an object");
                    if (!t.done)
                      return t;
                    u4 = t.value, c3 < 2 && (c3 = 0);
                  } else
                    1 === c3 && (t = i3["return"]) && t.call(i3), c3 < 2 && (u4 = TypeError("The iterator does not provide a '" + o4 + "' method"), c3 = 1);
                  i3 = e;
                } else if ((t = (y = G.n < 0) ? u4 : r3.call(n3, G)) !== a)
                  break;
              } catch (t2) {
                i3 = e, c3 = 1, u4 = t2;
              } finally {
                f2 = 1;
              }
            }
            return {
              value: t,
              done: y
            };
          };
        }(r2, o2, i2), true), u3;
      }
      var a = {};
      function Generator() {
      }
      function GeneratorFunction() {
      }
      function GeneratorFunctionPrototype() {
      }
      t = Object.getPrototypeOf;
      var c = [][n] ? t(t([][n]())) : (regeneratorDefine(t = {}, n, function() {
        return this;
      }), t), u2 = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c);
      function f(e2) {
        return Object.setPrototypeOf ? Object.setPrototypeOf(e2, GeneratorFunctionPrototype) : (e2.__proto__ = GeneratorFunctionPrototype, regeneratorDefine(e2, o, "GeneratorFunction")), e2.prototype = Object.create(u2), e2;
      }
      return GeneratorFunction.prototype = GeneratorFunctionPrototype, regeneratorDefine(u2, "constructor", GeneratorFunctionPrototype), regeneratorDefine(GeneratorFunctionPrototype, "constructor", GeneratorFunction), GeneratorFunction.displayName = "GeneratorFunction", regeneratorDefine(GeneratorFunctionPrototype, o, "GeneratorFunction"), regeneratorDefine(u2), regeneratorDefine(u2, o, "Generator"), regeneratorDefine(u2, n, function() {
        return this;
      }), regeneratorDefine(u2, "toString", function() {
        return "[object Generator]";
      }), (module.exports = _regenerator = function _regenerator2() {
        return {
          w: i,
          m: f
        };
      }, module.exports.__esModule = true, module.exports["default"] = module.exports)();
    }
    module.exports = _regenerator, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@babel/runtime/helpers/regeneratorAsyncIterator.js
var require_regeneratorAsyncIterator = __commonJS({
  "node_modules/@babel/runtime/helpers/regeneratorAsyncIterator.js"(exports, module) {
    var OverloadYield = require_OverloadYield();
    var regeneratorDefine = require_regeneratorDefine();
    function AsyncIterator(t, e) {
      function n(r2, o, i, f) {
        try {
          var c = t[r2](o), u2 = c.value;
          return u2 instanceof OverloadYield ? e.resolve(u2.v).then(function(t2) {
            n("next", t2, i, f);
          }, function(t2) {
            n("throw", t2, i, f);
          }) : e.resolve(u2).then(function(t2) {
            c.value = t2, i(c);
          }, function(t2) {
            return n("throw", t2, i, f);
          });
        } catch (t2) {
          f(t2);
        }
      }
      var r;
      this.next || (regeneratorDefine(AsyncIterator.prototype), regeneratorDefine(AsyncIterator.prototype, "function" == typeof Symbol && Symbol.asyncIterator || "@asyncIterator", function() {
        return this;
      })), regeneratorDefine(this, "_invoke", function(t2, o, i) {
        function f() {
          return new e(function(e2, r2) {
            n(t2, i, e2, r2);
          });
        }
        return r = r ? r.then(f, f) : f();
      }, true);
    }
    module.exports = AsyncIterator, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@babel/runtime/helpers/regeneratorAsyncGen.js
var require_regeneratorAsyncGen = __commonJS({
  "node_modules/@babel/runtime/helpers/regeneratorAsyncGen.js"(exports, module) {
    var regenerator = require_regenerator();
    var regeneratorAsyncIterator = require_regeneratorAsyncIterator();
    function _regeneratorAsyncGen(r, e, t, o, n) {
      return new regeneratorAsyncIterator(regenerator().w(r, e, t, o), n || Promise);
    }
    module.exports = _regeneratorAsyncGen, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@babel/runtime/helpers/regeneratorAsync.js
var require_regeneratorAsync = __commonJS({
  "node_modules/@babel/runtime/helpers/regeneratorAsync.js"(exports, module) {
    var regeneratorAsyncGen = require_regeneratorAsyncGen();
    function _regeneratorAsync(n, e, r, t, o) {
      var a = regeneratorAsyncGen(n, e, r, t, o);
      return a.next().then(function(n2) {
        return n2.done ? n2.value : a.next();
      });
    }
    module.exports = _regeneratorAsync, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@babel/runtime/helpers/regeneratorKeys.js
var require_regeneratorKeys = __commonJS({
  "node_modules/@babel/runtime/helpers/regeneratorKeys.js"(exports, module) {
    function _regeneratorKeys(e) {
      var n = Object(e), r = [];
      for (var t in n)
        r.unshift(t);
      return function e2() {
        for (; r.length; )
          if ((t = r.pop()) in n)
            return e2.value = t, e2.done = false, e2;
        return e2.done = true, e2;
      };
    }
    module.exports = _regeneratorKeys, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@babel/runtime/helpers/typeof.js
var require_typeof = __commonJS({
  "node_modules/@babel/runtime/helpers/typeof.js"(exports, module) {
    function _typeof(o) {
      "@babel/helpers - typeof";
      return module.exports = _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(o2) {
        return typeof o2;
      } : function(o2) {
        return o2 && "function" == typeof Symbol && o2.constructor === Symbol && o2 !== Symbol.prototype ? "symbol" : typeof o2;
      }, module.exports.__esModule = true, module.exports["default"] = module.exports, _typeof(o);
    }
    module.exports = _typeof, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@babel/runtime/helpers/regeneratorValues.js
var require_regeneratorValues = __commonJS({
  "node_modules/@babel/runtime/helpers/regeneratorValues.js"(exports, module) {
    var _typeof = require_typeof()["default"];
    function _regeneratorValues(e) {
      if (null != e) {
        var t = e["function" == typeof Symbol && Symbol.iterator || "@@iterator"], r = 0;
        if (t)
          return t.call(e);
        if ("function" == typeof e.next)
          return e;
        if (!isNaN(e.length))
          return {
            next: function next() {
              return e && r >= e.length && (e = void 0), {
                value: e && e[r++],
                done: !e
              };
            }
          };
      }
      throw new TypeError(_typeof(e) + " is not iterable");
    }
    module.exports = _regeneratorValues, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@babel/runtime/helpers/regeneratorRuntime.js
var require_regeneratorRuntime = __commonJS({
  "node_modules/@babel/runtime/helpers/regeneratorRuntime.js"(exports, module) {
    var OverloadYield = require_OverloadYield();
    var regenerator = require_regenerator();
    var regeneratorAsync = require_regeneratorAsync();
    var regeneratorAsyncGen = require_regeneratorAsyncGen();
    var regeneratorAsyncIterator = require_regeneratorAsyncIterator();
    var regeneratorKeys = require_regeneratorKeys();
    var regeneratorValues = require_regeneratorValues();
    function _regeneratorRuntime3() {
      "use strict";
      var r = regenerator(), e = r.m(_regeneratorRuntime3), t = (Object.getPrototypeOf ? Object.getPrototypeOf(e) : e.__proto__).constructor;
      function n(r2) {
        var e2 = "function" == typeof r2 && r2.constructor;
        return !!e2 && (e2 === t || "GeneratorFunction" === (e2.displayName || e2.name));
      }
      var o = {
        "throw": 1,
        "return": 2,
        "break": 3,
        "continue": 3
      };
      function a(r2) {
        var e2, t2;
        return function(n2) {
          e2 || (e2 = {
            stop: function stop() {
              return t2(n2.a, 2);
            },
            "catch": function _catch() {
              return n2.v;
            },
            abrupt: function abrupt(r3, e3) {
              return t2(n2.a, o[r3], e3);
            },
            delegateYield: function delegateYield(r3, o2, a2) {
              return e2.resultName = o2, t2(n2.d, regeneratorValues(r3), a2);
            },
            finish: function finish(r3) {
              return t2(n2.f, r3);
            }
          }, t2 = function t3(r3, _t, o2) {
            n2.p = e2.prev, n2.n = e2.next;
            try {
              return r3(_t, o2);
            } finally {
              e2.next = n2.n;
            }
          }), e2.resultName && (e2[e2.resultName] = n2.v, e2.resultName = void 0), e2.sent = n2.v, e2.next = n2.n;
          try {
            return r2.call(this, e2);
          } finally {
            n2.p = e2.prev, n2.n = e2.next;
          }
        };
      }
      return (module.exports = _regeneratorRuntime3 = function _regeneratorRuntime4() {
        return {
          wrap: function wrap(e2, t2, n2, o2) {
            return r.w(a(e2), t2, n2, o2 && o2.reverse());
          },
          isGeneratorFunction: n,
          mark: r.m,
          awrap: function awrap(r2, e2) {
            return new OverloadYield(r2, e2);
          },
          AsyncIterator: regeneratorAsyncIterator,
          async: function async(r2, e2, t2, o2, u2) {
            return (n(e2) ? regeneratorAsyncGen : regeneratorAsync)(a(r2), e2, t2, o2, u2);
          },
          keys: regeneratorKeys,
          values: regeneratorValues
        };
      }, module.exports.__esModule = true, module.exports["default"] = module.exports)();
    }
    module.exports = _regeneratorRuntime3, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@babel/runtime/regenerator/index.js
var require_regenerator2 = __commonJS({
  "node_modules/@babel/runtime/regenerator/index.js"(exports, module) {
    var runtime = require_regeneratorRuntime()();
    module.exports = runtime;
    try {
      regeneratorRuntime = runtime;
    } catch (accidentalStrictMode) {
      if (typeof globalThis === "object") {
        globalThis.regeneratorRuntime = runtime;
      } else {
        Function("r", "regeneratorRuntime = r")(runtime);
      }
    }
  }
});

// node_modules/@babel/runtime/helpers/esm/extends.js
function _extends() {
  return _extends = Object.assign ? Object.assign.bind() : function(n) {
    for (var e = 1; e < arguments.length; e++) {
      var t = arguments[e];
      for (var r in t)
        ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);
    }
    return n;
  }, _extends.apply(null, arguments);
}

// node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
function asyncGeneratorStep(n, t, e, r, o, a, c) {
  try {
    var i = n[a](c), u2 = i.value;
  } catch (n2) {
    return void e(n2);
  }
  i.done ? t(u2) : Promise.resolve(u2).then(r, o);
}
function _asyncToGenerator(n) {
  return function() {
    var t = this, e = arguments;
    return new Promise(function(r, o) {
      var a = n.apply(t, e);
      function _next(n2) {
        asyncGeneratorStep(a, r, o, _next, _throw, "next", n2);
      }
      function _throw(n2) {
        asyncGeneratorStep(a, r, o, _next, _throw, "throw", n2);
      }
      _next(void 0);
    });
  };
}

// node_modules/gdt-jsapi/es/invoker.js
var import_regenerator = __toESM(require_regenerator2());

// node_modules/gdt-jsapi/es/utils/common.js
function isUndef(n) {
  return "undefined" === n;
}
function isMiniApp() {
  return !isUndef(typeof my) && null !== my && !isUndef(typeof my.alert);
}

// node_modules/gdt-jsapi/es/invoker.js
var BRIDGE_ERROR_CODE;
!function(e) {
  e.CANCEL = "-1", e.SUCCESS = "0", e.API_UNDEFINED = "1", e.INVALID_PARAMS = "2", e.UNKNOWN_ERROR = "3", e.UNAUTHORIZED_CALL = "4", e.WRONG_CORP_ID = "5", e.CREATE_CHAT_FAILED = "6", e.UNAUTHORIZED_API = "7", e.INVALID_CORP_ID = "8", e.SERVER_RESPONSE_ERROR = "9", e.WRONG_DEVICE_INFO = "10", e.UPLOAD_FAIL = "11", e.PROCESS_FAIL = "12", e.DUPLICATED_CALL = "13", e.TOO_LARGE_PIC = "14", e.REQUEST_REJECT_OR_INSECURE_REQUEST = "15", e.PC_NOT_ALLOWED_TO_OPEN_SIDE_PANE_OR_MODAL = "21", e.PC_CLOSE_SIDE_PANE_OR_MODAL = "22", e.UNAUTHORIZED_PARAMS = "23", e.GESTURE_PASSWORD_DOES_NOT_EXIST = "24", e.NETWORK_ERROR = "25";
}(BRIDGE_ERROR_CODE || (BRIDGE_ERROR_CODE = {}));
var API_INVOKER_TYPE;
!function(e) {
  e.MOBILE = "mobile", e.PC = "pc", e.MINI_APP = "mini", e.UNKNOWN = "unknown";
}(API_INVOKER_TYPE || (API_INVOKER_TYPE = {}));
var PLATFORM_TYPE_ENUM;
!function(e) {
  e.ANDROID = "android", e.IOS = "ios", e.UNKNOW = "unknow";
}(PLATFORM_TYPE_ENUM || (PLATFORM_TYPE_ENUM = {}));
var CONTINUOUS_EVENT_LIST;
!function(e) {
  e.UPDATE_NETWORK_STATUS = "DINGGOV_ON_NETWORK_TYPE_CHANGED", e.UPDATE_LOCATION = "DINGGOV_GEO_LOCATION_UPDATE", e.UPDATE_TRACE = "DINGGOV_TRACE_UPDATE", e.ON_SHAKE = "onShake";
}(CONTINUOUS_EVENT_LIST || (CONTINUOUS_EVENT_LIST = {}));
var Container_Type_Enum;
!function(e) {
  e.isDingTalk = "DingTalk", e.isMpaas = "mPaaS", e.isUnknow = "unknow";
}(Container_Type_Enum || (Container_Type_Enum = {}));
var ua = navigator && (navigator.swuserAgent || navigator.userAgent) || "";
var Invoker = function() {
  function e() {
    this.readyFnStack = [], this.generalEventCallbackStack = {}, this.apiList = {}, this.continuousCallbackStack = {}, this.isH5Mobile = null, this.appType = null, this.platformType = null, this.aliBridge = window && window.navigator && window.AlipayJSBridge, this.isReady = false, this.init(), console.warn("\u8BF7\u5C06 gdt-jsapi \u7248\u672C\u8BF7\u5347\u7EA7\u5230 1.9.24 \u7248\u672C\u4EE5\u4E0A\u7684\u6700\u65B0\u7248\u672C\uFF0C\u8C22\u8C22");
  }
  var n = e.prototype;
  return n.h5AndroidbridgeInit = function() {
    var e2 = this;
    this.h5BridgeReadyPromise = new Promise(function(n2, i) {
      var t = function() {
        try {
          window.WebViewJavascriptBridgeAndroid = window.nuva && window.nuva.require(), e2.execReadyFn();
        } catch (e3) {
        }
      };
      window.nuva && (void 0 === window.nuva.isReady || window.nuva.isReady) ? t() : (document.addEventListener("runtimeready", function() {
        t();
      }, false), document.addEventListener("runtimefailed", function(t2) {
        var r = t2 && t2.detail || { errorCode: BRIDGE_ERROR_CODE.INVALID_PARAMS, errorMessage: "unknown nuvajs bootstrap error" };
        e2.handleBridgeResponse(r, n2, i);
      }, false));
    });
  }, n.h5IosBridgeInit = function() {
    var e2 = this;
    this.h5BridgeReadyPromise = new Promise(function(n2, i) {
      if ("undefined" != typeof WebViewJavascriptBridge)
        try {
          WebViewJavascriptBridge.init(function(e3, n3) {
          }), e2.execReadyFn();
        } catch (e3) {
        }
      else
        document.addEventListener("WebViewJavascriptBridgeReady", function() {
          try {
            WebViewJavascriptBridge && WebViewJavascriptBridge.init(function(e3, n3) {
            }), e2.execReadyFn();
          } catch (e3) {
          }
        }, false);
    });
  }, n.init = function() {
    var e2 = this, n2 = this.getAppType(), i = this.getContainerType();
    if (n2 === API_INVOKER_TYPE.PC && window.dingtalk && !window.dingtalk.isRegister && (window.dingtalk.isRegister = true, window.dingtalk.callbackStack = {}, window.dingtalk.event.register(function(n3, i2) {
      if (e2.continuousCallbackStack[n3])
        e2.continuousCallbackStack[n3](i2);
      else if (i2) {
        var t2 = "" + i2.msgId;
        "openapi.event.emit" === n3 ? (console.log("dingtalk receive event:", i2, "identifer is", t2), window.dingtalk.callbackStack[t2] && (window.dingtalk.callbackStack[t2](i2), delete window.dingtalk.callbackStack[t2])) : "im.fileTask.addNewTask" === n3 || "im.fileTask.updateTask" === n3 ? (i2.msgId || i2.taskId) && "function" == typeof e2.continuousCallbackStack[i2.msgId || i2.taskId] && e2.continuousCallbackStack[i2.msgId || i2.taskId](n3, i2) : e2.generalEventCallbackStack[n3] && e2.generalEventCallbackStack[n3].forEach(function(n4) {
          n4.call(e2, i2);
        });
      }
    })), n2 === API_INVOKER_TYPE.MOBILE) {
      if (i === Container_Type_Enum.isDingTalk)
        this.platformType === PLATFORM_TYPE_ENUM.ANDROID ? !this.h5BridgeReadyPromise && this.h5AndroidbridgeInit() : this.platformType === PLATFORM_TYPE_ENUM.IOS && !this.h5BridgeReadyPromise && this.h5IosBridgeInit();
      else if (i === Container_Type_Enum.isMpaas && n2 === API_INVOKER_TYPE.MOBILE)
        if (window.AlipayJSBridge)
          this.execReadyFn();
        else {
          var t = setTimeout(function() {
            console.warn("window.AlipayJSBridge \u672A\u521D\u59CB\u5316\u5B8C\u6BD5\uFF0C\u8D70\u5230\u515C\u5E95\u903B\u8F91", e2.isReady, window.AlipayJSBridge), e2.isReady || e2.execReadyFn.call(e2);
          }, 5200);
          document.addEventListener("AlipayJSBridgeReady", function() {
            e2.isReady || (clearTimeout(t), e2.execReadyFn.call(e2));
          }, false);
        }
    } else
      setTimeout(function() {
        e2.execReadyFn();
      });
  }, n.execReadyFn = function() {
    this.isReady = true;
    for (var e2 = this.readyFnStack.shift(); e2; )
      e2 && e2(this), e2 = this.readyFnStack.shift();
  }, n.onReady = function(e2) {
    this.isReady ? e2 && e2(this) : this.readyFnStack.push(e2);
  }, n.setCurrentInvoker = function(e2) {
    this.currentInvoker = e2;
  }, n.getCurrentInvoker = function() {
    return this.currentInvoker;
  }, n.getBridge = function() {
    return this.aliBridge;
  }, n.getContainerType = function() {
    return /TaurusApp/g.test(ua) ? /DingTalk/g.test(ua) ? Container_Type_Enum.isDingTalk : Container_Type_Enum.isMpaas : /DingTalk/g.test(ua) ? Container_Type_Enum.isDingTalk : /mPaaSClient/g.test(ua) || /Nebula/g.test(ua) ? Container_Type_Enum.isMpaas : Container_Type_Enum.isUnknow;
  }, n.getAppType = function() {
    return this.appType || (this.isMobile() ? this.appType = API_INVOKER_TYPE.MOBILE : window && window.navigator && window.navigator.userAgent && window.navigator.userAgent.indexOf("dingtalk-win") >= 0 && window.navigator.userAgent.indexOf("TaurusApp") >= 0 ? this.appType = API_INVOKER_TYPE.PC : isMiniApp() ? this.appType = API_INVOKER_TYPE.MINI_APP : (console.warn("\u68C0\u6D4B\u5230\u9875\u9762\u5728\u975E\u4E13\u6709\u9489\u9489\u5BA2\u6237\u7AEF\u4E2D\u6253\u5F00\uFF0CJSAPI \u8C03\u7528\u53EF\u80FD\u4E0D\u4F1A\u751F\u6548\uFF01"), this.appType = API_INVOKER_TYPE.UNKNOWN)), this.appType;
  }, n.isMobile = function() {
    var e2 = /iPhone|iPad|iPod|iOS/i.test(ua), n2 = /Android/i.test(ua), i = window && window.navigator && window.navigator.userAgent || "";
    return null !== this.isH5Mobile ? this.isH5Mobile : i && i.indexOf("dingtalk-win") >= 0 ? (this.isH5Mobile = false, false) : !(!i || !(i.includes("mPaaSClient") || i.includes("Nebula") || i.includes("DingTalk"))) && (this.isH5Mobile = true, this.platformType = e2 ? PLATFORM_TYPE_ENUM.IOS : n2 ? PLATFORM_TYPE_ENUM.ANDROID : PLATFORM_TYPE_ENUM.UNKNOW, true);
  }, n.registerEvent = function(e2, n2) {
    var i = this;
    if ("function" == typeof n2)
      return this.getAppType() === API_INVOKER_TYPE.PC ? (this.generalEventCallbackStack[e2] || (this.generalEventCallbackStack[e2] = []), this.generalEventCallbackStack[e2].push(n2), function() {
        var t = i.generalEventCallbackStack[e2].findIndex(function(e3) {
          return e3 === n2;
        });
        i.generalEventCallbackStack[e2].splice(t, 1);
      }) : this.getAppType() === API_INVOKER_TYPE.MOBILE ? (document.addEventListener(e2, n2, false), function() {
        document.removeEventListener(e2, n2);
      }) : void 0;
    console.error("callback \u53C2\u6570\u5E94\u8BE5\u4E3A\u51FD\u6570");
  }, n.registerClientAPI = function(e2, n2) {
    this.apiList[e2] = n2;
  }, n.registerAPI = function(e2, n2) {
    this.isMobile();
    if ("object" == typeof n2) {
      var i = n2, t = this.getAppType();
      this.registerClientAPI(e2, i[t]);
    } else
      this.registerClientAPI(e2, n2);
  }, n.invokeMiniApp = function() {
    var e2 = _asyncToGenerator(import_regenerator.default.mark(function e3(n2, i) {
      var t = this;
      return import_regenerator.default.wrap(function(e4) {
        for (; ; )
          switch (e4.prev = e4.next) {
            case 0:
              return void 0 === i && (i = {}), e4.abrupt("return", new Promise(function(e5, r) {
                i = _extends({ _apiName: n2 }, i);
                var a = t.apiList[n2], o = t.getContainerType();
                if (!a)
                  return console.warn("API: " + n2 + "\uFF0C\u672A\u6CE8\u518C"), r("API: " + n2 + "\uFF0C\u672A\u6CE8\u518C");
                if (o === Container_Type_Enum.isMpaas) {
                  if ("function" == typeof a)
                    return void a.call(null, i, { context: my, resolve: e5, reject: r, methodName: n2 });
                  my.call(n2, i, function(n3) {
                    t.handleBridgeResponse(n3, e5, r);
                  });
                } else if (o === Container_Type_Enum.isDingTalk) {
                  if ("function" == typeof a)
                    return void a.call(null, i, { context: dd.dtBridge, resolve: e5, reject: r, methodName: n2, containerType: o, appType: API_INVOKER_TYPE.MINI_APP });
                  dd.dtBridge({ m: "taurus.common." + n2, args: i, onSuccess: function(n3) {
                    t.handleBridgeResponse(n3, e5, r);
                  }, onFail: function(n3) {
                    t.handleBridgeResponse(n3, e5, r);
                  } });
                }
              }));
            case 2:
            case "end":
              return e4.stop();
          }
      }, e3);
    }));
    return function(n2, i) {
      return e2.apply(this, arguments);
    };
  }(), n.invokeMobile = function() {
    var e2 = _asyncToGenerator(import_regenerator.default.mark(function e3(n2, i, t) {
      var r = this;
      return import_regenerator.default.wrap(function(e4) {
        for (; ; )
          switch (e4.prev = e4.next) {
            case 0:
              return void 0 === i && (i = {}), e4.abrupt("return", new Promise(function(e5, a) {
                i = _extends({ _apiName: n2 }, i);
                var o = r.apiList[n2], s = r.getContainerType();
                if (!o)
                  return console.warn("API: " + n2 + "\uFF0C\u672A\u6CE8\u518C"), a("API: " + n2 + "\uFF0C\u672A\u6CE8\u518C");
                if (s === Container_Type_Enum.isDingTalk) {
                  if (r.platformType === PLATFORM_TYPE_ENUM.IOS) {
                    var c = Object.assign({}, i);
                    if (true === c.watch && "undefined" != typeof WebViewJavascriptBridge && WebViewJavascriptBridge.registerHandler(null != t && t.dingTalkAPIName ? null == t ? void 0 : t.dingTalkAPIName : "taurus.common." + n2, function(e6, n3) {
                      "function" == typeof i.onSuccess && i.onSuccess.call(null, e6), n3 && n3({ errorCode: "0", errorMessage: "success" });
                    }), "function" == typeof o)
                      return void o.call(null, i, { context: window.WebViewJavascriptBridge, resolve: e5, reject: a, methodName: n2, containerType: s, appType: API_INVOKER_TYPE.MOBILE, platformType: PLATFORM_TYPE_ENUM.IOS, watch: c.watch });
                    void 0 !== window.WebViewJavascriptBridge && window.WebViewJavascriptBridge.callHandler("taurus.common." + n2, Object.assign({}, c), function(n3) {
                      !c.watch && r.handleBridgeResponse(n3 || {}, e5, a);
                    });
                  } else if (r.platformType === PLATFORM_TYPE_ENUM.ANDROID) {
                    var u2 = n2.split("."), d = u2.pop() || "", l = u2.join(".") || "taurus.common";
                    if ("function" == typeof o)
                      return void o.call(null, i, { context: window.WebViewJavascriptBridgeAndroid, resolve: e5, reject: a, methodName: n2, containerType: s, appType: API_INVOKER_TYPE.MOBILE, platformType: PLATFORM_TYPE_ENUM.ANDROID });
                    "function" == typeof window.WebViewJavascriptBridgeAndroid && window.WebViewJavascriptBridgeAndroid(function(n3) {
                      r.handleBridgeResponse(n3, e5, a);
                    }, function(n3) {
                      r.handleBridgeResponse(n3, e5, a);
                    }, l, d, i);
                  }
                } else if (s === Container_Type_Enum.isMpaas) {
                  if ("function" == typeof o)
                    return void o.call(null, i, { context: AlipayJSBridge, resolve: e5, reject: a, methodName: n2 });
                  AlipayJSBridge.call(n2, i, function(n3) {
                    r.handleBridgeResponse(n3, e5, a);
                  });
                }
              }));
            case 2:
            case "end":
              return e4.stop();
          }
      }, e3);
    }));
    return function(n2, i, t) {
      return e2.apply(this, arguments);
    };
  }(), n.findFitMsgId = function(e2) {
    var n2, i;
    return null !== (n2 = window.dingtalk) && void 0 !== n2 && null !== (i = n2.callbackStack) && void 0 !== i && i[e2] ? this.findFitMsgId(e2 + 1) : e2;
  }, n.invokePC = function() {
    var e2 = _asyncToGenerator(import_regenerator.default.mark(function e3(n2, i, t) {
      var r = this;
      return import_regenerator.default.wrap(function(e4) {
        for (; ; )
          switch (e4.prev = e4.next) {
            case 0:
              return void 0 === i && (i = {}), void 0 === t && (t = { msgId: 1 }), e4.abrupt("return", new Promise(function(e5, a) {
                try {
                  i = _extends({ _apiName: n2 }, i);
                  var o = r.findFitMsgId(Date.now()), s = t.pcClientAPIName || n2;
                  if (t.msgId = o, !window.dingtalk)
                    return Promise.reject(new Error("\u8BF7\u5728\u9489\u9489\u5BB9\u5668\u5185\u4F7F\u7528 JSAPI"));
                  r.apiList[n2] ? r.apiList[n2].call(null, i, t) : (console.info("invoke bridge api:", s, o, i), window.dingtalk.platform.invokeAPI(o, s, i)), window.dingtalk && window.dingtalk.isRegister && !window.dingtalk.callbackStack && (window.dingtalk.callbackStack = {}), window.dingtalk.callbackStack["" + o] = function(n3) {
                    var i2 = n3;
                    return i2.body ? e5(i2.body) : e5(i2);
                  };
                } catch (e6) {
                  a(e6);
                }
              }));
            case 3:
            case "end":
              return e4.stop();
          }
      }, e3);
    }));
    return function(n2, i, t) {
      return e2.apply(this, arguments);
    };
  }(), n.handleBridgeResponse = function(e2, n2, i) {
    e2 && e2.errorCode ? e2.errorCode === BRIDGE_ERROR_CODE.SUCCESS ? n2(e2.result) : (console.warn("API \u8C03\u7528\u5931\u8D25", e2), i(e2)) : e2 && "false" === e2.success ? i(e2) : n2(e2);
  }, n.invoke = function() {
    var e2 = _asyncToGenerator(import_regenerator.default.mark(function e3(n2, i, t) {
      var r;
      return import_regenerator.default.wrap(function(e4) {
        for (; ; )
          switch (e4.prev = e4.next) {
            case 0:
              if (void 0 === i && (i = {}), (r = this.getAppType()) !== API_INVOKER_TYPE.MOBILE) {
                e4.next = 8;
                break;
              }
              if (this.isReady) {
                e4.next = 5;
                break;
              }
              return e4.abrupt("return", Promise.reject("\u9519\u8BEF\uFF1A\u8BF7\u5728 dd.ready() \u56DE\u8C03\u4E2D\u4F7F\u7528 JSAPI\uFF0C\u5F53\u524D\u8C03\u7528\u51FD\u6570\uFF1A" + n2));
            case 5:
              return e4.abrupt("return", this.invokeMobile(n2, i, t));
            case 8:
              if (r !== API_INVOKER_TYPE.PC) {
                e4.next = 12;
                break;
              }
              return e4.abrupt("return", this.invokePC(n2, i, t));
            case 12:
              if (r !== API_INVOKER_TYPE.MINI_APP) {
                e4.next = 16;
                break;
              }
              return e4.abrupt("return", this.invokeMiniApp(n2, i));
            case 16:
              return e4.abrupt("return", Promise.reject("\u9519\u8BEF\uFF1A\u672A\u5728\u9489\u9489\u8FD0\u884C\u73AF\u5883\u4E0B\u8C03\u7528\u8BE5 API\uFF0C\u65E0\u6548\uFF0C\u8BF7\u68C0\u67E5\u8FD0\u884C\u73AF\u5883"));
            case 17:
            case "end":
              return e4.stop();
          }
      }, e3, this);
    }));
    return function(n2, i, t) {
      return e2.apply(this, arguments);
    };
  }(), n.existEventListener = function(e2) {
    return !!this.continuousCallbackStack[e2];
  }, n.registerContinuesEvent = function(e2, n2) {
    this.continuousCallbackStack[e2] = n2;
  }, n.removeContinuesEvent = function(e2) {
    this.existEventListener(e2) && (this.continuousCallbackStack[e2](), delete this.continuousCallbackStack[e2]);
  }, e;
}();
isMiniApp() || (window._invoker = window._invoker || new Invoker());
var invoker_default = isMiniApp() ? new Invoker() : window._invoker;

// node_modules/gdt-jsapi/es/utils/transformReturn.js
function c2p(n, t) {
  if (n)
    return function(e) {
      return "function" == typeof e || t.includes("Sync") || t.startsWith("create") ? n(e) : new Promise(function(t2, c) {
        n(_extends({}, e, { success: function(n2) {
          t2(n2);
        }, fail: function(n2) {
          c(n2);
        } }));
      });
    };
}

// node_modules/gdt-jsapi/es/alert.js
function alertHandler(e, r) {
  var n = r.resolve, o = r.reject, a = r.context, t = r.containerType, i = r.appType, l = r.platformType;
  if (t) {
    var c = function(e2) {
      invoker_default.handleBridgeResponse(e2, n, o);
    }, s = function(e2) {
      invoker_default.handleBridgeResponse(e2, n, o);
    };
    i === API_INVOKER_TYPE.MINI_APP ? a && a({ m: "taurus.common.alert", args: e, onSuccess: c, onFail: s }) : l === PLATFORM_TYPE_ENUM.ANDROID ? a && a(c, s, "taurus.common", "alert", e) : l === PLATFORM_TYPE_ENUM.IOS && a.callHandler("taurus.common.alert", Object.assign({}, e), function(e2) {
      n(e2);
    });
  } else
    a && a.call("alert", e, function() {
      n();
    });
}
invoker_default.registerAPI("alert", { mini: alertHandler, mobile: alertHandler }), alert.version = { android: "1.3.2", ios: "1.3.2" };
function alert(e) {
  return invoker_default.invoke("alert", e);
}

// node_modules/gdt-jsapi/es/authConfig.js
invoker_default.registerAPI("authConfig", { mini: true, mobile: true }), authConfig.version = { android: "1.2.0", ios: "1.2.0", pc: "1.2.0" };
function authConfig(i) {
  return invoker_default.invoke("authConfig", i);
}

// node_modules/gdt-jsapi/es/bizContactDepartmentsPickerExternal.js
function chooseContactHandler(e, n) {
  var t = n.resolve, r = n.reject, o = n.context, a = n.containerType, i = n.appType, c = n.platformType;
  if (a) {
    var s = function(e2) {
      invoker_default.handleBridgeResponse(e2, t, r);
    }, l = function(e2) {
      invoker_default.handleBridgeResponse(e2, t, r);
    };
    i === API_INVOKER_TYPE.MINI_APP ? o && o({ m: "taurus.common.bizContactDepartmentsPickerExternal", args: e, onSuccess: s, onFail: l }) : c === PLATFORM_TYPE_ENUM.ANDROID ? o && o(s, l, "taurus.common", "bizContactDepartmentsPickerExternal", e) : c === PLATFORM_TYPE_ENUM.IOS && o.callHandler("taurus.common.bizContactDepartmentsPickerExternal", Object.assign({}, e), function(e2) {
      invoker_default.handleBridgeResponse(e2, t, r);
    });
  } else
    o && o.call("bizContactDepartmentsPickerExternal", e, function(e2) {
      invoker_default.handleBridgeResponse(e2, t, r);
    });
}
invoker_default.registerAPI("bizContactDepartmentsPickerExternal", { mini: chooseContactHandler, mobile: chooseContactHandler, pc: function(e, n) {
  window.dingtalk.platform.invokeAPI(n.msgId, "biz.contact.departmentsPickerEx", e);
} }), bizContactDepartmentsPickerExternal.version = { android: "3.0.3", ios: "3.0.3", pc: "3.0.3" };
function bizContactDepartmentsPickerExternal(e) {
  return invoker_default.invoke("bizContactDepartmentsPickerExternal", e);
}

// node_modules/gdt-jsapi/es/bizCustomContactChooseExternal.js
function chooseContactHandler2(o, n) {
  var e = n.resolve, t = n.reject, a = n.context, i = n.containerType, r = n.appType, s = n.platformType;
  if (i) {
    var c = function(o2) {
      invoker_default.handleBridgeResponse(o2, e, t);
    }, l = function(o2) {
      invoker_default.handleBridgeResponse(o2, e, t);
    };
    r === API_INVOKER_TYPE.MINI_APP ? a && a({ m: "taurus.common.bizCustomContactChooseExternal", args: o, onSuccess: c, onFail: l }) : s === PLATFORM_TYPE_ENUM.ANDROID ? a && a(c, l, "taurus.common", "bizCustomContactChooseExternal", o) : s === PLATFORM_TYPE_ENUM.IOS && a.callHandler("taurus.common.bizCustomContactChooseExternal", Object.assign({}, o), function(o2) {
      invoker_default.handleBridgeResponse(o2, e, t);
    });
  } else
    a && a.call("bizCustomContactChooseExternal", o, function(o2) {
      invoker_default.handleBridgeResponse(o2, e, t);
    });
}
invoker_default.registerAPI("bizCustomContactChooseExternal", { mini: chooseContactHandler2, mobile: chooseContactHandler2, pc: function(o, n) {
  window.dingtalk.platform.invokeAPI(n.msgId, "biz.customContact.chooseEx", o);
} }), bizCustomContactChooseExternal.version = { android: "3.0.3", ios: "3.0.3", pc: "3.0.3" };
function bizCustomContactChooseExternal(o) {
  return invoker_default.invoke("bizCustomContactChooseExternal", o);
}

// node_modules/gdt-jsapi/es/bizCustomContactMultipleChooseExternal.js
function chooseContactHandler3(o, e) {
  var t = e.resolve, n = e.reject, i = e.context, l = e.containerType, a = e.appType, r = e.platformType;
  if (l) {
    var s = function(o2) {
      invoker_default.handleBridgeResponse(o2, t, n);
    }, c = function(o2) {
      invoker_default.handleBridgeResponse(o2, t, n);
    };
    a === API_INVOKER_TYPE.MINI_APP ? i && i({ m: "taurus.common.bizCustomContactMultipleChooseExternal", args: o, onSuccess: s, onFail: c }) : r === PLATFORM_TYPE_ENUM.ANDROID ? i && i(s, c, "taurus.common", "bizCustomContactMultipleChooseExternal", o) : r === PLATFORM_TYPE_ENUM.IOS && i.callHandler("taurus.common.bizCustomContactMultipleChooseExternal", Object.assign({}, o), function(o2) {
      invoker_default.handleBridgeResponse(o2, t, n);
    });
  } else
    i && i.call("bizCustomContactMultipleChooseExternal", o, function(o2) {
      invoker_default.handleBridgeResponse(o2, t, n);
    });
}
invoker_default.registerAPI("bizCustomContactMultipleChooseExternal", { mini: chooseContactHandler3, mobile: chooseContactHandler3, pc: function(o, e) {
  window.dingtalk.platform.invokeAPI(e.msgId, "biz.customContact.multipleChooseEx", o);
} }), bizCustomContactMultipleChooseExternal.version = { android: "3.0.3", ios: "3.0.3", pc: "3.0.3" };
function bizCustomContactMultipleChooseExternal(o) {
  return invoker_default.invoke("bizCustomContactMultipleChooseExternal", o);
}

// node_modules/gdt-jsapi/es/callPhone.js
invoker_default.registerAPI("callPhone", { mini: true, mobile: true }), callPhone.version = { android: "1.1.0", ios: "1.1.0" };
function callPhone(o) {
  return invoker_default.invoke("callPhone", o);
}

// node_modules/gdt-jsapi/es/canIUse.js
var import_regenerator2 = __toESM(require_regenerator2());

// node_modules/gdt-jsapi/es/version.js
invoker_default.registerAPI("version", { mini: true, mobile: true, pc: function(i, n) {
  window.dingtalk.platform.invokeAPI(n.msgId, "version", {});
} });
function version() {
  return invoker_default.invoke("version");
}

// node_modules/gdt-jsapi/es/utils/compareVersion.js
var GT = 1;
var LT = -1;
var EQ = 0;
function compareItem(r, t) {
  return "number" != typeof r && (r = 0), "number" != typeof t && (t = 0), r > t ? GT : r < t ? LT : EQ;
}
function compareVersion(r, t) {
  void 0 === r && (r = ""), void 0 === t && (t = "");
  var e = /^\d+(\.\d+){2,3}$/;
  if (!e.test(r) || !e.test(t))
    throw new Error("\u8BF7\u4F20\u5165\u6B63\u786E\u7684\u7248\u672C\u53F7\u683C\u5F0F");
  for (var n = ("" + r).split(".").map(function(r2) {
    return parseInt(r2, 10);
  }), o = ("" + t).split(".").map(function(r2) {
    return parseInt(r2, 10);
  }), a = Math.max(n.length, o.length), p = 0, i = 0; i < a && (p = compareItem(n[i], o[i])) === EQ; i++)
    ;
  return p;
}
var compareVersion_default = compareVersion;

// node_modules/gdt-jsapi/es/canIUse.js
var u = navigator && navigator.userAgent || "";
var isAndroid = function() {
  return u.indexOf("Android") > -1 || u.indexOf("Adr") > -1;
};
var isiOS = function() {
  return !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
};
var isWindows = function() {
  return /(windows)/i.test(navigator.userAgent);
};
function canIUse(r) {
  return _canIUse.apply(this, arguments);
}
function _canIUse() {
  return (_canIUse = _asyncToGenerator(import_regenerator2.default.mark(function r(e) {
    var n, i, t, o;
    return import_regenerator2.default.wrap(function(r2) {
      for (; ; )
        switch (r2.prev = r2.next) {
          case 0:
            if (es_default[e]) {
              r2.next = 2;
              break;
            }
            return r2.abrupt("return", false);
          case 2:
            return r2.next = 4, version();
          case 4:
            return n = r2.sent, i = n.version, t = es_default[e].version, o = isAndroid() ? "android" : isiOS() ? "ios" : isWindows() ? "pc" : "unknown", r2.abrupt("return", !(!t || !t[o]) && compareVersion_default(i, t[o]) > 0);
          case 9:
          case "end":
            return r2.stop();
        }
    }, r);
  }))).apply(this, arguments);
}

// node_modules/gdt-jsapi/es/checkVPNAppInstalled.js
invoker_default.registerAPI("checkVPNAppInstalled", { mini: true, mobile: true }), checkVPNAppInstalled.version = { android: "1.6.0", ios: "1.6.0" };
function checkVPNAppInstalled() {
  return invoker_default.invoke("checkVPNAppInstalled");
}

// node_modules/gdt-jsapi/es/checkVPNAppOnline.js
invoker_default.registerAPI("checkVPNAppOnline", { mini: true, mobile: true }), checkVPNAppOnline.version = { android: "1.6.0", ios: "1.6.0" };
function checkVPNAppOnline() {
  return invoker_default.invoke("checkVPNAppOnline");
}

// node_modules/gdt-jsapi/es/chooseContact.js
var SelectVersionEnum;
!function(I) {
  I[I.DEFAULT = 1] = "DEFAULT", I[I.NEW = 2] = "NEW";
}(SelectVersionEnum || (SelectVersionEnum = {}));
var PanelTypeEnum;
!function(I) {
  I[I.GLOBAL_ORG = 1] = "GLOBAL_ORG", I[I.FRIEND = 2] = "FRIEND", I[I.GROUP = 4] = "GROUP", I[I.RECOMMEND = 5] = "RECOMMEND", I[I.SPECIAL_ATTENTION = 7] = "SPECIAL_ATTENTION", I[I.LOAD_GROUP_PERSON = 8] = "LOAD_GROUP_PERSON", I[I.ORG = 9] = "ORG";
}(PanelTypeEnum || (PanelTypeEnum = {}));
var VisibilityCodesEnum;
!function(I) {
  I.PHONE_HIDE = "PHONE_HIDE", I.CHAT_INVALID = "CHAT_INVALID", I.GROUP_CHAT_PULL_INVALID = "GROUP_CHAT_PULL_INVALID", I.APP_DING_INVALID = "APP_DING_INVALID", I.PHONE_DING_INVALID = "PHONE_DING_INVALID", I.SMS_DING_INVALID = "SMS_DING_INVALID", I.AUDIO_VIDEO_HIDE = "AUDIO_VIDEO_HIDE";
}(VisibilityCodesEnum || (VisibilityCodesEnum = {})), invoker_default.registerAPI("chooseContact", { pc: function(I, o) {
  window.dingtalk.platform.invokeAPI(o.msgId, "biz.contact.choose", I);
} }), chooseContact.version = { pc: "1.1.0" };
function chooseContact(I) {
  return invoker_default.invoke("chooseContact", I);
}

// node_modules/gdt-jsapi/es/chooseContactWithComplexPicker.js
function chooseContactHandler4(o, e) {
  var n = e.resolve, c = e.reject, i = e.context, t = e.containerType, r = e.appType, a = e.platformType;
  if (t) {
    var s = function(o2) {
      invoker_default.handleBridgeResponse(o2, n, c);
    }, l = function(o2) {
      invoker_default.handleBridgeResponse(o2, n, c);
    };
    r === API_INVOKER_TYPE.MINI_APP ? i && i({ m: "taurus.common.chooseContactWithComplexPicker", args: o, onSuccess: s, onFail: l }) : a === PLATFORM_TYPE_ENUM.ANDROID ? i && i(s, l, "taurus.common", "chooseContactWithComplexPicker", o) : a === PLATFORM_TYPE_ENUM.IOS && i.callHandler("taurus.common.chooseContactWithComplexPicker", Object.assign({}, o), function(o2) {
      invoker_default.handleBridgeResponse(o2, n, c);
    });
  } else
    i && i.call("chooseContactWithComplexPicker", o, function(e2) {
      e2.error && e2.error.toString() === BRIDGE_ERROR_CODE.API_UNDEFINED ? i.call("complexPicker", o, function(o2) {
        invoker_default.handleBridgeResponse(o2, n, c);
      }) : invoker_default.handleBridgeResponse(e2, n, c);
    });
}
invoker_default.registerAPI("chooseContactWithComplexPicker", { mini: chooseContactHandler4, mobile: chooseContactHandler4, pc: function(o, e) {
  window.dingtalk.platform.invokeAPI(e.msgId, "biz.contact.complexPicker", o);
} }), chooseContactWithComplexPicker.version = { android: "1.1.0", ios: "1.1.0", pc: "1.6.2" };
function chooseContactWithComplexPicker(o) {
  return invoker_default.invoke("chooseContactWithComplexPicker", o);
}

// node_modules/gdt-jsapi/es/chooseDateRangeWithCalendar.js
invoker_default.registerAPI("chooseDateRangeWithCalendar", { mini: true, mobile: true }), chooseDateRangeWithCalendar.version = { android: "1.3.10", ios: "1.3.10" };
function chooseDateRangeWithCalendar(e) {
  return invoker_default.invoke("chooseDateRangeWithCalendar", e);
}

// node_modules/gdt-jsapi/es/chooseDayWithCalendar.js
invoker_default.registerAPI("chooseDayWithCalendar", { mini: true, mobile: true }), chooseDayWithCalendar.version = { android: "1.3.10", ios: "1.3.10" };
function chooseDayWithCalendar(o) {
  return invoker_default.invoke("chooseDayWithCalendar", o);
}

// node_modules/gdt-jsapi/es/chooseDepartments.js
invoker_default.registerAPI("chooseDepartments", { mini: true, mobile: true, pc: function(e, o) {
  window.dingtalk.platform.invokeAPI(o.msgId, "biz.contact.departmentsPicker", e);
} }), chooseDepartments.version = { android: "1.1.0", ios: "1.1.0", pc: "1.6.2" };
function chooseDepartments(e) {
  return invoker_default.invoke("chooseDepartments", e);
}

// node_modules/gdt-jsapi/es/chooseFile.js
invoker_default.registerAPI("chooseFile", { mini: true, mobile: true, pc: function(o, i) {
  window.dingtalk.platform.invokeAPI(i.msgId, "biz.util.chooseFile", o);
} }), chooseFile.version = { android: "1.3.2", ios: "1.3.2", pc: "1.3.6" };
function chooseFile(o) {
  return invoker_default.invoke("chooseFile", o);
}

// node_modules/gdt-jsapi/es/chooseHalfDayWithCalendar.js
invoker_default.registerAPI("chooseHalfDayWithCalendar", { mini: true, mobile: true }), chooseHalfDayWithCalendar.version = { android: "1.3.10", ios: "1.3.10" };
function chooseHalfDayWithCalendar(o) {
  return invoker_default.invoke("chooseHalfDayWithCalendar", o);
}

// node_modules/gdt-jsapi/es/chooseImage.js
var ImageType;
!function(e) {
  e[e.image = 0] = "image", e[e.video = 1] = "video";
}(ImageType || (ImageType = {})), invoker_default.registerAPI("dgChooseImage", { mini: true, mobile: true, pc: function(e, o) {
  window.dingtalk.platform.invokeAPI(o.msgId, "biz.util.dgChooseImage", e);
} }), chooseImage.version = { android: "1.3.2", ios: "1.3.2", pc: "1.3.6" };
function chooseImage(e) {
  return invoker_default.invoke("dgChooseImage", _extends({}, e, { _apiName: "chooseImage" }));
}

// node_modules/gdt-jsapi/es/chooseInterconnectionChat.js
invoker_default.registerAPI("chooseInterconnectionChat", { mini: true, mobile: true, pc: function(o, n) {
  window.dingtalk.platform.invokeAPI(n.msgId, "chooseInterconnectionChat", o);
} }), chooseContact2.version = { pc: "2.9.0", ios: "2.9.0", android: "2.9.0" };
function chooseContact2(o) {
  return invoker_default.invoke("chooseInterconnectionChat", o);
}

// node_modules/gdt-jsapi/es/chooseLocalImage.js
invoker_default.registerAPI("chooseImage", { mini: true }), chooseLocalImage.version = { android: "1.6.2", ios: "1.6.2" };
function chooseLocalImage(e) {
  return new Promise(function(o, n) {
    my.chooseImage(_extends({}, e, { success: function(e2) {
      o(e2);
    }, fail: function(e2) {
      n(e2);
    } }));
  });
}

// node_modules/gdt-jsapi/es/chooseSpaceDir.js
invoker_default.registerAPI("chooseSpaceDir", { mini: true, mobile: true, pc: function(o, i) {
  void 0 === o && (o = {}), window.dingtalk.platform.invokeAPI(i.msgId, "biz.util.chooseSpaceDir", o);
} }), chooseSpaceDir.version = { android: "1.6.0", ios: "1.6.0", pc: "2.6.0" };
function chooseSpaceDir() {
  return invoker_default.invoke("chooseSpaceDir");
}

// node_modules/gdt-jsapi/es/chooseTimeWithCalendar.js
invoker_default.registerAPI("chooseTimeWithCalendar", { mini: true, mobile: true }), chooseTimeWithCalendar.version = { android: "1.3.10", ios: "1.3.10" };
function chooseTimeWithCalendar(e) {
  return invoker_default.invoke("chooseTimeWithCalendar", e);
}

// node_modules/gdt-jsapi/es/chooseVideo.js
invoker_default.registerAPI("chooseVideo", { mini: true, mobile: true }), chooseVideo.version = { android: "1.6.2", ios: "1.6.2" };
function chooseVideo(o) {
  return invoker_default.invoke("chooseVideo", o);
}

// node_modules/gdt-jsapi/es/closePage.js
function popWindow(e, n) {
  var o = n.resolve, i = n.reject, a = n.context, r = n.containerType, s = n.appType, t = n.platformType;
  if (r) {
    var l = function(e2) {
      invoker_default.handleBridgeResponse(e2, o, i);
    }, p = function(e2) {
      invoker_default.handleBridgeResponse(e2, o, i);
    };
    s === API_INVOKER_TYPE.MINI_APP ? a && a({ m: "biz.navigation.close", args: e, onSuccess: l, onFail: p }) : t === PLATFORM_TYPE_ENUM.ANDROID ? a && a(l, p, "biz.navigation", "close", e) : t === PLATFORM_TYPE_ENUM.IOS && a.callHandler("biz.navigation.close", Object.assign({}, e), function(e2) {
      invoker_default.handleBridgeResponse(e2, o, i);
    });
  } else
    a && a.call("popWindow", e, function(e2) {
      invoker_default.handleBridgeResponse(e2, o, i);
    });
}
invoker_default.registerAPI("closePage", { mini: popWindow, mobile: popWindow, pc: function(e, n) {
  window.dingtalk.platform.invokeAPI(n.msgId, "biz.navigation.quit", e);
} }), closePage.version = { android: "1.2.0", ios: "1.2.0", pc: "1.2.0" };
function closePage(e) {
  return invoker_default.invoke("closePage", _extends({}, e, { _apiName: "closePage" }));
}

// node_modules/gdt-jsapi/es/complexPickerAdmin.js
var EmployeeKeyTypeEnum;
!function(e) {
  e.CODE = "code", e.ACCOUNTID = "accountId";
}(EmployeeKeyTypeEnum || (EmployeeKeyTypeEnum = {}));
var KeyTypeEnum;
!function(e) {
  e.CODE = "code", e.id = "id";
}(KeyTypeEnum || (KeyTypeEnum = {})), invoker_default.registerAPI("complexPickerAdmin", { pc: function(e, n) {
  window.dingtalk.platform.invokeAPI(n.msgId, "biz.contact.complexPickerAdmin", e);
} }), complexPickerAdmin.version = { pc: "2.8.0" };
function complexPickerAdmin(e) {
  return invoker_default.invoke("complexPickerAdmin", e);
}

// node_modules/gdt-jsapi/es/confirm.js
invoker_default.registerAPI("confirm", { mini: function(e, n) {
  var o = n.resolve, r = n.reject, t = n.context, i = n.containerType, s = { message: e.message, title: e.title, okButton: e.buttonLabels[0], cancelButton: e.buttonLabels[1] };
  i === Container_Type_Enum.isDingTalk ? t({ m: "taurus.common.confirm", args: s, onSuccess: function(e2) {
    var n2 = { errorCode: BRIDGE_ERROR_CODE.SUCCESS, result: { buttonIndex: e2.ok ? 0 : 1 } };
    invoker_default.handleBridgeResponse(n2, o, r);
  }, onFail: function(e2) {
    invoker_default.handleBridgeResponse(e2, o, r);
  } }) : t && t.call("confirm", s, function(e2) {
    var n2 = { errorCode: BRIDGE_ERROR_CODE.SUCCESS, result: { buttonIndex: e2.ok ? 0 : 1 } };
    invoker_default.handleBridgeResponse(n2, o, r);
  });
}, mobile: function(e, n) {
  var o = n.resolve, r = n.reject, t = n.context, i = n.containerType, s = n.platformType, a = { message: e.message, title: e.title, okButton: e.buttonLabels[0], cancelButton: e.buttonLabels[1] };
  if (i) {
    s === PLATFORM_TYPE_ENUM.ANDROID ? t && t(function(e2) {
      var n2 = { errorCode: BRIDGE_ERROR_CODE.SUCCESS, result: { buttonIndex: e2.ok ? 0 : 1 } };
      invoker_default.handleBridgeResponse(n2, o, r);
    }, function(e2) {
      invoker_default.handleBridgeResponse(e2, o, r);
    }, "taurus.common", "confirm", a) : s === PLATFORM_TYPE_ENUM.IOS && t.callHandler("taurus.common.confirm", Object.assign({}, a), function(e2) {
      invoker_default.handleBridgeResponse(e2, o, r);
    });
  } else
    t && t.call("confirm", a, function(e2) {
      var n2 = { errorCode: BRIDGE_ERROR_CODE.SUCCESS, result: { buttonIndex: e2.ok ? 0 : 1 } };
      invoker_default.handleBridgeResponse(n2, o, r);
    });
}, pc: function(e, n) {
  window.dingtalk.platform.invokeAPI(n.msgId, "device.notification.confirm", e);
} }), confirm.version = { android: "1.3.2", ios: "1.3.2", pc: "1.3.2" };
function confirm(e) {
  return invoker_default.invoke("confirm", e);
}

// node_modules/gdt-jsapi/es/copyToClipboard.js
invoker_default.registerAPI("copyToClipboard", { mini: true, mobile: true }), copyToClipboard.version = { android: "1.3.2", ios: "1.3.2" };
function copyToClipboard(o) {
  return invoker_default.invoke("copyToClipboard", o);
}

// node_modules/gdt-jsapi/es/createChatGroup.js
invoker_default.registerAPI("createChatGroup", { mini: true, mobile: true }), createChatGroup.version = { android: "1.3.0", ios: "1.3.0", pc: "1.3.0" };
function createChatGroup(r) {
  return invoker_default.invoke("createChatGroup", r);
}

// node_modules/gdt-jsapi/es/createDing.js
invoker_default.registerAPI("createDing", { mini: true, mobile: true, pc: function(i, e) {
  window.dingtalk.platform.invokeAPI(e.msgId, "biz.ding.create", i);
} }), createDing.version = { android: "1.3.9", ios: "1.3.9", pc: "1.3.9" };
function createDing(i) {
  return invoker_default.invoke("createDing", i);
}

// node_modules/gdt-jsapi/es/createDingV2.js
invoker_default.registerAPI("createDingV2", { mini: true, mobile: true, pc: function(i, e) {
  window.dingtalk.platform.invokeAPI(e.msgId, "biz.ding.createV2", i);
} }), createDingV2.version = { android: "2.7.0", ios: "2.7.0", pc: "2.7.0" };
function createDingV2(i) {
  return invoker_default.invoke("createDingV2", i);
}

// node_modules/gdt-jsapi/es/createVideoConf.js
function createVideoConfHandler(e, n) {
  void 0 === e && (e = {});
  var o = n.resolve, i = n.reject, r = n.context, c = n.containerType, t = n.appType, a = n.platformType;
  if (c) {
    var d = function(e2) {
      invoker_default.handleBridgeResponse(e2, o, i);
    }, f = function(e2) {
      invoker_default.handleBridgeResponse(e2, o, i);
    };
    t === API_INVOKER_TYPE.MINI_APP ? r && r({ m: "biz.conference.createVideoConf", args: e, onSuccess: d, onFail: f }) : a === PLATFORM_TYPE_ENUM.ANDROID ? r && r(d, f, "biz.conference", "createVideoConf", e) : a === PLATFORM_TYPE_ENUM.IOS && r.callHandler("biz.conference.createVideoConf", Object.assign({}, e), function(e2) {
      invoker_default.handleBridgeResponse(e2, o, i);
    });
  } else
    r && r.call("createVideoConf", e, function() {
      o();
    });
}
invoker_default.registerAPI("createVideoConf", { mini: createVideoConfHandler, mobile: createVideoConfHandler, pc: function(e, n) {
  window.dingtalk.platform.invokeAPI(n.msgId, "biz.conference.createVideoConf", _extends({}, e));
} }), createVideoConf.version = { android: "3.7.5", ios: "3.7.5", pc: "3.7.5" };
function createVideoConf(e) {
  return invoker_default.invoke("createVideoConf", e);
}

// node_modules/gdt-jsapi/es/createVideoMeeting.js
invoker_default.registerAPI("createVideoMeeting", { mini: true, mobile: true, pc: function(e, i) {
  window.dingtalk.platform.invokeAPI(i.msgId, "biz.meeting.create", _extends({ isVideoConference: true }, e));
} }), createVideoMeeting.version = { android: "1.3.1.1", ios: "1.3.1.1", pc: "1.9.4" };
function createVideoMeeting(e) {
  return invoker_default.invoke("createVideoMeeting", e);
}

// node_modules/gdt-jsapi/es/dealWithBackAction.js
invoker_default.registerAPI("dealWithBackAction", { mobile: true }), dealWithBackAction.version = { android: "1.2.0.10" };
function dealWithBackAction(i) {
  return invoker_default.invoke("dealWithBackAction", i);
}

// node_modules/gdt-jsapi/es/disableClosePage.js
invoker_default.registerAPI("disableClosePage", { mini: true, mobile: true, pc: function(e, i) {
  window.dingtalk.platform.invokeAPI(i.msgId, "biz.util.disableClosePage", {});
} }), disableClosePage.version = { pc: "3.4.0" };
function disableClosePage() {
  return invoker_default.invoke("disableClosePage");
}

// node_modules/gdt-jsapi/es/disablePullToRefresh.js
invoker_default.registerAPI("disablePullToRefresh", { mobile: function(e, l) {
  var i = l.resolve, o = l.reject, r = l.context, n = l.containerType, s = l.platformType;
  if (n) {
    s === PLATFORM_TYPE_ENUM.ANDROID ? r && r(function() {
      i();
    }, function() {
      o();
    }, "ui.pullToRefresh", "disable", {}) : s === PLATFORM_TYPE_ENUM.IOS && r.callHandler("ui.pullToRefresh.disable", Object.assign({}, {}), function(e2) {
      i(e2);
    });
  } else
    r && r.call("pullRefresh", { pullRefresh: false }, function() {
      i();
    });
} }), disablePullToRefresh.version = { android: "1.3.0", ios: "1.3.0" };
function disablePullToRefresh() {
  return invoker_default.invoke("disablePullToRefresh", { _apiName: "disablePullToRefresh" });
}

// node_modules/gdt-jsapi/es/disableWebviewBounce.js
invoker_default.registerAPI("disableWebviewBounce", { mobile: function(e, i) {
  var n = i.resolve, o = i.reject, c = i.context, a = i.containerType, b = i.platformType;
  if (a) {
    b === PLATFORM_TYPE_ENUM.ANDROID ? c && c(function() {
      n();
    }, function() {
      o();
    }, "ui.webViewBounce", "disable", {}) : b === PLATFORM_TYPE_ENUM.IOS && c.callHandler("ui.webViewBounce.disable", Object.assign({}, {}), function(e2) {
      n(e2);
    });
  } else
    c && c.call("bounce", { enable: false }, function(e2) {
      n(e2);
    });
} }), disableWebviewBounce.version = { ios: "1.3.0" };
function disableWebviewBounce() {
  return invoker_default.invoke("disableWebviewBounce", { _apiName: "disableWebviewBounce" });
}

// node_modules/gdt-jsapi/es/downloadAudio.js
invoker_default.registerAPI("downloadAudio", { mini: true, mobile: true }), downloadAudio.version = { android: "1.3.0", ios: "1.3.0" };
function downloadAudio(o) {
  return invoker_default.invoke("downloadAudio", o);
}

// node_modules/gdt-jsapi/es/downloadFile.js
var TASK_COMPLETE = 1;
function downloadFile(e) {
  return invoker_default.invoke("downloadFile", e);
}
invoker_default.registerAPI("downloadFile", { mini: function(e, n) {
  var i = n.resolve, o = n.reject, r = n.containerType, t = n.context;
  if (r === Container_Type_Enum.isDingTalk) {
    t && t({ m: "taurus.common.downloadFile", args: e, onSuccess: function(e2) {
      invoker_default.handleBridgeResponse(e2, i, o);
    }, onFail: function(e2) {
      invoker_default.handleBridgeResponse(e2, i, o);
    } });
  } else
    t && t.call("downloadFile", e, function(e2) {
      e2.error ? o(e2) : i(e2);
    });
}, pc: function(e, n) {
  window.dingtalk.platform.invokeAPI(n.msgId, "biz.util.downloadFile", e), invoker_default.registerContinuesEvent(n.msgId, function(i, o) {
    "im.fileTask.addNewTask" === i && (invoker_default.removeContinuesEvent(n.msgId), invoker_default.registerContinuesEvent(o.taskId, function(n2, i2) {
      if ("im.fileTask.updateTask" === n2) {
        var o2 = i2.doneSize, r = i2.fileName, t = i2.filePath, a = i2.fileSize, l = i2.speed;
        e.onProgress({ doneSize: o2, fileName: r, filePath: t, fileSize: a, speed: l }), i2.status === TASK_COMPLETE && invoker_default.removeContinuesEvent(i2.taskId);
      }
    }));
  });
} }), downloadFile.version = { pc: "1.3.5" };
var downloadFile_default = downloadFile;

// node_modules/gdt-jsapi/es/enablePullToRefresh.js
invoker_default.registerAPI("enablePullToRefresh", { mobile: function(e, l) {
  var n = l.resolve, o = l.reject, r = l.context, i = l.containerType, f = l.platformType;
  if (i) {
    f === PLATFORM_TYPE_ENUM.ANDROID ? r && r(function() {
      n();
    }, function() {
      o();
    }, "ui.pullToRefresh", "enable", {}) : f === PLATFORM_TYPE_ENUM.IOS && r.callHandler("ui.pullToRefresh.enable", Object.assign({}, {}), function() {
      n();
    });
  } else
    r && r.call("pullRefresh", { pullRefresh: true }, function() {
      n();
    });
} }), enablePullToRefresh.version = { android: "1.3.0", ios: "1.3.0" };
function enablePullToRefresh() {
  return invoker_default.invoke("enablePullToRefresh", { _apiName: "enablePullToRefresh" });
}

// node_modules/gdt-jsapi/es/enableVpn.js
invoker_default.registerAPI("enableVpn", { mini: true, mobile: true }), enableVpn.version = { android: "1.1.0", ios: "1.1.0" };
function enableVpn() {
  return invoker_default.invoke("enableVpn");
}

// node_modules/gdt-jsapi/es/enableWebviewBounce.js
invoker_default.registerAPI("enableWebviewBounce", { mobile: function(e, n) {
  var o = n.resolve, i = n.reject, c = n.context, a = n.containerType, r = n.platformType;
  if (a) {
    r === PLATFORM_TYPE_ENUM.ANDROID ? c && c(function() {
      o();
    }, function() {
      i();
    }, "taurus.common", "bounce", { enable: true }) : r === PLATFORM_TYPE_ENUM.IOS && c.callHandler("taurus.common.bounce", Object.assign({}, { enable: true }), function(e2) {
      o(e2);
    });
  } else
    c && c.call("bounce", { enable: true }, function(e2) {
      o(e2);
    });
} }), enableWebviewBounce.version = { ios: "1.3.0" };
function enableWebviewBounce() {
  return invoker_default.invoke("enableWebviewBounce", { _apiName: "enableWebviewBounce" });
}

// node_modules/gdt-jsapi/es/exclusiveInvoke.js
invoker_default.registerAPI("exclusiveInvoke", { mini: true, mobile: true }), exclusiveInvoke.version = { ios: "1.9.5", android: "1.9.5" };
function exclusiveInvoke(e) {
  return invoker_default.invoke("exclusiveInvoke", e);
}

// node_modules/gdt-jsapi/es/faceComparison.js
invoker_default.registerAPI("faceComparison", { mobile: true, mini: true }), faceComparison.version = { android: "2.4.0", ios: "2.4.0" };
function faceComparison(o) {
  return invoker_default.invoke("faceComparison", o);
}

// node_modules/gdt-jsapi/es/faceRecognition.js
var ImageTypeEnum;
!function(e) {
  e.PNG = "png", e.JPG = "jpg";
}(ImageTypeEnum || (ImageTypeEnum = {})), invoker_default.registerAPI("faceRecognition", { mobile: true, mini: true }), faceRecognition.version = { android: "2.4.0", ios: "2.4.0" };
function faceRecognition(e) {
  return invoker_default.invoke("faceRecognition", e);
}

// node_modules/gdt-jsapi/es/getAppInstallStatus.js
invoker_default.registerAPI("getAppInstallStatus", { mini: true, mobile: true }), getAppInstallStatus.version = { android: "2.1.10", ios: "2.1.10" };
function getAppInstallStatus(t) {
  return invoker_default.invoke("getAppInstallStatus", t);
}

// node_modules/gdt-jsapi/es/getAuthCode.js
invoker_default.registerAPI("getAuthCode", { pc: function(e, o) {
  window.dingtalk.platform.invokeAPI(o.msgId, "runtime.permission.requestAuthCode", e);
}, mobile: true, mini: true }), getAuthCode.version = { android: "1.0.0", ios: "1.0.0", pc: "1.0.0" };
function getAuthCode(e) {
  return invoker_default.invoke("getAuthCode", e);
}

// node_modules/gdt-jsapi/es/getConfig.js
invoker_default.registerAPI("getConfig", { mobile: true, mini: true, pc: function(i, o) {
  window.dingtalk.platform.invokeAPI(o.msgId, "getConfig", i);
} }), getConfig.version = { android: "1.3.2", ios: "1.3.2", pc: "1.3.2" };
function getConfig() {
  return invoker_default.invoke("getConfig", {});
}

// node_modules/gdt-jsapi/es/getContainerType.js
function getContainerType() {
  return invoker_default.getContainerType();
}

// node_modules/gdt-jsapi/es/getDeviceId.js
invoker_default.registerAPI("getDeviceId", { mobile: true, mini: true }), getDeviceId.version = { android: "2.5.0", ios: "2.5.0" };
function getDeviceId() {
  return invoker_default.invoke("getDeviceId", {});
}

// node_modules/gdt-jsapi/es/getFromClipboard.js
invoker_default.registerAPI("getFromClipboard", { mini: true, mobile: true, pc: function(o, i) {
  window.dingtalk.platform.invokeAPI(i.msgId, "util.clipboardData.getData", o);
} }), getFromClipboard.version = { android: "2.3.1", ios: "2.3.1", pc: "2.6.10" };
function getFromClipboard() {
  return invoker_default.invoke("getFromClipboard");
}

// node_modules/gdt-jsapi/es/getGeolocation.js
invoker_default.registerAPI("getGeolocation", { mini: true, mobile: true }), getGeolocation.version = { android: "1.2.0", ios: "1.2.0" };
function getGeolocation(o) {
  return invoker_default.invoke("getGeolocation", o);
}

// node_modules/gdt-jsapi/es/getGeolocationStatus.js
invoker_default.registerAPI("getGeolocationStatus", { mobile: true, mini: true }), getGeolocationStatus.version = { android: "1.6.2", ios: "1.6.2" };
function getGeolocationStatus(o) {
  return invoker_default.invoke("getGeolocationStatus", o);
}

// node_modules/gdt-jsapi/es/getHotspotInfo.js
invoker_default.registerAPI("getHotspotInfo", { mobile: true, mini: true }), getHotspotInfo.version = { android: "1.3.5", ios: "1.3.5" };
function getHotspotInfo() {
  return invoker_default.invoke("getHotspotInfo");
}

// node_modules/gdt-jsapi/es/getLanguageSetting.js
invoker_default.registerAPI("getLanguageSetting", { mobile: true, mini: true, pc: function(e, n) {
  window.dingtalk.platform.invokeAPI(n.msgId, "getLanguageSetting", e);
} }), getLanguageSetting.version = { android: "1.4.0", ios: "1.4.0", pc: "1.4.0" };
function getLanguageSetting() {
  return invoker_default.invoke("getLanguageSetting");
}

// node_modules/gdt-jsapi/es/getLoginUser.js
invoker_default.registerAPI("getLoginUser", { mobile: true, mini: true }), getLoginUser.version = { android: "1.1.0", ios: "1.1.0" };
function getLoginUser() {
  return invoker_default.invoke("getLoginUser");
}

// node_modules/gdt-jsapi/es/getNetworkType.js
invoker_default.registerAPI("getNetworkType", { mobile: true, mini: true }), getNetworkType.version = { android: "1.3.0", ios: "1.3.0" };
function getNetworkType() {
  return invoker_default.invoke("getNetworkType");
}

// node_modules/gdt-jsapi/es/getPhoneInfo.js
invoker_default.registerAPI("getPhoneInfo", { mini: true, mobile: true }), getPhoneInfo.version = { android: "1.3.5", ios: "1.3.5" };
function getPhoneInfo() {
  return invoker_default.invoke("getPhoneInfo");
}

// node_modules/gdt-jsapi/es/getProxyInfo.js
var MODE_ENUM;
!function(o) {
  o.SOCKS5 = "SOCKS5", o.HTTP = "HTTP";
}(MODE_ENUM || (MODE_ENUM = {})), invoker_default.registerAPI("getProxyInfo", { pc: function(o, n) {
  void 0 === o && (o = {}), window.dingtalk.platform.invokeAPI(n.msgId, "net.util.getProxyInfo", o);
} }), getProxyInfo.version = { pc: "2.10.0" };
function getProxyInfo() {
  return invoker_default.invoke("getProxyInfo", {});
}

// node_modules/gdt-jsapi/es/getStorageItem.js
invoker_default.registerAPI("getStorageItem", { mobile: true, mini: true }), getStorageItem.version = { android: "1.3.1.1", ios: "1.3.1.1" };
function getStorageItem(e) {
  return invoker_default.invoke("getStorageItem", e);
}

// node_modules/gdt-jsapi/es/getTraceStatus.js
invoker_default.registerAPI("getTraceStatus", { mobile: true }), getTraceStatus.version = { android: "1.3.4", ios: "1.3.4" };
function getTraceStatus(e) {
  return invoker_default.invoke("getTraceStatus", e);
}

// node_modules/gdt-jsapi/es/getUUID.js
invoker_default.registerAPI("getUUID", { mobile: true, mini: true }), getUUID.version = { android: "1.3.5", ios: "1.3.5" };
function getUUID() {
  return invoker_default.invoke("getUUID");
}

// node_modules/gdt-jsapi/es/getUserAgent.js
var RGE_CLIENT_INFO = /TaurusApp\((\S*)\/(\S*)\)/;
function getUserAgentInternal() {
  if (window && window.navigator) {
    var e = window.navigator.userAgent;
    if (e) {
      var r = e.match(RGE_CLIENT_INFO);
      return Promise.resolve({ group: "TaurusApp", name: r[1], version: r[2] });
    }
    return Promise.reject("\u8C03\u7528\u9519\u8BEF\uFF1A\u65E0\u6CD5\u68C0\u6D4B\u5230\u5F53\u4E0B\u73AF\u5883\u7684 userAgent\uFF0C\u8BF7\u786E\u4FDD\u5728\u653F\u52A1\u9489\u9489\u5BA2\u6237\u7AEF H5 \u5BB9\u5668\u4E0B\u8C03\u7528\u3002");
  }
}
invoker_default.registerAPI("getUserAgent", { mobile: true, mini: true, pc: true }), getUserAgent.version = { android: "1.6.2", ios: "1.6.2", pc: "1.6.2" };
function getUserAgent() {
  var e = invoker_default.getAppType();
  return e === API_INVOKER_TYPE.PC || e === API_INVOKER_TYPE.MOBILE ? getUserAgentInternal() : e === API_INVOKER_TYPE.MINI_APP ? invoker_default.invoke("getUserAgent", {}) : void 0;
}

// node_modules/gdt-jsapi/es/utils/getWaterMarkConfig.js
var TargetPageConfigEnum;
var WatermarkStatusEnum;
var ContentType;
!function(n) {
  n.off = "0", n.on = "1";
}(TargetPageConfigEnum || (TargetPageConfigEnum = {})), function(n) {
  n[n.off = 0] = "off", n[n.on = 1] = "on";
}(WatermarkStatusEnum || (WatermarkStatusEnum = {})), function(n) {
  n[n.name = 1] = "name", n[n.id = 2] = "id", n[n.custom = 3] = "custom";
}(ContentType || (ContentType = {})), invoker_default.registerAPI("getWaterMarkConfig", { pc: function(n, t) {
  window.dingtalk.platform.invokeAPI(t.msgId, "getWaterMarkConfig", n);
}, mini: true, mobile: true });
function getWaterMarkConfig(n) {
  return invoker_default.invoke("getWaterMarkConfig", n);
}

// node_modules/gdt-jsapi/es/packages/h5-watermark/watermark.js
var systemInfo;
var H5_PAGE = "h5Page";
var MEETING_DETAIL = "meetingDetail";
var DOC_PREVIEW = "docPreview";
var targetPageArr = [H5_PAGE, MEETING_DETAIL, DOC_PREVIEW];
var isMiniProgram = !isUndef(typeof my) && null !== my && !isUndef(typeof my.alert);
isMiniProgram && (systemInfo = my.getSystemInfoSync());
var userAgent = isMiniProgram ? systemInfo.platform : navigator.userAgent;
var screenWidth = isMiniProgram ? systemInfo.screenWidth : window.screen.width;
var pixelRatio = (isMiniProgram ? systemInfo.pixelRatio : window.devicePixelRatio) || 2;
var emptyWatermark = isMiniProgram ? Promise.resolve("") : "";
var WaterMark = function() {
  function t(t2) {
    void 0 === t2 && (t2 = {}), this.options = _extends({ texts: [""], width: 50, height: 50, textRotate: -10, textColor: "#000000", textFont: "PingFangSC-Regular,system-ui,sans-serif", fontStyle: "normal", opacity: 90, canvas: [], fontSize: 14 }, t2), this.options.width *= this.options.fontSize / 12, this.options.height *= this.options.fontSize / 12, this.options.deg = this.options.textRotate * Math.PI / 180, this.options.cosDeg = Math.cos(this.options.deg), this.options.absSinDeg = Math.abs(Math.sin(this.options.deg));
  }
  var e = t.prototype;
  return e.init = function() {
    var t2 = this, e2 = null, i = null;
    isMiniProgram ? i = my.createCanvasContext("canvasBg") : (e2 = this.createCanvas(), i = e2.getContext("2d")), this.calcTextSize();
    var n = this.options, o = n.allItemsWidth, a = n.drawItems, r = n.height, s = n.containerComp, h = Math.ceil(screenWidth / o), l = new Array(h).fill(a).reduce(function(t3, e3) {
      return t3.concat(e3);
    }, []), f = function() {
      t2.setCanvasStyle(i), t2.drawText(i, l), i.translate(0, r), t2.drawText(i, l.reverse(), true);
    };
    if (isMiniProgram)
      return new Promise(function(t3) {
        s.setState({ width: o * h, height: 2 * r }, function() {
          setTimeout(function() {
            f(), i.draw(), t3(i.toDataURL("image/png"));
          }, 0);
        });
      });
    e2.width = o * h, e2.height = 2 * r, e2.style.display = "none", f();
    var m = e2.toDataURL("image/png");
    return this.destroy(), m;
  }, e.calcTextSize = function() {
    var t2 = 0, e2 = 0, i = this.options;
    i.drawItems = [].map.call(i.texts, function(n2) {
      var o, a, r, s;
      if (isMiniProgram) {
        for (var h = 0, l = 0; l < n2.length; l += 1)
          h += /[\uff00-\uffff]/.test(n2[l]) ? 1 : 0.5;
        o = 1.1 * i.fontSize * h, a = 1.2 * i.fontSize;
      } else {
        var f = (r = '<span style="font:' + i.fontSize + "px " + i.textFont + ';visibility:hidden;">' + n2 + "</span>", (s = document.createElement("div")).innerHTML = r.trim(), s.firstChild);
        document.body.appendChild(f), o = f.offsetWidth, a = f.offsetHeight, document.body.removeChild(f);
      }
      return t2 = Math.max(t2, o), i.fontHeight || (i.fontHeight = a), e2 += Math.ceil(i.cosDeg * (i.width < o ? o : i.width)), { txt: n2, width: o, height: a };
    }), t2 > i.width && (i.width = t2);
    var n = t2 * i.absSinDeg + i.fontHeight * i.cosDeg;
    n > i.height && (i.height = n), i.maxItemWidth = t2, i.allItemsWidth = e2;
  }, e.setCanvasStyle = function(t2) {
    var e2 = this.options, i = e2.deg, n = e2.absSinDeg, o = e2.height, a = e2.fontHeight, r = e2.fontStyle, s = e2.fontSize, h = e2.textFont, l = e2.textColor, f = e2.opacity;
    t2.rotate(i);
    var m = n * (o - a);
    t2.translate(-m, 0), t2.font = r + " " + s + "px " + h, t2.fillStyle = l, t2.textAlign = "left", t2.textBaseline = "bottom", t2.globalAlpha = f;
  }, e.drawText = function(t2, e2, i) {
    void 0 === i && (i = false);
    var n = this.options, o = n.maxItemWidth, a = n.width, r = n.height, s = n.deg, h = n.cosDeg, l = n.absSinDeg;
    e2.forEach(function(e3, n2) {
      var f = h * (o - e3.width) / 2, m = a * h * n2, c = Math.abs(m * Math.tan(s)) + r;
      t2.fillText(e3.txt, m + (i ? h * (a - e3.width) / 2 : f), c + (i ? l * (a - e3.width) / 2 : 0));
    });
  }, e.createCanvas = function() {
    var t2 = document.createElement("canvas");
    return this.options.canvas.push(t2), t2;
  }, e.destroy = function() {
    this.options.canvas.forEach(function(t2) {
      t2.remove(), t2 = null;
    });
  }, t;
}();
function drawWatermark(t, e) {
  var i = JSON.parse(t), n = i.watermark || i;
  if (!n || "0" === String(n.watermarkStatus))
    return emptyWatermark;
  if (!Array.isArray(n.targetPages) || !n.targetPages.some(function(t2) {
    return t2.name === e && "1" === String(t2.value);
  }))
    return emptyWatermark;
  var o = [];
  if (Array.isArray(n.contentType)) {
    var a = "";
    n.contentType.includes(1) && (a += n.userName + " "), n.contentType.includes(2) && (a += (n.account || "").slice(-4)), a && o.push(a), n.contentType.includes(0) && n.contentCustom && o.push(n.contentCustom);
  }
  if (!o.length)
    return emptyWatermark;
  var r, s, h = /Android|Adr|SymbianOS|Windows\s*Phone|Mobile/.test(userAgent), l = /iPhone|iPad|iPod|Mac\s*OS.*Mobile|iOS/.test(userAgent), f = "0" === String(n.watermarkShowDensity);
  l ? f ? (r = 114, s = 66) : (r = 86, s = 45) : h ? f ? (r = 47 * pixelRatio, s = 40 * pixelRatio) : (r = 25 * pixelRatio, s = 25 * pixelRatio) : f ? (r = 300, s = 126) : (r = 194, s = 106);
  return new WaterMark({ containerComp: this, texts: o, width: r, height: s, textRotate: -10, textColor: { 0: "#FF0000", 1: "#000000", 2: "#0000FF" }[n.fontColor] || "#000000", textFont: "PingFangSC-Regular,system-ui,sans-serif", fontStyle: "0" === String(n.fontStyle) ? "normal" : "bold", opacity: (120 - parseInt(n.fontDiaphaneity, 10)) / 100, fontSize: { 0: 12, 1: 16, 2: 28 }[n.fontSize] || 16 }).init();
}
function generateWatermark(t, e) {
  if (void 0 === t && (t = {}), void 0 === e && (e = H5_PAGE), !targetPageArr.includes(e))
    throw new Error("\u7B2C\u4E8C\u4E2A\u53EF\u9009\u53C2\u6570\uFF0C\u4EC5\u80FD\u4E3A\u201Ch5Page\u201D\u6216\u201CmeetingDetail\u201D");
  try {
    return drawWatermark.call(this, JSON.stringify(t), e);
  } catch (t2) {
    throw t2;
  }
}

// node_modules/gdt-jsapi/es/getWaterMark.js
getWaterMark.version = { android: "1.1.0", ios: "1.1.0", pc: "1.1.0" };
function getWaterMark(r, e) {
  return void 0 === r && (r = ""), new Promise(function(t, a) {
    getWaterMarkConfig({ pageInfo: r }).then(function(r2) {
      try {
        var n = generateWatermark(r2, e);
        t(n);
      } catch (r3) {
        a(r3);
      }
    });
  });
}

// node_modules/gdt-jsapi/es/getWaterMarkConfigV2.js
var EnableEnum;
!function(n) {
  n[n.ENABLE = 1] = "ENABLE", n[n.DISABLE = 0] = "DISABLE";
}(EnableEnum || (EnableEnum = {})), invoker_default.registerAPI("getWaterMarkConfigV2", { mobile: true, mini: true, pc: function(n, e) {
  window.dingtalk.platform.invokeAPI(e.msgId, "getWaterMarkConfigV2", n);
} }), getWaterMarkConfigV2.version = { android: "2.8.0", ios: "2.8.0", pc: "2.8.0" };
function getWaterMarkConfigV2(n) {
  return invoker_default.invoke("getWaterMarkConfigV2", n);
}

// node_modules/gdt-jsapi/es/packages/h5-watermark/generateWaterMarkV2.js
var EnableEnum2;
!function(t) {
  t[t.DISABLE = 0] = "DISABLE", t[t.ENABLE = 1] = "ENABLE";
}(EnableEnum2 || (EnableEnum2 = {}));
var PageInfoEnum;
!function(t) {
  t.IMSESSIONLIST = "imSessionList", t.DOCPREVIEW = "docPreview", t.H5PAGEOTHER = "h5PageOther", t.MEETINGDETAIL = "meetingDetail", t.H5PAGEBASIC = "h5PageBasic", t.SELECTIONCOMPONENT = "selectionComponent", t.CONTACTLIST = "contactList", t.CONTACTDETAIL = "contactDetail", t.CHAT = "chat", t.SECRETCHAT = "secretChat", t.CAMERA = "camera";
}(PageInfoEnum || (PageInfoEnum = {}));
var ShowDensityEnum;
var emptyWatermark2 = "";
var fontStyleMap = { 1: "normal", 2: "bold", 3: "italic" };
!function(t) {
  t[t.LOOSE = 0] = "LOOSE", t[t.NORMAL = 1] = "NORMAL", t[t.DENSE = 2] = "DENSE";
}(ShowDensityEnum || (ShowDensityEnum = {}));
var HorizontalTypeEnum;
!function(t) {
  t[t.RIGHT = 0] = "RIGHT", t[t.LEFT = 1] = "LEFT";
}(HorizontalTypeEnum || (HorizontalTypeEnum = {}));
var DEFAULT_CANVAS_WIDTH = 749;
var DEFAULT_CANVAS_HEIGHT = 326;
var DEFAULT_TEXT_WIDTH = 200;
var DEFAULT_TEXT_HEIGHT = 16;
var DEFAULT_RATIO = 1.3;
var WaterMark2 = function() {
  function t(t2) {
    this.options = Object.assign({ texts: "", width: 50, height: 50, tiltAngle: -15, fontColor: "#171A1D", textFont: "PingFangSC-Regular,system-ui,sans-serif", transparency: 90, canvas: [], fontSize: 13, tWidth: 0, tHeight: 0, deg: -15 }, t2, { width: t2.leftAndRightSpacing, height: t2.upAndDownSpacing }), this.options.deg = this.options.tiltAngle * Math.PI / 180;
  }
  var n = t.prototype;
  return n.init = function() {
    var t2, n2, e, i, o, a, l, r, u2, E = null;
    return u2 = (E = this.createCanvas()).getContext("2d"), E.width = (null === (t2 = window) || void 0 === t2 || null === (n2 = t2.screen) || void 0 === n2 ? void 0 : n2.width) || (null === (e = document) || void 0 === e || null === (i = e.documentElement) || void 0 === i ? void 0 : i.clientWidth) || DEFAULT_CANVAS_WIDTH, E.height = (null === (o = window) || void 0 === o || null === (a = o.screen) || void 0 === a ? void 0 : a.height) || (null === (l = document) || void 0 === l || null === (r = l.documentElement) || void 0 === r ? void 0 : r.clientHeight) || DEFAULT_CANVAS_HEIGHT, this.calcTextSize(), this.setCanvasStyle(u2), this.drawText(u2), E.toDataURL("image/png");
  }, n.calcTextSize = function() {
    var t2, n2, e = this.options, i = "exclusiveDingTalkWaterMarkCustomClass" + 100 * Math.random(), o = (t2 = '<span id="' + i + '" style="font:' + e.fontSize + "px " + e.textFont + ';visibility:hidden;display:inline-block;">' + e.texts + "</span>", (n2 = document.createElement("div")).innerHTML = t2.trim(), n2.firstChild);
    document.body.appendChild(o);
    var a = document.getElementById(i), l = Math.max(a.clientWidth, e.texts.length * e.fontSize * DEFAULT_RATIO) || DEFAULT_TEXT_WIDTH, r = Math.min(a.clientHeight, e.fontSize * DEFAULT_RATIO) || DEFAULT_TEXT_HEIGHT;
    e.tWidth = l, e.tHeight = r, document.body.removeChild(o);
  }, n.setCanvasStyle = function(t2) {
    var n2 = this.options, e = n2.deg, i = n2.fontStyle, o = n2.fontSize, a = n2.textFont, l = n2.fontColor, r = n2.transparency;
    t2.rotate(e), t2.font = i + " " + o + "px " + a, t2.fillStyle = l, t2.textAlign = "left", t2.textBaseline = "bottom", t2.globalAlpha = (100 - r) / 100;
  }, n.fillContent = function(t2, n2) {
    for (var e = this.options, i = e.width, o = e.height, a = e.texts, l = e.tWidth, r = e.tHeight, u2 = 0; u2 < 40; u2++)
      for (var E = u2 * o + r, d = 0; d < 40; d++) {
        var s = void 0;
        s = u2 % 2 == 0 ? t2 === HorizontalTypeEnum.RIGHT ? (l + i) * d : (l + i) * d + l + i : t2 === HorizontalTypeEnum.RIGHT ? (l + i) * d + i : (l + i) * d + l, n2.fillText(a, t2 === HorizontalTypeEnum.RIGHT ? s : -s, E);
      }
  }, n.drawText = function(t2) {
    this.fillContent(HorizontalTypeEnum.RIGHT, t2), this.fillContent(HorizontalTypeEnum.LEFT, t2);
  }, n.createCanvas = function() {
    var t2 = document.createElement("canvas");
    return this.options.canvas.push(t2), t2;
  }, t;
}();
function drawWatermark2(t, n) {
  var e, i, o, a, l, r, u2, E;
  void 0 === n && (n = PageInfoEnum.H5PAGEOTHER);
  var d = null;
  try {
    d = JSON.parse(t);
  } catch (t2) {
    d = {};
  }
  var s = null === (e = d) || void 0 === e || null === (i = e.watermark) || void 0 === i ? void 0 : i.ruleContent, m = null === (o = d) || void 0 === o ? void 0 : o.userInfo;
  if ((null == s ? void 0 : s.enable) === EnableEnum2.DISABLE || (null == s ? void 0 : s.enable) === EnableEnum2.ENABLE && (null == s || null === (a = s.effectPage) || void 0 === a ? void 0 : a[n]) !== EnableEnum2.ENABLE)
    return emptyWatermark2;
  var T, c = "";
  ((null == s || null === (l = s.watermarkContent) || void 0 === l ? void 0 : l.enableUsername) === EnableEnum2.ENABLE && (c += null == m ? void 0 : m.userName), (null == s || null === (r = s.watermarkContent) || void 0 === r ? void 0 : r.enablePhoneNumber) === EnableEnum2.ENABLE && (c += " " + (null == m ? void 0 : m.lastFourPhoneNo)), null != s && null !== (u2 = s.watermarkContent) && void 0 !== u2 && u2.customCopy) && (c += " " + (null == s || null === (T = s.watermarkContent) || void 0 === T ? void 0 : T.customCopy));
  return c.length ? new WaterMark2(Object.assign({ texts: c, textFont: "PingFangSC-Regular,system-ui,sans-serif" }, null == s ? void 0 : s.watermarkStyle, { fontStyle: fontStyleMap[null == s || null === (E = s.watermarkStyle) || void 0 === E ? void 0 : E.fontStyle] })).init() : emptyWatermark2;
}
function generateWaterMarkV2(t, n) {
  void 0 === n && (n = PageInfoEnum.H5PAGEOTHER);
  try {
    return drawWatermark2.call(null, JSON.stringify(t), n);
  } catch (t2) {
    return "";
  }
}

// node_modules/gdt-jsapi/es/getWaterMarkV2.js
function getWaterMarkV2(r) {
  return new Promise(function(e, t) {
    version().then(function(a) {
      var n = a.version;
      -1 !== compareVersion_default(n, "2.8.0") ? getWaterMarkConfigV2({ pageInfo: r }).then(function(a2) {
        try {
          var n2 = generateWaterMarkV2(a2, r);
          e(n2);
        } catch (r2) {
          t(r2);
        }
      }) : getWaterMarkConfig({ pageInfo: r }).then(function(a2) {
        try {
          var n2 = generateWatermark(a2, r);
          e(n2);
        } catch (r2) {
          t(r2);
        }
      });
    }).catch(function() {
      getWaterMarkConfig({ pageInfo: r }).then(function(a) {
        try {
          var n = generateWatermark(a, r);
          e(n);
        } catch (r2) {
          t(r2);
        }
      });
    });
  });
}

// node_modules/gdt-jsapi/es/getWifiStatus.js
invoker_default.registerAPI("getWifiStatus", { mobile: true, mini: true }), getWifiStatus.version = { android: "1.3.5", ios: "1.3.5" };
function getWifiStatus() {
  return invoker_default.invoke("getWifiStatus");
}

// node_modules/gdt-jsapi/es/getWorkbenchContext.js
invoker_default.registerAPI("getWorkbenchContext", { mini: true, mobile: true, pc: function(e, o) {
  window.dingtalk.platform.invokeAPI(o.msgId, "getWorkbenchContext", e);
} }), getWorkbenchContext.version = { android: "2.1.10", ios: "2.1.10" };
function getWorkbenchContext() {
  return invoker_default.invoke("getWorkbenchContext");
}

// node_modules/gdt-jsapi/es/goBack.js
invoker_default.registerAPI("h5PageBack", { mobile: function(e, n) {
  var o = n.resolve, i = n.reject, a = n.context, r = n.containerType, c = n.platformType;
  if (r) {
    c === PLATFORM_TYPE_ENUM.ANDROID ? a && a(function(e2) {
      invoker_default.handleBridgeResponse(e2, o, i);
    }, function(e2) {
      invoker_default.handleBridgeResponse(e2, o, i);
    }, "biz.navigation", "goBack", e) : c === PLATFORM_TYPE_ENUM.IOS && a.callHandler("biz.navigation.goBack", Object.assign({}, e), function(e2) {
      o(e2);
    });
  } else
    a && a.call("h5PageBack", { _apiName: "goBack" }, function() {
      o();
    });
} }), goBack.version = { android: "1.3.0", ios: "1.3.9" };
function goBack() {
  return invoker_default.invoke("h5PageBack", { _apiName: "goBack" });
}

// node_modules/gdt-jsapi/es/hideLoading.js
function hideLoadingMenuHandler(e, i) {
  var n = i.resolve, o = i.reject, d = i.context, r = i.containerType, a = i.appType, t = i.platformType;
  if (r) {
    var c = function(e2) {
      invoker_default.handleBridgeResponse(e2, n, o);
    }, l = function(e2) {
      invoker_default.handleBridgeResponse(e2, n, o);
    };
    a === API_INVOKER_TYPE.MINI_APP ? d && d({ m: "device.notification.hidePreloader", args: e, onSuccess: c, onFail: l }) : t === PLATFORM_TYPE_ENUM.ANDROID ? d && d(c, l, "device.notification", "hidePreloader", e) : t === PLATFORM_TYPE_ENUM.IOS && d.callHandler("device.notification.hidePreloader", Object.assign({}, e), function(e2) {
      n(e2);
    });
  } else
    d && d.call("hideLoading", e, function() {
      n();
    });
}
invoker_default.registerAPI("hideLoading", { mini: hideLoadingMenuHandler, mobile: hideLoadingMenuHandler }), hideLoading.version = { android: "1.3.2", ios: "1.3.2" };
function hideLoading() {
  return invoker_default.invoke("hideLoading");
}

// node_modules/gdt-jsapi/es/hideOptionMenu.js
function hideOptionMenuHandler(e, n) {
  var i = n.resolve, o = n.reject, t = n.context, r = n.containerType, a = (n.appType, n.platformType);
  if (r) {
    var d = { show: false, control: true, text: "" };
    a === PLATFORM_TYPE_ENUM.ANDROID ? t && t(function(e2) {
      invoker_default.handleBridgeResponse(e2, i, o);
    }, function(e2) {
      invoker_default.handleBridgeResponse(e2, i, o);
    }, "biz.navigation", "setRight", d) : a === PLATFORM_TYPE_ENUM.IOS && t.callHandler("biz.navigation.setRight", Object.assign({}, d), function(e2) {
      i(e2);
    });
  } else
    t && t.call("hideOptionMenu", e, function() {
      i();
    });
}
invoker_default.registerAPI("hideOptionMenu", { mobile: hideOptionMenuHandler }), hideOptionMenu.version = { android: "1.1.0", ios: "1.1.0" };
function hideOptionMenu() {
  return invoker_default.invoke("hideOptionMenu");
}

// node_modules/gdt-jsapi/es/hideTitleBar.js
function hideTitlebarHandler(i, e) {
  var n = e.resolve, a = e.reject, r = e.containerType, o = e.platformType, t = e.appType, l = e.context, d = Object.assign(i, { hidden: true });
  if (r) {
    var c = function() {
      i.onSuccess && i.onSuccess(), n();
    }, T = function() {
      i.onFail && i.onFail(), a();
    };
    t === API_INVOKER_TYPE.MINI_APP ? l && l({ m: "biz.navigation.hideBar", args: d, onSuccess: c, onFail: T }) : o === PLATFORM_TYPE_ENUM.ANDROID ? l && l(c, T, "biz.navigation", "hideBar", d) : o === PLATFORM_TYPE_ENUM.IOS && l.callHandler("biz.navigation.hideBar", Object.assign({}, d), function() {
      n();
    });
  } else
    l && l.call("hideTitlebar", d, function() {
      n();
    });
}
invoker_default.registerAPI("hideTitlebar", { mini: hideTitlebarHandler, mobile: hideTitlebarHandler }), hideTitleBar.version = { android: "2.1.0", ios: "2.1.0" };
function hideTitleBar() {
  return invoker_default.invoke("hideTitlebar");
}

// node_modules/gdt-jsapi/es/isDownloadFileExist.js
invoker_default.registerAPI("isDownloadFileExist", { pc: function(i, o) {
  window.dingtalk.platform.invokeAPI(o.msgId, "biz.util.isLocalFileExist", i);
} }), isDownloadFileExist.version = { pc: "1.3.5" };
function isDownloadFileExist(i) {
  return invoker_default.invoke("isDownloadFileExist", i);
}

// node_modules/gdt-jsapi/es/joinScheduleConf.js
function joinScheduleConfHandler(e, n) {
  void 0 === e && (e = {});
  var o = n.resolve, i = n.reject, r = n.context, c = n.containerType, l = n.appType, d = n.platformType;
  if (c) {
    var f = function(e2) {
      invoker_default.handleBridgeResponse(e2, o, i);
    }, t = function(e2) {
      invoker_default.handleBridgeResponse(e2, o, i);
    };
    l === API_INVOKER_TYPE.MINI_APP ? r && r({ m: "biz.conference.joinScheduleConf", args: e, onSuccess: f, onFail: t }) : d === PLATFORM_TYPE_ENUM.ANDROID ? r && r(f, t, "biz.conference", "joinScheduleConf", e) : d === PLATFORM_TYPE_ENUM.IOS && r.callHandler("biz.conference.joinScheduleConf", Object.assign({}, e), function(e2) {
      invoker_default.handleBridgeResponse(e2, o, i);
    });
  } else
    r && r.call("joinScheduleConf", e, function() {
      o();
    });
}
invoker_default.registerAPI("joinScheduleConf", { mini: joinScheduleConfHandler, mobile: joinScheduleConfHandler, pc: function(e, n) {
  window.dingtalk.platform.invokeAPI(n.msgId, "biz.conference.joinScheduleConf", _extends({}, e));
} }), joinScheduleConf.version = { android: "3.7.5", ios: "3.7.5", pc: "3.7.5" };
function joinScheduleConf(e) {
  return invoker_default.invoke("joinScheduleConf", e);
}

// node_modules/gdt-jsapi/es/joinVideoConf.js
function joinVideoConfHandler(n, e) {
  void 0 === n && (n = {});
  var o = e.resolve, i = e.reject, r = e.context, d = e.containerType, f = e.appType, c = e.platformType;
  if (d) {
    var t = function(n2) {
      invoker_default.handleBridgeResponse(n2, o, i);
    }, a = function(n2) {
      invoker_default.handleBridgeResponse(n2, o, i);
    };
    f === API_INVOKER_TYPE.MINI_APP ? r && r({ m: "biz.conference.joinVideoConf", args: n, onSuccess: t, onFail: a }) : c === PLATFORM_TYPE_ENUM.ANDROID ? r && r(t, a, "biz.conference", "joinVideoConf", n) : c === PLATFORM_TYPE_ENUM.IOS && r.callHandler("biz.conference.joinVideoConf", Object.assign({}, n), function(n2) {
      invoker_default.handleBridgeResponse(n2, o, i);
    });
  } else
    r && r.call("joinVideoConf", n, function() {
      o();
    });
}
invoker_default.registerAPI("joinVideoConf", { mini: joinVideoConfHandler, mobile: joinVideoConfHandler, pc: function(n, e) {
  window.dingtalk.platform.invokeAPI(e.msgId, "biz.conference.joinVideoConf", _extends({}, n));
} }), joinVideoConf.version = { android: "3.7.5", ios: "3.7.5", pc: "3.7.5" };
function joinVideoConf(n) {
  return invoker_default.invoke("joinVideoConf", n);
}

// node_modules/gdt-jsapi/es/joinVideoMeeting.js
invoker_default.registerAPI("joinVideoMeeting", { mini: true, mobile: true, pc: function(e, i) {
  window.dingtalk.platform.invokeAPI(i.msgId, "biz.conference.joinVideoMeeting", _extends({}, e));
} }), joinVideoMeeting.version = { android: "3.9.0", ios: "3.9.0", pc: "3.9.0" };
function joinVideoMeeting(e) {
  return invoker_default.invoke("joinVideoMeeting", e);
}

// node_modules/gdt-jsapi/es/locateOnMap.js
invoker_default.registerAPI("locateOnMap", { mobile: true, mini: true }), locateOnMap.version = { android: "1.3.0", ios: "1.3.0" };
function locateOnMap(o) {
  return invoker_default.invoke("locateOnMap", o);
}

// node_modules/gdt-jsapi/es/on.js
function on(r, e) {
  return invoker_default.registerEvent(r, e);
}

// node_modules/gdt-jsapi/es/onAudioPlayEnd.js
invoker_default.registerAPI("onAudioPlayEnd", { mini: true, mobile: true }), onAudioPlayEnd.version = { android: "1.6.2", ios: "1.6.2" };
function onAudioPlayEnd() {
  return invoker_default.invoke("onAudioPlayEnd");
}

// node_modules/gdt-jsapi/es/onRecordAudioEnd.js
invoker_default.registerAPI("onRecordAudioEnd", { mini: true, mobile: true }), onRecordAudioEnd.version = { android: "1.3.0", ios: "1.3.0" };
function onRecordAudioEnd(o) {
  return invoker_default.invoke("onRecordAudioEnd", o);
}

// node_modules/gdt-jsapi/es/openApiInvoker.js
invoker_default.registerAPI("openApiInvoker", { mini: true, mobile: true, pc: function(o, n) {
  window.dingtalk.platform.invokeAPI(n.msgId, "openApiInvoker", o);
} }), openApiInvoker.version = { ios: "3.0.1", android: "3.0.1", pc: "3.0.1" };
function openApiInvoker(o) {
  return invoker_default.invoke("openApiInvoker", o);
}

// node_modules/gdt-jsapi/es/openApp.js
invoker_default.registerAPI("openApp", { mini: true, mobile: true }), openApp.version = { android: "1.3.2", ios: "1.3.2" };
function openApp(o) {
  return invoker_default.invoke("openApp", o);
}

// node_modules/gdt-jsapi/es/openBrowser.js
invoker_default.registerAPI("openBrowser", { mini: true, mobile: true }), openBrowser.version = { android: "1.2.3" };
function openBrowser(r) {
  return invoker_default.invoke("openBrowser", r);
}

// node_modules/gdt-jsapi/es/openChat.js
invoker_default.registerAPI("openChat", { mini: true, mobile: true, pc: function(n, o) {
  window.dingtalk.platform.invokeAPI(o.msgId, "internal.chat.toConversation", { cid: n.chatId });
} }), openChat.version = { android: "1.2.0", ios: "1.2.0", pc: "1.2.0" };
function openChat(n) {
  return invoker_default.invoke("openChat", n);
}

// node_modules/gdt-jsapi/es/openDownloadFile.js
invoker_default.registerAPI("openDownloadFile", { pc: function(o, n) {
  window.dingtalk.platform.invokeAPI(n.msgId, "biz.util.openLocalFile", o);
} }), openDownloadFile.version = { pc: "1.3.5" };
function openDownloadFile(o) {
  return invoker_default.invoke("openDownloadFile", o);
}

// node_modules/gdt-jsapi/es/openLink.js
function openLinkHandler(n, o) {
  var e = o.resolve, i = o.reject, r = o.context, p = o.containerType, s = o.appType, t = o.platformType;
  version().then(function(o2) {
    var a = o2.version, u2 = -1 !== compareVersion_default(a, "1.6.2");
    if (p) {
      var c = function(n2) {
        invoker_default.handleBridgeResponse(n2, e, i);
      }, m = function(n2) {
        invoker_default.handleBridgeResponse(n2, e, i);
      };
      s === API_INVOKER_TYPE.MINI_APP ? r && r({ m: u2 ? "taurus.common.openLink" : "taurus.common.pushWindow", args: n, onSuccess: c, onFail: m }) : t === PLATFORM_TYPE_ENUM.ANDROID ? r && r(c, m, "taurus.common", u2 ? "openLink" : "pushWindow", n) : t === PLATFORM_TYPE_ENUM.IOS && r.callHandler(u2 ? "taurus.common.openLink" : "taurus.common.pushWindow", Object.assign({}, n), function(n2) {
        invoker_default.handleBridgeResponse(n2, e, i);
      });
    } else
      r && r.call(u2 ? "openLink" : "pushWindow", n, function(n2) {
        invoker_default.handleBridgeResponse(n2, e, i);
      });
  });
}
invoker_default.registerAPI("openLink", { mini: openLinkHandler, mobile: openLinkHandler, pc: function(n, o) {
  window.dingtalk.platform.invokeAPI(o.msgId, "biz.util.openLink", n);
} }), openLink.version = { android: "1.2.0", ios: "1.2.0", pc: "1.2.0" };
function openLink(n) {
  return invoker_default.invoke("openLink", n);
}

// node_modules/gdt-jsapi/es/openPage.js
invoker_default.registerAPI("openPage", { mini: true, mobile: true }), openPage.version = { android: "1.1.0", ios: "1.1.0" };
function openPage(e) {
  return invoker_default.invoke("openPage", e);
}

// node_modules/gdt-jsapi/es/openSchemeUrl.js
invoker_default.registerAPI("dgOpenApp", { mobile: true, mini: true }), openSchemeUrl.version = { android: "1.3.1.1", ios: "1.3.1.1" };
function openSchemeUrl(e) {
  return invoker_default.invoke("dgOpenApp", _extends({}, e, { _apiName: "openSchemeUrl" }));
}

// node_modules/gdt-jsapi/es/openSlidePanel.js
invoker_default.registerAPI("openSlidePanel", { pc: function(e, n) {
  window.dingtalk.platform.invokeAPI(n.msgId, "biz.util.openSlidePanel", e);
} }), openSlidePanel.version = { pc: "1.3.5" };
function openSlidePanel(e) {
  return invoker_default.invoke("openSlidePanel", e);
}

// node_modules/gdt-jsapi/es/openWatermarkCamera.js
invoker_default.registerAPI("openWatermarkCamera", { mobile: true, mini: true }), openWatermarkCamera.version = { android: "1.3.7", ios: "1.3.7" };
function openWatermarkCamera() {
  return invoker_default.invoke("openWatermarkCamera");
}

// node_modules/gdt-jsapi/es/pauseAudio.js
invoker_default.registerAPI("pauseAudio", { mini: true, mobile: true }), pauseAudio.version = { android: "1.3.0", ios: "1.3.0" };
function pauseAudio(i) {
  return invoker_default.invoke("pauseAudio", i);
}

// node_modules/gdt-jsapi/es/pickChat.js
invoker_default.registerAPI("pickChat", { mini: true, mobile: true, pc: function(i, o) {
  window.dingtalk.platform.invokeAPI(o.msgId, "biz.chat.pickConversation", i);
} }), pickChat.version = { android: "1.2.0", ios: "1.2.0", pc: "2.9.0" };
function pickChat(i) {
  return invoker_default.invoke("pickChat", i);
}

// node_modules/gdt-jsapi/es/pickChatByCorpId.js
invoker_default.registerAPI("pickChatByCorpId", { mini: true, mobile: true });
function pickChatByCorpId(i) {
  return invoker_default.invoke("pickChatByCorpId", i);
}

// node_modules/gdt-jsapi/es/pickGroupChat.js
invoker_default.registerAPI("pickGroupChat", { pc: function(o, i) {
  window.dingtalk.platform.invokeAPI(i.msgId, "biz.contact.pickGroupChat", o);
} }), pickGroupChat.version = { pc: "2.10.30" };
function pickGroupChat(o) {
  return invoker_default.invoke("pickGroupChat", o);
}

// node_modules/gdt-jsapi/es/pickGroupConversation.js
function pickGroupConversationHandler(o, n) {
  void 0 === o && (o = {});
  var r = n.resolve, i = n.reject, e = n.context, a = n.containerType, t = n.appType, p = n.platformType;
  if (a) {
    var c = function(o2) {
      invoker_default.handleBridgeResponse(o2, r, i);
    }, s = function(o2) {
      invoker_default.handleBridgeResponse(o2, r, i);
    };
    t === API_INVOKER_TYPE.MINI_APP ? e && e({ m: p === PLATFORM_TYPE_ENUM.ANDROID ? "taurus.common.pickGroupConversation" : "internal.chat.pickGroupConversation", args: o, onSuccess: c, onFail: s }) : p === PLATFORM_TYPE_ENUM.ANDROID ? e && e(c, s, "taurus.common", "pickGroupConversation", o) : p === PLATFORM_TYPE_ENUM.IOS && e.callHandler("internal.chat.pickGroupConversation", Object.assign({}, o), function(o2) {
      r(o2);
    });
  } else
    e && e.call("pickGroupConversation", o, function() {
      r();
    });
}
invoker_default.registerAPI("pickGroupConversation", { mini: pickGroupConversationHandler, mobile: pickGroupConversationHandler }), pickGroupConversation.version = { android: "2.8.0", ios: "2.8.0" };
function pickGroupConversation(o) {
  return void 0 === o && (o = { owner: false }), invoker_default.invoke("pickGroupConversation", o);
}

// node_modules/gdt-jsapi/es/playAudio.js
invoker_default.registerAPI("playAudio", { mini: true, mobile: true }), playAudio.version = { android: "1.3.0", ios: "1.3.0" };
function playAudio(i) {
  return invoker_default.invoke("playAudio", i);
}

// node_modules/gdt-jsapi/es/previewDoc.js
invoker_default.registerAPI("previewDoc", { mini: true, mobile: true }), previewDoc.version = { android: "1.1.0", ios: "1.1.0" };
function previewDoc(e) {
  return invoker_default.invoke("previewDoc", e);
}

// node_modules/gdt-jsapi/es/previewImage.js
invoker_default.registerAPI("previewImage", { mini: true, mobile: true, pc: function(e, i) {
  window.dingtalk.platform.invokeAPI(i.msgId, "biz.util.previewImage", e);
} }), previewImage.version = { android: "1.2.0", ios: "1.2.0", pc: "1.2.0" };
function previewImage(e) {
  return invoker_default.invoke("previewImage", e);
}

// node_modules/gdt-jsapi/es/printFile.js
invoker_default.registerAPI("printFile", { mini: true, mobile: true }), printFile.version = { android: "2.2.10" };
function printFile(i) {
  return invoker_default.invoke("printFile", i);
}

// node_modules/gdt-jsapi/es/printNativeLog.js
invoker_default.registerAPI("printNativeLog", { mini: true, mobile: true }), printNativeLog.version = { android: "1.9.4", ios: "1.9.4" };
function printNativeLog(i) {
  return invoker_default.invoke("printNativeLog", i);
}

// node_modules/gdt-jsapi/es/prompt.js
function promptHandler(e, o) {
  var n = o.resolve, r = o.reject, t = o.context, i = o.containerType, p = o.appType, a = o.platformType, s = { message: e.message, title: e.title, okButton: e.buttonLabels[0], cancelButton: e.buttonLabels[1] };
  if (i) {
    var l = function(e2) {
      invoker_default.handleBridgeResponse(e2, n, r);
    }, m = function(e2) {
      invoker_default.handleBridgeResponse(e2, n, r);
    };
    p === API_INVOKER_TYPE.MINI_APP ? t && t({ m: "taurus.common.prompt", args: s, onSuccess: l, onFail: m }) : a === PLATFORM_TYPE_ENUM.ANDROID ? t && t(l, m, "taurus.common", "prompt", s) : a === PLATFORM_TYPE_ENUM.IOS && t.callHandler("taurus.common.prompt", Object.assign({}, s), function(e2) {
      invoker_default.handleBridgeResponse(e2, n, r);
    });
  } else
    t && t.call("prompt", s, function(e2) {
      var o2 = { errorCode: BRIDGE_ERROR_CODE.SUCCESS, result: { buttonIndex: e2.ok ? 0 : 1, value: e2.inputValue } };
      invoker_default.handleBridgeResponse(o2, n, r);
    });
}
invoker_default.registerAPI("prompt", { mini: promptHandler, mobile: promptHandler, pc: function(e, o) {
  window.dingtalk.platform.invokeAPI(o.msgId, "device.notification.prompt", e);
} }), prompt.version = { android: "1.3.2", ios: "1.3.2", pc: "1.3.2" };
function prompt(e) {
  return invoker_default.invoke("prompt", e);
}

// node_modules/gdt-jsapi/es/pushWindow.js
invoker_default.registerAPI("pushWindow", { mini: true, mobile: true }), pushWindow.version = { android: "2.9.7", ios: "2.9.7" };
function pushWindow(i) {
  return invoker_default.invoke("pushWindow", i);
}

// node_modules/gdt-jsapi/es/readImageToBase64.js
invoker_default.registerAPI("readImageToBase64", { mini: true, mobile: true, pc: function(e, o) {
  window.dingtalk.platform.invokeAPI(o.msgId, "readImageToBase64", e);
} }), readImageToBase64.version = { ios: "2.1.0", android: "2.1.0", pc: "2.1.0" };
function readImageToBase64(e) {
  return invoker_default.invoke("readImageToBase64", e);
}

// node_modules/gdt-jsapi/es/ready.js
function ready(o) {
  "function" == typeof o ? invoker_default.onReady(o) : console.error("dd.ready's param must be function! ");
}

// node_modules/gdt-jsapi/es/reduceImageSize.js
var COMPRESS_LEVEL;
!function(e) {
  e[e.ADJUST_BY_NET = 0] = "ADJUST_BY_NET", e[e.LOW_QUALITY = 1] = "LOW_QUALITY", e[e.MID_QUALITY = 2] = "MID_QUALITY", e[e.HIGH_QUALITY = 3] = "HIGH_QUALITY", e[e.NOT_COMPRESSED = 4] = "NOT_COMPRESSED", e[e.CUSTOM = 5] = "CUSTOM";
}(COMPRESS_LEVEL || (COMPRESS_LEVEL = {})), invoker_default.registerAPI("reduceImageSize", { mini: true, mobile: true, pc: function(e, i) {
  window.dingtalk.platform.invokeAPI(i.msgId, "reduceImageSize", e);
} }), reduceImageSize.version = { ios: "2.1.0", android: "2.1.0", pc: "2.1.0" };
function reduceImageSize(e) {
  return invoker_default.invoke("reduceImageSize", e);
}

// node_modules/gdt-jsapi/es/removeStorageItem.js
invoker_default.registerAPI("removeStorageItem", { mobile: true, mini: true }), removeStorageItem.version = { android: "1.3.1.1", ios: "1.3.1.1" };
function removeStorageItem(e) {
  return invoker_default.invoke("removeStorageItem", e);
}

// node_modules/gdt-jsapi/es/replacePage.js
function replacePageHandler(e, a) {
  var n = a.resolve, r = a.reject, o = a.containerType, i = a.platformType, c = a.appType, l = a.context;
  if (o) {
    var P = function() {
      e.onSuccess && e.onSuccess(), n();
    }, t = function() {
      e.onFail && e.onFail(), r();
    };
    c === API_INVOKER_TYPE.MINI_APP ? l && l({ m: "biz.navigation.replace", args: e, onSuccess: P, onFail: t }) : i === PLATFORM_TYPE_ENUM.ANDROID ? l && l(P, t, "biz.navigation", "replace", e) : i === PLATFORM_TYPE_ENUM.IOS && l.callHandler("taurus.common.replacePage", Object.assign({}, e), function() {
      n();
    });
  } else
    l && l.call("replacePage", e, function() {
      n();
    });
}
invoker_default.registerAPI("replacePage", { mini: replacePageHandler, mobile: replacePageHandler }), replacePage.version = { android: "1.3.2", ios: "1.3.2" };
function replacePage(e) {
  return invoker_default.invoke("replacePage", e);
}

// node_modules/gdt-jsapi/es/resetView.js
invoker_default.registerAPI("resetView", { mini: true, mobile: true }), resetView.version = { android: "1.3.0", ios: "1.3.0" };
function resetView() {
  return invoker_default.invoke("resetView");
}

// node_modules/gdt-jsapi/es/resumeAudio.js
invoker_default.registerAPI("resumeAudio", { mini: true, mobile: true }), resumeAudio.version = { android: "1.3.0", ios: "1.3.0" };
function resumeAudio(e) {
  return invoker_default.invoke("resumeAudio", e);
}

// node_modules/gdt-jsapi/es/rotateView.js
invoker_default.registerAPI("rotateView", { mini: true, mobile: true }), rotateView.version = { android: "1.3.0", ios: "1.3.0" };
function rotateView(e) {
  return invoker_default.invoke("rotateView", e);
}

// node_modules/gdt-jsapi/es/scan.js
invoker_default.registerAPI("scan", { mini: true, mobile: true }), scan.version = { android: "1.3.2", ios: "1.3.2" };
function scan(n) {
  return invoker_default.invoke("scan", n);
}

// node_modules/gdt-jsapi/es/searchOnMap.js
invoker_default.registerAPI("searchOnMap", { mini: true, mobile: true }), searchOnMap.version = { android: "1.3.2", ios: "1.3.2" };
function searchOnMap(r) {
  return invoker_default.invoke("searchOnMap", r);
}

// node_modules/gdt-jsapi/es/sendOutData.js
function fillDefaultProperties(e) {
  return _extends({}, e, { actionId: "", actionType: "0" });
}
function cardSendOutDataHandler(e, t) {
  var n = t.resolve, a = t.context;
  a && a.call("sendOutData", fillDefaultProperties(e), function() {
    n();
  });
}
invoker_default.registerAPI("cardSendOutData", { mini: cardSendOutDataHandler, mobile: cardSendOutDataHandler }), cardSendOutData.version = { android: "2.5.0", ios: "2.5.0" };
function cardSendOutData(e) {
  return invoker_default.invoke("cardSendOutData", e);
}

// node_modules/gdt-jsapi/es/setLocalScreenShotPolicy.js
var POLICYENUM;
!function(o) {
  o.DEFAULT = "0", o.DISABLEALL = "1", o.ENABLEALL = "2";
}(POLICYENUM || (POLICYENUM = {})), invoker_default.registerAPI("setLocalScreenShotPolicy", { mini: true, mobile: true }), setLocalScreenShotPolicy.version = { android: "2.12.12", ios: "2.12.12" };
function setLocalScreenShotPolicy(o) {
  return invoker_default.invoke("setLocalScreenShotPolicy", o);
}

// node_modules/gdt-jsapi/es/setNavIcon.js
function setNavIconHandler(n, e) {
  var o = e.resolve, t = e.reject, i = e.containerType, c = e.platformType, a = e.context;
  if (i) {
    c === PLATFORM_TYPE_ENUM.ANDROID ? a && a(function(e2) {
      n.onSuccess && n.onSuccess(), o();
    }, function(n2) {
      t();
    }, "biz.navigation", "setIcon", n) : c === PLATFORM_TYPE_ENUM.IOS && a.callHandler("biz.navigation.setIcon", Object.assign({}, n), function(n2) {
      o();
    });
  } else
    a && a.call("setNavIcon", n, function(n2) {
      o();
    });
}
invoker_default.registerAPI("setNavIcon", { mobile: setNavIconHandler }), setNavIcon.version = { android: "1.3.0", ios: "1.3.0" };
function setNavIcon(n) {
  return invoker_default.invoke("setNavIcon", n);
}

// node_modules/gdt-jsapi/es/setNavLeftText.js
function setNavLeftTextHandler(e, n) {
  var t = n.resolve, i = n.reject, a = n.context, o = n.containerType, r = n.appType, s = n.platformType, T = n.watch;
  if (o) {
    var c = function(n2) {
      e.onSuccess && e.onSuccess(), invoker_default.handleBridgeResponse(n2, t, i);
    }, f = function(e2) {
      invoker_default.handleBridgeResponse(e2, t, i);
    };
    r === API_INVOKER_TYPE.MINI_APP ? a && a({ m: "biz.navigation.setLeft", args: e, onSuccess: c, onFail: f }) : s === PLATFORM_TYPE_ENUM.ANDROID ? a && a(c, f, "biz.navigation", "setLeft", e) : s === PLATFORM_TYPE_ENUM.IOS && a.callHandler("biz.navigation.setLeft", Object.assign({}, e), function(e2) {
      !T && t(e2);
    });
  } else
    a && a.call("setNavLeftText", e, function() {
      t();
    });
}
invoker_default.registerAPI("setNavLeftText", { mini: setNavLeftTextHandler, mobile: setNavLeftTextHandler, pc: function(e, n) {
  window.dingtalk.platform.invokeAPI(n.msgId, "biz.navigation.setLeft", e);
} }), setNavLeftText.version = { ios: "1.2.0", pc: "1.2.0" };
function setNavLeftText(e) {
  var n = getContainerType();
  return invoker_default.invoke("setNavLeftText", n === Container_Type_Enum.isDingTalk ? Object.assign({ watch: true, show: true, control: false, showIcon: true, text: "" }, e) : e, { dingTalkAPIName: n === Container_Type_Enum.isDingTalk ? "biz.navigation.setLeft" : null });
}

// node_modules/gdt-jsapi/es/setOptionMenu.js
invoker_default.registerAPI("setOptionMenu", { mobile: function(n, e) {
  var i = e.resolve, o = e.reject, t = e.context, l = e.containerType, s = e.platformType;
  if (l) {
    var a, r, u2 = { text: n.title, show: void 0 === n.show || n.show, control: void 0 === n.control || n.control };
    if (s === PLATFORM_TYPE_ENUM.ANDROID)
      t && t(function(e2) {
        n.onSuccess && n.onSuccess(e2), i(e2);
      }, function e2(i2) {
        n.onFail && n.onFail(i2), o(e2);
      }, "biz.navigation", (null == n || null === (a = n.menus) || void 0 === a ? void 0 : a.length) > 1 ? "setMenu" : "setRight", (null == n || null === (r = n.menus) || void 0 === r ? void 0 : r.length) > 1 ? n : u2);
    else if (s === PLATFORM_TYPE_ENUM.IOS) {
      var v, c;
      t.callHandler((null == n || null === (v = n.menus) || void 0 === v ? void 0 : v.length) > 1 ? "biz.navigation.setMenu" : "biz.navigation.setRight", Object.assign({}, (null == n || null === (c = n.menus) || void 0 === c ? void 0 : c.length) > 1 ? n : u2), function() {
        i();
      });
    }
  } else
    t && t.call("setOptionMenu", n, function() {
      i();
    });
} }), setOptionMenu.version = { android: "1.1.0", ios: "1.1.0" };
function setOptionMenu(n) {
  var e = getContainerType();
  return invoker_default.invoke("setOptionMenu", e === Container_Type_Enum.isDingTalk ? Object.assign({ watch: true, show: true, control: false, showIcon: true, text: "" }, n) : n, e === Container_Type_Enum.isDingTalk ? { dingTalkAPIName: "biz.navigation.setRight" } : null);
}

// node_modules/gdt-jsapi/es/setProxyInfo.js
invoker_default.registerAPI("setProxyInfo", { pc: function(o, n) {
  window.dingtalk.platform.invokeAPI(n.msgId, "net.util.setProxyInfo", o);
} }), setProxyInfo.version = { pc: "2.10.0" };
function setProxyInfo(o) {
  return invoker_default.invoke("setProxyInfo", o);
}

// node_modules/gdt-jsapi/es/setStorageItem.js
invoker_default.registerAPI("setStorageItem", { mobile: true, mini: true }), setStorageItem.version = { android: "1.3.1.1", ios: "1.3.1.1" };
function setStorageItem(e) {
  return invoker_default.invoke("setStorageItem", e);
}

// node_modules/gdt-jsapi/es/setTitle.js
var goBackBtnEnum;
function setTitleHandler(e, n) {
  var i = n.resolve, t = n.reject, o = n.context, a = n.containerType, r = n.appType, l = n.platformType;
  if (a) {
    var s = function(e2) {
      invoker_default.handleBridgeResponse(e2, i, t);
    }, T = function(e2) {
      invoker_default.handleBridgeResponse(e2, i, t);
    };
    r === API_INVOKER_TYPE.MINI_APP ? o && o({ m: "biz.navigation.setTitle", args: e, onSuccess: s, onFail: T }) : l === PLATFORM_TYPE_ENUM.ANDROID ? o && o(s, T, "biz.navigation", "setTitle", e) : l === PLATFORM_TYPE_ENUM.IOS && o.callHandler("biz.navigation.setTitle", Object.assign({}, e), function(e2) {
      invoker_default.handleBridgeResponse(e2, i, t);
    });
  } else
    o && o.call("setTitle", e, function() {
      i();
    });
}
!function(e) {
  e.TRUE = "true", e.FALSE = "false";
}(goBackBtnEnum || (goBackBtnEnum = {})), invoker_default.registerAPI("setTitle", { mini: setTitleHandler, mobile: setTitleHandler, pc: function(e, n) {
  window.dingtalk.platform.invokeAPI(n.msgId, "biz.navigation.setTitle", e);
} }), setTitle.version = { android: "1.2.0", ios: "1.2.0", pc: "1.2.0" };
function setTitle(e) {
  return invoker_default.invoke("setTitle", e);
}

// node_modules/gdt-jsapi/es/shareFileToMessage.js
invoker_default.registerAPI("shareFileToMessage", { mini: true, mobile: true, pc: function(e, i) {
  window.dingtalk.platform.invokeAPI(i.msgId, "shareFileToMessage", e);
} }), shareFileToMessage.version = { android: "1.8.2", ios: "1.8.2", pc: "1.8.2" };
function shareFileToMessage(e) {
  return invoker_default.invoke("shareFileToMessage", e);
}

// node_modules/gdt-jsapi/es/shareImageToMessage.js
invoker_default.registerAPI("shareImageToMessage", { mini: true, mobile: true, pc: function(e, o) {
  window.dingtalk.platform.invokeAPI(o.msgId, "shareImageToMessage", e);
} }), shareImageToMessage.version = { android: "1.8.2", ios: "1.8.2", pc: "1.8.2" };
function shareImageToMessage(e) {
  return invoker_default.invoke("shareImageToMessage", e);
}

// node_modules/gdt-jsapi/es/shareToMessage.js
invoker_default.registerAPI("shareToMessage", { mini: true, mobile: true, pc: function(e, o) {
  window.dingtalk.platform.invokeAPI(o.msgId, "biz.util.share", e);
} }), shareToMessage.version = { android: "1.3.5", ios: "1.3.5", pc: "1.3.5" };
function shareToMessage(e) {
  return invoker_default.invoke("shareToMessage", e);
}

// node_modules/gdt-jsapi/es/shootVideo.js
invoker_default.registerAPI("shootVideo", { mini: true, mobile: true }), shootVideo.version = { android: "1.3.5", ios: "1.3.5" };
function shootVideo() {
  return invoker_default.invoke("shootVideo");
}

// node_modules/gdt-jsapi/es/showActionSheet.js
invoker_default.registerAPI("showActionSheet", { mini: true, mobile: true, pc: function(o, i) {
  window.dingtalk.platform.invokeAPI(i.msgId, "device.notification.actionSheet", o);
} }), showActionSheet.version = { android: "1.3.2", ios: "1.3.2", pc: "1.3.2" };
function showActionSheet(o) {
  return invoker_default.invoke("showActionSheet", o);
}

// node_modules/gdt-jsapi/es/showCallMenu.js
invoker_default.registerAPI("showCallMenu", { mini: true, mobile: true }), showCallMenu.version = { android: "1.3.9", ios: "1.3.9" };
function showCallMenu(o) {
  return invoker_default.invoke("showCallMenu", o);
}

// node_modules/gdt-jsapi/es/showDatePicker.js
invoker_default.registerAPI("showDatePicker", { mobile: true, mini: true }), showDatePicker.version = { android: "1.3.0", ios: "1.3.0" };
function showDatePicker(e) {
  return invoker_default.invoke("showDatePicker", e);
}

// node_modules/gdt-jsapi/es/showDateTimePicker.js
invoker_default.registerAPI("showDateTimePicker", { mini: true, mobile: true }), showDateTimePicker.version = { android: "1.3.10", ios: "1.3.10" };
function showDateTimePicker(e) {
  return invoker_default.invoke("showDateTimePicker", e);
}

// node_modules/gdt-jsapi/es/showExtendModal.js
invoker_default.registerAPI("showExtendModal", { mini: true, mobile: true }), showExtendModal.version = { android: "1.3.5", ios: "1.3.5" };
function showExtendModal(o) {
  return invoker_default.invoke("showExtendModal", o);
}

// node_modules/gdt-jsapi/es/showHomeBottomTab.js
invoker_default.registerAPI("showHomeBottomTab", { mobile: true }), showHomeBottomTab.version = { android: "1.3.0", ios: "1.3.0" };
function showHomeBottomTab(o) {
  return invoker_default.invoke("showHomeBottomTab", o);
}

// node_modules/gdt-jsapi/es/showLoading.js
function showLoadingMenuHandler(o, e) {
  var n = e.resolve, i = e.reject, r = e.context, a = e.containerType, d = e.appType, s = e.platformType;
  if (a) {
    var t = function(o2) {
      invoker_default.handleBridgeResponse(o2, n, i);
    }, c = function(o2) {
      invoker_default.handleBridgeResponse(o2, n, i);
    };
    d === API_INVOKER_TYPE.MINI_APP ? r && r({ m: "device.notification.showPreloader", args: o, onSuccess: t, onFail: c }) : s === PLATFORM_TYPE_ENUM.ANDROID ? r && r(t, c, "device.notification", "showPreloader", o) : s === PLATFORM_TYPE_ENUM.IOS && r.callHandler("device.notification.showPreloader", Object.assign({}, o), function(o2) {
      n(o2);
    });
  } else
    r && r.call("showLoading", o, function() {
      n();
    });
}
invoker_default.registerAPI("showLoading", { mini: showLoadingMenuHandler, mobile: showLoadingMenuHandler }), showLoading.version = { android: "1.3.2", ios: "1.3.2" };
function showLoading(o) {
  return invoker_default.invoke("showLoading", o);
}

// node_modules/gdt-jsapi/es/showModal.js
invoker_default.registerAPI("showModal", { mini: true, mobile: true, pc: function(o, i) {
  window.dingtalk.platform.invokeAPI(i.msgId, "biz.util.openModal", o);
} }), showModal.version = { android: "1.3.5", ios: "1.3.5", pc: "1.3.5" };
function showModal(o) {
  return invoker_default.invoke("showModal", o);
}

// node_modules/gdt-jsapi/es/showMultiSelect.js
invoker_default.registerAPI("showMultiSelect", { mini: true, mobile: true }), showMultiSelect.version = { android: "1.3.10", ios: "1.3.10" };
function showMultiSelect(e) {
  return invoker_default.invoke("showMultiSelect", e);
}

// node_modules/gdt-jsapi/es/showOnMap.js
invoker_default.registerAPI("showOnMap", { mini: true, mobile: true }), searchOnMap2.version = { android: "1.3.2", ios: "1.3.2" };
function searchOnMap2(n) {
  return invoker_default.invoke("showOnMap", n);
}

// node_modules/gdt-jsapi/es/showOptionMenu.js
invoker_default.registerAPI("showOptionMenu", { mobile: function(n, o) {
  var e = o.resolve, i = o.reject, t = o.context, r = o.platformType;
  if (o.containerType) {
    r === PLATFORM_TYPE_ENUM.ANDROID ? t && t(function() {
      e();
    }, function() {
      i();
    }, "taurus.common", "showOptionMenu", n) : r === PLATFORM_TYPE_ENUM.IOS && t.callHandler("taurus.common.showOptionMenu", Object.assign({}, n), function() {
      e();
    });
  } else
    t && t.call("showOptionMenu", n, function() {
      e();
    });
} }), showOptionMenu.version = { android: "1.1.0", ios: "1.1.0" };
function showOptionMenu() {
  return invoker_default.invoke("showOptionMenu");
}

// node_modules/gdt-jsapi/es/showPlainInputUponKeyboard.js
invoker_default.registerAPI("showPlainInputUponKeyboard", { mobile: true, mini: true }), showPlainInputUponKeyboard.version = { android: "1.3.0", ios: "1.3.0" };
function showPlainInputUponKeyboard(o) {
  return invoker_default.invoke("showPlainInputUponKeyboard", o);
}

// node_modules/gdt-jsapi/es/showQuickCallMenu.js
invoker_default.registerAPI("showQuickCallMenu", { mini: true, mobile: true }), showQuickCallMenu.version = { android: "1.6.2", ios: "1.6.2" };
function showQuickCallMenu(i) {
  return invoker_default.invoke("showQuickCallMenu", i);
}

// node_modules/gdt-jsapi/es/showSelect.js
invoker_default.registerAPI("showSelect", { mini: true, mobile: true }), showSelect.version = { android: "1.3.2", ios: "1.3.2" };
function showSelect(e) {
  return invoker_default.invoke("showSelect", e);
}

// node_modules/gdt-jsapi/es/showSignature.js
invoker_default.registerAPI("showSignature", { mobile: true }), showSignature.version = { android: "1.3.4" };
function showSignature(r) {
  return invoker_default.invoke("showSignature", r);
}

// node_modules/gdt-jsapi/es/showSocialShare.js
invoker_default.registerAPI("showSocialShare", { mini: true, mobile: true }), showSocialShare.version = { android: "1.2.0.10", ios: "1.2.0.10" };
function showSocialShare(o) {
  return invoker_default.invoke("showSocialShare", o);
}

// node_modules/gdt-jsapi/es/showTimePicker.js
invoker_default.registerAPI("showTimePicker", { mobile: true, mini: true }), showTimePicker.version = { android: "1.3.0", ios: "1.3.0" };
function showTimePicker(i) {
  return invoker_default.invoke("showTimePicker", i);
}

// node_modules/gdt-jsapi/es/showTitleBar.js
function showTitlebarHandler(i, e) {
  var n = e.resolve, o = e.reject, a = e.containerType, r = e.platformType, t = e.appType, l = e.context, s = Object.assign(i, { hidden: false });
  if (a) {
    var c = function() {
      i.onSuccess && i.onSuccess(), n();
    }, T = function() {
      i.onFail && i.onFail(), o();
    };
    t === API_INVOKER_TYPE.MINI_APP ? l && l({ m: "biz.navigation.hideBar", args: s, onSuccess: c, onFail: T }) : r === PLATFORM_TYPE_ENUM.ANDROID ? l && l(c, T, "biz.navigation", "hideBar", s) : r === PLATFORM_TYPE_ENUM.IOS && l.callHandler("biz.navigation.hideBar", Object.assign({}, s), function() {
      n();
    });
  } else
    l && l.call("showTitlebar", s, function() {
      n();
    });
}
invoker_default.registerAPI("showTitlebar", { mini: showTitlebarHandler, mobile: showTitlebarHandler }), showTitleBar.version = { android: "2.1.0", ios: "2.1.0" };
function showTitleBar() {
  return invoker_default.invoke("showTitlebar");
}

// node_modules/gdt-jsapi/es/startFaceRecognition.js
invoker_default.registerAPI("startFaceRecognition", { mini: true, mobile: true }), startFaceRecognition.version = { android: "1.8.2", ios: "1.8.2" };
function startFaceRecognition(i) {
  return invoker_default.invoke("startFaceRecognition", i);
}

// node_modules/gdt-jsapi/es/startGeolocation.js
function startGeolocationHandler(e, o) {
  var n = o.resolve, t = o.reject, r = o.context, i = o.platformType, a = o.containerType, s = o.appType, c = invoker_default.registerEvent(CONTINUOUS_EVENT_LIST.UPDATE_LOCATION, function(o2) {
    var n2 = o2.data;
    n2.errorCode !== BRIDGE_ERROR_CODE.SUCCESS ? e.onFail && e.onFail(n2) : e.onSuccess && e.onSuccess(n2.result);
  });
  if (a) {
    var l = function(o2) {
      invoker_default.registerContinuesEvent(e.sceneId, c), invoker_default.handleBridgeResponse(o2, n, t);
    }, E = function(o2) {
      invoker_default.registerContinuesEvent(e.sceneId, c), invoker_default.handleBridgeResponse(o2, n, t);
    };
    s === API_INVOKER_TYPE.MINI_APP ? (console.log("taurus.common.startGeolocation", e), r && r({ m: "taurus.common.startGeolocation", args: e, onSuccess: l, onFail: E })) : i === PLATFORM_TYPE_ENUM.ANDROID ? r && r(l, E, "taurus.common", "startGeolocation", e) : i === PLATFORM_TYPE_ENUM.IOS && r.callHandler("taurus.common.startGeolocation", Object.assign({}, e), function(o2) {
      invoker_default.registerContinuesEvent(e.sceneId, c), invoker_default.handleBridgeResponse(o2, n, t);
    });
  } else
    r && r.call("startGeolocation", e, function(o2) {
      invoker_default.registerContinuesEvent(e.sceneId, c), invoker_default.handleBridgeResponse(o2, n, t);
    });
}
invoker_default.registerAPI("startGeolocation", { mobile: startGeolocationHandler, mini: startGeolocationHandler }), startGeolocation.version = { android: "1.3.2", ios: "1.3.2" };
function startGeolocation(e) {
  return invoker_default.invoke("startGeolocation", e);
}

// node_modules/gdt-jsapi/es/startListenNetworkStatus.js
function startListenNetworkStatusHandler(e, t) {
  var r = t.resolve, n = t.reject, s = t.context, o = t.containerType, i = t.appType, a = t.platformType, u2 = invoker_default.registerEvent(CONTINUOUS_EVENT_LIST.UPDATE_NETWORK_STATUS, function(t2) {
    var r2 = t2.data;
    r2.errorCode !== BRIDGE_ERROR_CODE.SUCCESS ? e.onFail && e.onFail(r2) : e.onSuccess && e.onSuccess(r2.result);
  });
  if (o) {
    var E = function(e2) {
      invoker_default.registerContinuesEvent(e2.result.requestId, u2), invoker_default.handleBridgeResponse(e2, r, n);
    }, k = function(e2) {
      invoker_default.registerContinuesEvent(e2.result.requestId, u2), invoker_default.handleBridgeResponse(e2, r, n);
    };
    i === API_INVOKER_TYPE.MINI_APP ? s && s({ m: "taurus.common.startListenNetworkStatus", args: e, onSuccess: E, onFail: k }) : a === PLATFORM_TYPE_ENUM.ANDROID ? s && s(E, k, "taurus.common", "startListenNetworkStatus", e) : a === PLATFORM_TYPE_ENUM.IOS && s.callHandler("taurus.common.startListenNetworkStatus", Object.assign({}, e), function(e2) {
      invoker_default.registerContinuesEvent(e2.result.requestId, u2), invoker_default.handleBridgeResponse(e2, r, n);
    });
  } else
    s && s.call("startListenNetworkStatus", e, function(e2) {
      invoker_default.registerContinuesEvent(e2.result.requestId, u2), invoker_default.handleBridgeResponse(e2, r, n);
    });
}
invoker_default.registerAPI("startListenNetworkStatus", { mobile: startListenNetworkStatusHandler, mini: startListenNetworkStatusHandler }), startListenNetworkStatus.version = { android: "1.3.1.1", ios: "1.3.1.1" };
function startListenNetworkStatus(e) {
  return invoker_default.invoke("startListenNetworkStatus", e);
}

// node_modules/gdt-jsapi/es/startRecordAudio.js
invoker_default.registerAPI("startRecordAudio", { mini: true, mobile: true }), startRecordAudio.version = { android: "1.3.0", ios: "1.3.0" };
function startRecordAudio(o) {
  return invoker_default.invoke("startRecordAudio", o);
}

// node_modules/gdt-jsapi/es/startTraceReport.js
function startTraceReportHandler(e, r) {
  var t = r.resolve, n = r.reject, o = r.context, a = r.containerType, i = r.platformType, s = r.appType, c = invoker_default.registerEvent(CONTINUOUS_EVENT_LIST.UPDATE_TRACE, function(r2) {
    var t2 = r2.data;
    t2.errorCode && t2.errorCode !== BRIDGE_ERROR_CODE.SUCCESS ? e.onFail && e.onFail(t2) : e.onSuccess && e.onSuccess(t2.result || t2);
  });
  if (a) {
    var R = function(r2) {
      invoker_default.registerContinuesEvent(e.traceId, c), invoker_default.handleBridgeResponse(r2, t, n);
    }, T = function(r2) {
      invoker_default.registerContinuesEvent(e.traceId, c), invoker_default.handleBridgeResponse(r2, t, n);
    };
    s === API_INVOKER_TYPE.MINI_APP ? o && o({ m: "taurus.common.startTraceReport", args: e, onSuccess: R, onFail: T }) : i === PLATFORM_TYPE_ENUM.ANDROID ? o && o(R, T, "taurus.common", "startTraceReport", e) : i === PLATFORM_TYPE_ENUM.IOS && o.callHandler("taurus.common.startTraceReport", Object.assign({}, e), function(r2) {
      invoker_default.registerContinuesEvent(e.traceId, c), invoker_default.handleBridgeResponse(r2, t, n);
    });
  } else
    o && o.call("startTraceReport", e, function(r2) {
      invoker_default.registerContinuesEvent(e.traceId, c), invoker_default.handleBridgeResponse(r2, t, n);
    });
}
invoker_default.registerAPI("startTraceReport", { mobile: startTraceReportHandler, mini: true }), startTraceReport.version = { android: "1.3.4", ios: "1.3.4" };
function startTraceReport(e) {
  return invoker_default.invoke("startTraceReport", e);
}

// node_modules/gdt-jsapi/es/startVPNApp.js
invoker_default.registerAPI("startVPNApp", { mini: true, mobile: true }), startVPNApp.version = { android: "1.6.0", ios: "1.6.0" };
function startVPNApp(r) {
  return invoker_default.invoke("startVPNApp", r);
}

// node_modules/gdt-jsapi/es/startWatchShake.js
function startShakeHandler(e, t) {
  var n = t.resolve, a = t.reject, r = t.context, o = t.containerType, s = t.platformType, c = invoker_default.registerEvent(CONTINUOUS_EVENT_LIST.ON_SHAKE, function() {
    e.onSuccess && e.onSuccess();
  });
  if (o) {
    s === PLATFORM_TYPE_ENUM.ANDROID ? r && r(function(t2) {
      e.onSuccess && e.onSuccess(), n();
    }, function(e2) {
      a();
    }, "taurus.common", "startWatchShake", e) : s === PLATFORM_TYPE_ENUM.IOS && r.callHandler("taurus.common.startWatchShake", Object.assign({}, e), function(e2) {
      n();
    });
  } else
    r && r.call("startWatchShake", e, function(e2) {
      invoker_default.registerContinuesEvent("shake", c), invoker_default.handleBridgeResponse(e2, n, a);
    });
}
invoker_default.registerAPI("startWatchShake", { mobile: startShakeHandler }), startWatchShake.version = { android: "1.6.2", ios: "1.6.2" };
function startWatchShake(e) {
  return invoker_default.invoke("startWatchShake", e);
}

// node_modules/gdt-jsapi/es/stopAudio.js
invoker_default.registerAPI("stopAudio", { mini: true, mobile: true }), stopAudio.version = { android: "1.3.0", ios: "1.3.0" };
function stopAudio(o) {
  return invoker_default.invoke("stopAudio", o);
}

// node_modules/gdt-jsapi/es/stopGeolocation.js
function stopGeolocationHandler(o, e) {
  var n = e.resolve, i = e.reject, t = e.containerType, r = e.platformType, s = e.appType, a = e.context;
  if (t) {
    var c = function(e2) {
      invoker_default.removeContinuesEvent(o.sceneId), invoker_default.handleBridgeResponse(e2, n, i);
    }, l = function(e2) {
      invoker_default.removeContinuesEvent(o.sceneId), invoker_default.handleBridgeResponse(e2, n, i);
    };
    s === API_INVOKER_TYPE.MINI_APP ? a && a({ m: "taurus.common.stopGeolocation", args: o, onSuccess: c, onFail: l }) : r === PLATFORM_TYPE_ENUM.ANDROID ? a && a(c, l, "taurus.common", "stopGeolocation", o) : r === PLATFORM_TYPE_ENUM.IOS && a.callHandler("taurus.common.stopGeolocation", Object.assign({}, o), function(e2) {
      invoker_default.removeContinuesEvent(o.sceneId), invoker_default.handleBridgeResponse(e2, n, i);
    });
  } else
    a && a.call("stopGeolocation", o, function(e2) {
      invoker_default.removeContinuesEvent(o.sceneId), invoker_default.handleBridgeResponse(e2, n, i);
    });
}
invoker_default.registerAPI("stopGeolocation", { mobile: stopGeolocationHandler, mini: stopGeolocationHandler }), stopGeolocation.version = { android: "1.3.2", ios: "1.3.2" };
function stopGeolocation(o) {
  return invoker_default.invoke("stopGeolocation", o);
}

// node_modules/gdt-jsapi/es/stopListenNetworkStatus.js
function stopListenNetworkStatusHandler(e, t) {
  var n = t.resolve, o = t.reject, s = t.containerType, r = t.appType, i = t.platformType, a = t.context;
  if (s) {
    var u2 = function(t2) {
      invoker_default.removeContinuesEvent(e.requestId), invoker_default.handleBridgeResponse(t2, n, o);
    }, v = function(t2) {
      invoker_default.removeContinuesEvent(e.requestId), invoker_default.handleBridgeResponse(t2, n, o);
    };
    r === API_INVOKER_TYPE.MINI_APP ? a && a({ m: "taurus.common.stopListenNetworkStatus", args: e, onSuccess: u2, onFail: v }) : i === PLATFORM_TYPE_ENUM.ANDROID ? a && a(u2, v, "taurus.common", "stopListenNetworkStatus", e) : i === PLATFORM_TYPE_ENUM.IOS && a.callHandler("taurus.common.stopListenNetworkStatus", Object.assign({}, e), function(t2) {
      invoker_default.removeContinuesEvent(e.requestId), invoker_default.handleBridgeResponse(t2, n, o);
    });
  } else
    a && a.call("stopListenNetworkStatus", e, function(t2) {
      invoker_default.removeContinuesEvent(e.requestId), invoker_default.handleBridgeResponse(t2, n, o);
    });
}
invoker_default.registerAPI("stopListenNetworkStatus", { mini: stopListenNetworkStatusHandler, mobile: stopListenNetworkStatusHandler }), stopListenNetworkStatus.version = { android: "1.3.1.1", ios: "1.3.1.1" };
function stopListenNetworkStatus(e) {
  return invoker_default.invoke("stopListenNetworkStatus", e);
}

// node_modules/gdt-jsapi/es/stopPullToRefresh.js
invoker_default.registerAPI("stopPullToRefresh", { mobile: function(e, o) {
  var r = o.resolve, l = o.reject, n = o.context, t = o.containerType, i = o.platformType;
  if (t) {
    i === PLATFORM_TYPE_ENUM.ANDROID ? n && n(function() {
      r();
    }, function() {
      l();
    }, "ui.pullToRefresh", "stop", e) : i === PLATFORM_TYPE_ENUM.IOS && n.callHandler("ui.pullToRefresh.stop", Object.assign({}, e), function() {
      r();
    });
  } else
    n && n.call("restorePullToRefresh", e, function() {
      r();
    });
} }), stopPullToRefresh.version = { android: "1.3.0", ios: "1.3.0" };
function stopPullToRefresh() {
  return invoker_default.invoke("stopPullToRefresh", { _apiName: "stopPullToRefresh" });
}

// node_modules/gdt-jsapi/es/stopRecordAudio.js
invoker_default.registerAPI("stopRecordAudio", { mini: true, mobile: true }), stopRecordAudio.version = { android: "1.3.0", ios: "1.3.0" };
function stopRecordAudio(o) {
  return invoker_default.invoke("stopRecordAudio", o);
}

// node_modules/gdt-jsapi/es/stopTraceReport.js
function stopTraceReportHandler(e, o) {
  var r = o.resolve, n = o.reject, t = o.containerType, i = o.platformType, a = o.context;
  if (t) {
    i === PLATFORM_TYPE_ENUM.ANDROID ? a && a(function(o2) {
      invoker_default.removeContinuesEvent(e.traceId), invoker_default.handleBridgeResponse(o2, r, n);
    }, function(o2) {
      invoker_default.removeContinuesEvent(e.traceId), invoker_default.handleBridgeResponse(o2, r, n);
    }, "taurus.common", "stopTraceReport", e) : i === PLATFORM_TYPE_ENUM.IOS && a.callHandler("taurus.common.stopTraceReport", Object.assign({}, e), function(o2) {
      invoker_default.removeContinuesEvent(e.traceId), invoker_default.handleBridgeResponse(o2, r, n);
    });
  } else
    a && a.call("stopTraceReport", e, function(o2) {
      invoker_default.removeContinuesEvent(e.traceId), invoker_default.handleBridgeResponse(o2, r, n);
    });
}
invoker_default.registerAPI("stopTraceReport", { mobile: stopTraceReportHandler }), stopTraceReport.version = { android: "1.3.4", ios: "1.3.4" };
function stopTraceReport(e) {
  return invoker_default.invoke("stopTraceReport", e);
}

// node_modules/gdt-jsapi/es/stopVPNApp.js
invoker_default.registerAPI("stopVPNApp", { mini: true, mobile: true }), stopVPNApp.version = { android: "1.6.0", ios: "1.6.0" };
function stopVPNApp(o) {
  return invoker_default.invoke("stopVPNApp", o);
}

// node_modules/gdt-jsapi/es/stopWatchShake.js
function stopWatchShakeHandler(e, o) {
  var t = o.resolve, n = o.reject, a = o.containerType, c = o.platformType, r = o.context;
  if (a) {
    c === PLATFORM_TYPE_ENUM.ANDROID ? r && r(function(o2) {
      e.onSuccess && e.onSuccess(), t();
    }, function(e2) {
      n();
    }, "taurus.common", "stopWatchShake", e) : c === PLATFORM_TYPE_ENUM.IOS && r.callHandler("taurus.common.stopWatchShake", Object.assign({}, e), function(e2) {
      t();
    });
  } else
    r && r.call("stopWatchShake", e, function(e2) {
      invoker_default.removeContinuesEvent("shake"), invoker_default.handleBridgeResponse(e2, t, n);
    });
}
invoker_default.registerAPI("stopWatchShake", { mobile: stopWatchShakeHandler }), stopWatchShake.version = { android: "1.6.2", ios: "1.6.2" };
function stopWatchShake() {
  return invoker_default.invoke("stopWatchShake");
}

// node_modules/gdt-jsapi/es/subscribe.js
invoker_default.registerAPI("subscribe", { mobile: function(e, o) {
  var n = o.resolve, s = o.reject, r = o.context, i = o.containerType, c = o.platformType, u2 = false;
  if (i) {
    c === PLATFORM_TYPE_ENUM.ANDROID ? r && r(function(o2) {
      u2 ? (e.onSuccess || e.onFail) && ("0" !== o2.errorCode ? e.onFail && e.onFail(o2) : e.onSuccess && e.onSuccess(o2.result)) : (u2 = true, invoker_default.handleBridgeResponse(o2, n, s));
    }, function(o2) {
      u2 ? e.onFail && e.onFail(o2) : (u2 = true, invoker_default.handleBridgeResponse(o2, n, s));
    }, "taurus.common", "subscribe", e) : c === PLATFORM_TYPE_ENUM.IOS && r.callHandler("taurus.common.subscribe", Object.assign({}, e), function(o2) {
      u2 ? (e.onSuccess || e.onFail) && ("0" !== o2.errorCode ? e.onFail && e.onFail(o2) : e.onSuccess && e.onSuccess(o2.result)) : (u2 = true, invoker_default.handleBridgeResponse(o2, n, s));
    });
  } else
    r && r.call("subscribe", e, function(o2) {
      u2 ? (e.onSuccess || e.onFail) && ("0" !== o2.errorCode ? e.onFail && e.onFail(o2) : e.onSuccess && e.onSuccess(o2.result)) : (u2 = true, invoker_default.handleBridgeResponse(o2, n, s));
    });
} }), subscribe.version = { android: "1.6.0", ios: "1.6.0" };
function subscribe(e) {
  return invoker_default.invoke("subscribe", e);
}

// node_modules/gdt-jsapi/es/takePhoto.js
invoker_default.registerAPI("takePhoto", { mini: true, mobile: true }), takePhoto.version = { android: "1.3.5", ios: "1.3.5" };
function takePhoto() {
  return invoker_default.invoke("takePhoto");
}

// node_modules/gdt-jsapi/es/testProxy.js
invoker_default.registerAPI("testProxy", { pc: function(t, o) {
  void 0 === t && (t = {}), window.dingtalk.platform.invokeAPI(o.msgId, "net.util.testProxy", t);
} }), testProxy.version = { pc: "2.10.0" };
function testProxy() {
  return invoker_default.invoke("testProxy", {});
}

// node_modules/gdt-jsapi/es/toast.js
function toastHandler(e, o) {
  var t = o.resolve, n = o.reject, r = o.context, a = o.containerType, i = o.platformType, s = o.appType, c = { type: "error" === e.icon ? "fail" : "success" === e.icon ? "success" : "none", content: e.text, duration: 1e3 * e.duration, taurusToastStyle: e.taurusToastStyle };
  if (a) {
    var u2 = function() {
      invoker_default.handleBridgeResponse({ errorCode: BRIDGE_ERROR_CODE.SUCCESS, result: {} }, t, n);
    }, d = function(e2) {
      invoker_default.handleBridgeResponse(e2, t, n);
    };
    s === API_INVOKER_TYPE.MINI_APP ? r && r({ m: "taurus.common.toast", args: c, onSuccess: u2, onFail: d }) : i === PLATFORM_TYPE_ENUM.ANDROID ? r && r(u2, d, "taurus.common", "toast", c) : i === PLATFORM_TYPE_ENUM.IOS && r.callHandler("taurus.common.toast", Object.assign({}, c), function() {
      invoker_default.handleBridgeResponse({ errorCode: BRIDGE_ERROR_CODE.SUCCESS, result: {} }, t, n);
    });
  } else
    r && r.call("toast", c, function() {
      invoker_default.handleBridgeResponse({ errorCode: BRIDGE_ERROR_CODE.SUCCESS, result: {} }, t, n);
    });
}
invoker_default.registerAPI("toast", { mobile: toastHandler, mini: toastHandler, pc: function(e, o) {
  var t = e.icon, n = e.text, r = e.duration, a = e.delay;
  window.dingtalk.platform.invokeAPI(o.msgId, "device.notification.toast", { type: t, text: n, duration: r, delay: a });
} }), toast.version = { android: "1.3.2", ios: "1.3.2" };
function toast(e) {
  return invoker_default.invoke("toast", e);
}

// node_modules/gdt-jsapi/es/unlockWithSecurityVerification.js
invoker_default.registerAPI("unlockWithSecurityVerification", { mini: true, mobile: true }), unlockWithSecurityVerification.version = { android: "1.3.1.1", ios: "1.3.1.1" };
function unlockWithSecurityVerification() {
  return invoker_default.invoke("unlockWithSecurityVerification");
}

// node_modules/gdt-jsapi/es/unsubscribe.js
invoker_default.registerAPI("unsubscribe", { mobile: true }), unsubscribe.version = { android: "1.6.0", ios: "1.6.0" };
function unsubscribe(r) {
  return invoker_default.invoke("unsubscribe", r);
}

// node_modules/gdt-jsapi/es/uploadFile.js
invoker_default.registerAPI("dgUploadFile", { mini: true, mobile: true, pc: function(e, i) {
  window.dingtalk.platform.invokeAPI(i.msgId, "biz.util.dgUploadFile", e);
} }), uploadFile.version = { android: "1.3.2", ios: "1.3.2", pc: "1.3.6" };
function uploadFile(e) {
  return invoker_default.invoke("dgUploadFile", _extends({}, e, { _apiName: "uploadFile" }));
}

// node_modules/gdt-jsapi/es/uploadFileByType.js
invoker_default.registerAPI("uploadFileByType", { mini: true, mobile: true }), uploadFileByType.version = { android: "1.3.0", ios: "1.3.0" };
function uploadFileByType(e) {
  return invoker_default.invoke("uploadFileByType", e);
}

// node_modules/gdt-jsapi/es/uploadLocalFile.js
invoker_default.registerAPI("uploadFile", { mini: true }), uploadLocalFile.version = { android: "1.6.2", ios: "1.6.2" };
function uploadLocalFile(e) {
  return new Promise(function(i, o) {
    my.uploadFile(_extends({}, e, { success: function(e2) {
      i(e2);
    }, fail: function(e2) {
      o(e2);
    } }));
  });
}

// node_modules/gdt-jsapi/es/uploadRemoteFileToDisk.js
invoker_default.registerAPI("uploadRemoteFileToDisk", { mini: true, mobile: true, pc: function(o, i) {
  window.dingtalk.platform.invokeAPI(i.msgId, "biz.util.uploadRemoteFileToDisk", o);
} }), uploadRemoteFileToDisk.version = { android: "1.6.0", ios: "1.6.0", pc: "2.6.0" };
function uploadRemoteFileToDisk(o) {
  return invoker_default.invoke("uploadRemoteFileToDisk", o);
}

// node_modules/gdt-jsapi/es/ut.js
invoker_default.registerAPI("ut", { pc: function(i, t) {
  window.dingtalk.platform.invokeAPI(t.msgId, "biz.util.ut", i);
} }), ut.version = { pc: "1.3.10" };
function ut(i) {
  return invoker_default.invoke("ut", i);
}

// node_modules/gdt-jsapi/es/vibrate.js
invoker_default.registerAPI("vibrate", { mini: true, mobile: true }), vibrate.version = { android: "1.3.1", ios: "1.3.1" };
function vibrate(i) {
  return invoker_default.invoke("vibrate", i);
}

// node_modules/gdt-jsapi/es/index.js
var dd2 = { alert, authConfig, bizContactDepartmentsPickerExternal, bizCustomContactChooseExternal, bizCustomContactMultipleChooseExternal, callPhone, canIUse, checkVPNAppInstalled, checkVPNAppOnline, chooseContact, chooseContactWithComplexPicker, chooseDateRangeWithCalendar, chooseDayWithCalendar, chooseDepartments, chooseFile, chooseHalfDayWithCalendar, chooseImage, chooseInterconnectionChat: chooseContact2, chooseLocalImage, chooseSpaceDir, chooseTimeWithCalendar, chooseVideo, closePage, complexPickerAdmin, confirm, copyToClipboard, createChatGroup, createDing, createDingV2, createVideoConf, createVideoMeeting, dealWithBackAction, disableClosePage, disablePullToRefresh, disableWebviewBounce, downloadAudio, downloadFile: downloadFile_default, enablePullToRefresh, enableVpn, enableWebviewBounce, exclusiveInvoke, faceComparison, faceRecognition, getAppInstallStatus, getAuthCode, getConfig, getContainerType, getDeviceId, getFromClipboard, getGeolocation, getGeolocationStatus, getHotspotInfo, getLanguageSetting, getLoginUser, getNetworkType, getPhoneInfo, getProxyInfo, getStorageItem, getTraceStatus, getUUID, getUserAgent, getWaterMark, getWaterMarkConfigV2, getWaterMarkV2, getWifiStatus, getWorkbenchContext, goBack, hideLoading, hideOptionMenu, hideTitleBar, isDownloadFileExist, joinScheduleConf, joinVideoConf, joinVideoMeeting, locateOnMap, on, onAudioPlayEnd, onRecordAudioEnd, openApiInvoker, openApp, openBrowser, openChat, openDownloadFile, openLink, openPage, openSchemeUrl, openSlidePanel, openWatermarkCamera, pauseAudio, pickChat, pickChatByCorpId, pickGroupChat, pickGroupConversation, playAudio, previewDoc, previewImage, printFile, printNativeLog, prompt, pushWindow, readImageToBase64, ready, reduceImageSize, removeStorageItem, replacePage, resetView, resumeAudio, rotateView, scan, searchOnMap, sendOutData: cardSendOutData, setLocalScreenShotPolicy, setNavIcon, setNavLeftText, setOptionMenu, setProxyInfo, setStorageItem, setTitle, shareFileToMessage, shareImageToMessage, shareToMessage, shootVideo, showActionSheet, showCallMenu, showDatePicker, showDateTimePicker, showExtendModal, showHomeBottomTab, showLoading, showModal, showMultiSelect, showOnMap: searchOnMap2, showOptionMenu, showPlainInputUponKeyboard, showQuickCallMenu, showSelect, showSignature, showSocialShare, showTimePicker, showTitleBar, startFaceRecognition, startGeolocation, startListenNetworkStatus, startRecordAudio, startTraceReport, startVPNApp, startWatchShake, stopAudio, stopGeolocation, stopListenNetworkStatus, stopPullToRefresh, stopRecordAudio, stopTraceReport, stopVPNApp, stopWatchShake, subscribe, takePhoto, testProxy, toast, unlockWithSecurityVerification, unsubscribe, uploadFile, uploadFileByType, uploadLocalFile, uploadRemoteFileToDisk, ut, version, vibrate };
if (invoker_default.getAppType() === API_INVOKER_TYPE.MINI_APP)
  dd2 = new Proxy(dd2, { get: function(o, e, t) {
    return e in dd2 ? Reflect.get(o, e, t) : c2p(Reflect.get(my, e, t), e);
  } });
else {
  window.dd && console.warn("\u5DF2\u7ECF\u5B58\u5728 window.dd \u53D8\u91CF\uFF0C\u5F15\u5165 gdt-jsapi \u4F1A\u4FEE\u6539 window.dd \u7684\u503C\u3002");
  try {
    Object.defineProperty(window, "dd", { value: dd2, writable: true });
  } catch (o) {
    console.error(o);
  }
  window.gdt && console.warn("\u5DF2\u7ECF\u5B58\u5728 window.gdt \u53D8\u91CF\uFF0C\u5F15\u5165 gdt-jsapi \u4F1A\u4FEE\u6539 window.gdt \u7684\u503C\u3002");
  try {
    Object.defineProperty(window, "gdt", { value: dd2, writable: true });
  } catch (o) {
    console.error(o);
  }
}
var es_default = dd2;
export {
  es_default as default
};
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */
//# sourceMappingURL=gdt-jsapi.js.map
