(function() {
    // 获取当前环境的API基础路径
    const baseURL = window.location.protocol + '//' + window.location.hostname + ':' + window.location.port;
    //alert('API baseURL: ' + baseURL);

    // HTTP请求函数
    async function request(url, options = {}) {
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        };

        const config = {
            ...defaultOptions,
            ...options,
            headers: {
                ...defaultOptions.headers,
                ...options.headers
            }
        };

        if (config.data) {
            config.body = JSON.stringify(config.data);
        }

        //alert(`发起请求: ${url}\n参数: ${JSON.stringify(config, null, 2)}`);

        const response = await fetch(baseURL + url, config);
        if (!response.ok) {
            throw new Error(`请求失败: ${response.status}`);
        }
        const result = await response.json();
        //alert(`请求结果: ${JSON.stringify(result, null, 2)}`);
        return result;
    }

    // 解析URL参数
    //alert(window.location.href)
    function getUrlParams() {
        const urlParams = new URLSearchParams(window.location.search);
        let redirect_url = urlParams.get('redirect_url');
        
        // 处理 redirect_url 中的 asec_code 参数
        if (redirect_url) {
            try {
                const redirectUrlObj = new URL(redirect_url);
                const searchParams = new URLSearchParams(redirectUrlObj.search);
                searchParams.delete('asec_code');  // 移除所有的 asec_code 参数
                redirectUrlObj.search = searchParams.toString();
                redirect_url = redirectUrlObj.toString();
            } catch (error) {
                console.error('处理 redirect_url 失败:', error);
            }
        }

        const params = {
            code: urlParams.get('code'),
            state: urlParams.get('state'),
            redirect_url: redirect_url,
            auth_type: urlParams.get('auth_type')
        };
        //alert('URL参数: ' + JSON.stringify(params, null, 2));
        return params;
    }

    // 显示加载状态
    function showLoading(message) {
        const loadingDiv = document.createElement('div');
        loadingDiv.id = 'wx-loading';
        loadingDiv.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 20px;
            border-radius: 4px;
            box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
        `;
        loadingDiv.textContent = message || '处理中...';
        document.body.appendChild(loadingDiv);
    }

    // 更新加载状态
    function updateLoading(message) {
        const loadingDiv = document.getElementById('wx-loading');
        if (loadingDiv) {
            loadingDiv.textContent = message;
        }
    }

    // 初始化处理
    async function init() {
        try {
            showLoading('正在处理认证信息...');
            const params = getUrlParams();
            
            // 参数校验
            if (!params.code || !params.state) {
                throw new Error('缺少必要的认证参数');
            }

            // 调用登录接口
            updateLoading('验证登录信息...');
            const loginData = {
                clientId: 'client_portal',
                grantType: 'implicit',
                redirect_uri: '',
                idpId: params.state,
                authWeb: {
                    authWebCode: params.code
                }
            };

            const loginResult = await request('/auth/login/v1/user/third', {
                method: 'POST',
                data: loginData
            });

            // 从登录结果中获取token
            const token = loginResult.data?.accessToken;
            if (!token) {
                console.error('登录失败，返回结果：', loginResult);
                // 重定向回原始URL
                if (params.redirect_url) {
                    window.location.href = params.redirect_url;
                }
                return;
            }

            // 获取asec_code，带上token和redirect_url
            //updateLoading('获取验证信息...');
            const verifyResult = await request(`/auth/user/v1/redirect_verify?redirect_url=${encodeURIComponent(params.redirect_url)}`, {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + token
                }
            });

            //alert('最终重定向URL: ' + verifyResult.url);
            // 执行重定向
            window.location.href = verifyResult.url;

        } catch (error) {
            alert('登录失败：' + error.message);
            console.error('登录失败:', error);
        }
    }

    // 启动处理流程
    init();
})(); 