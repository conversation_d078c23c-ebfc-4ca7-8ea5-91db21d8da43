/*! 
 Build based on gin-vue-admin 
 Time : 1754879545000 */
!function(){function e(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var n,r,i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",a=i.toStringTag||"@@toStringTag";function u(e,i,o,a){var u=i&&i.prototype instanceof c?i:c,s=Object.create(u.prototype);return t(s,"_invoke",function(e,t,i){var o,a,u,c=0,s=i||[],f=!1,p={p:0,n:0,v:n,a:d,f:d.bind(n,4),d:function(e,t){return o=e,a=0,u=n,p.n=t,l}};function d(e,t){for(a=e,u=t,r=0;!f&&c&&!i&&r<s.length;r++){var i,o=s[r],d=p.p,v=o[2];e>3?(i=v===t)&&(u=o[(a=o[4])?5:(a=3,3)],o[4]=o[5]=n):o[0]<=d&&((i=e<2&&d<o[1])?(a=0,p.v=t,p.n=o[1]):d<v&&(i=e<3||o[0]>t||t>v)&&(o[4]=e,o[5]=t,p.n=v,a=0))}if(i||e>1)return l;throw f=!0,t}return function(i,s,v){if(c>1)throw TypeError("Generator is already running");for(f&&1===s&&d(s,v),a=s,u=v;(r=a<2?n:u)||!f;){o||(a?a<3?(a>1&&(p.n=-1),d(a,u)):p.n=u:p.v=u);try{if(c=2,o){if(a||(i="next"),r=o[i]){if(!(r=r.call(o,u)))throw TypeError("iterator result is not an object");if(!r.done)return r;u=r.value,a<2&&(a=0)}else 1===a&&(r=o.return)&&r.call(o),a<2&&(u=TypeError("The iterator does not provide a '"+i+"' method"),a=1);o=n}else if((r=(f=p.n<0)?u:e.call(t,p))!==l)break}catch(r){o=n,a=1,u=r}finally{c=1}}return{value:r,done:f}}}(e,o,a),!0),s}var l={};function c(){}function s(){}function f(){}r=Object.getPrototypeOf;var p=[][o]?r(r([][o]())):(t(r={},o,(function(){return this})),r),d=f.prototype=c.prototype=Object.create(p);function v(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,t(e,a,"GeneratorFunction")),e.prototype=Object.create(d),e}return s.prototype=f,t(d,"constructor",f),t(f,"constructor",s),s.displayName="GeneratorFunction",t(f,a,"GeneratorFunction"),t(d),t(d,a,"Generator"),t(d,o,(function(){return this})),t(d,"toString",(function(){return"[object Generator]"})),(e=function(){return{w:u,m:v}})()}function t(e,n,r,i){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}t=function(e,n,r,i){if(n)o?o(e,n,{value:r,enumerable:!i,configurable:!i,writable:!i}):e[n]=r;else{var a=function(n,r){t(e,n,(function(e){return this._invoke(n,r,e)}))};a("next",0),a("throw",1),a("return",2)}},t(e,n,r,i)}function n(e,t,n,r,i,o,a){try{var u=e[o](a),l=u.value}catch(e){return void n(e)}u.done?t(l):Promise.resolve(l).then(r,i)}function r(e){return function(){var t=this,r=arguments;return new Promise((function(i,o){var a=e.apply(t,r);function u(e){n(a,i,o,u,l,"next",e)}function l(e){n(a,i,o,u,l,"throw",e)}u(void 0)}))}}System.register(["./index-legacy.39a66d41.js"],(function(t,n){"use strict";var i,o,a,u,l,c,s,f,p,d,v,y,h,g,m,x,_=document.createElement("style");return _.textContent='@charset "UTF-8";.input-with-select .el-input__inner{font-size:12px}\n',document.head.appendChild(_),{setters:[function(e){i=e.r,o=e.u,a=e.h,u=e.a,l=e.b,c=e.d,s=e.i,f=e.t,p=e.j,d=e.w,v=e.k,y=e.l,h=e.s,g=e.p,m=e.M,x=e.m}],execute:function(){var n={style:{width:"100%",height:"100%",background:"#FFFFFF"}},_={style:{width:"442px",height:"175px","padding-left":"38.5%","padding-top":"21%","text-align":"center"}},b={style:{"font-size":"24px",display:"flex","justify-content":"center","align-items":"center"}},w={class:"icon",style:{"margin-right":"10px","font-size":"14px",width:"24px",height:"24px"},"aria-hidden":"true"},k={key:0,style:{"margin-top":"20px"}},j={style:{float:"left","margin-left":"34px"}},O={key:1,style:{"margin-top":"20px"}};t("default",Object.assign({name:"Appverify"},{setup:function(t){var F=i(),q=i({}),P=o(),T=function(){var t=r(e().m((function t(){var n,r,i;return e().w((function(e){for(;;)switch(e.n){case 0:return n={user_id:P.query.user_id,idp_id:P.query.idp_id},e.n=1,h(n);case 1:if(200!==(r=e.v).status){e.n=2;break}if(q.value=r.data.data,null!==(i=q.value)&&void 0!==i&&i.notPhone){e.n=2;break}return e.n=2,S();case 2:return e.a(2)}}),t)})));return function(){return t.apply(this,arguments)}}();T();var z,C=i(60),G=function(){clearInterval(z)},S=function(){var t=r(e().m((function t(){var n,r;return e().w((function(e){for(;;)switch(e.n){case 0:return n={uniq_key:q.value.uniqKey,idp_id:P.query.idp_id},e.n=1,g(n);case 1:200===(r=e.v).status&&-1!==r.data.code?(C.value=60,z=setInterval((function(){C.value--,0===C.value&&G()}),1e3)):(m({showClose:!0,message:r.data.msg,type:"error"}),C.value=0);case 2:return e.a(2)}}),t)})));return function(){return t.apply(this,arguments)}}(),E=function(){var t=r(e().m((function t(){var n,r;return e().w((function(e){for(;;)switch(e.n){case 0:return n={uniq_key:q.value.uniqKey,auth_code:F.value,user_name:q.value.userName,idp_id:P.query.idp_id,redirect_uri:"app_redirect",grant_type:"implicit",client_id:"client_portal"},e.n=1,x(n);case 1:200===(r=e.v).status&&-1!==r.data.code?location.href=P.query.redirect_url:m({showClose:!0,message:r.data.msg,type:"error"});case 2:return e.a(2)}}),t)})));return function(){return t.apply(this,arguments)}}();return function(e,t){var r,i,o,h,g=a("base-input"),m=a("base-button");return u(),l("div",n,[c("div",_,[c("div",b,[(u(),l("svg",w,t[1]||(t[1]=[s("--\x3e "),c("use",{"xlink:href":"#icon-shuoming2"},null,-1)]))),t[2]||(t[2]=s("--\x3e 该应用需通过安全认证后才可继续访问 "))]),!1===(null===(r=q.value)||void 0===r?void 0:r.notPhone)?(u(),l("div",k,[c("span",j,"验证码已发送至您的账号("+f(null===(i=q.value)||void 0===i?void 0:i.userName)+")关联的手机，请注意查收",1),p(g,{modelValue:F.value,"onUpdate:modelValue":t[0]||(t[0]=function(e){return F.value=e}),placeholder:"请输入短信验证码",style:{float:"left","margin-left":"34px","font-size":"12px","margin-top":"12px",width:"258px",height:"32px"},class:"input-with-select"},null,8,["modelValue"]),p(m,{style:{"border-radius":"4px","font-size":"12px",float:"left","margin-top":"12px",position:"relative","margin-left":"10px",width:"92px",height:"32px"},disabled:C.value>0,onClick:S},{default:d((function(){return[s("重新发送 "+f(C.value>0?"(".concat(C.value,"秒)"):""),1)]})),_:1},8,["disabled"])])):(u(),l("div",O,[c("span",null,"您的账号("+f(null===(o=q.value)||void 0===o?void 0:o.userName)+")未关联手机号码，请联系管理员",1)])),!1===(null===(h=q.value)||void 0===h?void 0:h.notPhone)?(u(),v(m,{key:2,type:"primary",size:"large",style:{float:"left","margin-left":"34px",height:"44px","margin-top":"14px",width:"365px"},disabled:!F.value,onClick:E},{default:d((function(){return t[3]||(t[3]=[s("确 定 ")])})),_:1,__:[3]},8,["disabled"])):y("",!0)])])}}}))}}}))}();
