/*! 
 Build based on gin-vue-admin 
 Time : 1754879545000 */
!function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function n(e){for(var n=1;n<arguments.length;n++){var r=null!=arguments[n]?arguments[n]:{};n%2?t(Object(r),!0).forEach((function(t){a(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):t(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function a(t,n,a){return(n=function(t){var n=function(t,n){if("object"!=e(t)||!t)return t;var a=t[Symbol.toPrimitive];if(void 0!==a){var r=a.call(t,n||"default");if("object"!=e(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==e(n)?n:n+""}(n))in t?Object.defineProperty(t,n,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[n]=a,t}function r(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(e){if("string"==typeof e)return o(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?o(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var a=0,r=function(){};return{s:r,n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,l=!0,u=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return l=e.done,e},e:function(e){u=!0,i=e},f:function(){try{l||null==n.return||n.return()}finally{if(u)throw i}}}}function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=Array(t);n<t;n++)a[n]=e[n];return a}function i(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,t,n="function"==typeof Symbol?Symbol:{},a=n.iterator||"@@iterator",r=n.toStringTag||"@@toStringTag";function o(n,a,r,o){var i=a&&a.prototype instanceof c?a:c,s=Object.create(i.prototype);return l(s,"_invoke",function(n,a,r){var o,i,l,c=0,s=r||[],d=!1,h={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(t,n){return o=t,i=0,l=e,h.n=n,u}};function p(n,a){for(i=n,l=a,t=0;!d&&c&&!r&&t<s.length;t++){var r,o=s[t],p=h.p,f=o[2];n>3?(r=f===a)&&(l=o[(i=o[4])?5:(i=3,3)],o[4]=o[5]=e):o[0]<=p&&((r=n<2&&p<o[1])?(i=0,h.v=a,h.n=o[1]):p<f&&(r=n<3||o[0]>a||a>f)&&(o[4]=n,o[5]=a,h.n=f,i=0))}if(r||n>1)return u;throw d=!0,a}return function(r,s,f){if(c>1)throw TypeError("Generator is already running");for(d&&1===s&&p(s,f),i=s,l=f;(t=i<2?e:l)||!d;){o||(i?i<3?(i>1&&(h.n=-1),p(i,l)):h.n=l:h.v=l);try{if(c=2,o){if(i||(r="next"),t=o[r]){if(!(t=t.call(o,l)))throw TypeError("iterator result is not an object");if(!t.done)return t;l=t.value,i<2&&(i=0)}else 1===i&&(t=o.return)&&t.call(o),i<2&&(l=TypeError("The iterator does not provide a '"+r+"' method"),i=1);o=e}else if((t=(d=h.n<0)?l:n.call(a,h))!==u)break}catch(t){o=e,i=1,l=t}finally{c=1}}return{value:t,done:d}}}(n,r,o),!0),s}var u={};function c(){}function s(){}function d(){}t=Object.getPrototypeOf;var h=[][a]?t(t([][a]())):(l(t={},a,(function(){return this})),t),p=d.prototype=c.prototype=Object.create(h);function f(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,d):(e.__proto__=d,l(e,r,"GeneratorFunction")),e.prototype=Object.create(p),e}return s.prototype=d,l(p,"constructor",d),l(d,"constructor",s),s.displayName="GeneratorFunction",l(d,r,"GeneratorFunction"),l(p),l(p,r,"Generator"),l(p,a,(function(){return this})),l(p,"toString",(function(){return"[object Generator]"})),(i=function(){return{w:o,m:f}})()}function l(e,t,n,a){var r=Object.defineProperty;try{r({},"",{})}catch(e){r=0}l=function(e,t,n,a){if(t)r?r(e,t,{value:n,enumerable:!a,configurable:!a,writable:!a}):e[t]=n;else{var o=function(t,n){l(e,t,(function(e){return this._invoke(t,n,e)}))};o("next",0),o("throw",1),o("return",2)}},l(e,t,n,a)}function u(e,t,n,a,r,o,i){try{var l=e[o](i),u=l.value}catch(e){return void n(e)}l.done?t(u):Promise.resolve(u).then(a,r)}function c(e){return function(){var t=this,n=arguments;return new Promise((function(a,r){var o=e.apply(t,n);function i(e){u(o,a,r,i,l,"next",e)}function l(e){u(o,a,r,i,l,"throw",e)}i(void 0)}))}}System.register(["./index-legacy.39a66d41.js","./config-legacy.6e7b5c15.js"],(function(e,t){"use strict";var a,o,l,u,s,d,h,p,f,v,m,g,y,b,x,w,k,j,O,C,S,P,T,_,z,q=document.createElement("style");return q.textContent='@charset "UTF-8";.login-page{width:100%;height:100%;background-image:url('+new URL("login_background.4576f25d.png",t.meta.url).href+');background-size:cover;background-position:center;min-height:100vh}.header{height:60px;display:flex;align-items:center;background-color:rgba(255,255,255,.8)}.logo{height:20px;margin-left:50px;margin-right:10px}.separator{width:1px;height:14px;background-color:#ccc;margin:0 10px}.company-name{font-size:24px}.header-text{font-size:12px;opacity:.6}.content{display:flex;height:calc(100% - 60px)}.left-panel{flex:1;display:flex;flex-direction:column;justify-content:center;padding:20px;margin-left:310px}.slogan{font-size:36px;margin-bottom:20px}.image{width:718px;height:470px;margin-bottom:20px}.icons{display:flex;justify-content:space-between;width:150px}.icons img{width:30px;height:30px}.right-panel{width:auto;height:auto;min-height:560px;max-height:560px;box-sizing:border-box;min-width:450px;max-width:450px;margin-right:310px;margin-bottom:auto;padding:40px;background-color:#fff;border-radius:8px;box-shadow:0 2px 16px rgba(16,36,66,.1);backdrop-filter:blur(2px);display:flex;flex-direction:column;justify-content:center;position:absolute;z-index:2;top:50%;left:76%;transform:translate(-50%,-50%)}.title{height:57px;font-size:18px;text-align:center}.login_panel{display:flex;flex-direction:column}.form-group{display:flex;flex-direction:column;margin-bottom:20px}.label{font-size:16px;margin-bottom:5px}.input-field{height:40px;padding:5px;font-size:16px;border:1px solid #ccc;border-radius:5px}.login-submit-button{width:100%;height:40px;margin-top:12px;font-size:16px;color:#fff;background-color:#536ce6;border:none;border-radius:5px;cursor:pointer;margin-bottom:60px}.submit-button:hover,.submit-button:active{background-color:#536ce6}.login-page .auth-class:hover .avatar{border:1px #204ED9 solid!important}.login-page .title{text-align:center;display:block;width:100%}.login-page .right-panel{background-color:#fff!important}.login-page .right-panel .login_panel_form{height:370px;padding-top:16px}.auth-waiting{text-align:center;padding:30px 20px;background-color:#f8f9fa;border-radius:8px;border:1px dashed #dee2e6}.auth-waiting .waiting-icon{margin-bottom:15px}.auth-waiting .waiting-title{font-size:16px;color:#495057;margin-bottom:8px;font-weight:500}.auth-waiting .waiting-message{color:#6c757d;font-size:13px;line-height:1.4;margin-bottom:15px}.auth-waiting .security-tips{display:flex;align-items:center;justify-content:center;gap:8px;padding:10px;background-color:#f0f9ff;border-radius:6px;font-size:12px;color:#1f2937}.error-component{text-align:center;padding:20px;background-color:#fef2f2;border:1px solid #fecaca;border-radius:8px;color:#dc2626;font-size:14px;margin:10px 0}.error-component:before{content:"\\26a0\\fe0f";display:block;font-size:24px;margin-bottom:8px}@media screen and (min-width: 900px) and (max-width: 900px){.right-panel{margin-right:0;right:0px;top:43px;left:auto;transform:none!important}}@media screen and (min-width: 768px) and (max-width: 1000px){.right-panel{border-radius:0!important;box-shadow:none!important;backdrop-filter:none!important;background-color:#fff!important;max-height:560px}}@media screen and (max-width: 768px){.right-panel{padding:0;min-width:95%;max-width:95%;left:50%!important}}.auth-switcher{margin-top:24px}.auth-switcher .auth-switcher-title{text-align:center;color:#929298;font-size:14px;margin-bottom:8px;position:relative}.auth-switcher .auth-switcher-title:before,.auth-switcher .auth-switcher-title:after{content:"";position:absolute;top:50%;width:130px;height:1px}.auth-switcher .auth-switcher-title:before{left:0;background:linear-gradient(270deg,#e2e2e6,#f5f7fd)}.auth-switcher .auth-switcher-title:after{right:0;background:linear-gradient(270deg,#f5f7fd,#e2e2e6)}.auth-switcher .auth-switcher-container{display:flex;align-items:center;justify-content:center;position:relative}.auth-switcher .auth-switcher-container .auth-nav-btn{display:flex;align-items:center;justify-content:center;width:14px;padding:0;height:32px;border:none;border-radius:50%;background:#ffffff;color:#666;cursor:pointer;transition:all .3s ease;z-index:2}.auth-switcher .auth-switcher-container .auth-nav-btn:hover:not(:disabled){border-color:#1890ff;color:#1890ff}.auth-switcher .auth-switcher-container .auth-nav-btn:disabled{opacity:.3;cursor:not-allowed}.auth-switcher .auth-switcher-container .auth-nav-btn:hover{border:none}.auth-switcher .auth-switcher-container .auth-nav-btn:focus{border:none}.auth-switcher .auth-switcher-container .auth-methods-wrapper{flex:1;max-width:336px;overflow:hidden;position:relative}.auth-switcher .auth-switcher-container .auth-methods-wrapper .auth-methods-container{display:flex;transition:transform .3s ease}.auth-switcher .auth-switcher-container .auth-methods-wrapper .auth-methods-container.auth-methods-centered{width:auto;justify-content:center}.auth-switcher .auth-switcher-container .auth-methods-wrapper .auth-methods-container .auth-method-item{flex:0 0 84px;display:flex;flex-direction:column;align-items:center;justify-content:center;padding:12px 0;cursor:pointer;transition:all .3s ease;border-radius:8px}.auth-switcher .auth-switcher-container .auth-methods-wrapper .auth-methods-container .auth-method-item .auth-method-icon{width:36px;height:36px;border-radius:50%;background:#f5f5f7;display:flex;align-items:center;justify-content:center;margin-bottom:8px;color:#fff;font-size:18px;transition:all .3s ease}.auth-switcher .auth-switcher-container .auth-methods-wrapper .auth-methods-container .auth-method-item .auth-method-name{font-size:12px;color:#666;text-align:center;line-height:1.2;max-width:64px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.auth-switcher .auth-switcher-container .auth-methods-wrapper .auth-methods-container .auth-method-item:hover .auth-method-icon{transform:scale(1.1);box-shadow:0 2px 4px rgba(0,0,0,.2)}.auth-switcher .auth-switcher-container .auth-methods-wrapper .auth-methods-container .auth-method-item:hover .auth-method-name{color:#536ce6;font-weight:500}\n',document.head.appendChild(q),{setters:[function(e){a=e.q,o=e.r,l=e.u,u=e.c,s=e.f,d=e.o,h=e.v,p=e.x,f=e.a,v=e.b,m=e.d,g=e.j,y=e.y,b=e.t,x=e.l,w=e.k,k=e.z,j=e.e,O=e.n,C=e.F,S=e.A,P=e.B,T=e.L,_=e.C},function(e){z=e.g}],execute:function(){var q={class:"login-page"},U={class:"content"},I={class:"right-panel"},L={key:0},A={key:1},E={key:0,class:"login_panel_form"},D={key:0,class:"title"},G={key:1,class:"title"},F={key:1,class:"auth-switcher"},N={class:"auth-switcher-container"},R=["disabled"],H={"aria-hidden":"true",class:"icon",style:{height:"16px",width:"16px"}},K={class:"auth-methods-wrapper"},M=["title","onClick"],W=["data-auth-type"],J={"aria-hidden":"true",class:"icon",style:{height:"18px",width:"18px"}},B=["xlink:href"],V={class:"auth-method-name"},X=["disabled"],$={"aria-hidden":"true",class:"icon",style:{height:"16px",width:"16px"}},Q={key:2,class:"auth-waiting"},Y={class:"waiting-icon"},Z={"aria-hidden":"true",class:"icon",style:{height:"32px",width:"32px",color:"#f4a261"}},ee=["xlink:href"],te={class:"waiting-title"},ne={class:"security-tips"},ae={"aria-hidden":"true",class:"icon",style:{height:"16px",width:"16px",color:"#67c23a"}};e("default",Object.assign({name:"Login"},{setup:function(e){var re=a({loader:function(){return P((function(){return t.import("./localLogin-legacy.e5a9dbea.js")}),void 0,t.meta.url)},loadingComponent:T,errorComponent:{template:'<div class="error-component">组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),oe=a({loader:function(){return P((function(){return t.import("./wechat-legacy.62ba32e0.js")}),void 0,t.meta.url)},loadingComponent:T,errorComponent:{template:'<div class="error-component">微信组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),ie=a({loader:function(){return P((function(){return t.import("./feishu-legacy.3482291d.js")}),void 0,t.meta.url)},loadingComponent:T,errorComponent:{template:'<div class="error-component">飞书组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),le=a({loader:function(){return P((function(){return t.import("./dingtalk-legacy.6e3b0725.js")}),void 0,t.meta.url)},loadingComponent:T,errorComponent:{template:'<div class="error-component">钉钉组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),ue=a({loader:function(){return P((function(){return t.import("./oauth2-legacy.8d5fb523.js")}),void 0,t.meta.url)},loadingComponent:T,errorComponent:{template:'<div class="error-component">OAuth2组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),ce=a({loader:function(){return P((function(){return t.import("./secondaryAuth-legacy.8c5b84ca.js")}),void 0,t.meta.url)},loadingComponent:T,errorComponent:{template:'<div class="error-component">二次认证组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),se=a({loader:function(){return P((function(){return t.import("./serverConfig-legacy.39744138.js")}),void 0,t.meta.url)},loadingComponent:T,errorComponent:{template:'<div class="error-component">服务器配置组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),de=o({}),he=l(),pe=o(0),fe=o([]),ve=o("local"),me=o(""),ge=o(""),ye=o(""),be=o([]),xe=o([]),we=o(!1),ke=o(!1),je=o(),Oe=o(""),Ce=o(!1),Se=o(""),Pe=o(!1),Te=o(""),_e=o(!1),ze=o(""),qe=o(""),Ue=o(""),Ie=o({}),Le=o(0),Ae=o(84),Ee=o(4),De=o(null),Ge=u((function(){var e=we.value?ze.value:ge.value;return fe.value.filter((function(t){return t.id!==e}))})),Fe=u((function(){return Math.max(0,Ge.value.length-Ee.value)})),Ne=s(),Re=function(){var e={};if(he.query.type&&(e.type=he.query.type),he.query.wp&&(e.wp=he.query.wp),he.query.redirect&&0===Object.keys(e).length)try{var t=decodeURIComponent(he.query.redirect);if(t.includes("?")){var n=t.substring(t.indexOf("?")+1),a=new URLSearchParams(n);a.get("type")&&(e.type=a.get("type")),a.get("wp")&&(e.wp=a.get("wp"))}}catch(r){console.warn("解析redirect参数失败:",r)}return e},He=function(){var e=c(i().m((function e(){var t,n,a;return i().w((function(e){for(;;)switch(e.n){case 0:if(!_.isClient()){e.n=5;break}if(window.self===window.top){e.n=1;break}return logger.log("iframe中不展示服务器配置页面"),e.a(2,!1);case 1:t=globalUrlHashParams?globalUrlHashParams.get("WebUrl"):"";try{t&&(n=new URL(t),t="".concat(n.protocol,"//").concat(n.host))}catch(r){t="",console.warn("解析 WebUrl 参数失败:",r)}if(!t){e.n=2;break}return e.a(2,!1);case 2:return e.n=3,_.getClientConfig();case 3:if(a=e.v,de.value=a||{},a&&a.ServerUrl){e.n=4;break}return e.a(2,!0);case 4:case 5:return e.a(2,!1)}}),e)})));return function(){return e.apply(this,arguments)}}(),Ke=function(e){logger.log("服务器配置完成:",e),ke.value=!1,globalUrlHashParams.set("WebUrl",e.serverUrl),Me()},Me=function(){var e=c(i().m((function e(){var t,n,a,o,l,u,c,s,d,h,p,f,v,m,g,y,b,x,w,k;return i().w((function(e){for(;;)switch(e.n){case 0:return e.p=0,e.n=1,He();case 1:if(!e.v){e.n=2;break}return ke.value=!0,logger.log("显示服务器配置页面"),e.a(2);case 2:return t=Re(),Object.keys(t).length>0&&(localStorage.setItem("client_params",JSON.stringify(t)),sessionStorage.setItem("client_params",JSON.stringify(t))),logger.log("登录页初始化开始"),We(),e.n=3,z();case 3:if(200===(n=e.v).status){if(fe.value=n.data.idpList,a=he.query.idp_id||Ne.loginType,logger.log("传入或上次记住的认证源ID:",a),a&&"undefined"!==a){o=!1,l=r(n.data.idpList);try{for(l.s();!(u=l.n()).done;)c=u.value,a===c.id&&(o=!0,ge.value=c.id,ve.value=c.type,me.value=c.templateType,be.value=c.attrs,be.value.name=c.name,be.value.authType=c.type,logger.log("传入匹配到认证源"))}catch(i){l.e(i)}finally{l.f()}o||(logger.log("未匹配到认证源"),ye.value=null===(s=fe.value[0])||void 0===s?void 0:s.id,ge.value=null===(d=fe.value[0])||void 0===d?void 0:d.id,ve.value=null===(h=fe.value[0])||void 0===h?void 0:h.type,me.value=null===(p=fe.value[0])||void 0===p?void 0:p.templateType,be.value=null===(f=fe.value[0])||void 0===f?void 0:f.attrs,be.value.name=fe.value[0].name,be.value.authType=null===(v=fe.value[0])||void 0===v?void 0:v.type)}else ye.value=null===(m=fe.value[0])||void 0===m?void 0:m.id,ge.value=null===(g=fe.value[0])||void 0===g?void 0:g.id,ve.value=null===(y=fe.value[0])||void 0===y?void 0:y.type,me.value=null===(b=fe.value[0])||void 0===b?void 0:b.templateType,be.value=null===(x=fe.value[0])||void 0===x?void 0:x.attrs,be.value.name=fe.value[0].name,be.value.authType=null===(w=fe.value[0])||void 0===w?void 0:w.type,logger.log("未记住和传入使用第一个认证源:",ge.value);++pe.value}e.n=5;break;case 4:e.p=4,k=e.v,console.error("获取认证列表失败:",k),_.isClient()&&We();case 5:return e.a(2)}}),e,null,[[0,4]])})));return function(){return e.apply(this,arguments)}}(),We=function(){logger.log("使用默认本地登录");var e={id:"default-local",type:"local",name:"本地账号登录",templateType:"local",attrs:{name:"本地账号登录",authType:"local",placeholder_username:"请输入用户名",placeholder_password:"请输入密码"}};fe.value=[e],ge.value=e.id,ve.value=e.type,me.value=e.templateType,be.value=e.attrs,++pe.value,logger.log("已设置默认本地登录认证源")};d(c(i().m((function e(){var t;return i().w((function(e){for(;;)switch(e.n){case 0:return e.p=0,logger.log("认证页面挂载:",he.query),e.n=1,Me();case 1:e.n=3;break;case 2:e.p=2,t=e.v,console.error(t);case 3:return e.a(2)}}),e,null,[[0,2]])}))));var Je=u((function(){switch(ve.value){case"local":case"msad":case"ldap":case"web":case"email":return re;case"qiyewx":return oe;case"feishu":return ie;case"dingtalk":return le;case"oauth2":case"cas":return ue;default:return"oauth2"===me.value?ue:"local"}})),Be=u((function(){return[{type:"sms",name:"短信验证",icon:"duanxin",available:"phone"===Se.value},{type:"email",name:"邮箱验证",icon:"email",available:"email"===Se.value},{type:"totp",name:"TOTP",icon:"totp",available:"totp"===Se.value}]})),Ve=function(){we.value=!1,xe.value=[],je.value="",Oe.value="",Se.value="",Pe.value=!1,Te.value="",_e.value=!1,ze.value&&(ge.value=ze.value,ve.value=qe.value,me.value=Ue.value,be.value=n({},Ie.value),ze.value="",qe.value="",Ue.value="",Ie.value={}),++pe.value,logger.log("取消后恢复的状态:",{isSecondary:we.value,auth_id:ge.value,auth_type:ve.value})},Xe=function(){var e=c(i().m((function e(t){var n,a,r;return i().w((function(e){for(;;)switch(e.n){case 0:n=T.service({fullscreen:!0,text:"认证成功，正在跳转..."});try{a=he.query.redirect_url||"/",t.clientParams&&((r=new URLSearchParams).set("type",t.clientParams.type),t.clientParams.wp&&r.set("wp",t.clientParams.wp),a+=(a.includes("?")?"&":"?")+r.toString()),window.location.href=a}finally{null==n||n.close()}case 1:return e.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}(),$e=u((function(){return!["dingtalk","feishu","qiyewx"].includes(ve.value)&&("oauth2"!==me.value&&"cas"!==ve.value||("cas"===ve.value?1===parseInt(be.value.casOpenType):"oauth2"===me.value&&1===parseInt(be.value.oauth2OpenType)))})),Qe=function(){Le.value>0&&Le.value--},Ye=function(){Le.value<Fe.value&&Le.value++},Ze=function(e){ye.value=e.id,be.value=e.attrs||{},be.value.name=e.name,be.value.authType=e.type,we.value&&(be.value.uniqKey=je.value,be.value.notPhone=Ce.value),ge.value=e.id,ve.value=e.type,me.value=e.templateType,++pe.value};return h(we,c(i().m((function e(){return i().w((function(e){for(;;)switch(e.n){case 0:we.value&&(ze.value=ge.value,qe.value=ve.value,Ue.value=me.value,Ie.value=n({},be.value),logger.log("二次认证数据:",{secondary:xe.value,secondaryLength:xe.value.length}),xe.value.length>0&&Ze(xe.value[0]));case 1:return e.a(2)}}),e)})))),p("secondary",xe),p("isSecondary",we),p("uniqKey",je),p("userName",Oe),p("notPhone",Ce),p("last_id",ye),p("contactType",Se),p("hasContactInfo",Pe),p("qrcode",Te),p("CurrentSecret",_e),function(e,t){return f(),v("div",q,[m("div",U,[t[6]||(t[6]=m("div",{class:"left-panel"},null,-1)),m("div",I,[ke.value?(f(),v("div",L,[g(y(se),{onServerConfigured:Ke})])):we.value?(f(),v("div",Q,[m("div",Y,[(f(),v("svg",Z,[m("use",{"xlink:href":"#icon-auth-".concat(qe.value||ve.value)},null,8,ee)]))]),m("h4",te,b(Ie.value.name||be.value.name)+" 登录成功",1),t[5]||(t[5]=m("p",{class:"waiting-message"},"需要进行安全验证以确保账户安全",-1)),m("div",ne,[(f(),v("svg",ae,t[3]||(t[3]=[m("use",{"xlink:href":"#icon-shield"},null,-1)]))),t[4]||(t[4]=m("span",null,"为了您的账户安全，请完成二次身份验证",-1))])])):(f(),v("div",A,[ge.value?(f(),v("div",E,["local"===ve.value?(f(),v("span",D,"本地账号登录")):$e.value?(f(),v("span",G,b(be.value.name),1)):x("",!0),(f(),w(k(Je.value),{"auth-id":ge.value,"auth-info":be.value},null,8,["auth-id","auth-info"]))])):x("",!0),Ge.value.length>0?(f(),v("div",F,[t[2]||(t[2]=m("div",{class:"auth-switcher-title"}," 其他登录方式 ",-1)),m("div",N,[Ge.value.length>4?(f(),v("button",{key:0,disabled:0===Le.value,class:"auth-nav-btn auth-nav-prev",onClick:Qe},[(f(),v("svg",H,t[0]||(t[0]=[m("use",{"xlink:href":"#icon-chevron-left"},null,-1)])))],8,R)):x("",!0),m("div",K,[m("div",{ref_key:"authMethodsContainer",ref:De,class:j(["auth-methods-container",{"auth-methods-centered":Ge.value.length<=4}]),style:O({transform:"translateX(-".concat(Le.value*Ae.value,"px)")})},[(f(!0),v(C,null,S(Ge.value,(function(e){return f(),v("div",{key:e.id,title:e.name,class:"auth-method-item",onClick:function(t){return Ze(e)}},[m("div",{"data-auth-type":e.type,class:"auth-method-icon"},[(f(),v("svg",J,[m("use",{"xlink:href":"#icon-auth-".concat(e.type)},null,8,B)]))],8,W),m("div",V,b(e.name),1)],8,M)})),128))],6)]),Ge.value.length>4?(f(),v("button",{key:1,disabled:Le.value>=Fe.value,class:"auth-nav-btn auth-nav-next",onClick:Ye},[(f(),v("svg",$,t[1]||(t[1]=[m("use",{"xlink:href":"#icon-chevron-right"},null,-1)])))],8,X)):x("",!0)])])):x("",!0)]))])]),we.value?(f(),w(y(ce),{key:0,"auth-id":ge.value,"auth-info":{uniqKey:je.value,contactType:Se.value,hasContactInfo:Pe.value,qrCode:Te.value,CurrentSecret:_e.value},"auth-methods":Be.value,"last-id":ye.value,"user-name":Oe.value,onCancel:Ve,onVerificationSuccess:Xe},null,8,["auth-id","auth-info","auth-methods","last-id","user-name"])):x("",!0)])}}}))}}}))}();
