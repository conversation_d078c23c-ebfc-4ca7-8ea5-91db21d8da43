/*! 
 Build based on gin-vue-admin 
 Time : 1754879545000 */
!function(){function e(a){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(a)}function a(e,a){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);a&&(n=n.filter((function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable}))),t.push.apply(t,n)}return t}function t(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?a(Object(o),!0).forEach((function(a){n(e,a,o[a])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):a(Object(o)).forEach((function(a){Object.defineProperty(e,a,Object.getOwnPropertyDescriptor(o,a))}))}return e}function n(a,t,n){return(t=function(a){var t=function(a,t){if("object"!=e(a)||!a)return a;var n=a[Symbol.toPrimitive];if(void 0!==n){var o=n.call(a,t||"default");if("object"!=e(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(a)}(a,"string");return"symbol"==e(t)?t:t+""}(t))in a?Object.defineProperty(a,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):a[t]=n,a}function o(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,a,t="function"==typeof Symbol?Symbol:{},n=t.iterator||"@@iterator",i=t.toStringTag||"@@toStringTag";function p(t,n,o,i){var p=n&&n.prototype instanceof c?n:c,d=Object.create(p.prototype);return r(d,"_invoke",function(t,n,o){var r,i,p,c=0,d=o||[],l=!1,u={p:0,n:0,v:e,a:f,f:f.bind(e,4),d:function(a,t){return r=a,i=0,p=e,u.n=t,s}};function f(t,n){for(i=t,p=n,a=0;!l&&c&&!o&&a<d.length;a++){var o,r=d[a],f=u.p,g=r[2];t>3?(o=g===n)&&(p=r[(i=r[4])?5:(i=3,3)],r[4]=r[5]=e):r[0]<=f&&((o=t<2&&f<r[1])?(i=0,u.v=n,u.n=r[1]):f<g&&(o=t<3||r[0]>n||n>g)&&(r[4]=t,r[5]=n,u.n=g,i=0))}if(o||t>1)return s;throw l=!0,n}return function(o,d,g){if(c>1)throw TypeError("Generator is already running");for(l&&1===d&&f(d,g),i=d,p=g;(a=i<2?e:p)||!l;){r||(i?i<3?(i>1&&(u.n=-1),f(i,p)):u.n=p:u.v=p);try{if(c=2,r){if(i||(o="next"),a=r[o]){if(!(a=a.call(r,p)))throw TypeError("iterator result is not an object");if(!a.done)return a;p=a.value,i<2&&(i=0)}else 1===i&&(a=r.return)&&a.call(r),i<2&&(p=TypeError("The iterator does not provide a '"+o+"' method"),i=1);r=e}else if((a=(l=u.n<0)?p:t.call(n,u))!==s)break}catch(a){r=e,i=1,p=a}finally{c=1}}return{value:a,done:l}}}(t,o,i),!0),d}var s={};function c(){}function d(){}function l(){}a=Object.getPrototypeOf;var u=[][n]?a(a([][n]())):(r(a={},n,(function(){return this})),a),f=l.prototype=c.prototype=Object.create(u);function g(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,l):(e.__proto__=l,r(e,i,"GeneratorFunction")),e.prototype=Object.create(f),e}return d.prototype=l,r(f,"constructor",l),r(l,"constructor",d),d.displayName="GeneratorFunction",r(l,i,"GeneratorFunction"),r(f),r(f,i,"Generator"),r(f,n,(function(){return this})),r(f,"toString",(function(){return"[object Generator]"})),(o=function(){return{w:p,m:g}})()}function r(e,a,t,n){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}r=function(e,a,t,n){if(a)o?o(e,a,{value:t,enumerable:!n,configurable:!n,writable:!n}):e[a]=t;else{var i=function(a,t){r(e,a,(function(e){return this._invoke(a,t,e)}))};i("next",0),i("throw",1),i("return",2)}},r(e,a,t,n)}function i(e,a){return function(e){if(Array.isArray(e))return e}(e)||function(e,a){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var n,o,r,i,p=[],s=!0,c=!1;try{if(r=(t=t.call(e)).next,0===a){if(Object(t)!==t)return;s=!1}else for(;!(s=(n=r.call(t)).done)&&(p.push(n.value),p.length!==a);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=t.return&&(i=t.return(),Object(i)!==i))return}finally{if(c)throw o}}return p}}(e,a)||function(e,a){if(e){if("string"==typeof e)return p(e,a);var t={}.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?p(e,a):void 0}}(e,a)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function p(e,a){(null==a||a>e.length)&&(a=e.length);for(var t=0,n=Array(a);t<a;t++)n[t]=e[t];return n}function s(e,a,t,n,o,r,i){try{var p=e[r](i),s=p.value}catch(e){return void t(e)}p.done?a(s):Promise.resolve(s).then(n,o)}function c(e){return function(){var a=this,t=arguments;return new Promise((function(n,o){var r=e.apply(a,t);function i(e){s(r,n,o,i,p,"next",e)}function p(e){s(r,n,o,i,p,"throw",e)}i(void 0)}))}}System.register(["./index-legacy.39a66d41.js"],(function(e,a){"use strict";var n,r,p,s,d,l,u,f,g,m,v,h,b,x,y,w,A,k,_,C,z,j,E,F,P,I,S,O,M,U,L,D,R=document.createElement("style");return R.textContent='@charset "UTF-8";.form-fill-dialog[data-v-99b414e6]{position:fixed;top:0;left:0;right:0;bottom:0;z-index:1000;display:flex;align-items:center;justify-content:center}.form-fill-dialog .dialog-mask[data-v-99b414e6]{position:absolute;top:0;left:0;right:0;bottom:0;background:rgba(0,0,0,.5);z-index:1001}.form-fill-dialog .dialog-wrapper[data-v-99b414e6]{position:relative;z-index:1002}.form-fill-dialog .dialog-wrapper .dialog-container[data-v-99b414e6]{width:500px;background:#ffffff;border-radius:8px;box-shadow:0 4px 20px rgba(0,0,0,.15);overflow:hidden;animation:dialogSlideIn-99b414e6 .3s ease-out}.form-fill-dialog .dialog-header[data-v-99b414e6]{display:flex;align-items:center;justify-content:space-between;padding:20px 24px;border-bottom:1px solid #e9ecef}.form-fill-dialog .dialog-header .dialog-title[data-v-99b414e6]{font-size:16px;font-weight:600;color:#212529}.form-fill-dialog .dialog-header .dialog-close[data-v-99b414e6]{cursor:pointer;color:#6c757d;font-size:16px;transition:color .3s}.form-fill-dialog .dialog-header .dialog-close[data-v-99b414e6]:hover{color:#212529}.form-fill-dialog .dialog-body[data-v-99b414e6]{padding:24px}.form-fill-dialog .dialog-footer[data-v-99b414e6]{display:flex;justify-content:flex-end;gap:12px;padding:16px 24px;border-top:1px solid #e9ecef;background:#f8f9fa}.form-fill-dialog .form-content .form-header[data-v-99b414e6]{margin-bottom:24px}.form-fill-dialog .form-content .form-header .app-info[data-v-99b414e6]{display:flex;align-items:center;gap:12px;padding:16px;background:#f8f9fa;border-radius:8px}.form-fill-dialog .form-content .form-header .app-info .app-icon[data-v-99b414e6]{width:48px;height:48px;border-radius:8px;display:flex;align-items:center;justify-content:center}.form-fill-dialog .form-content .form-header .app-info .app-icon[data-v-99b414e6] .base-avatar{color:#fff;font-size:16px;font-weight:500;border-radius:8px}.form-fill-dialog .form-content .form-header .app-info .app-icon[data-v-99b414e6] .base-avatar.default-avatar{background-color:#f0f2f5!important;color:#909399}.form-fill-dialog .form-content .form-header .app-info .app-details[data-v-99b414e6]{flex:1}.form-fill-dialog .form-content .form-header .app-info .app-details .app-name[data-v-99b414e6]{font-size:16px;font-weight:600;color:#212529;margin-bottom:4px}.form-fill-dialog .form-content .form-header .app-info .app-details .app-desc[data-v-99b414e6]{font-size:14px;color:#6c757d;line-height:1.4}.form-fill-dialog .form-content .form-tip[data-v-99b414e6]{display:flex;align-items:flex-start;gap:8px;padding:12px;background:#e7f4ff;border-radius:6px;margin-top:16px}.form-fill-dialog .form-content .form-tip[data-v-99b414e6] .base-icon{color:#536ce6;margin-top:2px;flex-shrink:0}.form-fill-dialog .form-content .form-tip span[data-v-99b414e6]{font-size:14px;color:#536ce6;line-height:1.4}@keyframes dialogSlideIn-99b414e6{0%{opacity:0;transform:translateY(-50px) scale(.9)}to{opacity:1;transform:translateY(0) scale(1)}}[data-v-99b414e6] .base-input .base-input__inner[type=password]{-webkit-text-security:disc;font-family:Courier New,monospace;letter-spacing:2px}[data-v-99b414e6] .base-input .base-input__inner[type=password]::selection{background:transparent}[data-v-99b414e6] .base-input .base-input__inner[type=password]::-webkit-textfield-decoration-container{display:none}.app-page-root[data-v-d155529e]{flex:1;min-height:0;display:flex;flex-direction:column;overflow:hidden;width:100%;max-width:100%}.person[data-v-d155529e]{flex:1;min-height:0;display:flex;flex-direction:column;background:#FFFFFF;border-radius:4px;box-sizing:border-box;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;width:100%;max-width:100%}.person[data-v-d155529e] .base-header--shadow{box-shadow:none}.person .app-header[data-v-d155529e]{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;-ms-flex-pack:justify;justify-content:space-between;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;background:#ffffff;box-sizing:border-box;-webkit-box-sizing:border-box;-moz-box-sizing:border-box}.person .app-header[data-v-d155529e] .base-input{padding:5px 12px 5px 30px}.person .app-header .header-left .page-title[data-v-d155529e]{margin:0;font-size:16px;font-weight:600;color:#1f2329;line-height:28px}.person .app-header .header-right .search-controls[data-v-d155529e]{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center}.person .app-header .header-right .search-controls .search-icon[data-v-d155529e]{width:14px;height:14px;position:relative;margin-right:-24px;z-index:1000;color:#b3b6c1!important}.person .app-header .header-right .search-controls .search-input[data-v-d155529e]{width:200px;height:28px;margin-right:10px}.person .app-header .header-right .search-controls .search-input[data-v-d155529e] .base-input__wrapper{border-radius:6px;background-color:#f7f8fa;border:1px solid transparent;transition:all .2s;box-sizing:border-box}.person .app-header .header-right .search-controls .search-input[data-v-d155529e] .base-input__wrapper:hover{background-color:#fff;border-color:#d0d7de}.person .app-header .header-right .search-controls .search-input[data-v-d155529e] .base-input__wrapper.is-focus{background-color:#fff;border-color:#536ce6;box-shadow:0 0 0 2px rgba(64,158,255,.1)}.person .app-header .header-right .search-controls .search-input[data-v-d155529e] .base-input__inner{height:36px;line-height:36px;font-size:14px;color:#1f2329}.person .app-header .header-right .search-controls .search-input[data-v-d155529e] .base-input__inner::placeholder{color:#8a919f}.person .app-header .header-right .search-controls .refresh-btn[data-v-d155529e]{width:28px;height:28px;padding:0;margin-right:10px;border-radius:4px;background:#f5f5f7;color:#686e84;display:flex;align-items:center;justify-content:center}.person .app-header .header-right .search-controls .refresh-btn .refresh-btn-icon[data-v-d155529e]{width:14px;height:14px}.person .app-header .header-right .search-controls .refresh-btn[data-v-d155529e]:hover{background:#536ce6;border-color:#d0d7de;color:#fff}.person .app-header .header-right .search-controls .view-select[data-v-d155529e]{width:70px;height:20px}.person .app-header .header-right .search-controls .view-select[data-v-d155529e] .base-select__input{padding:0;border:none}.person .app-header .header-right .search-controls .view-select[data-v-d155529e] .base-select__dropdown{width:88px;padding:7px 7px 3px}.person .app-header .header-right .search-controls .view-select[data-v-d155529e] .base-option{padding:4px 8px;font-size:14px;font-family:PingFang SC,PingFang SC-Regular,Microsoft YaHei,\\5fae\\8f6f\\96c5\\9ed1;background:#f5f5f7;border-radius:4px;margin-bottom:4px}.person .app-header .header-right .search-controls .view-select[data-v-d155529e] .base-option.is-selected{color:#fff;background:#536ce6;border-radius:4px}.person .app-header .header-right .search-controls .view-select[data-v-d155529e] .base-select__wrapper{border-radius:6px;background-color:#f7f8fa;border:1px solid transparent;height:36px}.person .app-header .header-right .search-controls .view-select[data-v-d155529e] .base-select__wrapper:hover{background-color:#fff;border-color:#d0d7de}.person .app-header .search-input[data-v-d155529e]{width:200px;height:28px}.person .app-header .search-input[data-v-d155529e] .el-input__wrapper{border-radius:4px;background-color:#f5f7fa}.person .app-header .search-input[data-v-d155529e] .el-input__wrapper.is-focus{background-color:#fff}.person .app-header .search-input[data-v-d155529e] .el-input__inner{height:32px;line-height:32px;font-size:14px}.person .app-header .search-input[data-v-d155529e] .el-input__inner::placeholder{color:#909399}.person .flex-container[data-v-d155529e]{flex:1;min-height:0;display:flex;height:100%;width:100%;max-width:100%}.person .flex-container.flex-row[data-v-d155529e]{flex-direction:row}.person .category-aside[data-v-d155529e]{flex:0 0 104px;width:104px;height:100%;border-bottom:none;overflow:hidden;min-width:104px}.person .category-aside[data-v-d155529e] .base-menu--vertical{width:100%;height:100%}.person .category-aside .category-menu[data-v-d155529e]{border-right:none;background:transparent;padding:0 8px 8px;height:100%;box-sizing:border-box;overflow-y:auto;overflow-x:hidden}.person .category-aside .category-menu[data-v-d155529e]::-webkit-scrollbar{width:6px}.person .category-aside .category-menu .category-menu-item[data-v-d155529e]{width:88px;height:28px;flex:0 0 auto}.person .category-aside .category-menu[data-v-d155529e] .base-menu-item__content{padding:0;height:28px;line-height:28px;justify-content:center;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;width:100%;box-sizing:border-box;text-align:center}.person .category-aside .category-menu[data-v-d155529e] .base-menu-item{margin:8px 0 8px 8px;font-size:14px;color:#4e5969;border-radius:6px;transition:all .2s ease;cursor:pointer;width:80px;min-height:28px;display:flex;align-items:center;justify-content:center}.person .category-aside .category-menu[data-v-d155529e] .base-menu-item:not(.base-menu-item--active){background-color:transparent;color:#4e5969}.person .category-aside .category-menu[data-v-d155529e] .base-menu-item:hover:not(.base-menu-item--active){background-color:#f5f5f7}.person .category-aside .category-menu[data-v-d155529e] .base-menu-item:hover:not(.base-menu-item--active) .base-menu-item__content{color:#686e84}.person .category-aside .category-menu[data-v-d155529e] .base-menu-item.base-menu-item--active{background-color:#536ce6;color:#fff;font-weight:500}.person .category-aside .category-menu[data-v-d155529e] .base-menu-item:active{background-color:#3370ff}.person .app-main[data-v-d155529e]{flex:1;min-height:0;min-width:0;width:auto;max-width:calc(100% - 104px);height:100%;padding:10px 0 24px 8px;overflow-y:auto;overflow-x:hidden;background:#ffffff;box-sizing:border-box;-webkit-box-sizing:border-box;-moz-box-sizing:border-box}.person .app-main .category-section[data-v-d155529e]{margin-bottom:16px;margin-top:0}.person .app-main .category-section[data-v-d155529e]:last-child{margin-bottom:0}.person .app-main .category-section .apps-grid[data-v-d155529e]{display:-ms-grid;display:grid;gap:8px;padding:0 16px 0 0;box-sizing:border-box;-webkit-box-sizing:border-box;-moz-box-sizing:border-box}.person .app-main .category-section .apps-grid.view-standard[data-v-d155529e]{grid-template-columns:repeat(auto-fit,minmax(210px,1fr));gap:8px;justify-content:start}.person .app-main .category-section .apps-grid.view-standard.apps-grid-limited[data-v-d155529e]{grid-template-columns:repeat(auto-fit,minmax(210px,228px))}.person .app-main .category-section .apps-grid.view-standard .app-item[data-v-d155529e]{width:100%;height:64px;background:#f7f7fa;border:1px solid #f2f2f5;border-radius:4px}.person .app-main .category-section .apps-grid.view-standard .app-item .app-collect-icon[data-v-d155529e]{width:16px;height:15px;position:absolute;color:#b3b6c1!important;display:none;top:-4px;right:20px}.person .app-main .category-section .apps-grid.view-standard .app-item .app-collect-icon.base-icon--yishoucang[data-v-d155529e]{color:#ffbf00!important}.person .app-main .category-section .apps-grid.view-standard .app-item .app-collect-icon.base-icon--shoucang[data-v-d155529e],.person .app-main .category-section .apps-grid.view-standard .app-item .app-collect-icon.base-icon--shoucang[data-v-d155529e]:hover{color:#b3b6c1!important}.person .app-main .category-section .apps-grid.view-standard .app-item .app-form-fill-icon[data-v-d155529e]{width:16px;height:15px;position:absolute;color:#b3b6c1!important;display:none;top:26px;right:20px}.person .app-main .category-section .apps-grid.view-standard .app-item .app-form-fill-icon[data-v-d155529e]:hover{color:#007bff!important}.person .app-main .category-section .apps-grid.view-standard .app-item .app-content[data-v-d155529e]{display:flex;flex-direction:row;text-align:left;height:40px;margin:12px}.person .app-main .category-section .apps-grid.view-standard .app-item .app-content .app-icon[data-v-d155529e]{margin-bottom:0;margin-right:12px;margin-top:6px}.person .app-main .category-section .apps-grid.view-standard .app-item .app-content .app-icon[data-v-d155529e] .avatar-text{font-size:14px}.person .app-main .category-section .apps-grid.view-standard .app-item .app-content .app-details[data-v-d155529e]{display:flex;flex-direction:column;justify-content:center}.person .app-main .category-section .apps-grid.view-standard .app-item .app-content .app-details .app-name[data-v-d155529e]{font-size:14px;line-height:20px;color:#282a33;height:20px;display:flex}.person .app-main .category-section .apps-grid.view-standard .app-item .app-content .app-details .app-name .app-name-text[data-v-d155529e]{min-width:56px;height:20px;max-width:150px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;font-size:14px;font-family:PingFang SC,PingFang SC-Medium,Microsoft YaHei,\\5fae\\8f6f\\96c5\\9ed1;font-weight:500;text-align:left;color:#282a33;line-height:20px;margin-top:-2px}.person .app-main .category-section .apps-grid.view-standard .app-item .app-content .app-details .app-desc[data-v-d155529e]{margin-top:2px;font-size:12px;color:#8a919f;line-height:16px;min-height:16px;text-align:left;cursor:pointer;user-select:none}.person .app-main .category-section .apps-grid.view-standard .app-item .app-content .app-details .app-desc .app-desc-text[data-v-d155529e]{display:inline-block;width:100%;overflow:hidden;font-size:12px;text-overflow:ellipsis;white-space:nowrap;max-width:150px;cursor:pointer;user-select:none}.person .app-main .category-section .apps-grid.view-standard .app-item:hover:not(.disabled) .app-collect-icon[data-v-d155529e]{display:block}.person .app-main .category-section .apps-grid.view-standard .app-item:hover:not(.disabled) .app-form-fill-icon[data-v-d155529e]{display:block}.person .app-main .category-section .apps-grid.view-standard .app-item .app-collect-icon.base-icon--yishoucang[data-v-d155529e]{display:block!important}.person .app-main .category-section .apps-grid.view-compact[data-v-d155529e]{grid-template-columns:repeat(auto-fit,minmax(110px,1fr));gap:8px}.person .app-main .category-section .apps-grid.view-compact.apps-grid-limited[data-v-d155529e]{grid-template-columns:repeat(auto-fit,minmax(110px,120px))}.person .app-main .category-section .apps-grid.view-compact .app-item[data-v-d155529e]{width:100%;height:64px;background:#f7f7fa;border:1px solid #f2f2f5;border-radius:4px}.person .app-main .category-section .apps-grid.view-compact .app-item .app-collect-icon[data-v-d155529e]{width:16px;height:15px;position:absolute;color:#b3b6c1!important;display:none;top:8px;right:8px}.person .app-main .category-section .apps-grid.view-compact .app-item .app-collect-icon.base-icon--yishoucang[data-v-d155529e]{color:#ffbf00!important}.person .app-main .category-section .apps-grid.view-compact .app-item .app-collect-icon.base-icon--shoucang[data-v-d155529e],.person .app-main .category-section .apps-grid.view-compact .app-item .app-collect-icon.base-icon--shoucang[data-v-d155529e]:hover{color:#b3b6c1!important}.person .app-main .category-section .apps-grid.view-compact .app-item .app-form-fill-icon[data-v-d155529e]{width:16px;height:15px;position:absolute;color:#b3b6c1!important;display:none;top:38px;right:8px}.person .app-main .category-section .apps-grid.view-compact .app-item .app-form-fill-icon[data-v-d155529e]:hover{color:#007bff!important}.person .app-main .category-section .apps-grid.view-compact .app-item .app-content[data-v-d155529e]{display:flex;flex-direction:column;align-items:center}.person .app-main .category-section .apps-grid.view-compact .app-item .app-content .app-icon[data-v-d155529e]{margin:10px 0 0;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0}.person .app-main .category-section .apps-grid.view-compact .app-item .app-content .app-icon[data-v-d155529e] .avatar-text{font-size:14px}.person .app-main .category-section .apps-grid.view-compact .app-item .app-content .app-details[data-v-d155529e]{-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;text-align:left}.person .app-main .category-section .apps-grid.view-compact .app-item .app-content .app-details .app-name[data-v-d155529e]{font-size:14px;line-height:20px;font-weight:500;max-width:98px}.person .app-main .category-section .apps-grid.view-compact .app-item:hover:not(.disabled) .app-collect-icon[data-v-d155529e]{display:block}.person .app-main .category-section .apps-grid.view-compact .app-item:hover:not(.disabled) .app-form-fill-icon[data-v-d155529e]{display:block}.person .app-main .category-section .apps-grid.view-compact .app-item .app-collect-icon.base-icon--yishoucang[data-v-d155529e]{display:block!important}.person .app-main .category-section .apps-grid .app-item[data-v-d155529e]{background:#f7f7fa;border:1px solid #f2f2f5;border-radius:8px;position:relative;cursor:pointer;transition:all .2s ease;overflow:hidden}.person .app-main .category-section .apps-grid .app-item .app-collect-icon[data-v-d155529e]{width:16px;height:15px;position:absolute;color:#b3b6c1!important;display:none;transition:color .2s ease}.person .app-main .category-section .apps-grid .app-item .app-collect-icon.base-icon--yishoucang[data-v-d155529e]{color:#ffbf00!important}.person .app-main .category-section .apps-grid .app-item .app-collect-icon.base-icon--shoucang[data-v-d155529e],.person .app-main .category-section .apps-grid .app-item .app-collect-icon.base-icon--shoucang[data-v-d155529e]:hover{color:#b3b6c1!important}.person .app-main .category-section .apps-grid .app-item[data-v-d155529e]:hover:not(.disabled){border-color:#536ce6;box-shadow:0 4px 12px rgba(64,158,255,.15);transform:translateY(-2px)}.person .app-main .category-section .apps-grid .app-item:hover:not(.disabled) .app-collect-icon[data-v-d155529e]{display:block}.person .app-main .category-section .apps-grid .app-item:hover:not(.disabled) .app-form-fill-icon[data-v-d155529e]{display:block}.person .app-main .category-section .apps-grid .app-item .app-content[data-v-d155529e]{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;width:100%;height:100%;box-sizing:border-box;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;position:relative}.person .app-main .category-section .apps-grid .app-item .app-content .app-icon[data-v-d155529e] .base-avatar{color:#fff;font-size:16px;font-weight:500;border-radius:8px}.person .app-main .category-section .apps-grid .app-item .app-content .app-icon[data-v-d155529e] .base-avatar.default-avatar{background-color:#f0f2f5!important;color:#909399}.person .app-main .category-section .apps-grid .app-item .app-content .app-details .app-name[data-v-d155529e]{color:#1f2329;font-weight:500;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.person .app-main .category-section .apps-grid .app-item .status-badge[data-v-d155529e]{position:absolute;top:8px;padding:2px 6px;border-radius:4px;font-size:10px;color:#fff;font-weight:500;z-index:10}.person .app-main .category-section .apps-grid .app-item .status-badge-compact[data-v-d155529e]{top:0px;left:0px}.person .app-main .category-section .apps-grid .app-item .status-badge-compact .status-maint[data-v-d155529e]{width:45px;height:45px}.person .app-main .category-section .apps-grid .app-item .status-badge-inline[data-v-d155529e]{height:18px;line-height:18px;top:-8px;margin-left:10px;border-radius:2px;font-size:12px;background-color:#ededf1;color:#686e84}.person .app-main .category-section .apps-grid .app-item .status-badge-inline .status-maint[data-v-d155529e]{width:44px;height:18px}.person .app-main .category-section .apps-grid .app-item.disabled[data-v-d155529e]{cursor:not-allowed;opacity:.6}.person .app-main .category-section .apps-grid .app-item.disabled[data-v-d155529e]:hover{border-color:#e5e6eb;box-shadow:none;transform:none}.person .app-main .category-section .apps-grid .app-item.disabled .app-content .app-icon[data-v-d155529e] .base-avatar{filter:grayscale(100%)}.person .app-main .category-section .apps-grid .app-item.disabled .app-content .app-details .app-name[data-v-d155529e]{color:#b3b6c1}.app-header[data-v-d155529e] .el-row{display:flex}.app-header .el-recent-access[data-v-d155529e]{display:flex;width:80px;height:20px;font-size:14px;font-family:PingFang SC,PingFang SC-Regular,Microsoft YaHei,\\5fae\\8f6f\\96c5\\9ed1;font-weight:Medium;text-align:left;color:#282a33;line-height:20px;justify-content:center}.app-header .el-recent-access .el-recent-text[data-v-d155529e]{width:56px}.app-header .el-recent-data[data-v-d155529e]{padding-left:16px;height:20px}.app-header .el-recent-item[data-v-d155529e]{margin-top:2px;margin-right:6px;height:25px;padding:4px 0 4px 6px;background:#f5f6fe;border-radius:4px;font-size:12px;font-family:PingFang SC,PingFang SC-Regular,Microsoft YaHei,\\5fae\\8f6f\\96c5\\9ed1;font-weight:Regular;color:#536ce6;cursor:pointer}.app-header .el-recent-icon[data-v-d155529e]{opacity:.6;width:8px;height:8px;margin:8px 6px 8px 5px}.app-header .el-recent-clear[data-v-d155529e]{opacity:.6;margin-top:6px;position:absolute;width:14px;height:14px;right:20px;cursor:pointer}.app-header .el-recent-empty[data-v-d155529e]{color:#b3b6c1;font-size:14px;height:20px}.base-icon--yishoucang[data-v-d155529e]{color:#ffbf00!important}@media screen and (max-width: 1200px){.person .app-header .header-right .search-controls .search-input[data-v-d155529e]{width:200px;height:28px}.person .app-main[data-v-d155529e]{padding:10px 0 16px 8px;flex:1;min-height:0}.person .app-main .apps-grid.view-compact[data-v-d155529e]{grid-template-columns:repeat(auto-fit,minmax(110px,1fr));gap:8px}.person .app-main .apps-grid.view-compact.apps-grid-limited[data-v-d155529e]{grid-template-columns:repeat(auto-fit,minmax(110px,120px))}.person .app-main .apps-grid.view-compact .app-item[data-v-d155529e]{height:64px}.person .app-main .apps-grid.view-compact .app-item .app-content .app-icon[data-v-d155529e]{margin-bottom:8px}.person .app-main .apps-grid.view-compact .app-item .app-content .app-icon[data-v-d155529e] .base-avatar{width:24px;height:24px}.person .app-main .apps-grid.view-compact .app-item .app-content .app-details .app-name[data-v-d155529e]{font-size:12px;line-height:16px;max-width:98px}.person .app-main .apps-grid.view-compact .app-item .app-collect-icon[data-v-d155529e]{width:16px;height:15px;position:absolute;color:#b3b6c1!important;display:none;top:8px;right:8px}.person .app-main .apps-grid.view-compact .app-item .app-collect-icon.base-icon--yishoucang[data-v-d155529e]{color:#ffbf00!important}.person .app-main .apps-grid.view-compact .app-item .app-collect-icon.base-icon--shoucang[data-v-d155529e],.person .app-main .apps-grid.view-compact .app-item .app-collect-icon.base-icon--shoucang[data-v-d155529e]:hover{color:#b3b6c1!important}.person .app-main .apps-grid.view-compact .app-item .app-form-fill-icon[data-v-d155529e]{width:16px;height:15px;position:absolute;color:#b3b6c1!important;display:none;top:8px;right:32px}.person .app-main .apps-grid.view-compact .app-item .app-form-fill-icon[data-v-d155529e]:hover{color:#007bff!important}.person .app-main .apps-grid.view-compact .app-item:hover:not(.disabled) .app-collect-icon[data-v-d155529e]{display:block}.person .app-main .apps-grid.view-compact .app-item:hover:not(.disabled) .app-form-fill-icon[data-v-d155529e]{display:block}.person .app-main .apps-grid.view-compact .app-item .app-collect-icon.base-icon--yishoucang[data-v-d155529e]{display:block!important}}@media screen and (max-width: 768px){.person .app-header[data-v-d155529e]{flex-direction:column;align-items:stretch}.person .app-header .header-left[data-v-d155529e]{margin-bottom:16px;display:none}.person .app-header .header-left .page-title[data-v-d155529e]{font-size:18px}.person .app-header .header-right .search-controls[data-v-d155529e]{justify-content:space-between}.person .app-header .header-right .search-controls .search-input[data-v-d155529e]{flex:1;max-width:none}.person .base-container[data-v-d155529e]{flex-direction:column}.person .base-container .category-aside[data-v-d155529e]{width:100%!important;display:none}.person .base-container .category-aside .category-menu[data-v-d155529e]{display:flex;overflow-x:auto;padding:12px 16px;scrollbar-width:none;-ms-overflow-style:none}.person .base-container .category-aside .category-menu[data-v-d155529e]::-webkit-scrollbar{display:none}.person .base-container .category-aside .category-menu[data-v-d155529e] .base-menu-item{flex-shrink:0;margin:0 4px;white-space:nowrap;min-width:60px;max-width:120px}.person .base-container .category-aside .category-menu[data-v-d155529e] .base-menu-item .category-menu-text{max-width:100px}.person .app-main[data-v-d155529e]{padding:10px 0 16px 8px}.person .app-main .apps-grid.view-standard .app-item[data-v-d155529e]{width:100%;height:64px}.person .app-main .apps-grid.view-standard .app-item .app-content[data-v-d155529e]{padding:8px 4px}.person .app-main .apps-grid.view-standard .app-item .app-content .app-details .app-name[data-v-d155529e]{font-size:11px;line-height:14px}.person .app-main .apps-grid.view-standard .app-item .app-content .app-details .app-desc[data-v-d155529e]{display:none}.person .app-main .apps-grid.view-compact .app-item[data-v-d155529e]{height:56px}.person .app-main .apps-grid.view-compact .app-item .app-content[data-v-d155529e]{padding:8px 12px}.person .app-main .apps-grid.view-compact .app-item .app-content .app-icon[data-v-d155529e]{margin-right:8px}.person .app-main .apps-grid.view-compact .app-item .app-content .app-icon[data-v-d155529e] .base-avatar{width:28px!important;height:28px!important}}.tooltip-content[data-v-d155529e]{width:200px;text-align:center}.web-link[data-v-d155529e]{color:#536ce6;text-decoration:none;word-break:break-all}.el-select__popper[data-v-d155529e]{background:#ffffff;border-radius:4px;box-shadow:0 2px 20px rgba(46,60,128,.1);margin-left:-10px!important;right:15px;top:110px;max-width:88px}.el-select__popper .el-select-dropdown__item[data-v-d155529e]{width:72px;height:28px;border-radius:4px;margin-left:7px;margin-bottom:4px;padding:0 8px;font-size:14px;font-family:PingFang SC,PingFang SC-Regular,Microsoft YaHei,\\5fae\\8f6f\\96c5\\9ed1;line-height:20px;display:flex;align-items:center;background:#f5f5f7!important}.el-select__popper .el-select-dropdown__item.selected[data-v-d155529e]{color:#fff;background:#536ce6!important}.text-center[data-v-d155529e]{text-align:center}.web-link[data-v-d155529e]{color:#536ce6;text-decoration:none}.web-link[data-v-d155529e]:hover{text-decoration:underline}.connected-content[data-v-d155529e]{flex:1;min-height:0;display:flex;flex-direction:column}.disconnected-content[data-v-d155529e]{display:flex;align-items:center;justify-content:center;background-color:#fff}.no-connection-wrapper[data-v-d155529e]{text-align:center;padding:40px}.no-connection-wrapper .no-connection-image[data-v-d155529e]{margin-bottom:24px}.no-connection-wrapper .no-connection-image img[data-v-d155529e]{width:222px;height:120px;opacity:.8}.no-connection-wrapper .no-connection-text h3[data-v-d155529e]{font-size:18px;font-weight:500;color:#333;margin:0 0 8px}.no-connection-wrapper .no-connection-text p[data-v-d155529e]{font-size:14px;color:#666;margin:0}.no-apps-wrapper[data-v-d155529e]{height:100%;display:flex;align-items:center;justify-content:center;padding:40px}.no-apps-content[data-v-d155529e]{text-align:center}.no-apps-content .no-apps-image[data-v-d155529e]{margin-bottom:16px}.no-apps-content .no-apps-image img[data-v-d155529e]{width:222px;height:120px;opacity:.8}.no-apps-content .no-apps-text[data-v-d155529e]{font-size:14px;color:#999;font-weight:400}.el-message{white-space:pre-line!important;line-height:1.5!important;padding:12px 20px!important}\n',document.head.appendChild(R),{setters:[function(e){n=e.D,r=e.r,p=e.N,s=e.c,d=e.v,l=e.h,u=e.a,f=e.b,g=e.d,m=e.t,v=e.j,h=e.w,b=e.n,x=e.O,y=e.l,w=e._,A=e.M,k=e.i,_=e.P,C=e.o,z=e.f,j=e.k,E=e.y,F=e.F,P=e.A,I=e.e,S=e.C,O=e.Q,M=e.R,U=e.K,L=e.S,D=e.T}],execute:function(){var R=""+new URL("fault_compact.3c013a62.png",a.meta.url).href,N=""+new URL("maintenance_compact.52fb32ae.png",a.meta.url).href,W=function(e){return n({url:"/console/v1/form-fill/account",method:"get",params:{app_id:e}})},V={key:0,class:"form-fill-dialog"},B={class:"dialog-wrapper"},q={class:"dialog-container"},Q={class:"dialog-header"},X={class:"dialog-title"},G={class:"dialog-body"},Y={class:"form-content"},H={class:"form-header"},T={class:"app-info"},K={class:"app-icon"},J={class:"app-details"},Z={class:"app-name"},$={class:"app-desc"},ee={class:"form-tip"},ae={class:"dialog-footer"},te=Object.assign({name:"FormFillDialog"},{props:{visible:{type:Boolean,default:!1},appInfo:{type:Object,required:!0,default:function(){return{}}}},emits:["update:visible","success"],setup:function(e,a){var t=a.emit,w=e,_=t,C=r(!1),z=r(),j=r(!1),E=r(!1),F=p({username:"",password:""}),P={username:[{required:!0,message:"请输入账户名称",trigger:"blur"},{min:1,max:100,message:"账户名称长度应在1-100字符之间",trigger:"blur"}],password:[{required:!0,message:"请输入账户密码",trigger:"blur"},{min:1,max:100,message:"账户密码长度应在1-100字符之间",trigger:"blur"}]},I=s((function(){return E.value?"编辑表单代填账户":"创建表单代填账户"}));d((function(){return w.visible}),(function(e){C.value=e,e&&S()}),{immediate:!0}),d(C,(function(e){e||(_("update:visible",!1),U())}));var S=function(){var e=c(o().m((function e(){var a,t,n,r,p,s,c,d;return o().w((function(e){for(;;)switch(e.n){case 0:if(w.appInfo.id){e.n=1;break}return e.a(2);case 1:return j.value=!0,e.p=2,a=String(w.appInfo.id),e.n=3,W(a);case 3:(t=e.v).data&&"0"===t.data.errcode&&t.data.credentials?(n=atob(t.data.credentials),r=n.split(":"),p=i(r,2),s=p[0],c=p[1],F.username=s,F.password=c,E.value=!0):(E.value=!1,F.username="",F.password=""),e.n=5;break;case 4:e.p=4,d=e.v,console.error("加载表单代填账户失败:",d),A.error("加载账户信息失败");case 5:return e.p=5,j.value=!1,e.f(5);case 6:return e.a(2)}}),e,null,[[2,4,5,6]])})));return function(){return e.apply(this,arguments)}}(),O=function(){var e=c(o().m((function e(){var a,t,r,i,p,s;return o().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,null===(a=z.value)||void 0===a?void 0:a.validate();case 1:if(e.v){e.n=2;break}return e.a(2);case 2:return j.value=!0,e.p=3,t=btoa("".concat(F.username,":").concat(F.password)),r={app_id:String(w.appInfo.id),credentials:t},e.n=4,n({url:"/console/v1/form-fill/account",method:"put",data:r});case 4:200===(i=e.v).status||"0"===i.data.errcode?(A.success(E.value?"账户信息已更新":"账户信息已创建"),_("success"),M()):A.error((null===(p=i.data)||void 0===p?void 0:p.message)||"操作失败"),e.n=6;break;case 5:e.p=5,s=e.v,console.error("提交表单失败:",s),A.error("操作失败，请重试");case 6:return e.p=6,j.value=!1,e.f(6);case 7:return e.a(2)}}),e,null,[[3,5,6,7]])})));return function(){return e.apply(this,arguments)}}(),M=function(){C.value=!1},U=function(){var e;F.username="",F.password="",E.value=!1,null===(e=z.value)||void 0===e||e.resetFields()},L=function(e){for(var a=["#71BDDF","#8AB05D","#9571DF","#DF7171","#DFC271","#71DFA7","#B05D8A","#5D8AB0"],t=0,n=0;n<e.length;n++)t+=e.charCodeAt(n);return a[t%a.length]};return function(a,t){var n=l("base-icon"),o=l("base-avatar"),r=l("base-input"),i=l("base-form-item"),p=l("base-form"),s=l("base-button");return C.value?(u(),f("div",V,[g("div",{class:"dialog-mask",onClick:M}),g("div",B,[g("div",q,[g("div",Q,[g("span",X,m(I.value),1),v(n,{class:"dialog-close",name:"close",onClick:M})]),g("div",G,[g("div",Y,[g("div",H,[g("div",T,[g("div",K,[v(o,{shape:"square",size:36,src:e.appInfo.iconError?"":e.appInfo.icon,style:b(!e.appInfo.icon||e.appInfo.iconError?"background-color: ".concat(L(e.appInfo.app_name)," !important"):"background-color: #f7f7fa !important"),onError:t[0]||(t[0]=function(){e.appInfo.iconError=!0})},{default:h((function(){return[k(m(!e.appInfo.icon||e.appInfo.iconError?e.appInfo.app_name.slice(0,1):""),1)]})),_:1},8,["src","style"])]),g("div",J,[g("div",Z,m(e.appInfo.app_name),1),g("div",$,m(e.appInfo.app_desc||"设置表单代填账户信息"),1)])])]),v(p,{ref_key:"formRef",ref:z,model:F,rules:P,"label-width":"80px",onSubmit:x(O,["prevent"])},{default:h((function(){return[v(i,{label:"账户名称",prop:"username"},{default:h((function(){return[v(r,{modelValue:F.username,"onUpdate:modelValue":t[1]||(t[1]=function(e){return F.username=e}),placeholder:"请输入账户名称",maxlength:100,clearable:""},null,8,["modelValue"])]})),_:1}),v(i,{label:"账户密码",prop:"password"},{default:h((function(){return[v(r,{modelValue:F.password,"onUpdate:modelValue":t[2]||(t[2]=function(e){return F.password=e}),type:"password",placeholder:"请输入账户密码",maxlength:100,clearable:"","show-password":""},null,8,["modelValue"])]})),_:1}),g("div",ee,[v(n,{name:"info",color:"#536ce6"}),t[3]||(t[3]=g("span",null,"此账户信息将用于应用的自动登录，请确保账户信息准确无误。",-1))])]})),_:1},8,["model"])])]),g("div",ae,[v(s,{onClick:M},{default:h((function(){return t[4]||(t[4]=[k("取消")])})),_:1,__:[4]}),v(s,{type:"primary",loading:j.value,onClick:O},{default:h((function(){return[k(m(E.value?"保存":"创建"),1)]})),_:1},8,["loading"])])])])])):y("",!0)}}}),ne=w(te,[["__scopeId","data-v-99b414e6"]]),oe=""+new URL("no_power.31f14e62.png",a.meta.url).href,re=""+new URL("no_result.d0219d8d.png",a.meta.url).href,ie={class:"app-page-root"},pe={class:"person"},se={class:"header-right"},ce={class:"search-controls"},de={class:"el-row"},le={class:"el-recent-data"},ue=["onClick"],fe={key:0,class:"el-recent-empty"},ge={key:1,class:"connected-content"},me={class:"category-menu-text"},ve={key:0,class:"loading-wrapper"},he={key:1},be=["onClick"],xe={key:0,class:"status-badge status-badge-compact"},ye={key:1,class:"status-badge status-badge-compact"},we={class:"app-content"},Ae={class:"app-icon"},ke={class:"tooltip-content"},_e={key:0},Ce={key:1},ze={key:2},je={class:"app-details"},Ee=["title"],Fe={class:"app-name-text"},Pe={key:0,class:"status-badge-inline"},Ie={key:1,class:"status-badge-inline"},Se={key:0,class:"app-desc"},Oe={class:"app-desc-text"},Me={key:2,class:"no-apps-wrapper"},Ue={class:"no-apps-content"},Le={class:"no-apps-image"},De=["src"],Re={class:"no-apps-text"},Ne={key:2,class:"disconnected-content"},We={class:"no-connection-wrapper"},Ve={class:"no-connection-image"},Be=["src"],qe={name:"AppPage",props:{isConnected:{type:Boolean,default:!1}}},Qe=Object.assign(qe,{props:{isConnected:{type:Boolean,default:!1}},setup:function(e){var a=e,i=s((function(){return!S.isClient()||(!(!ca.token||!ca.token.accessToken)||a.isConnected)})),w=s((function(){return!(!H.value&&T.value)||!(!Q.value||0===Q.value.length)&&Q.value.some((function(e){return e.apps&&e.apps.length>0}))})),V=r(""),B=r(null),q=r([]),Q=r([]),X=r("0"),G=r("standard"),Y=p([{key:"standard",label:"标准视图"},{key:"compact",label:"紧凑视图"}]),H=r(!1),T=r(!1),K=r([]),J=r([]),Z=r(!1),$=r(null),ee=function(e){var a,t,n=(null===(a=ca.userInfo)||void 0===a?void 0:a.id)||(null===(t=ca.userInfo)||void 0===t?void 0:t.name)||"anonymous";return"".concat(e,"_").concat(n)},ae="app_favorites",te="app_recent",qe=function(e){A({message:e,type:arguments.length>1&&void 0!==arguments[1]?arguments[1]:"success",duration:arguments.length>2&&void 0!==arguments[2]?arguments[2]:3e3})},Qe=function(e){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];try{var t=ee(e),n=localStorage.getItem(t);return n?JSON.parse(n):a}catch(o){return console.error("加载".concat(e,"失败:"),o),a}},Xe=function(e,a){try{var t=ee(e);localStorage.setItem(t,JSON.stringify(a))}catch(n){console.error("保存".concat(e,"失败:"),n)}},Ge=function(e){return K.value.some((function(a){return a.id===e.id}))},Ye=function(e){var a=J.value.findIndex((function(a){return a.id===e.id}));a>-1&&J.value.splice(a,1),J.value.unshift({id:e.id,app_name:e.app_name,app_desc:e.app_desc,icon:e.icon,WebUrl:e.WebUrl,maint:e.maint,app_type:e.app_type,open_config:e.open_config,health_status:e.health_status,form_fill_enabled:e.form_fill_enabled,accessTime:Date.now()}),J.value.length>8&&(J.value=J.value.slice(0,8)),Xe(te,J.value)},He=function(){J.value=[],Xe(te,[]),qe("已清空最近访问","info")},Te=function(e){1===e.form_fill_enabled?($.value=e,Z.value=!0):qe("该应用未启用表单代填功能","warning")},Ke=function(){console.log("表单代填设置成功")},Je=function(){var e=c(o().m((function e(a){var t,n;return o().w((function(e){for(;;)switch(e.n){case 0:return e.p=0,e.n=1,D(a);case 1:if(t=e.v,logger.log("应用启动响应:",t),!(t&&t.startsWith&&t.startsWith("Ok"))){e.n=2;break}return e.a(2,Promise.resolve());case 2:if(!(t&&t.startsWith&&t.startsWith("Failed"))){e.n=3;break}return qe(t,"error"),e.a(2,Promise.reject(new Error(t)));case 3:return e.a(2,Promise.resolve());case 4:e.n=6;break;case 5:return e.p=5,n=e.v,logger.log("应用启动失败:",n),e.a(2,Promise.reject(n));case 6:return e.a(2)}}),e,null,[[0,5]])})));return function(a){return e.apply(this,arguments)}}(),Ze=function(){var e=c(o().m((function e(a){var t,n,r;return o().w((function(e){for(;;)switch(e.n){case 0:if(!(!a.WebUrl&&"portal"!==a.app_type||!a.WebUrl&&"portal"===a.app_type&&!1===a.open_config.enabled||a.maint)){e.n=1;break}return e.a(2);case 1:if("portal"!==a.app_type||!0!==a.open_config.enabled){e.n=2;break}if(a.WebUrl="",a.form_fill_enabled=0,S.isClient()){e.n=2;break}return qe("请在客户端访问","warning"),e.a(2);case 2:if(1!==a.form_fill_enabled){e.n=7;break}return e.p=3,e.n=4,W(String(a.id));case 4:if((t=e.v).data&&"0"===t.data.errcode&&t.data.credentials){e.n=5;break}return qe("请先设置表单代填账户信息","warning"),Te(a),e.a(2);case 5:e.n=7;break;case 6:return e.p=6,e.v,qe("请先设置表单代填账户信息","warning"),Te(a),e.a(2);case 7:if(Ye(a),!a.WebUrl.toLowerCase().startsWith("cs:")){e.n=12;break}return n=a.WebUrl.substring(3),e.p=8,qe("正在启动爱尔企业浏览器...","info"),e.n=9,Je(n);case 9:qe("启动成功","success"),e.n=11;break;case 10:e.p=10,e.v,qe("启动企业浏览器失败：\n      检查是否已安装企业浏览器，\n      如仍然无法启动，请手动运行企业浏览器访问该应用！","warning",8e3);case 11:e.n=16;break;case 12:if(!S.isClient()){e.n=15;break}return e.n=13,$e(a);case 13:return r=e.v,e.n=14,S.openResource(r);case 14:e.n=16;break;case 15:window.open(a.WebUrl,"_blank");case 16:return e.a(2)}}),e,null,[[8,10],[3,6]])})));return function(a){return e.apply(this,arguments)}}(),$e=function(){var e=c(o().m((function e(a){var t,n,r,i,p,s,c,d,l;return o().w((function(e){for(;;)switch(e.n){case 0:if(t={Type:"URL",Data:{URL:a.WebUrl,OpenExplorer:[]}},!a.open_config.enabled){e.n=15;break}if("browser"!==a.open_config.open_type){e.n=1;break}for(n=[],r=0;r<a.open_config.browser_configs.length;r++)n.push({ExplorerName:a.open_config.browser_configs[r].type,ExplorerParam:a.open_config.browser_configs[r].params});t.Data.OpenExplorer=n,e.n=15;break;case 1:if(t.Type="APP","client"!==a.open_config.open_type){e.n=8;break}i={Windows:{AppName:"",AppPath:"",AppParam:"",NotFoundMsg:""},MacOS:{AppName:"",AppPath:"",AppParam:"",NotFoundMsg:""},Linux:{AppName:"",AppPath:"",AppParam:"",NotFoundMsg:""}},p=0;case 2:if(!(p<a.open_config.program_configs.length)){e.n=7;break}d=a.open_config.program_configs[p].os,e.n="windows"===d?3:"macos"===d?4:"linux"===d?5:6;break;case 3:return i.Windows.AppName=a.open_config.program_configs[p].name,i.Windows.AppPath=a.open_config.program_configs[p].path,i.Windows.AppParam=a.open_config.program_configs[p].params,i.Windows.NotFoundMsg=a.open_config.program_configs[p].notFoundMessage,e.a(3,6);case 4:return i.MacOS.AppName=a.open_config.program_configs[p].bundleId,i.MacOS.AppParam=a.open_config.program_configs[p].params,i.MacOS.NotFoundMsg=a.open_config.program_configs[p].notFoundMessage,e.a(3,6);case 5:return i.Linux.AppName=a.open_config.program_configs[p].name,i.Linux.AppPath=a.open_config.program_configs[p].path,i.Linux.AppParam=a.open_config.program_configs[p].params,i.Linux.NotFoundMsg=a.open_config.program_configs[p].notFoundMessage,e.a(3,6);case 6:p++,e.n=2;break;case 7:t.Data=i,e.n=15;break;case 8:if("system"!==a.open_config.open_type){e.n=15;break}s={Windows:{AppName:"",AppPath:""},MacOS:{AppName:"",AppPath:""},Linux:{AppName:"",AppPath:""}},c=0;case 9:if(!(c<a.open_config.system_app_configs.length)){e.n=14;break}l=a.open_config.system_app_configs[c].os,e.n="windows"===l?10:"macos"===l?11:"linux"===l?12:13;break;case 10:return s.Windows.AppName=a.open_config.system_app_configs[c].type,s.Windows.AppPath=a.open_config.system_app_configs[c].type,e.a(3,13);case 11:return s.MacOS.AppName=a.open_config.system_app_configs[c].type,s.MacOS.AppPath=a.open_config.system_app_configs[c].type,e.a(3,13);case 12:return s.Linux.AppName=a.open_config.system_app_configs[c].type,s.Linux.AppPath=a.open_config.system_app_configs[c].type,e.a(3,13);case 13:c++,e.n=9;break;case 14:t.Data=s;case 15:return e.a(2,t)}}),e)})));return function(a){return e.apply(this,arguments)}}(),ea=function(){var e=c(o().m((function e(){return o().w((function(e){for(;;)switch(e.n){case 0:if(!S.isClient()){e.n=2;break}return e.n=1,M(ca);case 1:logger.log("客户端模式：发送隧道状态刷新事件"),U.emit("refreshTunnelStatus",{timestamp:Date.now(),source:"app-refresh-btn"});case 2:return e.n=3,pa();case 3:return e.a(2)}}),e)})));return function(){return e.apply(this,arguments)}}();_((function(){}));var aa=function(e){for(var a=["#71BDDF","#8AB05D","#9571DF","#DF7171","#DFC271","#71DFA7","#B05D8A","#5D8AB0"],t=0,n=0;n<e.length;n++)t+=e.charCodeAt(n);return a[t%a.length]},ta=function(){var e=c(o().m((function e(a){return o().w((function(e){for(;;)switch(e.n){case 0:return a.iconError=!0,e.n=1,L();case 1:return e.a(2)}}),e)})));return function(a){return e.apply(this,arguments)}}(),na=s((function(){return-1===B.value})),oa=function(){if(-1===B.value)K.value.length>0?Q.value=[{id:-1,name:"我的收藏",apps:K.value}]:Q.value=[{id:-1,name:"我的收藏",apps:[]}];else if(null===B.value||0===B.value){var e=[],a=new Set;q.value.forEach((function(t){t.apps&&t.apps.length>0&&t.apps.forEach((function(t){a.has(t.id)||(a.add(t.id),e.push(t))}))})),Q.value=[{id:0,name:"全部应用",apps:e}]}else{var t=q.value.filter((function(e){return e.id===B.value}));if(0===t.length)return console.warn("当前选中的分类不存在，回退到全部分类"),B.value=null,X.value="0",void oa();Q.value=t}},ra=function(e){-1===e?(B.value=-1,X.value="-1"):null===e||0===e?(B.value=null,X.value="0"):(B.value=parseInt(e),X.value=e.toString()),oa()},ia=function(){if(V.value){var e=V.value.toLowerCase().trim();if(-1===B.value){var a=K.value.filter((function(a){return a.app_name.toLowerCase().includes(e)}));Q.value=[{id:-1,name:"我的收藏",apps:a}]}else if(null===B.value||0===B.value){var n=[],o=new Set;q.value.forEach((function(a){a.apps&&a.apps.length>0&&a.apps.filter((function(a){return a.app_name.toLowerCase().includes(e)})).forEach((function(e){o.has(e.id)||(o.add(e.id),n.push(e))}))})),Q.value=[{id:0,name:"全部应用",apps:n}]}else{var r=q.value.filter((function(e){return e.id===B.value}));Q.value=r.map((function(a){return t(t({},a),{},{apps:a.apps.filter((function(a){return a.app_name.toLowerCase().includes(e)}))})})).filter((function(e){return e.apps.length>0}))}}else oa()},pa=function(){var e=c(o().m((function e(){var a,t,r,i;return o().w((function(e){for(;;)switch(e.n){case 0:return H.value=!0,e.p=1,e.n=2,n({url:"/console/v1/application/getuserapp",method:"get"});case 2:a=e.v,t=a.data,logger.log("API返回数据:",t),0===t.code&&t.data?(r=t.data.map((function(e,a){return{id:a+1,name:e.category,apps:e.apps.map((function(e){return{id:e.id,app_name:e.portal_show_name?e.portal_show_name:e.app_name,app_desc:e.portal_desc?e.portal_desc:"web"===e.app_type?"Web应用":"tun"===e.app_type?"隧道应用":"应用程序",icon:e.icon,maint:2===e.app_status,app_type:e.app_type,app_sites:e.app_sites,WebUrl:e.WebUrl,form_fill_enabled:e.form_fill_enabled,open_config:e.open_config,health_status:e.health_status}}))}})),logger.log("格式化后的数据:",r),q.value=r,null!==B.value&&-1!==B.value&&0!==B.value&&(r.some((function(e){return e.id===B.value}))||(console.warn("当前选中的分类不存在于新数据中，重置为全部分类"),B.value=null,X.value="0")),oa()):0===t.code&&null===t.data&&(q.value=[],oa()),e.n=4;break;case 3:e.p=3,i=e.v,console.error("API调用出错:",i);case 4:return e.p=4,H.value=!1,T.value=!0,e.f(4);case 5:return e.a(2)}}),e,null,[[1,3,4,5]])})));return function(){return e.apply(this,arguments)}}(),sa=function(){var e,a,t,n;null!==(e=ca.userInfo)&&void 0!==e&&e.id||null!==(a=ca.userInfo)&&void 0!==a&&a.name?(K.value=Qe(ae,[]),J.value=Qe(te,[]),logger.log("加载用户收藏应用:",K.value.length,"个"),logger.log("加载用户最近访问:",J.value.length,"个"),logger.log("当前用户ID:",(null===(t=ca.userInfo)||void 0===t?void 0:t.id)||(null===(n=ca.userInfo)||void 0===n?void 0:n.name))):console.warn("用户信息未加载，跳过存储数据初始化")};C(c(o().m((function e(){var a;return o().w((function(e){for(;;)switch(e.n){case 0:return(a=localStorage.getItem("appViewType"))&&["standard","compact"].includes(a)&&(G.value=a),B.value=null,X.value="0",e.n=1,pa();case 1:sa();case 2:return e.a(2)}}),e)}))));var ca=z();d((function(){return G.value}),(function(e){localStorage.setItem("appViewType",e)})),d((function(){var e;return null===(e=ca.userInfo)||void 0===e?void 0:e.id}),(function(e,a){e&&e!==a&&(logger.log("用户切换，重新加载存储数据:",{oldUserId:a,newUserId:e}),sa(),oa())}),{immediate:!1}),d((function(){return ca.token}),(function(e,a){a&&!e&&(logger.log("用户注销，清理存储数据"),K.value=[],J.value=[],oa())}),{immediate:!1});var da=function(e){var a=e.apps?e.apps.length:0;if("compact"===G.value){if(a<=5)return"apps-grid-limited"}else if(a<=2)return"apps-grid-limited";return""};return function(e,a){var t=l("base-icon"),n=l("base-input"),o=l("base-button"),r=l("base-option"),p=l("base-select"),s=l("base-header"),c=l("base-menu-item"),d=l("base-tooltip"),A=l("base-menu"),_=l("base-aside"),C=l("base-avatar"),z=l("base-main"),S=l("base-container");return u(),f("div",ie,[g("div",pe,[v(s,{class:"app-header",height:"44px",padding:"0 16px"},{default:h((function(){return[a[6]||(a[6]=g("div",{class:"header-left"},[g("h1",{class:"page-title"},"应用门户")],-1)),g("div",se,[g("div",ce,[v(t,{class:"search-icon",name:"search"}),v(n,{modelValue:V.value,"onUpdate:modelValue":a[0]||(a[0]=function(e){return V.value=e}),class:"search-input",clearable:"",placeholder:"搜索应用","prefix-icon":"Search",onInput:ia},null,8,["modelValue"]),v(o,{class:"refresh-btn",icon:"Refresh",size:"small",onClick:ea},{default:h((function(){return a[5]||(a[5]=[g("svg",{"aria-hidden":"true",class:"icon refresh-btn-icon"},[g("use",{"xlink:href":"#icon-search"})],-1)])})),_:1,__:[5]}),v(p,{modelValue:G.value,"onUpdate:modelValue":a[1]||(a[1]=function(e){return G.value=e}),class:"view-select",size:"small"},{default:h((function(){return[(u(!0),f(F,null,P(Y,(function(e){return u(),j(r,{key:e.key,label:e.label,value:e.key},null,8,["label","value"])})),128))]})),_:1},8,["modelValue"])])])]})),_:1,__:[6]}),i.value?(u(),j(s,{key:0,class:"app-header",height:"36px",padding:"12px 16px 8px 16px"},{default:h((function(){return[g("div",de,[a[8]||(a[8]=g("span",{class:"el-recent-access"},[g("span",{class:"el-recent-text"},"最近访问")],-1)),g("span",le,[(u(!0),f(F,null,P(J.value,(function(e,a){return u(),f("span",{key:e.id,class:"el-recent-item",onClick:function(a){return Ze(e)}},[k(m(e.app_name)+" ",1),v(t,{class:"el-recent-icon",name:"close",size:"8px",onClick:x((function(e){return function(e){var a=J.value.splice(e,1)[0];a&&(qe("已从最近访问中移除 ".concat(a.app_name),"info"),Xe(te,J.value))}(a)}),["stop"])},null,8,["onClick"])],8,ue)})),128)),0===J.value.length?(u(),f("span",fe," 暂无最近访问记录 ")):y("",!0),J.value.length>0?(u(),f("svg",{key:1,"aria-hidden":"true",class:"icon el-recent-clear",title:"清空最近访问",onClick:He},a[7]||(a[7]=[g("use",{"xlink:href":"#icon-qingkong"},null,-1)]))):y("",!0)])])]})),_:1})):y("",!0),i.value?(u(),f("div",ge,[v(S,{class:"flex-container flex-row"},{default:h((function(){return[v(_,{class:"category-aside",width:"104px"},{default:h((function(){return[v(A,{"default-active":X.value,"background-color":"#ffffff",class:"category-menu",mode:"vertical",onSelect:ra},{default:h((function(){return[v(c,{class:"category-menu-item",index:"-1",onClick:a[2]||(a[2]=function(e){return ra(-1)})},{default:h((function(){return a[9]||(a[9]=[k(" 收藏 ")])})),_:1,__:[9]}),v(c,{class:"category-menu-item",index:"0",onClick:a[3]||(a[3]=function(e){return ra(null)})},{default:h((function(){return a[10]||(a[10]=[k(" 全部 ")])})),_:1,__:[10]}),(u(!0),f(F,null,P(q.value,(function(e){return u(),j(c,{key:e.id,index:e.id.toString()},{default:h((function(){return[v(d,{content:e.name,disabled:e.name.length<=5,effect:"light",placement:"right"},{default:h((function(){return[g("span",me,m((a=e.name,a.length<=5?a:a.substring(0,4)+"...")),1)];var a})),_:2},1032,["content","disabled"])]})),_:2},1032,["index"])})),128))]})),_:1},8,["default-active"])]})),_:1}),v(z,{class:"app-main custom-scrollbar"},{default:h((function(){return[H.value?(u(),f("div",ve)):w.value?(u(),f("div",he,[(u(!0),f(F,null,P(Q.value,(function(e){return u(),f("div",{key:e.id,class:"category-section"},[g("div",{class:I([["view-".concat(G.value),da(e),{"favorite-category":na.value}],"apps-grid"])},[(u(!0),f(F,null,P(e.apps,(function(e){return u(),f("div",{key:e.id,class:I([{disabled:!e.WebUrl&&"portal"!==e.app_type||!e.WebUrl&&"portal"===e.app_type&&!1===e.open_config.enabled||e.maint},"app-item transition transform-hover"]),onClick:function(a){return Ze(e)}},[2===e.health_status&&"compact"===G.value?(u(),f("div",xe,a[11]||(a[11]=[g("img",{alt:"故障",class:"status-maint",src:R},null,-1)]))):e.maint&&"compact"===G.value?(u(),f("div",ye,a[12]||(a[12]=[g("img",{alt:"维护中",class:"status-maint",src:N},null,-1)]))):y("",!0),g("div",we,[g("div",Ae,[v(d,{effect:"light",placement:"bottom"},{content:h((function(){return[g("div",ke,[e.WebUrl?(u(),f("span",_e,m(e.WebUrl),1)):"portal"===e.app_type&&e.open_config.enabled?(u(),f("span",Ce,"打开方式："+m("browser"===e.open_config.open_type?"浏览器":"client"===e.open_config.open_type?"指定程序":"系统应用"),1)):(u(),f("span",ze,"暂无访问地址"))])]})),default:h((function(){return[v(C,{size:"compact"===G.value?20:28,src:e.iconError?"":(a=e.icon,a?/^(https?:)?\/\//.test(a)||a.startsWith("data:")||a.startsWith("blob:")||a.endsWith(".svg")?a:O()+a:""),style:b(!e.icon||e.iconError?"background-color: ".concat(aa(e.app_name)," !important"):"background-color: #f7f7fa !important"),shape:"square",onError:function(){return ta(e)}},{default:h((function(){return[k(m(!e.icon||e.iconError?e.app_name.slice(0,1):""),1)]})),_:2},1032,["size","src","style","onError"])];var a})),_:2},1024)]),g("div",je,[g("div",{title:e.app_name,class:"app-name"},[g("span",Fe,m(e.app_name),1),2===e.health_status&&"standard"===G.value?(u(),f("span",Pe,a[13]||(a[13]=[g("img",{alt:"故障",class:"status-maint",src:"data:image/png;base64,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"},null,-1)]))):e.maint&&"standard"===G.value?(u(),f("span",Ie,a[14]||(a[14]=[g("img",{alt:"维护中",class:"status-maint",src:"data:image/png;base64,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"},null,-1)]))):y("",!0)],8,Ee),"standard"===G.value?(u(),f("div",Se,[v(d,{content:e.app_desc||"应用程序",disabled:!(e.app_desc&&e.app_desc.length>8),effect:"light",placement:"bottom"},{default:h((function(){return[g("span",Oe,m(e.app_desc||"应用程序"),1)]})),_:2},1032,["content","disabled"])])):y("",!0)]),v(t,{name:Ge(e)?"yishoucang":"shoucang",title:Ge(e)?"取消收藏":"收藏",class:"app-collect-icon",onClick:x((function(a){return t=e,(n=K.value.findIndex((function(e){return e.id===t.id})))>-1?(K.value.splice(n,1),qe("已取消收藏 ".concat(t.app_name),"info")):(K.value.push({id:t.id,app_name:t.app_name,app_desc:t.app_desc,icon:t.icon,WebUrl:t.WebUrl,maint:t.maint,app_type:t.app_type,open_config:t.open_config,health_status:t.health_status,form_fill_enabled:t.form_fill_enabled,favoriteTime:Date.now()}),qe("已收藏 ".concat(t.app_name),"success")),Xe(ae,K.value),void oa();var t,n}),["stop"])},null,8,["name","title","onClick"]),1===e.form_fill_enabled||na.value&&0!==e.form_fill_enabled?(u(),j(t,{key:0,class:"app-form-fill-icon",name:"bianji",title:"编辑表单代填",onClick:x((function(a){return Te(e)}),["stop"])},null,8,["onClick"])):y("",!0)])],10,be)})),128))],2)])})),128))])):(u(),f("div",Me,[g("div",Ue,[g("div",Le,[g("img",{src:E(re),alt:"暂无搜索结果"},null,8,De)]),g("div",Re,m(V.value?"暂无搜索结果":"暂无应用数据，请添加应用"),1)])]))]})),_:1})]})),_:1})])):(u(),f("div",Ne,[g("div",We,[g("div",Ve,[g("img",{src:E(oe),alt:"请先建立安全连接"},null,8,Be)]),a[15]||(a[15]=g("div",{class:"no-connection-text"},[g("h3",{style:{"font-size":"16px"}},"请先建立安全连接"),g("p",{style:{"font-size":"12px"}},"成功连接后可查看授权的应用")],-1))])]))]),v(ne,{"app-info":$.value,visible:Z.value,onSuccess:Ke,"onUpdate:visible":a[4]||(a[4]=function(e){return Z.value=e})},null,8,["app-info","visible"])])}}});e("default",w(Qe,[["__scopeId","data-v-d155529e"]]))}}}))}();
