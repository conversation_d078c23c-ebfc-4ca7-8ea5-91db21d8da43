/*! 
 Build based on gin-vue-admin 
 Time : 1754836337000 */
import e from"./menuItem.a3a28ae4.js";import t from"./asyncSubmenu.a7112638.js";import{c as o,h as n,a as r,k as s,w as a,b as l,F as u,A as i,l as f,z as m}from"./index.57c3624b.js";const c=Object.assign({name:"AsideComponent"},{props:{routerInfo:{type:Object,default:()=>null},isCollapse:{default:function(){return!1},type:Boolean},theme:{default:function(){return{}},type:Object}},setup(c){const d=c,h=o((()=>d.routerInfo.children&&d.routerInfo.children.filter((e=>!e.hidden)).length?t:e));return(e,t)=>{const o=n("AsideComponent");return c.routerInfo.hidden?f("",!0):(r(),s(m(h.value),{key:0,"is-collapse":c.isCollapse,theme:c.theme,"router-info":c.routerInfo},{default:a((()=>[c.routerInfo.children&&c.routerInfo.children.length?(r(!0),l(u,{key:0},i(c.routerInfo.children,(e=>(r(),s(o,{key:e.name,"is-collapse":!1,"router-info":e,theme:c.theme},null,8,["router-info","theme"])))),128)):f("",!0)])),_:1},8,["is-collapse","theme","router-info"]))}}});export{c as default};
