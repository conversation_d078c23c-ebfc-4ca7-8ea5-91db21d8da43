/*! 
 Build based on gin-vue-admin 
 Time : 1754834760000 */
import{_ as e,u as n,r as o,o as i,h as t,a as r,b as a,d as s,i as c,t as l,j as u,w as d,l as p}from"./index.0f69a27d.js";const f="function"==typeof Buffer,g="function"==typeof TextDecoder?new TextDecoder:void 0,v=("function"==typeof TextEncoder&&new TextEncoder,Array.prototype.slice.call("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=")),m=(()=>{let e={};return v.forEach(((n,o)=>e[n]=o)),e})(),h=/^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/,I=String.fromCharCode.bind(String),A="function"==typeof Uint8Array.from?Uint8Array.from.bind(Uint8Array):e=>new Uint8Array(Array.prototype.slice.call(e,0)),k=e=>e.replace(/[^A-Za-z0-9\+\/]/g,""),b=e=>{let n,o,i,t,r="";const a=e.length%3;for(let s=0;s<e.length;){if((o=e.charCodeAt(s++))>255||(i=e.charCodeAt(s++))>255||(t=e.charCodeAt(s++))>255)throw new TypeError("invalid character found");n=o<<16|i<<8|t,r+=v[n>>18&63]+v[n>>12&63]+v[n>>6&63]+v[63&n]}return a?r.slice(0,a-3)+"===".substring(a):r},P=/[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g,w=e=>{switch(e.length){case 4:var n=((7&e.charCodeAt(0))<<18|(63&e.charCodeAt(1))<<12|(63&e.charCodeAt(2))<<6|63&e.charCodeAt(3))-65536;return I((n>>>10)+55296)+I(56320+(1023&n));case 3:return I((15&e.charCodeAt(0))<<12|(63&e.charCodeAt(1))<<6|63&e.charCodeAt(2));default:return I((31&e.charCodeAt(0))<<6|63&e.charCodeAt(1))}},y=e=>e.replace(P,w),S=e=>{if(e=e.replace(/\s+/g,""),!h.test(e))throw new TypeError("malformed base64.");e+="==".slice(2-(3&e.length));let n,o,i,t="";for(let r=0;r<e.length;)n=m[e.charAt(r++)]<<18|m[e.charAt(r++)]<<12|(o=m[e.charAt(r++)])<<6|(i=m[e.charAt(r++)]),t+=64===o?I(n>>16&255):64===i?I(n>>16&255,n>>8&255):I(n>>16&255,n>>8&255,255&n);return t},C="function"==typeof atob?e=>atob(k(e)):f?e=>Buffer.from(e,"base64").toString("binary"):S,T=f?e=>A(Buffer.from(e,"base64")):e=>A(C(e).split("").map((e=>e.charCodeAt(0)))),R=f?e=>Buffer.from(e,"base64").toString("utf8"):g?e=>g.decode(T(e)):e=>y(C(e)),x=e=>k(e.replace(/[-_]/g,(e=>"-"==e?"+":"/"))),O=e=>R(x(e)),E=O;function D(){return D=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var o=arguments[n];for(var i in o)({}).hasOwnProperty.call(o,i)&&(e[i]=o[i])}return e},D.apply(null,arguments)}function N(e,n,o,i,t,r,a){try{var s=e[r](a),c=s.value}catch(l){return void o(l)}s.done?n(c):Promise.resolve(c).then(i,t)}function B(e){return function(){var n=this,o=arguments;return new Promise((function(i,t){var r=e.apply(n,o);function a(e){N(r,i,t,a,s,"next",e)}function s(e){N(r,i,t,a,s,"throw",e)}a(void 0)}))}}var _,M={exports:{}},L={exports:{}};(_=L).exports=function(e,n){this.v=e,this.k=n},_.exports.__esModule=!0,_.exports.default=_.exports;var F={exports:{}},j={exports:{}};!function(e){function n(o,i,t,r){var a=Object.defineProperty;try{a({},"",{})}catch(s){a=0}e.exports=n=function(e,o,i,t){if(o)a?a(e,o,{value:i,enumerable:!t,configurable:!t,writable:!t}):e[o]=i;else{var r=function(o,i){n(e,o,(function(e){return this._invoke(o,i,e)}))};r("next",0),r("throw",1),r("return",2)}},e.exports.__esModule=!0,e.exports.default=e.exports,n(o,i,t,r)}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports}(j),function(e){var n=j.exports;function o(){
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */
var i,t,r="function"==typeof Symbol?Symbol:{},a=r.iterator||"@@iterator",s=r.toStringTag||"@@toStringTag";function c(e,o,r,a){var s=o&&o.prototype instanceof u?o:u,c=Object.create(s.prototype);return n(c,"_invoke",function(e,n,o){var r,a,s,c=0,u=o||[],d=!1,p={p:0,n:0,v:i,a:f,f:f.bind(i,4),d:function(e,n){return r=e,a=0,s=i,p.n=n,l}};function f(e,n){for(a=e,s=n,t=0;!d&&c&&!o&&t<u.length;t++){var o,r=u[t],f=p.p,g=r[2];e>3?(o=g===n)&&(s=r[(a=r[4])?5:(a=3,3)],r[4]=r[5]=i):r[0]<=f&&((o=e<2&&f<r[1])?(a=0,p.v=n,p.n=r[1]):f<g&&(o=e<3||r[0]>n||n>g)&&(r[4]=e,r[5]=n,p.n=g,a=0))}if(o||e>1)return l;throw d=!0,n}return function(o,u,g){if(c>1)throw TypeError("Generator is already running");for(d&&1===u&&f(u,g),a=u,s=g;(t=a<2?i:s)||!d;){r||(a?a<3?(a>1&&(p.n=-1),f(a,s)):p.n=s:p.v=s);try{if(c=2,r){if(a||(o="next"),t=r[o]){if(!(t=t.call(r,s)))throw TypeError("iterator result is not an object");if(!t.done)return t;s=t.value,a<2&&(a=0)}else 1===a&&(t=r.return)&&t.call(r),a<2&&(s=TypeError("The iterator does not provide a '"+o+"' method"),a=1);r=i}else if((t=(d=p.n<0)?s:e.call(n,p))!==l)break}catch(v){r=i,a=1,s=v}finally{c=1}}return{value:t,done:d}}}(e,r,a),!0),c}var l={};function u(){}function d(){}function p(){}t=Object.getPrototypeOf;var f=[][a]?t(t([][a]())):(n(t={},a,(function(){return this})),t),g=p.prototype=u.prototype=Object.create(f);function v(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,p):(e.__proto__=p,n(e,s,"GeneratorFunction")),e.prototype=Object.create(g),e}return d.prototype=p,n(g,"constructor",p),n(p,"constructor",d),d.displayName="GeneratorFunction",n(p,s,"GeneratorFunction"),n(g),n(g,s,"Generator"),n(g,a,(function(){return this})),n(g,"toString",(function(){return"[object Generator]"})),(e.exports=o=function(){return{w:c,m:v}},e.exports.__esModule=!0,e.exports.default=e.exports)()}e.exports=o,e.exports.__esModule=!0,e.exports.default=e.exports}(F);var z={exports:{}},W={exports:{}},H={exports:{}};!function(e){var n=L.exports,o=j.exports;e.exports=function e(i,t){function r(e,o,a,s){try{var c=i[e](o),l=c.value;return l instanceof n?t.resolve(l.v).then((function(e){r("next",e,a,s)}),(function(e){r("throw",e,a,s)})):t.resolve(l).then((function(e){c.value=e,a(c)}),(function(e){return r("throw",e,a,s)}))}catch(u){s(u)}}var a;this.next||(o(e.prototype),o(e.prototype,"function"==typeof Symbol&&Symbol.asyncIterator||"@asyncIterator",(function(){return this}))),o(this,"_invoke",(function(e,n,o){function i(){return new t((function(n,i){r(e,o,n,i)}))}return a=a?a.then(i,i):i()}),!0)},e.exports.__esModule=!0,e.exports.default=e.exports}(H),function(e){var n=F.exports,o=H.exports;e.exports=function(e,i,t,r,a){return new o(n().w(e,i,t,r),a||Promise)},e.exports.__esModule=!0,e.exports.default=e.exports}(W),function(e){var n=W.exports;e.exports=function(e,o,i,t,r){var a=n(e,o,i,t,r);return a.next().then((function(e){return e.done?e.value:a.next()}))},e.exports.__esModule=!0,e.exports.default=e.exports}(z);var U={exports:{}};!function(e){e.exports=function(e){var n=Object(e),o=[];for(var i in n)o.unshift(i);return function e(){for(;o.length;)if((i=o.pop())in n)return e.value=i,e.done=!1,e;return e.done=!0,e}},e.exports.__esModule=!0,e.exports.default=e.exports}(U);var V={exports:{}},G={exports:{}};!function(e){function n(o){return e.exports=n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,n(o)}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports}(G),function(e){var n=G.exports.default;e.exports=function(e){if(null!=e){var o=e["function"==typeof Symbol&&Symbol.iterator||"@@iterator"],i=0;if(o)return o.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length))return{next:function(){return e&&i>=e.length&&(e=void 0),{value:e&&e[i++],done:!e}}}}throw new TypeError(n(e)+" is not iterable")},e.exports.__esModule=!0,e.exports.default=e.exports}(V),function(e){var n=L.exports,o=F.exports,i=z.exports,t=W.exports,r=H.exports,a=U.exports,s=V.exports;function c(){var l=o(),u=l.m(c),d=(Object.getPrototypeOf?Object.getPrototypeOf(u):u.__proto__).constructor;function p(e){var n="function"==typeof e&&e.constructor;return!!n&&(n===d||"GeneratorFunction"===(n.displayName||n.name))}var f={throw:1,return:2,break:3,continue:3};function g(e){var n,o;return function(i){n||(n={stop:function(){return o(i.a,2)},catch:function(){return i.v},abrupt:function(e,n){return o(i.a,f[e],n)},delegateYield:function(e,t,r){return n.resultName=t,o(i.d,s(e),r)},finish:function(e){return o(i.f,e)}},o=function(e,o,t){i.p=n.prev,i.n=n.next;try{return e(o,t)}finally{n.next=i.n}}),n.resultName&&(n[n.resultName]=i.v,n.resultName=void 0),n.sent=i.v,n.next=i.n;try{return e.call(this,n)}finally{i.p=n.prev,i.n=n.next}}}return(e.exports=c=function(){return{wrap:function(e,n,o,i){return l.w(g(e),n,o,i&&i.reverse())},isGeneratorFunction:p,mark:l.m,awrap:function(e,o){return new n(e,o)},AsyncIterator:r,async:function(e,n,o,r,a){return(p(n)?t:i)(g(e),n,o,r,a)},keys:a,values:s}},e.exports.__esModule=!0,e.exports.default=e.exports)()}e.exports=c,e.exports.__esModule=!0,e.exports.default=e.exports}(M);var J,K,q,Q,Y,Z,$=M.exports(),X=$;try{regeneratorRuntime=$}catch(Dt){"object"==typeof globalThis?globalThis.regeneratorRuntime=$:Function("r","regeneratorRuntime = r")($)}function ee(e){return"undefined"===e}function ne(){return!ee(typeof my)&&null!==my&&!ee(typeof my.alert)}(K=J||(J={})).CANCEL="-1",K.SUCCESS="0",K.API_UNDEFINED="1",K.INVALID_PARAMS="2",K.UNKNOWN_ERROR="3",K.UNAUTHORIZED_CALL="4",K.WRONG_CORP_ID="5",K.CREATE_CHAT_FAILED="6",K.UNAUTHORIZED_API="7",K.INVALID_CORP_ID="8",K.SERVER_RESPONSE_ERROR="9",K.WRONG_DEVICE_INFO="10",K.UPLOAD_FAIL="11",K.PROCESS_FAIL="12",K.DUPLICATED_CALL="13",K.TOO_LARGE_PIC="14",K.REQUEST_REJECT_OR_INSECURE_REQUEST="15",K.PC_NOT_ALLOWED_TO_OPEN_SIDE_PANE_OR_MODAL="21",K.PC_CLOSE_SIDE_PANE_OR_MODAL="22",K.UNAUTHORIZED_PARAMS="23",K.GESTURE_PASSWORD_DOES_NOT_EXIST="24",K.NETWORK_ERROR="25",function(e){e.MOBILE="mobile",e.PC="pc",e.MINI_APP="mini",e.UNKNOWN="unknown"}(q||(q={})),function(e){e.ANDROID="android",e.IOS="ios",e.UNKNOW="unknow"}(Q||(Q={})),function(e){e.UPDATE_NETWORK_STATUS="DINGGOV_ON_NETWORK_TYPE_CHANGED",e.UPDATE_LOCATION="DINGGOV_GEO_LOCATION_UPDATE",e.UPDATE_TRACE="DINGGOV_TRACE_UPDATE",e.ON_SHAKE="onShake"}(Y||(Y={})),function(e){e.isDingTalk="DingTalk",e.isMpaas="mPaaS",e.isUnknow="unknow"}(Z||(Z={}));var oe=navigator&&(navigator.swuserAgent||navigator.userAgent)||"",ie=function(){function e(){this.readyFnStack=[],this.generalEventCallbackStack={},this.apiList={},this.continuousCallbackStack={},this.isH5Mobile=null,this.appType=null,this.platformType=null,this.aliBridge=window&&window.navigator&&window.AlipayJSBridge,this.isReady=!1,this.init(),console.warn("请将 gdt-jsapi 版本请升级到 1.9.24 版本以上的最新版本，谢谢")}var n,o=e.prototype;return o.h5AndroidbridgeInit=function(){var e=this;this.h5BridgeReadyPromise=new Promise((function(n,o){var i=function(){try{window.WebViewJavascriptBridgeAndroid=window.nuva&&window.nuva.require(),e.execReadyFn()}catch(n){}};window.nuva&&(void 0===window.nuva.isReady||window.nuva.isReady)?i():(document.addEventListener("runtimeready",(function(){i()}),!1),document.addEventListener("runtimefailed",(function(i){var t=i&&i.detail||{errorCode:J.INVALID_PARAMS,errorMessage:"unknown nuvajs bootstrap error"};e.handleBridgeResponse(t,n,o)}),!1))}))},o.h5IosBridgeInit=function(){var e=this;this.h5BridgeReadyPromise=new Promise((function(n,o){if("undefined"!=typeof WebViewJavascriptBridge)try{WebViewJavascriptBridge.init((function(e,n){})),e.execReadyFn()}catch(i){}else document.addEventListener("WebViewJavascriptBridgeReady",(function(){try{WebViewJavascriptBridge&&WebViewJavascriptBridge.init((function(e,n){})),e.execReadyFn()}catch(i){}}),!1)}))},o.init=function(){var e=this,n=this.getAppType(),o=this.getContainerType();if(n===q.PC&&window.dingtalk&&!window.dingtalk.isRegister&&(window.dingtalk.isRegister=!0,window.dingtalk.callbackStack={},window.dingtalk.event.register((function(n,o){if(e.continuousCallbackStack[n])e.continuousCallbackStack[n](o);else if(o){var i=""+o.msgId;"openapi.event.emit"===n?(console.log("dingtalk receive event:",o,"identifer is",i),window.dingtalk.callbackStack[i]&&(window.dingtalk.callbackStack[i](o),delete window.dingtalk.callbackStack[i])):"im.fileTask.addNewTask"===n||"im.fileTask.updateTask"===n?(o.msgId||o.taskId)&&"function"==typeof e.continuousCallbackStack[o.msgId||o.taskId]&&e.continuousCallbackStack[o.msgId||o.taskId](n,o):e.generalEventCallbackStack[n]&&e.generalEventCallbackStack[n].forEach((function(n){n.call(e,o)}))}}))),n===q.MOBILE){if(o===Z.isDingTalk)this.platformType===Q.ANDROID?!this.h5BridgeReadyPromise&&this.h5AndroidbridgeInit():this.platformType===Q.IOS&&!this.h5BridgeReadyPromise&&this.h5IosBridgeInit();else if(o===Z.isMpaas&&n===q.MOBILE)if(window.AlipayJSBridge)this.execReadyFn();else{var i=setTimeout((function(){console.warn("window.AlipayJSBridge 未初始化完毕，走到兜底逻辑",e.isReady,window.AlipayJSBridge),e.isReady||e.execReadyFn.call(e)}),5200);document.addEventListener("AlipayJSBridgeReady",(function(){e.isReady||(clearTimeout(i),e.execReadyFn.call(e))}),!1)}}else setTimeout((function(){e.execReadyFn()}))},o.execReadyFn=function(){this.isReady=!0;for(var e=this.readyFnStack.shift();e;)e&&e(this),e=this.readyFnStack.shift()},o.onReady=function(e){this.isReady?e&&e(this):this.readyFnStack.push(e)},o.setCurrentInvoker=function(e){this.currentInvoker=e},o.getCurrentInvoker=function(){return this.currentInvoker},o.getBridge=function(){return this.aliBridge},o.getContainerType=function(){return/TaurusApp/g.test(oe)?/DingTalk/g.test(oe)?Z.isDingTalk:Z.isMpaas:/DingTalk/g.test(oe)?Z.isDingTalk:/mPaaSClient/g.test(oe)||/Nebula/g.test(oe)?Z.isMpaas:Z.isUnknow},o.getAppType=function(){return this.appType||(this.isMobile()?this.appType=q.MOBILE:window&&window.navigator&&window.navigator.userAgent&&window.navigator.userAgent.indexOf("dingtalk-win")>=0&&window.navigator.userAgent.indexOf("TaurusApp")>=0?this.appType=q.PC:ne()?this.appType=q.MINI_APP:(console.warn("检测到页面在非专有钉钉客户端中打开，JSAPI 调用可能不会生效！"),this.appType=q.UNKNOWN)),this.appType},o.isMobile=function(){var e=/iPhone|iPad|iPod|iOS/i.test(oe),n=/Android/i.test(oe),o=window&&window.navigator&&window.navigator.userAgent||"";return null!==this.isH5Mobile?this.isH5Mobile:o&&o.indexOf("dingtalk-win")>=0?(this.isH5Mobile=!1,!1):!(!o||!(o.includes("mPaaSClient")||o.includes("Nebula")||o.includes("DingTalk"))||(this.isH5Mobile=!0,this.platformType=e?Q.IOS:n?Q.ANDROID:Q.UNKNOW,0))},o.registerEvent=function(e,n){var o=this;if("function"==typeof n)return this.getAppType()===q.PC?(this.generalEventCallbackStack[e]||(this.generalEventCallbackStack[e]=[]),this.generalEventCallbackStack[e].push(n),function(){var i=o.generalEventCallbackStack[e].findIndex((function(e){return e===n}));o.generalEventCallbackStack[e].splice(i,1)}):this.getAppType()===q.MOBILE?(document.addEventListener(e,n,!1),function(){document.removeEventListener(e,n)}):void 0;console.error("callback 参数应该为函数")},o.registerClientAPI=function(e,n){this.apiList[e]=n},o.registerAPI=function(e,n){if(this.isMobile(),"object"==typeof n){var o=n,i=this.getAppType();this.registerClientAPI(e,o[i])}else this.registerClientAPI(e,n)},o.invokeMiniApp=(n=B(X.mark((function e(n,o){var i=this;return X.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return void 0===o&&(o={}),e.abrupt("return",new Promise((function(e,t){o=D({_apiName:n},o);var r=i.apiList[n],a=i.getContainerType();if(!r)return console.warn("API: "+n+"，未注册"),t("API: "+n+"，未注册");if(a===Z.isMpaas){if("function"==typeof r)return void r.call(null,o,{context:my,resolve:e,reject:t,methodName:n});my.call(n,o,(function(n){i.handleBridgeResponse(n,e,t)}))}else if(a===Z.isDingTalk){if("function"==typeof r)return void r.call(null,o,{context:dd.dtBridge,resolve:e,reject:t,methodName:n,containerType:a,appType:q.MINI_APP});dd.dtBridge({m:"taurus.common."+n,args:o,onSuccess:function(n){i.handleBridgeResponse(n,e,t)},onFail:function(n){i.handleBridgeResponse(n,e,t)}})}})));case 2:case"end":return e.stop()}}),e)}))),function(e,o){return n.apply(this,arguments)}),o.invokeMobile=function(){var e=B(X.mark((function e(n,o,i){var t=this;return X.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return void 0===o&&(o={}),e.abrupt("return",new Promise((function(e,r){o=D({_apiName:n},o);var a=t.apiList[n],s=t.getContainerType();if(!a)return console.warn("API: "+n+"，未注册"),r("API: "+n+"，未注册");if(s===Z.isDingTalk){if(t.platformType===Q.IOS){var c=Object.assign({},o);if(!0===c.watch&&"undefined"!=typeof WebViewJavascriptBridge&&WebViewJavascriptBridge.registerHandler(null!=i&&i.dingTalkAPIName?null==i?void 0:i.dingTalkAPIName:"taurus.common."+n,(function(e,n){"function"==typeof o.onSuccess&&o.onSuccess.call(null,e),n&&n({errorCode:"0",errorMessage:"success"})})),"function"==typeof a)return void a.call(null,o,{context:window.WebViewJavascriptBridge,resolve:e,reject:r,methodName:n,containerType:s,appType:q.MOBILE,platformType:Q.IOS,watch:c.watch});void 0!==window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("taurus.common."+n,Object.assign({},c),(function(n){!c.watch&&t.handleBridgeResponse(n||{},e,r)}))}else if(t.platformType===Q.ANDROID){var l=n.split("."),u=l.pop()||"",d=l.join(".")||"taurus.common";if("function"==typeof a)return void a.call(null,o,{context:window.WebViewJavascriptBridgeAndroid,resolve:e,reject:r,methodName:n,containerType:s,appType:q.MOBILE,platformType:Q.ANDROID});"function"==typeof window.WebViewJavascriptBridgeAndroid&&window.WebViewJavascriptBridgeAndroid((function(n){t.handleBridgeResponse(n,e,r)}),(function(n){t.handleBridgeResponse(n,e,r)}),d,u,o)}}else if(s===Z.isMpaas){if("function"==typeof a)return void a.call(null,o,{context:AlipayJSBridge,resolve:e,reject:r,methodName:n});AlipayJSBridge.call(n,o,(function(n){t.handleBridgeResponse(n,e,r)}))}})));case 2:case"end":return e.stop()}}),e)})));return function(n,o,i){return e.apply(this,arguments)}}(),o.findFitMsgId=function(e){var n,o;return null!==(n=window.dingtalk)&&void 0!==n&&null!==(o=n.callbackStack)&&void 0!==o&&o[e]?this.findFitMsgId(e+1):e},o.invokePC=function(){var e=B(X.mark((function e(n,o,i){var t=this;return X.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return void 0===o&&(o={}),void 0===i&&(i={msgId:1}),e.abrupt("return",new Promise((function(e,r){try{o=D({_apiName:n},o);var a=t.findFitMsgId(Date.now()),s=i.pcClientAPIName||n;if(i.msgId=a,!window.dingtalk)return Promise.reject(new Error("请在钉钉容器内使用 JSAPI"));t.apiList[n]?t.apiList[n].call(null,o,i):(console.info("invoke bridge api:",s,a,o),window.dingtalk.platform.invokeAPI(a,s,o)),window.dingtalk&&window.dingtalk.isRegister&&!window.dingtalk.callbackStack&&(window.dingtalk.callbackStack={}),window.dingtalk.callbackStack[""+a]=function(n){var o=n;return o.body?e(o.body):e(o)}}catch(c){r(c)}})));case 3:case"end":return e.stop()}}),e)})));return function(n,o,i){return e.apply(this,arguments)}}(),o.handleBridgeResponse=function(e,n,o){e&&e.errorCode?e.errorCode===J.SUCCESS?n(e.result):(console.warn("API 调用失败",e),o(e)):e&&"false"===e.success?o(e):n(e)},o.invoke=function(){var e=B(X.mark((function e(n,o,i){var t;return X.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(void 0===o&&(o={}),(t=this.getAppType())!==q.MOBILE){e.next=8;break}if(this.isReady){e.next=5;break}return e.abrupt("return",Promise.reject("错误：请在 dd.ready() 回调中使用 JSAPI，当前调用函数："+n));case 5:return e.abrupt("return",this.invokeMobile(n,o,i));case 8:if(t!==q.PC){e.next=12;break}return e.abrupt("return",this.invokePC(n,o,i));case 12:if(t!==q.MINI_APP){e.next=16;break}return e.abrupt("return",this.invokeMiniApp(n,o));case 16:return e.abrupt("return",Promise.reject("错误：未在钉钉运行环境下调用该 API，无效，请检查运行环境"));case 17:case"end":return e.stop()}}),e,this)})));return function(n,o,i){return e.apply(this,arguments)}}(),o.existEventListener=function(e){return!!this.continuousCallbackStack[e]},o.registerContinuesEvent=function(e,n){this.continuousCallbackStack[e]=n},o.removeContinuesEvent=function(e){this.existEventListener(e)&&(this.continuousCallbackStack[e](),delete this.continuousCallbackStack[e])},e}();ne()||(window._invoker=window._invoker||new ie);const te=ne()?new ie:window._invoker;function re(e,n){var o=n.resolve,i=n.reject,t=n.context,r=n.containerType,a=n.appType,s=n.platformType;if(r){var c=function(e){te.handleBridgeResponse(e,o,i)},l=function(e){te.handleBridgeResponse(e,o,i)};a===q.MINI_APP?t&&t({m:"taurus.common.alert",args:e,onSuccess:c,onFail:l}):s===Q.ANDROID?t&&t(c,l,"taurus.common","alert",e):s===Q.IOS&&t.callHandler("taurus.common.alert",Object.assign({},e),(function(e){o(e)}))}else t&&t.call("alert",e,(function(){o()}))}function ae(e){return te.invoke("alert",e)}function se(e){return te.invoke("authConfig",e)}function ce(e,n){var o=n.resolve,i=n.reject,t=n.context,r=n.containerType,a=n.appType,s=n.platformType;if(r){var c=function(e){te.handleBridgeResponse(e,o,i)},l=function(e){te.handleBridgeResponse(e,o,i)};a===q.MINI_APP?t&&t({m:"taurus.common.bizContactDepartmentsPickerExternal",args:e,onSuccess:c,onFail:l}):s===Q.ANDROID?t&&t(c,l,"taurus.common","bizContactDepartmentsPickerExternal",e):s===Q.IOS&&t.callHandler("taurus.common.bizContactDepartmentsPickerExternal",Object.assign({},e),(function(e){te.handleBridgeResponse(e,o,i)}))}else t&&t.call("bizContactDepartmentsPickerExternal",e,(function(e){te.handleBridgeResponse(e,o,i)}))}function le(e){return te.invoke("bizContactDepartmentsPickerExternal",e)}function ue(e,n){var o=n.resolve,i=n.reject,t=n.context,r=n.containerType,a=n.appType,s=n.platformType;if(r){var c=function(e){te.handleBridgeResponse(e,o,i)},l=function(e){te.handleBridgeResponse(e,o,i)};a===q.MINI_APP?t&&t({m:"taurus.common.bizCustomContactChooseExternal",args:e,onSuccess:c,onFail:l}):s===Q.ANDROID?t&&t(c,l,"taurus.common","bizCustomContactChooseExternal",e):s===Q.IOS&&t.callHandler("taurus.common.bizCustomContactChooseExternal",Object.assign({},e),(function(e){te.handleBridgeResponse(e,o,i)}))}else t&&t.call("bizCustomContactChooseExternal",e,(function(e){te.handleBridgeResponse(e,o,i)}))}function de(e){return te.invoke("bizCustomContactChooseExternal",e)}function pe(e,n){var o=n.resolve,i=n.reject,t=n.context,r=n.containerType,a=n.appType,s=n.platformType;if(r){var c=function(e){te.handleBridgeResponse(e,o,i)},l=function(e){te.handleBridgeResponse(e,o,i)};a===q.MINI_APP?t&&t({m:"taurus.common.bizCustomContactMultipleChooseExternal",args:e,onSuccess:c,onFail:l}):s===Q.ANDROID?t&&t(c,l,"taurus.common","bizCustomContactMultipleChooseExternal",e):s===Q.IOS&&t.callHandler("taurus.common.bizCustomContactMultipleChooseExternal",Object.assign({},e),(function(e){te.handleBridgeResponse(e,o,i)}))}else t&&t.call("bizCustomContactMultipleChooseExternal",e,(function(e){te.handleBridgeResponse(e,o,i)}))}function fe(e){return te.invoke("bizCustomContactMultipleChooseExternal",e)}function ge(e){return te.invoke("callPhone",e)}function ve(){return te.invoke("version")}te.registerAPI("alert",{mini:re,mobile:re}),ae.version={android:"1.3.2",ios:"1.3.2"},te.registerAPI("authConfig",{mini:!0,mobile:!0}),se.version={android:"1.2.0",ios:"1.2.0",pc:"1.2.0"},te.registerAPI("bizContactDepartmentsPickerExternal",{mini:ce,mobile:ce,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.contact.departmentsPickerEx",e)}}),le.version={android:"3.0.3",ios:"3.0.3",pc:"3.0.3"},te.registerAPI("bizCustomContactChooseExternal",{mini:ue,mobile:ue,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.customContact.chooseEx",e)}}),de.version={android:"3.0.3",ios:"3.0.3",pc:"3.0.3"},te.registerAPI("bizCustomContactMultipleChooseExternal",{mini:pe,mobile:pe,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.customContact.multipleChooseEx",e)}}),fe.version={android:"3.0.3",ios:"3.0.3",pc:"3.0.3"},te.registerAPI("callPhone",{mini:!0,mobile:!0}),ge.version={android:"1.1.0",ios:"1.1.0"},te.registerAPI("version",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"version",{})}});function me(e,n){return"number"!=typeof e&&(e=0),"number"!=typeof n&&(n=0),e>n?1:e<n?-1:0}function he(e,n){void 0===e&&(e=""),void 0===n&&(n="");var o=/^\d+(\.\d+){2,3}$/;if(!o.test(e)||!o.test(n))throw new Error("请传入正确的版本号格式");for(var i=(""+e).split(".").map((function(e){return parseInt(e,10)})),t=(""+n).split(".").map((function(e){return parseInt(e,10)})),r=Math.max(i.length,t.length),a=0,s=0;s<r&&0===(a=me(i[s],t[s]));s++);return a}var Ie,Ae,ke,be,Pe,we,ye,Se=navigator&&navigator.userAgent||"";function Ce(){return(Ce=B(X.mark((function e(n){var o,i,t,r;return X.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(yt[n]){e.next=2;break}return e.abrupt("return",!1);case 2:return e.next=4,ve();case 4:return o=e.sent,i=o.version,t=yt[n].version,r=Se.indexOf("Android")>-1||Se.indexOf("Adr")>-1?"android":Se.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)?"ios":/(windows)/i.test(navigator.userAgent)?"pc":"unknown",e.abrupt("return",!(!t||!t[r])&&he(i,t[r])>0);case 9:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function Te(){return te.invoke("checkVPNAppInstalled")}function Re(){return te.invoke("checkVPNAppOnline")}function xe(e){return te.invoke("chooseContact",e)}function Oe(e,n){var o=n.resolve,i=n.reject,t=n.context,r=n.containerType,a=n.appType,s=n.platformType;if(r){var c=function(e){te.handleBridgeResponse(e,o,i)},l=function(e){te.handleBridgeResponse(e,o,i)};a===q.MINI_APP?t&&t({m:"taurus.common.chooseContactWithComplexPicker",args:e,onSuccess:c,onFail:l}):s===Q.ANDROID?t&&t(c,l,"taurus.common","chooseContactWithComplexPicker",e):s===Q.IOS&&t.callHandler("taurus.common.chooseContactWithComplexPicker",Object.assign({},e),(function(e){te.handleBridgeResponse(e,o,i)}))}else t&&t.call("chooseContactWithComplexPicker",e,(function(n){n.error&&n.error.toString()===J.API_UNDEFINED?t.call("complexPicker",e,(function(e){te.handleBridgeResponse(e,o,i)})):te.handleBridgeResponse(n,o,i)}))}function Ee(e){return te.invoke("chooseContactWithComplexPicker",e)}function De(e){return te.invoke("chooseDateRangeWithCalendar",e)}function Ne(e){return te.invoke("chooseDayWithCalendar",e)}function Be(e){return te.invoke("chooseDepartments",e)}function _e(e){return te.invoke("chooseFile",e)}function Me(e){return te.invoke("chooseHalfDayWithCalendar",e)}function Le(e){return te.invoke("dgChooseImage",D({},e,{_apiName:"chooseImage"}))}function Fe(e){return te.invoke("chooseInterconnectionChat",e)}function je(e){return new Promise((function(n,o){my.chooseImage(D({},e,{success:function(e){n(e)},fail:function(e){o(e)}}))}))}function ze(){return te.invoke("chooseSpaceDir")}function We(e){return te.invoke("chooseTimeWithCalendar",e)}function He(e){return te.invoke("chooseVideo",e)}function Ue(e,n){var o=n.resolve,i=n.reject,t=n.context,r=n.containerType,a=n.appType,s=n.platformType;if(r){var c=function(e){te.handleBridgeResponse(e,o,i)},l=function(e){te.handleBridgeResponse(e,o,i)};a===q.MINI_APP?t&&t({m:"biz.navigation.close",args:e,onSuccess:c,onFail:l}):s===Q.ANDROID?t&&t(c,l,"biz.navigation","close",e):s===Q.IOS&&t.callHandler("biz.navigation.close",Object.assign({},e),(function(e){te.handleBridgeResponse(e,o,i)}))}else t&&t.call("popWindow",e,(function(e){te.handleBridgeResponse(e,o,i)}))}function Ve(e){return te.invoke("closePage",D({},e,{_apiName:"closePage"}))}function Ge(e){return te.invoke("complexPickerAdmin",e)}function Je(e){return te.invoke("confirm",e)}function Ke(e){return te.invoke("copyToClipboard",e)}function qe(e){return te.invoke("createChatGroup",e)}function Qe(e){return te.invoke("createDing",e)}function Ye(e){return te.invoke("createDingV2",e)}function Ze(e,n){void 0===e&&(e={});var o=n.resolve,i=n.reject,t=n.context,r=n.containerType,a=n.appType,s=n.platformType;if(r){var c=function(e){te.handleBridgeResponse(e,o,i)},l=function(e){te.handleBridgeResponse(e,o,i)};a===q.MINI_APP?t&&t({m:"biz.conference.createVideoConf",args:e,onSuccess:c,onFail:l}):s===Q.ANDROID?t&&t(c,l,"biz.conference","createVideoConf",e):s===Q.IOS&&t.callHandler("biz.conference.createVideoConf",Object.assign({},e),(function(e){te.handleBridgeResponse(e,o,i)}))}else t&&t.call("createVideoConf",e,(function(){o()}))}function $e(e){return te.invoke("createVideoConf",e)}function Xe(e){return te.invoke("createVideoMeeting",e)}function en(e){return te.invoke("dealWithBackAction",e)}function nn(){return te.invoke("disableClosePage")}function on(){return te.invoke("disablePullToRefresh",{_apiName:"disablePullToRefresh"})}function tn(){return te.invoke("disableWebviewBounce",{_apiName:"disableWebviewBounce"})}function rn(e){return te.invoke("downloadAudio",e)}te.registerAPI("checkVPNAppInstalled",{mini:!0,mobile:!0}),Te.version={android:"1.6.0",ios:"1.6.0"},te.registerAPI("checkVPNAppOnline",{mini:!0,mobile:!0}),Re.version={android:"1.6.0",ios:"1.6.0"},(Ae=Ie||(Ie={}))[Ae.DEFAULT=1]="DEFAULT",Ae[Ae.NEW=2]="NEW",function(e){e[e.GLOBAL_ORG=1]="GLOBAL_ORG",e[e.FRIEND=2]="FRIEND",e[e.GROUP=4]="GROUP",e[e.RECOMMEND=5]="RECOMMEND",e[e.SPECIAL_ATTENTION=7]="SPECIAL_ATTENTION",e[e.LOAD_GROUP_PERSON=8]="LOAD_GROUP_PERSON",e[e.ORG=9]="ORG"}(ke||(ke={})),function(e){e.PHONE_HIDE="PHONE_HIDE",e.CHAT_INVALID="CHAT_INVALID",e.GROUP_CHAT_PULL_INVALID="GROUP_CHAT_PULL_INVALID",e.APP_DING_INVALID="APP_DING_INVALID",e.PHONE_DING_INVALID="PHONE_DING_INVALID",e.SMS_DING_INVALID="SMS_DING_INVALID",e.AUDIO_VIDEO_HIDE="AUDIO_VIDEO_HIDE"}(be||(be={})),te.registerAPI("chooseContact",{pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.contact.choose",e)}}),xe.version={pc:"1.1.0"},te.registerAPI("chooseContactWithComplexPicker",{mini:Oe,mobile:Oe,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.contact.complexPicker",e)}}),Ee.version={android:"1.1.0",ios:"1.1.0",pc:"1.6.2"},te.registerAPI("chooseDateRangeWithCalendar",{mini:!0,mobile:!0}),De.version={android:"1.3.10",ios:"1.3.10"},te.registerAPI("chooseDayWithCalendar",{mini:!0,mobile:!0}),Ne.version={android:"1.3.10",ios:"1.3.10"},te.registerAPI("chooseDepartments",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.contact.departmentsPicker",e)}}),Be.version={android:"1.1.0",ios:"1.1.0",pc:"1.6.2"},te.registerAPI("chooseFile",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.chooseFile",e)}}),_e.version={android:"1.3.2",ios:"1.3.2",pc:"1.3.6"},te.registerAPI("chooseHalfDayWithCalendar",{mini:!0,mobile:!0}),Me.version={android:"1.3.10",ios:"1.3.10"},function(e){e[e.image=0]="image",e[e.video=1]="video"}(Pe||(Pe={})),te.registerAPI("dgChooseImage",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.dgChooseImage",e)}}),Le.version={android:"1.3.2",ios:"1.3.2",pc:"1.3.6"},te.registerAPI("chooseInterconnectionChat",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"chooseInterconnectionChat",e)}}),Fe.version={pc:"2.9.0",ios:"2.9.0",android:"2.9.0"},te.registerAPI("chooseImage",{mini:!0}),je.version={android:"1.6.2",ios:"1.6.2"},te.registerAPI("chooseSpaceDir",{mini:!0,mobile:!0,pc:function(e,n){void 0===e&&(e={}),window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.chooseSpaceDir",e)}}),ze.version={android:"1.6.0",ios:"1.6.0",pc:"2.6.0"},te.registerAPI("chooseTimeWithCalendar",{mini:!0,mobile:!0}),We.version={android:"1.3.10",ios:"1.3.10"},te.registerAPI("chooseVideo",{mini:!0,mobile:!0}),He.version={android:"1.6.2",ios:"1.6.2"},te.registerAPI("closePage",{mini:Ue,mobile:Ue,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.navigation.quit",e)}}),Ve.version={android:"1.2.0",ios:"1.2.0",pc:"1.2.0"},function(e){e.CODE="code",e.ACCOUNTID="accountId"}(we||(we={})),function(e){e.CODE="code",e.id="id"}(ye||(ye={})),te.registerAPI("complexPickerAdmin",{pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.contact.complexPickerAdmin",e)}}),Ge.version={pc:"2.8.0"},te.registerAPI("confirm",{mini:function(e,n){var o=n.resolve,i=n.reject,t=n.context,r=n.containerType,a={message:e.message,title:e.title,okButton:e.buttonLabels[0],cancelButton:e.buttonLabels[1]};r===Z.isDingTalk?t({m:"taurus.common.confirm",args:a,onSuccess:function(e){var n={errorCode:J.SUCCESS,result:{buttonIndex:e.ok?0:1}};te.handleBridgeResponse(n,o,i)},onFail:function(e){te.handleBridgeResponse(e,o,i)}}):t&&t.call("confirm",a,(function(e){var n={errorCode:J.SUCCESS,result:{buttonIndex:e.ok?0:1}};te.handleBridgeResponse(n,o,i)}))},mobile:function(e,n){var o=n.resolve,i=n.reject,t=n.context,r=n.containerType,a=n.platformType,s={message:e.message,title:e.title,okButton:e.buttonLabels[0],cancelButton:e.buttonLabels[1]};r?a===Q.ANDROID?t&&t((function(e){var n={errorCode:J.SUCCESS,result:{buttonIndex:e.ok?0:1}};te.handleBridgeResponse(n,o,i)}),(function(e){te.handleBridgeResponse(e,o,i)}),"taurus.common","confirm",s):a===Q.IOS&&t.callHandler("taurus.common.confirm",Object.assign({},s),(function(e){te.handleBridgeResponse(e,o,i)})):t&&t.call("confirm",s,(function(e){var n={errorCode:J.SUCCESS,result:{buttonIndex:e.ok?0:1}};te.handleBridgeResponse(n,o,i)}))},pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"device.notification.confirm",e)}}),Je.version={android:"1.3.2",ios:"1.3.2",pc:"1.3.2"},te.registerAPI("copyToClipboard",{mini:!0,mobile:!0}),Ke.version={android:"1.3.2",ios:"1.3.2"},te.registerAPI("createChatGroup",{mini:!0,mobile:!0}),qe.version={android:"1.3.0",ios:"1.3.0",pc:"1.3.0"},te.registerAPI("createDing",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.ding.create",e)}}),Qe.version={android:"1.3.9",ios:"1.3.9",pc:"1.3.9"},te.registerAPI("createDingV2",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.ding.createV2",e)}}),Ye.version={android:"2.7.0",ios:"2.7.0",pc:"2.7.0"},te.registerAPI("createVideoConf",{mini:Ze,mobile:Ze,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.conference.createVideoConf",D({},e))}}),$e.version={android:"3.7.5",ios:"3.7.5",pc:"3.7.5"},te.registerAPI("createVideoMeeting",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.meeting.create",D({isVideoConference:!0},e))}}),Xe.version={android:"*******",ios:"*******",pc:"1.9.4"},te.registerAPI("dealWithBackAction",{mobile:!0}),en.version={android:"1.2.0.10"},te.registerAPI("disableClosePage",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.disableClosePage",{})}}),nn.version={pc:"3.4.0"},te.registerAPI("disablePullToRefresh",{mobile:function(e,n){var o=n.resolve,i=n.reject,t=n.context,r=n.containerType,a=n.platformType;r?a===Q.ANDROID?t&&t((function(){o()}),(function(){i()}),"ui.pullToRefresh","disable",{}):a===Q.IOS&&t.callHandler("ui.pullToRefresh.disable",Object.assign({},{}),(function(e){o(e)})):t&&t.call("pullRefresh",{pullRefresh:!1},(function(){o()}))}}),on.version={android:"1.3.0",ios:"1.3.0"},te.registerAPI("disableWebviewBounce",{mobile:function(e,n){var o=n.resolve,i=n.reject,t=n.context,r=n.containerType,a=n.platformType;r?a===Q.ANDROID?t&&t((function(){o()}),(function(){i()}),"ui.webViewBounce","disable",{}):a===Q.IOS&&t.callHandler("ui.webViewBounce.disable",Object.assign({},{}),(function(e){o(e)})):t&&t.call("bounce",{enable:!1},(function(e){o(e)}))}}),tn.version={ios:"1.3.0"},te.registerAPI("downloadAudio",{mini:!0,mobile:!0}),rn.version={android:"1.3.0",ios:"1.3.0"};var an,sn,cn;function ln(e){return te.invoke("downloadFile",e)}function un(){return te.invoke("enablePullToRefresh",{_apiName:"enablePullToRefresh"})}function dn(){return te.invoke("enableVpn")}function pn(){return te.invoke("enableWebviewBounce",{_apiName:"enableWebviewBounce"})}function fn(e){return te.invoke("exclusiveInvoke",e)}function gn(e){return te.invoke("faceComparison",e)}function vn(e){return te.invoke("faceRecognition",e)}function mn(e){return te.invoke("getAppInstallStatus",e)}function hn(e){return te.invoke("getAuthCode",e)}function In(){return te.invoke("getConfig",{})}function An(){return te.getContainerType()}function kn(){return te.invoke("getDeviceId",{})}function bn(){return te.invoke("getFromClipboard")}function Pn(e){return te.invoke("getGeolocation",e)}function wn(e){return te.invoke("getGeolocationStatus",e)}function yn(){return te.invoke("getHotspotInfo")}function Sn(){return te.invoke("getLanguageSetting")}function Cn(){return te.invoke("getLoginUser")}function Tn(){return te.invoke("getNetworkType")}function Rn(){return te.invoke("getPhoneInfo")}function xn(){return te.invoke("getProxyInfo",{})}function On(e){return te.invoke("getStorageItem",e)}function En(e){return te.invoke("getTraceStatus",e)}function Dn(){return te.invoke("getUUID")}te.registerAPI("downloadFile",{mini:function(e,n){var o=n.resolve,i=n.reject,t=n.containerType,r=n.context;t===Z.isDingTalk?r&&r({m:"taurus.common.downloadFile",args:e,onSuccess:function(e){te.handleBridgeResponse(e,o,i)},onFail:function(e){te.handleBridgeResponse(e,o,i)}}):r&&r.call("downloadFile",e,(function(e){e.error?i(e):o(e)}))},pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.downloadFile",e),te.registerContinuesEvent(n.msgId,(function(o,i){"im.fileTask.addNewTask"===o&&(te.removeContinuesEvent(n.msgId),te.registerContinuesEvent(i.taskId,(function(n,o){if("im.fileTask.updateTask"===n){var i=o.doneSize,t=o.fileName,r=o.filePath,a=o.fileSize,s=o.speed;e.onProgress({doneSize:i,fileName:t,filePath:r,fileSize:a,speed:s}),1===o.status&&te.removeContinuesEvent(o.taskId)}})))}))}}),ln.version={pc:"1.3.5"},te.registerAPI("enablePullToRefresh",{mobile:function(e,n){var o=n.resolve,i=n.reject,t=n.context,r=n.containerType,a=n.platformType;r?a===Q.ANDROID?t&&t((function(){o()}),(function(){i()}),"ui.pullToRefresh","enable",{}):a===Q.IOS&&t.callHandler("ui.pullToRefresh.enable",Object.assign({},{}),(function(){o()})):t&&t.call("pullRefresh",{pullRefresh:!0},(function(){o()}))}}),un.version={android:"1.3.0",ios:"1.3.0"},te.registerAPI("enableVpn",{mini:!0,mobile:!0}),dn.version={android:"1.1.0",ios:"1.1.0"},te.registerAPI("enableWebviewBounce",{mobile:function(e,n){var o=n.resolve,i=n.reject,t=n.context,r=n.containerType,a=n.platformType;r?a===Q.ANDROID?t&&t((function(){o()}),(function(){i()}),"taurus.common","bounce",{enable:!0}):a===Q.IOS&&t.callHandler("taurus.common.bounce",Object.assign({},{enable:!0}),(function(e){o(e)})):t&&t.call("bounce",{enable:!0},(function(e){o(e)}))}}),pn.version={ios:"1.3.0"},te.registerAPI("exclusiveInvoke",{mini:!0,mobile:!0}),fn.version={ios:"1.9.5",android:"1.9.5"},te.registerAPI("faceComparison",{mobile:!0,mini:!0}),gn.version={android:"2.4.0",ios:"2.4.0"},function(e){e.PNG="png",e.JPG="jpg"}(an||(an={})),te.registerAPI("faceRecognition",{mobile:!0,mini:!0}),vn.version={android:"2.4.0",ios:"2.4.0"},te.registerAPI("getAppInstallStatus",{mini:!0,mobile:!0}),mn.version={android:"2.1.10",ios:"2.1.10"},te.registerAPI("getAuthCode",{pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"runtime.permission.requestAuthCode",e)},mobile:!0,mini:!0}),hn.version={android:"1.0.0",ios:"1.0.0",pc:"1.0.0"},te.registerAPI("getConfig",{mobile:!0,mini:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"getConfig",e)}}),In.version={android:"1.3.2",ios:"1.3.2",pc:"1.3.2"},te.registerAPI("getDeviceId",{mobile:!0,mini:!0}),kn.version={android:"2.5.0",ios:"2.5.0"},te.registerAPI("getFromClipboard",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"util.clipboardData.getData",e)}}),bn.version={android:"2.3.1",ios:"2.3.1",pc:"2.6.10"},te.registerAPI("getGeolocation",{mini:!0,mobile:!0}),Pn.version={android:"1.2.0",ios:"1.2.0"},te.registerAPI("getGeolocationStatus",{mobile:!0,mini:!0}),wn.version={android:"1.6.2",ios:"1.6.2"},te.registerAPI("getHotspotInfo",{mobile:!0,mini:!0}),yn.version={android:"1.3.5",ios:"1.3.5"},te.registerAPI("getLanguageSetting",{mobile:!0,mini:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"getLanguageSetting",e)}}),Sn.version={android:"1.4.0",ios:"1.4.0",pc:"1.4.0"},te.registerAPI("getLoginUser",{mobile:!0,mini:!0}),Cn.version={android:"1.1.0",ios:"1.1.0"},te.registerAPI("getNetworkType",{mobile:!0,mini:!0}),Tn.version={android:"1.3.0",ios:"1.3.0"},te.registerAPI("getPhoneInfo",{mini:!0,mobile:!0}),Rn.version={android:"1.3.5",ios:"1.3.5"},(cn=sn||(sn={})).SOCKS5="SOCKS5",cn.HTTP="HTTP",te.registerAPI("getProxyInfo",{pc:function(e,n){void 0===e&&(e={}),window.dingtalk.platform.invokeAPI(n.msgId,"net.util.getProxyInfo",e)}}),xn.version={pc:"2.10.0"},te.registerAPI("getStorageItem",{mobile:!0,mini:!0}),On.version={android:"*******",ios:"*******"},te.registerAPI("getTraceStatus",{mobile:!0}),En.version={android:"1.3.4",ios:"1.3.4"},te.registerAPI("getUUID",{mobile:!0,mini:!0}),Dn.version={android:"1.3.5",ios:"1.3.5"};var Nn,Bn,_n,Mn,Ln=/TaurusApp\((\S*)\/(\S*)\)/;function Fn(){var e=te.getAppType();return e===q.PC||e===q.MOBILE?function(){if(window&&window.navigator){var e=window.navigator.userAgent;if(e){var n=e.match(Ln);return Promise.resolve({group:"TaurusApp",name:n[1],version:n[2]})}return Promise.reject("调用错误：无法检测到当下环境的 userAgent，请确保在政务钉钉客户端 H5 容器下调用。")}}():e===q.MINI_APP?te.invoke("getUserAgent",{}):void 0}function jn(e){return te.invoke("getWaterMarkConfig",e)}te.registerAPI("getUserAgent",{mobile:!0,mini:!0,pc:!0}),Fn.version={android:"1.6.2",ios:"1.6.2",pc:"1.6.2"},(Mn=Nn||(Nn={})).off="0",Mn.on="1",function(e){e[e.off=0]="off",e[e.on=1]="on"}(Bn||(Bn={})),function(e){e[e.name=1]="name",e[e.id=2]="id",e[e.custom=3]="custom"}(_n||(_n={})),te.registerAPI("getWaterMarkConfig",{pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"getWaterMarkConfig",e)},mini:!0,mobile:!0});var zn,Wn="h5Page",Hn=[Wn,"meetingDetail","docPreview"],Un=!ee(typeof my)&&null!==my&&!ee(typeof my.alert);Un&&(zn=my.getSystemInfoSync());var Vn,Gn,Jn,Kn,qn=Un?zn.platform:navigator.userAgent,Qn=Un?zn.screenWidth:window.screen.width,Yn=(Un?zn.pixelRatio:window.devicePixelRatio)||2,Zn=Un?Promise.resolve(""):"",$n=function(){function e(e){void 0===e&&(e={}),this.options=D({texts:[""],width:50,height:50,textRotate:-10,textColor:"#000000",textFont:"PingFangSC-Regular,system-ui,sans-serif",fontStyle:"normal",opacity:90,canvas:[],fontSize:14},e),this.options.width*=this.options.fontSize/12,this.options.height*=this.options.fontSize/12,this.options.deg=this.options.textRotate*Math.PI/180,this.options.cosDeg=Math.cos(this.options.deg),this.options.absSinDeg=Math.abs(Math.sin(this.options.deg))}var n=e.prototype;return n.init=function(){var e=this,n=null,o=null;Un?o=my.createCanvasContext("canvasBg"):(n=this.createCanvas(),o=n.getContext("2d")),this.calcTextSize();var i=this.options,t=i.allItemsWidth,r=i.drawItems,a=i.height,s=i.containerComp,c=Math.ceil(Qn/t),l=new Array(c).fill(r).reduce((function(e,n){return e.concat(n)}),[]),u=function(){e.setCanvasStyle(o),e.drawText(o,l),o.translate(0,a),e.drawText(o,l.reverse(),!0)};if(Un)return new Promise((function(e){s.setState({width:t*c,height:2*a},(function(){setTimeout((function(){u(),o.draw(),e(o.toDataURL("image/png"))}),0)}))}));n.width=t*c,n.height=2*a,n.style.display="none",u();var d=n.toDataURL("image/png");return this.destroy(),d},n.calcTextSize=function(){var e=0,n=0,o=this.options;o.drawItems=[].map.call(o.texts,(function(i){var t,r,a,s;if(Un){for(var c=0,l=0;l<i.length;l+=1)c+=/[\uff00-\uffff]/.test(i[l])?1:.5;t=1.1*o.fontSize*c,r=1.2*o.fontSize}else{var u=(a='<span style="font:'+o.fontSize+"px "+o.textFont+';visibility:hidden;">'+i+"</span>",(s=document.createElement("div")).innerHTML=a.trim(),s.firstChild);document.body.appendChild(u),t=u.offsetWidth,r=u.offsetHeight,document.body.removeChild(u)}return e=Math.max(e,t),o.fontHeight||(o.fontHeight=r),n+=Math.ceil(o.cosDeg*(o.width<t?t:o.width)),{txt:i,width:t,height:r}})),e>o.width&&(o.width=e);var i=e*o.absSinDeg+o.fontHeight*o.cosDeg;i>o.height&&(o.height=i),o.maxItemWidth=e,o.allItemsWidth=n},n.setCanvasStyle=function(e){var n=this.options,o=n.deg,i=n.absSinDeg,t=n.height,r=n.fontHeight,a=n.fontStyle,s=n.fontSize,c=n.textFont,l=n.textColor,u=n.opacity;e.rotate(o);var d=i*(t-r);e.translate(-d,0),e.font=a+" "+s+"px "+c,e.fillStyle=l,e.textAlign="left",e.textBaseline="bottom",e.globalAlpha=u},n.drawText=function(e,n,o){void 0===o&&(o=!1);var i=this.options,t=i.maxItemWidth,r=i.width,a=i.height,s=i.deg,c=i.cosDeg,l=i.absSinDeg;n.forEach((function(n,i){var u=c*(t-n.width)/2,d=r*c*i,p=Math.abs(d*Math.tan(s))+a;e.fillText(n.txt,d+(o?c*(r-n.width)/2:u),p+(o?l*(r-n.width)/2:0))}))},n.createCanvas=function(){var e=document.createElement("canvas");return this.options.canvas.push(e),e},n.destroy=function(){this.options.canvas.forEach((function(e){e.remove(),e=null}))},e}();function Xn(e,n){var o=JSON.parse(e),i=o.watermark||o;if(!i||"0"===String(i.watermarkStatus))return Zn;if(!Array.isArray(i.targetPages)||!i.targetPages.some((function(e){return e.name===n&&"1"===String(e.value)})))return Zn;var t=[];if(Array.isArray(i.contentType)){var r="";i.contentType.includes(1)&&(r+=i.userName+" "),i.contentType.includes(2)&&(r+=(i.account||"").slice(-4)),r&&t.push(r),i.contentType.includes(0)&&i.contentCustom&&t.push(i.contentCustom)}if(!t.length)return Zn;var a,s,c=/Android|Adr|SymbianOS|Windows\s*Phone|Mobile/.test(qn),l=/iPhone|iPad|iPod|Mac\s*OS.*Mobile|iOS/.test(qn),u="0"===String(i.watermarkShowDensity);return l?u?(a=114,s=66):(a=86,s=45):c?u?(a=47*Yn,s=40*Yn):(a=25*Yn,s=25*Yn):u?(a=300,s=126):(a=194,s=106),new $n({containerComp:this,texts:t,width:a,height:s,textRotate:-10,textColor:{0:"#FF0000",1:"#000000",2:"#0000FF"}[i.fontColor]||"#000000",textFont:"PingFangSC-Regular,system-ui,sans-serif",fontStyle:"0"===String(i.fontStyle)?"normal":"bold",opacity:(120-parseInt(i.fontDiaphaneity,10))/100,fontSize:{0:12,1:16,2:28}[i.fontSize]||16}).init()}function eo(e,n){if(void 0===e&&(e={}),void 0===n&&(n=Wn),!Hn.includes(n))throw new Error("第二个可选参数，仅能为“h5Page”或“meetingDetail”");try{return Xn.call(this,JSON.stringify(e),n)}catch(o){throw o}}function no(e,n){return void 0===e&&(e=""),new Promise((function(o,i){jn({pageInfo:e}).then((function(e){try{var t=eo(e,n);o(t)}catch(r){i(r)}}))}))}function oo(e){return te.invoke("getWaterMarkConfigV2",e)}no.version={android:"1.1.0",ios:"1.1.0",pc:"1.1.0"},function(e){e[e.ENABLE=1]="ENABLE",e[e.DISABLE=0]="DISABLE"}(Vn||(Vn={})),te.registerAPI("getWaterMarkConfigV2",{mobile:!0,mini:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"getWaterMarkConfigV2",e)}}),oo.version={android:"2.8.0",ios:"2.8.0",pc:"2.8.0"},(Jn=Gn||(Gn={}))[Jn.DISABLE=0]="DISABLE",Jn[Jn.ENABLE=1]="ENABLE",function(e){e.IMSESSIONLIST="imSessionList",e.DOCPREVIEW="docPreview",e.H5PAGEOTHER="h5PageOther",e.MEETINGDETAIL="meetingDetail",e.H5PAGEBASIC="h5PageBasic",e.SELECTIONCOMPONENT="selectionComponent",e.CONTACTLIST="contactList",e.CONTACTDETAIL="contactDetail",e.CHAT="chat",e.SECRETCHAT="secretChat",e.CAMERA="camera"}(Kn||(Kn={}));var io,to,ro={1:"normal",2:"bold",3:"italic"};!function(e){e[e.LOOSE=0]="LOOSE",e[e.NORMAL=1]="NORMAL",e[e.DENSE=2]="DENSE"}(io||(io={})),function(e){e[e.RIGHT=0]="RIGHT",e[e.LEFT=1]="LEFT"}(to||(to={}));var ao,so,co,lo=function(){function e(e){this.options=Object.assign({texts:"",width:50,height:50,tiltAngle:-15,fontColor:"#171A1D",textFont:"PingFangSC-Regular,system-ui,sans-serif",transparency:90,canvas:[],fontSize:13,tWidth:0,tHeight:0,deg:-15},e,{width:e.leftAndRightSpacing,height:e.upAndDownSpacing}),this.options.deg=this.options.tiltAngle*Math.PI/180}var n=e.prototype;return n.init=function(){var e,n,o,i,t,r,a,s,c,l=null;return c=(l=this.createCanvas()).getContext("2d"),l.width=(null===(e=window)||void 0===e||null===(n=e.screen)||void 0===n?void 0:n.width)||(null===(o=document)||void 0===o||null===(i=o.documentElement)||void 0===i?void 0:i.clientWidth)||749,l.height=(null===(t=window)||void 0===t||null===(r=t.screen)||void 0===r?void 0:r.height)||(null===(a=document)||void 0===a||null===(s=a.documentElement)||void 0===s?void 0:s.clientHeight)||326,this.calcTextSize(),this.setCanvasStyle(c),this.drawText(c),l.toDataURL("image/png")},n.calcTextSize=function(){var e,n,o=this.options,i="exclusiveDingTalkWaterMarkCustomClass"+100*Math.random(),t=(e='<span id="'+i+'" style="font:'+o.fontSize+"px "+o.textFont+';visibility:hidden;display:inline-block;">'+o.texts+"</span>",(n=document.createElement("div")).innerHTML=e.trim(),n.firstChild);document.body.appendChild(t);var r=document.getElementById(i),a=Math.max(r.clientWidth,o.texts.length*o.fontSize*1.3)||200,s=Math.min(r.clientHeight,1.3*o.fontSize)||16;o.tWidth=a,o.tHeight=s,document.body.removeChild(t)},n.setCanvasStyle=function(e){var n=this.options,o=n.deg,i=n.fontStyle,t=n.fontSize,r=n.textFont,a=n.fontColor,s=n.transparency;e.rotate(o),e.font=i+" "+t+"px "+r,e.fillStyle=a,e.textAlign="left",e.textBaseline="bottom",e.globalAlpha=(100-s)/100},n.fillContent=function(e,n){for(var o=this.options,i=o.width,t=o.height,r=o.texts,a=o.tWidth,s=o.tHeight,c=0;c<40;c++)for(var l=c*t+s,u=0;u<40;u++){var d;d=c%2==0?e===to.RIGHT?(a+i)*u:(a+i)*u+a+i:e===to.RIGHT?(a+i)*u+i:(a+i)*u+a,n.fillText(r,e===to.RIGHT?d:-d,l)}},n.drawText=function(e){this.fillContent(to.RIGHT,e),this.fillContent(to.LEFT,e)},n.createCanvas=function(){var e=document.createElement("canvas");return this.options.canvas.push(e),e},e}();function uo(e,n){var o,i,t,r,a,s,c,l;void 0===n&&(n=Kn.H5PAGEOTHER);var u=null;try{u=JSON.parse(e)}catch(v){u={}}var d=null===(o=u)||void 0===o||null===(i=o.watermark)||void 0===i?void 0:i.ruleContent,p=null===(t=u)||void 0===t?void 0:t.userInfo;if((null==d?void 0:d.enable)===Gn.DISABLE||(null==d?void 0:d.enable)===Gn.ENABLE&&(null==d||null===(r=d.effectPage)||void 0===r?void 0:r[n])!==Gn.ENABLE)return"";var f,g="";return(null==d||null===(a=d.watermarkContent)||void 0===a?void 0:a.enableUsername)===Gn.ENABLE&&(g+=null==p?void 0:p.userName),(null==d||null===(s=d.watermarkContent)||void 0===s?void 0:s.enablePhoneNumber)===Gn.ENABLE&&(g+=" "+(null==p?void 0:p.lastFourPhoneNo)),null!=d&&null!==(c=d.watermarkContent)&&void 0!==c&&c.customCopy&&(g+=" "+(null==d||null===(f=d.watermarkContent)||void 0===f?void 0:f.customCopy)),g.length?new lo(Object.assign({texts:g,textFont:"PingFangSC-Regular,system-ui,sans-serif"},null==d?void 0:d.watermarkStyle,{fontStyle:ro[null==d||null===(l=d.watermarkStyle)||void 0===l?void 0:l.fontStyle]})).init():""}function po(e,n){void 0===n&&(n=Kn.H5PAGEOTHER);try{return uo.call(null,JSON.stringify(e),n)}catch(o){return""}}function fo(){return te.invoke("getWifiStatus")}function go(){return te.invoke("getWorkbenchContext")}function vo(){return te.invoke("h5PageBack",{_apiName:"goBack"})}function mo(e,n){var o=n.resolve,i=n.reject,t=n.context,r=n.containerType,a=n.appType,s=n.platformType;if(r){var c=function(e){te.handleBridgeResponse(e,o,i)},l=function(e){te.handleBridgeResponse(e,o,i)};a===q.MINI_APP?t&&t({m:"device.notification.hidePreloader",args:e,onSuccess:c,onFail:l}):s===Q.ANDROID?t&&t(c,l,"device.notification","hidePreloader",e):s===Q.IOS&&t.callHandler("device.notification.hidePreloader",Object.assign({},e),(function(e){o(e)}))}else t&&t.call("hideLoading",e,(function(){o()}))}function ho(){return te.invoke("hideLoading")}function Io(){return te.invoke("hideOptionMenu")}function Ao(e,n){var o=n.resolve,i=n.reject,t=n.containerType,r=n.platformType,a=n.appType,s=n.context,c=Object.assign(e,{hidden:!0});if(t){var l=function(){e.onSuccess&&e.onSuccess(),o()},u=function(){e.onFail&&e.onFail(),i()};a===q.MINI_APP?s&&s({m:"biz.navigation.hideBar",args:c,onSuccess:l,onFail:u}):r===Q.ANDROID?s&&s(l,u,"biz.navigation","hideBar",c):r===Q.IOS&&s.callHandler("biz.navigation.hideBar",Object.assign({},c),(function(){o()}))}else s&&s.call("hideTitlebar",c,(function(){o()}))}function ko(){return te.invoke("hideTitlebar")}function bo(e){return te.invoke("isDownloadFileExist",e)}function Po(e,n){void 0===e&&(e={});var o=n.resolve,i=n.reject,t=n.context,r=n.containerType,a=n.appType,s=n.platformType;if(r){var c=function(e){te.handleBridgeResponse(e,o,i)},l=function(e){te.handleBridgeResponse(e,o,i)};a===q.MINI_APP?t&&t({m:"biz.conference.joinScheduleConf",args:e,onSuccess:c,onFail:l}):s===Q.ANDROID?t&&t(c,l,"biz.conference","joinScheduleConf",e):s===Q.IOS&&t.callHandler("biz.conference.joinScheduleConf",Object.assign({},e),(function(e){te.handleBridgeResponse(e,o,i)}))}else t&&t.call("joinScheduleConf",e,(function(){o()}))}function wo(e){return te.invoke("joinScheduleConf",e)}function yo(e,n){void 0===e&&(e={});var o=n.resolve,i=n.reject,t=n.context,r=n.containerType,a=n.appType,s=n.platformType;if(r){var c=function(e){te.handleBridgeResponse(e,o,i)},l=function(e){te.handleBridgeResponse(e,o,i)};a===q.MINI_APP?t&&t({m:"biz.conference.joinVideoConf",args:e,onSuccess:c,onFail:l}):s===Q.ANDROID?t&&t(c,l,"biz.conference","joinVideoConf",e):s===Q.IOS&&t.callHandler("biz.conference.joinVideoConf",Object.assign({},e),(function(e){te.handleBridgeResponse(e,o,i)}))}else t&&t.call("joinVideoConf",e,(function(){o()}))}function So(e){return te.invoke("joinVideoConf",e)}function Co(e){return te.invoke("joinVideoMeeting",e)}function To(e){return te.invoke("locateOnMap",e)}function Ro(){return te.invoke("onAudioPlayEnd")}function xo(e){return te.invoke("onRecordAudioEnd",e)}function Oo(e){return te.invoke("openApiInvoker",e)}function Eo(e){return te.invoke("openApp",e)}function Do(e){return te.invoke("openBrowser",e)}function No(e){return te.invoke("openChat",e)}function Bo(e){return te.invoke("openDownloadFile",e)}function _o(e,n){var o=n.resolve,i=n.reject,t=n.context,r=n.containerType,a=n.appType,s=n.platformType;ve().then((function(n){var c=-1!==he(n.version,"1.6.2");if(r){var l=function(e){te.handleBridgeResponse(e,o,i)},u=function(e){te.handleBridgeResponse(e,o,i)};a===q.MINI_APP?t&&t({m:c?"taurus.common.openLink":"taurus.common.pushWindow",args:e,onSuccess:l,onFail:u}):s===Q.ANDROID?t&&t(l,u,"taurus.common",c?"openLink":"pushWindow",e):s===Q.IOS&&t.callHandler(c?"taurus.common.openLink":"taurus.common.pushWindow",Object.assign({},e),(function(e){te.handleBridgeResponse(e,o,i)}))}else t&&t.call(c?"openLink":"pushWindow",e,(function(e){te.handleBridgeResponse(e,o,i)}))}))}function Mo(e){return te.invoke("openLink",e)}function Lo(e){return te.invoke("openPage",e)}function Fo(e){return te.invoke("dgOpenApp",D({},e,{_apiName:"openSchemeUrl"}))}function jo(e){return te.invoke("openSlidePanel",e)}function zo(){return te.invoke("openWatermarkCamera")}function Wo(e){return te.invoke("pauseAudio",e)}function Ho(e){return te.invoke("pickChat",e)}function Uo(e){return te.invoke("pickGroupChat",e)}function Vo(e,n){void 0===e&&(e={});var o=n.resolve,i=n.reject,t=n.context,r=n.containerType,a=n.appType,s=n.platformType;if(r){var c=function(e){te.handleBridgeResponse(e,o,i)},l=function(e){te.handleBridgeResponse(e,o,i)};a===q.MINI_APP?t&&t({m:s===Q.ANDROID?"taurus.common.pickGroupConversation":"internal.chat.pickGroupConversation",args:e,onSuccess:c,onFail:l}):s===Q.ANDROID?t&&t(c,l,"taurus.common","pickGroupConversation",e):s===Q.IOS&&t.callHandler("internal.chat.pickGroupConversation",Object.assign({},e),(function(e){o(e)}))}else t&&t.call("pickGroupConversation",e,(function(){o()}))}function Go(e){return void 0===e&&(e={owner:!1}),te.invoke("pickGroupConversation",e)}function Jo(e){return te.invoke("playAudio",e)}function Ko(e){return te.invoke("previewDoc",e)}function qo(e){return te.invoke("previewImage",e)}function Qo(e){return te.invoke("printFile",e)}function Yo(e){return te.invoke("printNativeLog",e)}function Zo(e,n){var o=n.resolve,i=n.reject,t=n.context,r=n.containerType,a=n.appType,s=n.platformType,c={message:e.message,title:e.title,okButton:e.buttonLabels[0],cancelButton:e.buttonLabels[1]};if(r){var l=function(e){te.handleBridgeResponse(e,o,i)},u=function(e){te.handleBridgeResponse(e,o,i)};a===q.MINI_APP?t&&t({m:"taurus.common.prompt",args:c,onSuccess:l,onFail:u}):s===Q.ANDROID?t&&t(l,u,"taurus.common","prompt",c):s===Q.IOS&&t.callHandler("taurus.common.prompt",Object.assign({},c),(function(e){te.handleBridgeResponse(e,o,i)}))}else t&&t.call("prompt",c,(function(e){var n={errorCode:J.SUCCESS,result:{buttonIndex:e.ok?0:1,value:e.inputValue}};te.handleBridgeResponse(n,o,i)}))}function $o(e){return te.invoke("prompt",e)}function Xo(e){return te.invoke("pushWindow",e)}function ei(e){return te.invoke("readImageToBase64",e)}function ni(e){return te.invoke("reduceImageSize",e)}function oi(e){return te.invoke("removeStorageItem",e)}function ii(e,n){var o=n.resolve,i=n.reject,t=n.containerType,r=n.platformType,a=n.appType,s=n.context;if(t){var c=function(){e.onSuccess&&e.onSuccess(),o()},l=function(){e.onFail&&e.onFail(),i()};a===q.MINI_APP?s&&s({m:"biz.navigation.replace",args:e,onSuccess:c,onFail:l}):r===Q.ANDROID?s&&s(c,l,"biz.navigation","replace",e):r===Q.IOS&&s.callHandler("taurus.common.replacePage",Object.assign({},e),(function(){o()}))}else s&&s.call("replacePage",e,(function(){o()}))}function ti(e){return te.invoke("replacePage",e)}function ri(){return te.invoke("resetView")}function ai(e){return te.invoke("resumeAudio",e)}function si(e){return te.invoke("rotateView",e)}function ci(e){return te.invoke("scan",e)}function li(e){return te.invoke("searchOnMap",e)}function ui(e,n){var o=n.resolve,i=n.context;i&&i.call("sendOutData",function(e){return D({},e,{actionId:"",actionType:"0"})}(e),(function(){o()}))}function di(e){return te.invoke("cardSendOutData",e)}function pi(e){return te.invoke("setLocalScreenShotPolicy",e)}function fi(e){return te.invoke("setNavIcon",e)}function gi(e,n){var o=n.resolve,i=n.reject,t=n.context,r=n.containerType,a=n.appType,s=n.platformType,c=n.watch;if(r){var l=function(n){e.onSuccess&&e.onSuccess(),te.handleBridgeResponse(n,o,i)},u=function(e){te.handleBridgeResponse(e,o,i)};a===q.MINI_APP?t&&t({m:"biz.navigation.setLeft",args:e,onSuccess:l,onFail:u}):s===Q.ANDROID?t&&t(l,u,"biz.navigation","setLeft",e):s===Q.IOS&&t.callHandler("biz.navigation.setLeft",Object.assign({},e),(function(e){!c&&o(e)}))}else t&&t.call("setNavLeftText",e,(function(){o()}))}function vi(e){var n=An();return te.invoke("setNavLeftText",n===Z.isDingTalk?Object.assign({watch:!0,show:!0,control:!1,showIcon:!0,text:""},e):e,{dingTalkAPIName:n===Z.isDingTalk?"biz.navigation.setLeft":null})}function mi(e){var n=An();return te.invoke("setOptionMenu",n===Z.isDingTalk?Object.assign({watch:!0,show:!0,control:!1,showIcon:!0,text:""},e):e,n===Z.isDingTalk?{dingTalkAPIName:"biz.navigation.setRight"}:null)}function hi(e){return te.invoke("setProxyInfo",e)}function Ii(e){return te.invoke("setStorageItem",e)}function Ai(e,n){var o=n.resolve,i=n.reject,t=n.context,r=n.containerType,a=n.appType,s=n.platformType;if(r){var c=function(e){te.handleBridgeResponse(e,o,i)},l=function(e){te.handleBridgeResponse(e,o,i)};a===q.MINI_APP?t&&t({m:"biz.navigation.setTitle",args:e,onSuccess:c,onFail:l}):s===Q.ANDROID?t&&t(c,l,"biz.navigation","setTitle",e):s===Q.IOS&&t.callHandler("biz.navigation.setTitle",Object.assign({},e),(function(e){te.handleBridgeResponse(e,o,i)}))}else t&&t.call("setTitle",e,(function(){o()}))}function ki(e){return te.invoke("setTitle",e)}function bi(e){return te.invoke("shareFileToMessage",e)}function Pi(e){return te.invoke("shareImageToMessage",e)}function wi(e){return te.invoke("shareToMessage",e)}function yi(){return te.invoke("shootVideo")}function Si(e){return te.invoke("showActionSheet",e)}function Ci(e){return te.invoke("showCallMenu",e)}function Ti(e){return te.invoke("showDatePicker",e)}function Ri(e){return te.invoke("showDateTimePicker",e)}function xi(e){return te.invoke("showExtendModal",e)}function Oi(e){return te.invoke("showHomeBottomTab",e)}function Ei(e,n){var o=n.resolve,i=n.reject,t=n.context,r=n.containerType,a=n.appType,s=n.platformType;if(r){var c=function(e){te.handleBridgeResponse(e,o,i)},l=function(e){te.handleBridgeResponse(e,o,i)};a===q.MINI_APP?t&&t({m:"device.notification.showPreloader",args:e,onSuccess:c,onFail:l}):s===Q.ANDROID?t&&t(c,l,"device.notification","showPreloader",e):s===Q.IOS&&t.callHandler("device.notification.showPreloader",Object.assign({},e),(function(e){o(e)}))}else t&&t.call("showLoading",e,(function(){o()}))}function Di(e){return te.invoke("showLoading",e)}function Ni(e){return te.invoke("showModal",e)}function Bi(e){return te.invoke("showMultiSelect",e)}function _i(e){return te.invoke("showOnMap",e)}function Mi(){return te.invoke("showOptionMenu")}function Li(e){return te.invoke("showPlainInputUponKeyboard",e)}function Fi(e){return te.invoke("showQuickCallMenu",e)}function ji(e){return te.invoke("showSelect",e)}function zi(e){return te.invoke("showSignature",e)}function Wi(e){return te.invoke("showSocialShare",e)}function Hi(e){return te.invoke("showTimePicker",e)}function Ui(e,n){var o=n.resolve,i=n.reject,t=n.containerType,r=n.platformType,a=n.appType,s=n.context,c=Object.assign(e,{hidden:!1});if(t){var l=function(){e.onSuccess&&e.onSuccess(),o()},u=function(){e.onFail&&e.onFail(),i()};a===q.MINI_APP?s&&s({m:"biz.navigation.hideBar",args:c,onSuccess:l,onFail:u}):r===Q.ANDROID?s&&s(l,u,"biz.navigation","hideBar",c):r===Q.IOS&&s.callHandler("biz.navigation.hideBar",Object.assign({},c),(function(){o()}))}else s&&s.call("showTitlebar",c,(function(){o()}))}function Vi(){return te.invoke("showTitlebar")}function Gi(e){return te.invoke("startFaceRecognition",e)}function Ji(e,n){var o=n.resolve,i=n.reject,t=n.context,r=n.platformType,a=n.containerType,s=n.appType,c=te.registerEvent(Y.UPDATE_LOCATION,(function(n){var o=n.data;o.errorCode!==J.SUCCESS?e.onFail&&e.onFail(o):e.onSuccess&&e.onSuccess(o.result)}));if(a){var l=function(n){te.registerContinuesEvent(e.sceneId,c),te.handleBridgeResponse(n,o,i)},u=function(n){te.registerContinuesEvent(e.sceneId,c),te.handleBridgeResponse(n,o,i)};s===q.MINI_APP?(console.log("taurus.common.startGeolocation",e),t&&t({m:"taurus.common.startGeolocation",args:e,onSuccess:l,onFail:u})):r===Q.ANDROID?t&&t(l,u,"taurus.common","startGeolocation",e):r===Q.IOS&&t.callHandler("taurus.common.startGeolocation",Object.assign({},e),(function(n){te.registerContinuesEvent(e.sceneId,c),te.handleBridgeResponse(n,o,i)}))}else t&&t.call("startGeolocation",e,(function(n){te.registerContinuesEvent(e.sceneId,c),te.handleBridgeResponse(n,o,i)}))}function Ki(e){return te.invoke("startGeolocation",e)}function qi(e,n){var o=n.resolve,i=n.reject,t=n.context,r=n.containerType,a=n.appType,s=n.platformType,c=te.registerEvent(Y.UPDATE_NETWORK_STATUS,(function(n){var o=n.data;o.errorCode!==J.SUCCESS?e.onFail&&e.onFail(o):e.onSuccess&&e.onSuccess(o.result)}));if(r){var l=function(e){te.registerContinuesEvent(e.result.requestId,c),te.handleBridgeResponse(e,o,i)},u=function(e){te.registerContinuesEvent(e.result.requestId,c),te.handleBridgeResponse(e,o,i)};a===q.MINI_APP?t&&t({m:"taurus.common.startListenNetworkStatus",args:e,onSuccess:l,onFail:u}):s===Q.ANDROID?t&&t(l,u,"taurus.common","startListenNetworkStatus",e):s===Q.IOS&&t.callHandler("taurus.common.startListenNetworkStatus",Object.assign({},e),(function(e){te.registerContinuesEvent(e.result.requestId,c),te.handleBridgeResponse(e,o,i)}))}else t&&t.call("startListenNetworkStatus",e,(function(e){te.registerContinuesEvent(e.result.requestId,c),te.handleBridgeResponse(e,o,i)}))}function Qi(e){return te.invoke("startListenNetworkStatus",e)}function Yi(e){return te.invoke("startRecordAudio",e)}function Zi(e){return te.invoke("startTraceReport",e)}function $i(e){return te.invoke("startVPNApp",e)}function Xi(e){return te.invoke("startWatchShake",e)}function et(e){return te.invoke("stopAudio",e)}function nt(e,n){var o=n.resolve,i=n.reject,t=n.containerType,r=n.platformType,a=n.appType,s=n.context;if(t){var c=function(n){te.removeContinuesEvent(e.sceneId),te.handleBridgeResponse(n,o,i)},l=function(n){te.removeContinuesEvent(e.sceneId),te.handleBridgeResponse(n,o,i)};a===q.MINI_APP?s&&s({m:"taurus.common.stopGeolocation",args:e,onSuccess:c,onFail:l}):r===Q.ANDROID?s&&s(c,l,"taurus.common","stopGeolocation",e):r===Q.IOS&&s.callHandler("taurus.common.stopGeolocation",Object.assign({},e),(function(n){te.removeContinuesEvent(e.sceneId),te.handleBridgeResponse(n,o,i)}))}else s&&s.call("stopGeolocation",e,(function(n){te.removeContinuesEvent(e.sceneId),te.handleBridgeResponse(n,o,i)}))}function ot(e){return te.invoke("stopGeolocation",e)}function it(e,n){var o=n.resolve,i=n.reject,t=n.containerType,r=n.appType,a=n.platformType,s=n.context;if(t){var c=function(n){te.removeContinuesEvent(e.requestId),te.handleBridgeResponse(n,o,i)},l=function(n){te.removeContinuesEvent(e.requestId),te.handleBridgeResponse(n,o,i)};r===q.MINI_APP?s&&s({m:"taurus.common.stopListenNetworkStatus",args:e,onSuccess:c,onFail:l}):a===Q.ANDROID?s&&s(c,l,"taurus.common","stopListenNetworkStatus",e):a===Q.IOS&&s.callHandler("taurus.common.stopListenNetworkStatus",Object.assign({},e),(function(n){te.removeContinuesEvent(e.requestId),te.handleBridgeResponse(n,o,i)}))}else s&&s.call("stopListenNetworkStatus",e,(function(n){te.removeContinuesEvent(e.requestId),te.handleBridgeResponse(n,o,i)}))}function tt(e){return te.invoke("stopListenNetworkStatus",e)}function rt(){return te.invoke("stopPullToRefresh",{_apiName:"stopPullToRefresh"})}function at(e){return te.invoke("stopRecordAudio",e)}function st(e){return te.invoke("stopTraceReport",e)}function ct(e){return te.invoke("stopVPNApp",e)}function lt(){return te.invoke("stopWatchShake")}function ut(e){return te.invoke("subscribe",e)}function dt(){return te.invoke("takePhoto")}function pt(){return te.invoke("testProxy",{})}function ft(e,n){var o=n.resolve,i=n.reject,t=n.context,r=n.containerType,a=n.platformType,s=n.appType,c={type:"error"===e.icon?"fail":"success"===e.icon?"success":"none",content:e.text,duration:1e3*e.duration,taurusToastStyle:e.taurusToastStyle};if(r){var l=function(){te.handleBridgeResponse({errorCode:J.SUCCESS,result:{}},o,i)},u=function(e){te.handleBridgeResponse(e,o,i)};s===q.MINI_APP?t&&t({m:"taurus.common.toast",args:c,onSuccess:l,onFail:u}):a===Q.ANDROID?t&&t(l,u,"taurus.common","toast",c):a===Q.IOS&&t.callHandler("taurus.common.toast",Object.assign({},c),(function(){te.handleBridgeResponse({errorCode:J.SUCCESS,result:{}},o,i)}))}else t&&t.call("toast",c,(function(){te.handleBridgeResponse({errorCode:J.SUCCESS,result:{}},o,i)}))}function gt(e){return te.invoke("toast",e)}function vt(){return te.invoke("unlockWithSecurityVerification")}function mt(e){return te.invoke("unsubscribe",e)}function ht(e){return te.invoke("dgUploadFile",D({},e,{_apiName:"uploadFile"}))}function It(e){return te.invoke("uploadFileByType",e)}function At(e){return new Promise((function(n,o){my.uploadFile(D({},e,{success:function(e){n(e)},fail:function(e){o(e)}}))}))}function kt(e){return te.invoke("uploadRemoteFileToDisk",e)}function bt(e){return te.invoke("ut",e)}function Pt(e){return te.invoke("vibrate",e)}te.registerAPI("getWifiStatus",{mobile:!0,mini:!0}),fo.version={android:"1.3.5",ios:"1.3.5"},te.registerAPI("getWorkbenchContext",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"getWorkbenchContext",e)}}),go.version={android:"2.1.10",ios:"2.1.10"},te.registerAPI("h5PageBack",{mobile:function(e,n){var o=n.resolve,i=n.reject,t=n.context,r=n.containerType,a=n.platformType;r?a===Q.ANDROID?t&&t((function(e){te.handleBridgeResponse(e,o,i)}),(function(e){te.handleBridgeResponse(e,o,i)}),"biz.navigation","goBack",e):a===Q.IOS&&t.callHandler("biz.navigation.goBack",Object.assign({},e),(function(e){o(e)})):t&&t.call("h5PageBack",{_apiName:"goBack"},(function(){o()}))}}),vo.version={android:"1.3.0",ios:"1.3.9"},te.registerAPI("hideLoading",{mini:mo,mobile:mo}),ho.version={android:"1.3.2",ios:"1.3.2"},te.registerAPI("hideOptionMenu",{mobile:function(e,n){var o=n.resolve,i=n.reject,t=n.context,r=n.containerType,a=(n.appType,n.platformType);if(r){var s={show:!1,control:!0,text:""};a===Q.ANDROID?t&&t((function(e){te.handleBridgeResponse(e,o,i)}),(function(e){te.handleBridgeResponse(e,o,i)}),"biz.navigation","setRight",s):a===Q.IOS&&t.callHandler("biz.navigation.setRight",Object.assign({},s),(function(e){o(e)}))}else t&&t.call("hideOptionMenu",e,(function(){o()}))}}),Io.version={android:"1.1.0",ios:"1.1.0"},te.registerAPI("hideTitlebar",{mini:Ao,mobile:Ao}),ko.version={android:"2.1.0",ios:"2.1.0"},te.registerAPI("isDownloadFileExist",{pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.isLocalFileExist",e)}}),bo.version={pc:"1.3.5"},te.registerAPI("joinScheduleConf",{mini:Po,mobile:Po,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.conference.joinScheduleConf",D({},e))}}),wo.version={android:"3.7.5",ios:"3.7.5",pc:"3.7.5"},te.registerAPI("joinVideoConf",{mini:yo,mobile:yo,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.conference.joinVideoConf",D({},e))}}),So.version={android:"3.7.5",ios:"3.7.5",pc:"3.7.5"},te.registerAPI("joinVideoMeeting",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.conference.joinVideoMeeting",D({},e))}}),Co.version={android:"3.9.0",ios:"3.9.0",pc:"3.9.0"},te.registerAPI("locateOnMap",{mobile:!0,mini:!0}),To.version={android:"1.3.0",ios:"1.3.0"},te.registerAPI("onAudioPlayEnd",{mini:!0,mobile:!0}),Ro.version={android:"1.6.2",ios:"1.6.2"},te.registerAPI("onRecordAudioEnd",{mini:!0,mobile:!0}),xo.version={android:"1.3.0",ios:"1.3.0"},te.registerAPI("openApiInvoker",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"openApiInvoker",e)}}),Oo.version={ios:"3.0.1",android:"3.0.1",pc:"3.0.1"},te.registerAPI("openApp",{mini:!0,mobile:!0}),Eo.version={android:"1.3.2",ios:"1.3.2"},te.registerAPI("openBrowser",{mini:!0,mobile:!0}),Do.version={android:"1.2.3"},te.registerAPI("openChat",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"internal.chat.toConversation",{cid:e.chatId})}}),No.version={android:"1.2.0",ios:"1.2.0",pc:"1.2.0"},te.registerAPI("openDownloadFile",{pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.openLocalFile",e)}}),Bo.version={pc:"1.3.5"},te.registerAPI("openLink",{mini:_o,mobile:_o,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.openLink",e)}}),Mo.version={android:"1.2.0",ios:"1.2.0",pc:"1.2.0"},te.registerAPI("openPage",{mini:!0,mobile:!0}),Lo.version={android:"1.1.0",ios:"1.1.0"},te.registerAPI("dgOpenApp",{mobile:!0,mini:!0}),Fo.version={android:"*******",ios:"*******"},te.registerAPI("openSlidePanel",{pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.openSlidePanel",e)}}),jo.version={pc:"1.3.5"},te.registerAPI("openWatermarkCamera",{mobile:!0,mini:!0}),zo.version={android:"1.3.7",ios:"1.3.7"},te.registerAPI("pauseAudio",{mini:!0,mobile:!0}),Wo.version={android:"1.3.0",ios:"1.3.0"},te.registerAPI("pickChat",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.chat.pickConversation",e)}}),Ho.version={android:"1.2.0",ios:"1.2.0",pc:"2.9.0"},te.registerAPI("pickChatByCorpId",{mini:!0,mobile:!0}),te.registerAPI("pickGroupChat",{pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.contact.pickGroupChat",e)}}),Uo.version={pc:"2.10.30"},te.registerAPI("pickGroupConversation",{mini:Vo,mobile:Vo}),Go.version={android:"2.8.0",ios:"2.8.0"},te.registerAPI("playAudio",{mini:!0,mobile:!0}),Jo.version={android:"1.3.0",ios:"1.3.0"},te.registerAPI("previewDoc",{mini:!0,mobile:!0}),Ko.version={android:"1.1.0",ios:"1.1.0"},te.registerAPI("previewImage",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.previewImage",e)}}),qo.version={android:"1.2.0",ios:"1.2.0",pc:"1.2.0"},te.registerAPI("printFile",{mini:!0,mobile:!0}),Qo.version={android:"2.2.10"},te.registerAPI("printNativeLog",{mini:!0,mobile:!0}),Yo.version={android:"1.9.4",ios:"1.9.4"},te.registerAPI("prompt",{mini:Zo,mobile:Zo,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"device.notification.prompt",e)}}),$o.version={android:"1.3.2",ios:"1.3.2",pc:"1.3.2"},te.registerAPI("pushWindow",{mini:!0,mobile:!0}),Xo.version={android:"2.9.7",ios:"2.9.7"},te.registerAPI("readImageToBase64",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"readImageToBase64",e)}}),ei.version={ios:"2.1.0",android:"2.1.0",pc:"2.1.0"},function(e){e[e.ADJUST_BY_NET=0]="ADJUST_BY_NET",e[e.LOW_QUALITY=1]="LOW_QUALITY",e[e.MID_QUALITY=2]="MID_QUALITY",e[e.HIGH_QUALITY=3]="HIGH_QUALITY",e[e.NOT_COMPRESSED=4]="NOT_COMPRESSED",e[e.CUSTOM=5]="CUSTOM"}(ao||(ao={})),te.registerAPI("reduceImageSize",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"reduceImageSize",e)}}),ni.version={ios:"2.1.0",android:"2.1.0",pc:"2.1.0"},te.registerAPI("removeStorageItem",{mobile:!0,mini:!0}),oi.version={android:"*******",ios:"*******"},te.registerAPI("replacePage",{mini:ii,mobile:ii}),ti.version={android:"1.3.2",ios:"1.3.2"},te.registerAPI("resetView",{mini:!0,mobile:!0}),ri.version={android:"1.3.0",ios:"1.3.0"},te.registerAPI("resumeAudio",{mini:!0,mobile:!0}),ai.version={android:"1.3.0",ios:"1.3.0"},te.registerAPI("rotateView",{mini:!0,mobile:!0}),si.version={android:"1.3.0",ios:"1.3.0"},te.registerAPI("scan",{mini:!0,mobile:!0}),ci.version={android:"1.3.2",ios:"1.3.2"},te.registerAPI("searchOnMap",{mini:!0,mobile:!0}),li.version={android:"1.3.2",ios:"1.3.2"},te.registerAPI("cardSendOutData",{mini:ui,mobile:ui}),di.version={android:"2.5.0",ios:"2.5.0"},function(e){e.DEFAULT="0",e.DISABLEALL="1",e.ENABLEALL="2"}(so||(so={})),te.registerAPI("setLocalScreenShotPolicy",{mini:!0,mobile:!0}),pi.version={android:"2.12.12",ios:"2.12.12"},te.registerAPI("setNavIcon",{mobile:function(e,n){var o=n.resolve,i=n.reject,t=n.containerType,r=n.platformType,a=n.context;t?r===Q.ANDROID?a&&a((function(n){e.onSuccess&&e.onSuccess(),o()}),(function(e){i()}),"biz.navigation","setIcon",e):r===Q.IOS&&a.callHandler("biz.navigation.setIcon",Object.assign({},e),(function(e){o()})):a&&a.call("setNavIcon",e,(function(e){o()}))}}),fi.version={android:"1.3.0",ios:"1.3.0"},te.registerAPI("setNavLeftText",{mini:gi,mobile:gi,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.navigation.setLeft",e)}}),vi.version={ios:"1.2.0",pc:"1.2.0"},te.registerAPI("setOptionMenu",{mobile:function(e,n){var o=n.resolve,i=n.reject,t=n.context,r=n.containerType,a=n.platformType;if(r){var s,c,l={text:e.title,show:void 0===e.show||e.show,control:void 0===e.control||e.control};if(a===Q.ANDROID)t&&t((function(n){e.onSuccess&&e.onSuccess(n),o(n)}),(function n(o){e.onFail&&e.onFail(o),i(n)}),"biz.navigation",(null==e||null===(s=e.menus)||void 0===s?void 0:s.length)>1?"setMenu":"setRight",(null==e||null===(c=e.menus)||void 0===c?void 0:c.length)>1?e:l);else if(a===Q.IOS){var u,d;t.callHandler((null==e||null===(u=e.menus)||void 0===u?void 0:u.length)>1?"biz.navigation.setMenu":"biz.navigation.setRight",Object.assign({},(null==e||null===(d=e.menus)||void 0===d?void 0:d.length)>1?e:l),(function(){o()}))}}else t&&t.call("setOptionMenu",e,(function(){o()}))}}),mi.version={android:"1.1.0",ios:"1.1.0"},te.registerAPI("setProxyInfo",{pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"net.util.setProxyInfo",e)}}),hi.version={pc:"2.10.0"},te.registerAPI("setStorageItem",{mobile:!0,mini:!0}),Ii.version={android:"*******",ios:"*******"},function(e){e.TRUE="true",e.FALSE="false"}(co||(co={})),te.registerAPI("setTitle",{mini:Ai,mobile:Ai,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.navigation.setTitle",e)}}),ki.version={android:"1.2.0",ios:"1.2.0",pc:"1.2.0"},te.registerAPI("shareFileToMessage",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"shareFileToMessage",e)}}),bi.version={android:"1.8.2",ios:"1.8.2",pc:"1.8.2"},te.registerAPI("shareImageToMessage",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"shareImageToMessage",e)}}),Pi.version={android:"1.8.2",ios:"1.8.2",pc:"1.8.2"},te.registerAPI("shareToMessage",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.share",e)}}),wi.version={android:"1.3.5",ios:"1.3.5",pc:"1.3.5"},te.registerAPI("shootVideo",{mini:!0,mobile:!0}),yi.version={android:"1.3.5",ios:"1.3.5"},te.registerAPI("showActionSheet",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"device.notification.actionSheet",e)}}),Si.version={android:"1.3.2",ios:"1.3.2",pc:"1.3.2"},te.registerAPI("showCallMenu",{mini:!0,mobile:!0}),Ci.version={android:"1.3.9",ios:"1.3.9"},te.registerAPI("showDatePicker",{mobile:!0,mini:!0}),Ti.version={android:"1.3.0",ios:"1.3.0"},te.registerAPI("showDateTimePicker",{mini:!0,mobile:!0}),Ri.version={android:"1.3.10",ios:"1.3.10"},te.registerAPI("showExtendModal",{mini:!0,mobile:!0}),xi.version={android:"1.3.5",ios:"1.3.5"},te.registerAPI("showHomeBottomTab",{mobile:!0}),Oi.version={android:"1.3.0",ios:"1.3.0"},te.registerAPI("showLoading",{mini:Ei,mobile:Ei}),Di.version={android:"1.3.2",ios:"1.3.2"},te.registerAPI("showModal",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.openModal",e)}}),Ni.version={android:"1.3.5",ios:"1.3.5",pc:"1.3.5"},te.registerAPI("showMultiSelect",{mini:!0,mobile:!0}),Bi.version={android:"1.3.10",ios:"1.3.10"},te.registerAPI("showOnMap",{mini:!0,mobile:!0}),_i.version={android:"1.3.2",ios:"1.3.2"},te.registerAPI("showOptionMenu",{mobile:function(e,n){var o=n.resolve,i=n.reject,t=n.context,r=n.platformType;n.containerType?r===Q.ANDROID?t&&t((function(){o()}),(function(){i()}),"taurus.common","showOptionMenu",e):r===Q.IOS&&t.callHandler("taurus.common.showOptionMenu",Object.assign({},e),(function(){o()})):t&&t.call("showOptionMenu",e,(function(){o()}))}}),Mi.version={android:"1.1.0",ios:"1.1.0"},te.registerAPI("showPlainInputUponKeyboard",{mobile:!0,mini:!0}),Li.version={android:"1.3.0",ios:"1.3.0"},te.registerAPI("showQuickCallMenu",{mini:!0,mobile:!0}),Fi.version={android:"1.6.2",ios:"1.6.2"},te.registerAPI("showSelect",{mini:!0,mobile:!0}),ji.version={android:"1.3.2",ios:"1.3.2"},te.registerAPI("showSignature",{mobile:!0}),zi.version={android:"1.3.4"},te.registerAPI("showSocialShare",{mini:!0,mobile:!0}),Wi.version={android:"1.2.0.10",ios:"1.2.0.10"},te.registerAPI("showTimePicker",{mobile:!0,mini:!0}),Hi.version={android:"1.3.0",ios:"1.3.0"},te.registerAPI("showTitlebar",{mini:Ui,mobile:Ui}),Vi.version={android:"2.1.0",ios:"2.1.0"},te.registerAPI("startFaceRecognition",{mini:!0,mobile:!0}),Gi.version={android:"1.8.2",ios:"1.8.2"},te.registerAPI("startGeolocation",{mobile:Ji,mini:Ji}),Ki.version={android:"1.3.2",ios:"1.3.2"},te.registerAPI("startListenNetworkStatus",{mobile:qi,mini:qi}),Qi.version={android:"*******",ios:"*******"},te.registerAPI("startRecordAudio",{mini:!0,mobile:!0}),Yi.version={android:"1.3.0",ios:"1.3.0"},te.registerAPI("startTraceReport",{mobile:function(e,n){var o=n.resolve,i=n.reject,t=n.context,r=n.containerType,a=n.platformType,s=n.appType,c=te.registerEvent(Y.UPDATE_TRACE,(function(n){var o=n.data;o.errorCode&&o.errorCode!==J.SUCCESS?e.onFail&&e.onFail(o):e.onSuccess&&e.onSuccess(o.result||o)}));if(r){var l=function(n){te.registerContinuesEvent(e.traceId,c),te.handleBridgeResponse(n,o,i)},u=function(n){te.registerContinuesEvent(e.traceId,c),te.handleBridgeResponse(n,o,i)};s===q.MINI_APP?t&&t({m:"taurus.common.startTraceReport",args:e,onSuccess:l,onFail:u}):a===Q.ANDROID?t&&t(l,u,"taurus.common","startTraceReport",e):a===Q.IOS&&t.callHandler("taurus.common.startTraceReport",Object.assign({},e),(function(n){te.registerContinuesEvent(e.traceId,c),te.handleBridgeResponse(n,o,i)}))}else t&&t.call("startTraceReport",e,(function(n){te.registerContinuesEvent(e.traceId,c),te.handleBridgeResponse(n,o,i)}))},mini:!0}),Zi.version={android:"1.3.4",ios:"1.3.4"},te.registerAPI("startVPNApp",{mini:!0,mobile:!0}),$i.version={android:"1.6.0",ios:"1.6.0"},te.registerAPI("startWatchShake",{mobile:function(e,n){var o=n.resolve,i=n.reject,t=n.context,r=n.containerType,a=n.platformType,s=te.registerEvent(Y.ON_SHAKE,(function(){e.onSuccess&&e.onSuccess()}));r?a===Q.ANDROID?t&&t((function(n){e.onSuccess&&e.onSuccess(),o()}),(function(e){i()}),"taurus.common","startWatchShake",e):a===Q.IOS&&t.callHandler("taurus.common.startWatchShake",Object.assign({},e),(function(e){o()})):t&&t.call("startWatchShake",e,(function(e){te.registerContinuesEvent("shake",s),te.handleBridgeResponse(e,o,i)}))}}),Xi.version={android:"1.6.2",ios:"1.6.2"},te.registerAPI("stopAudio",{mini:!0,mobile:!0}),et.version={android:"1.3.0",ios:"1.3.0"},te.registerAPI("stopGeolocation",{mobile:nt,mini:nt}),ot.version={android:"1.3.2",ios:"1.3.2"},te.registerAPI("stopListenNetworkStatus",{mini:it,mobile:it}),tt.version={android:"*******",ios:"*******"},te.registerAPI("stopPullToRefresh",{mobile:function(e,n){var o=n.resolve,i=n.reject,t=n.context,r=n.containerType,a=n.platformType;r?a===Q.ANDROID?t&&t((function(){o()}),(function(){i()}),"ui.pullToRefresh","stop",e):a===Q.IOS&&t.callHandler("ui.pullToRefresh.stop",Object.assign({},e),(function(){o()})):t&&t.call("restorePullToRefresh",e,(function(){o()}))}}),rt.version={android:"1.3.0",ios:"1.3.0"},te.registerAPI("stopRecordAudio",{mini:!0,mobile:!0}),at.version={android:"1.3.0",ios:"1.3.0"},te.registerAPI("stopTraceReport",{mobile:function(e,n){var o=n.resolve,i=n.reject,t=n.containerType,r=n.platformType,a=n.context;t?r===Q.ANDROID?a&&a((function(n){te.removeContinuesEvent(e.traceId),te.handleBridgeResponse(n,o,i)}),(function(n){te.removeContinuesEvent(e.traceId),te.handleBridgeResponse(n,o,i)}),"taurus.common","stopTraceReport",e):r===Q.IOS&&a.callHandler("taurus.common.stopTraceReport",Object.assign({},e),(function(n){te.removeContinuesEvent(e.traceId),te.handleBridgeResponse(n,o,i)})):a&&a.call("stopTraceReport",e,(function(n){te.removeContinuesEvent(e.traceId),te.handleBridgeResponse(n,o,i)}))}}),st.version={android:"1.3.4",ios:"1.3.4"},te.registerAPI("stopVPNApp",{mini:!0,mobile:!0}),ct.version={android:"1.6.0",ios:"1.6.0"},te.registerAPI("stopWatchShake",{mobile:function(e,n){var o=n.resolve,i=n.reject,t=n.containerType,r=n.platformType,a=n.context;t?r===Q.ANDROID?a&&a((function(n){e.onSuccess&&e.onSuccess(),o()}),(function(e){i()}),"taurus.common","stopWatchShake",e):r===Q.IOS&&a.callHandler("taurus.common.stopWatchShake",Object.assign({},e),(function(e){o()})):a&&a.call("stopWatchShake",e,(function(e){te.removeContinuesEvent("shake"),te.handleBridgeResponse(e,o,i)}))}}),lt.version={android:"1.6.2",ios:"1.6.2"},te.registerAPI("subscribe",{mobile:function(e,n){var o=n.resolve,i=n.reject,t=n.context,r=n.containerType,a=n.platformType,s=!1;r?a===Q.ANDROID?t&&t((function(n){s?(e.onSuccess||e.onFail)&&("0"!==n.errorCode?e.onFail&&e.onFail(n):e.onSuccess&&e.onSuccess(n.result)):(s=!0,te.handleBridgeResponse(n,o,i))}),(function(n){s?e.onFail&&e.onFail(n):(s=!0,te.handleBridgeResponse(n,o,i))}),"taurus.common","subscribe",e):a===Q.IOS&&t.callHandler("taurus.common.subscribe",Object.assign({},e),(function(n){s?(e.onSuccess||e.onFail)&&("0"!==n.errorCode?e.onFail&&e.onFail(n):e.onSuccess&&e.onSuccess(n.result)):(s=!0,te.handleBridgeResponse(n,o,i))})):t&&t.call("subscribe",e,(function(n){s?(e.onSuccess||e.onFail)&&("0"!==n.errorCode?e.onFail&&e.onFail(n):e.onSuccess&&e.onSuccess(n.result)):(s=!0,te.handleBridgeResponse(n,o,i))}))}}),ut.version={android:"1.6.0",ios:"1.6.0"},te.registerAPI("takePhoto",{mini:!0,mobile:!0}),dt.version={android:"1.3.5",ios:"1.3.5"},te.registerAPI("testProxy",{pc:function(e,n){void 0===e&&(e={}),window.dingtalk.platform.invokeAPI(n.msgId,"net.util.testProxy",e)}}),pt.version={pc:"2.10.0"},te.registerAPI("toast",{mobile:ft,mini:ft,pc:function(e,n){var o=e.icon,i=e.text,t=e.duration,r=e.delay;window.dingtalk.platform.invokeAPI(n.msgId,"device.notification.toast",{type:o,text:i,duration:t,delay:r})}}),gt.version={android:"1.3.2",ios:"1.3.2"},te.registerAPI("unlockWithSecurityVerification",{mini:!0,mobile:!0}),vt.version={android:"*******",ios:"*******"},te.registerAPI("unsubscribe",{mobile:!0}),mt.version={android:"1.6.0",ios:"1.6.0"},te.registerAPI("dgUploadFile",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.dgUploadFile",e)}}),ht.version={android:"1.3.2",ios:"1.3.2",pc:"1.3.6"},te.registerAPI("uploadFileByType",{mini:!0,mobile:!0}),It.version={android:"1.3.0",ios:"1.3.0"},te.registerAPI("uploadFile",{mini:!0}),At.version={android:"1.6.2",ios:"1.6.2"},te.registerAPI("uploadRemoteFileToDisk",{mini:!0,mobile:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.uploadRemoteFileToDisk",e)}}),kt.version={android:"1.6.0",ios:"1.6.0",pc:"2.6.0"},te.registerAPI("ut",{pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.ut",e)}}),bt.version={pc:"1.3.10"},te.registerAPI("vibrate",{mini:!0,mobile:!0}),Pt.version={android:"1.3.1",ios:"1.3.1"};var wt={alert:ae,authConfig:se,bizContactDepartmentsPickerExternal:le,bizCustomContactChooseExternal:de,bizCustomContactMultipleChooseExternal:fe,callPhone:ge,canIUse:function(e){return Ce.apply(this,arguments)},checkVPNAppInstalled:Te,checkVPNAppOnline:Re,chooseContact:xe,chooseContactWithComplexPicker:Ee,chooseDateRangeWithCalendar:De,chooseDayWithCalendar:Ne,chooseDepartments:Be,chooseFile:_e,chooseHalfDayWithCalendar:Me,chooseImage:Le,chooseInterconnectionChat:Fe,chooseLocalImage:je,chooseSpaceDir:ze,chooseTimeWithCalendar:We,chooseVideo:He,closePage:Ve,complexPickerAdmin:Ge,confirm:Je,copyToClipboard:Ke,createChatGroup:qe,createDing:Qe,createDingV2:Ye,createVideoConf:$e,createVideoMeeting:Xe,dealWithBackAction:en,disableClosePage:nn,disablePullToRefresh:on,disableWebviewBounce:tn,downloadAudio:rn,downloadFile:ln,enablePullToRefresh:un,enableVpn:dn,enableWebviewBounce:pn,exclusiveInvoke:fn,faceComparison:gn,faceRecognition:vn,getAppInstallStatus:mn,getAuthCode:hn,getConfig:In,getContainerType:An,getDeviceId:kn,getFromClipboard:bn,getGeolocation:Pn,getGeolocationStatus:wn,getHotspotInfo:yn,getLanguageSetting:Sn,getLoginUser:Cn,getNetworkType:Tn,getPhoneInfo:Rn,getProxyInfo:xn,getStorageItem:On,getTraceStatus:En,getUUID:Dn,getUserAgent:Fn,getWaterMark:no,getWaterMarkConfigV2:oo,getWaterMarkV2:function(e){return new Promise((function(n,o){ve().then((function(i){-1!==he(i.version,"2.8.0")?oo({pageInfo:e}).then((function(i){try{var t=po(i,e);n(t)}catch(r){o(r)}})):jn({pageInfo:e}).then((function(i){try{var t=eo(i,e);n(t)}catch(r){o(r)}}))})).catch((function(){jn({pageInfo:e}).then((function(i){try{var t=eo(i,e);n(t)}catch(r){o(r)}}))}))}))},getWifiStatus:fo,getWorkbenchContext:go,goBack:vo,hideLoading:ho,hideOptionMenu:Io,hideTitleBar:ko,isDownloadFileExist:bo,joinScheduleConf:wo,joinVideoConf:So,joinVideoMeeting:Co,locateOnMap:To,on:function(e,n){return te.registerEvent(e,n)},onAudioPlayEnd:Ro,onRecordAudioEnd:xo,openApiInvoker:Oo,openApp:Eo,openBrowser:Do,openChat:No,openDownloadFile:Bo,openLink:Mo,openPage:Lo,openSchemeUrl:Fo,openSlidePanel:jo,openWatermarkCamera:zo,pauseAudio:Wo,pickChat:Ho,pickChatByCorpId:function(e){return te.invoke("pickChatByCorpId",e)},pickGroupChat:Uo,pickGroupConversation:Go,playAudio:Jo,previewDoc:Ko,previewImage:qo,printFile:Qo,printNativeLog:Yo,prompt:$o,pushWindow:Xo,readImageToBase64:ei,ready:function(e){"function"==typeof e?te.onReady(e):console.error("dd.ready's param must be function! ")},reduceImageSize:ni,removeStorageItem:oi,replacePage:ti,resetView:ri,resumeAudio:ai,rotateView:si,scan:ci,searchOnMap:li,sendOutData:di,setLocalScreenShotPolicy:pi,setNavIcon:fi,setNavLeftText:vi,setOptionMenu:mi,setProxyInfo:hi,setStorageItem:Ii,setTitle:ki,shareFileToMessage:bi,shareImageToMessage:Pi,shareToMessage:wi,shootVideo:yi,showActionSheet:Si,showCallMenu:Ci,showDatePicker:Ti,showDateTimePicker:Ri,showExtendModal:xi,showHomeBottomTab:Oi,showLoading:Di,showModal:Ni,showMultiSelect:Bi,showOnMap:_i,showOptionMenu:Mi,showPlainInputUponKeyboard:Li,showQuickCallMenu:Fi,showSelect:ji,showSignature:zi,showSocialShare:Wi,showTimePicker:Hi,showTitleBar:Vi,startFaceRecognition:Gi,startGeolocation:Ki,startListenNetworkStatus:Qi,startRecordAudio:Yi,startTraceReport:Zi,startVPNApp:$i,startWatchShake:Xi,stopAudio:et,stopGeolocation:ot,stopListenNetworkStatus:tt,stopPullToRefresh:rt,stopRecordAudio:at,stopTraceReport:st,stopVPNApp:ct,stopWatchShake:lt,subscribe:ut,takePhoto:dt,testProxy:pt,toast:gt,unlockWithSecurityVerification:vt,unsubscribe:mt,uploadFile:ht,uploadFileByType:It,uploadLocalFile:At,uploadRemoteFileToDisk:kt,ut:bt,version:ve,vibrate:Pt};if(te.getAppType()===q.MINI_APP)wt=new Proxy(wt,{get:function(e,n,o){return n in wt?Reflect.get(e,n,o):function(e,n){if(e)return function(o){return"function"==typeof o||n.includes("Sync")||n.startsWith("create")?e(o):new Promise((function(n,i){e(D({},o,{success:function(e){n(e)},fail:function(e){i(e)}}))}))}}(Reflect.get(my,n,o),n)}});else{window.dd&&console.warn("已经存在 window.dd 变量，引入 gdt-jsapi 会修改 window.dd 的值。");try{Object.defineProperty(window,"dd",{value:wt,writable:!0})}catch(cn){console.error(cn)}window.gdt&&console.warn("已经存在 window.gdt 变量，引入 gdt-jsapi 会修改 window.gdt 的值。");try{Object.defineProperty(window,"gdt",{value:wt,writable:!0})}catch(cn){console.error(cn)}}const yt=wt,St={class:"premises-page"},Ct={style:{"text-align":"center"}},Tt={class:"title",style:{height:"24px","line-height":"24px",margin:"0 auto",color:"#0082ef","font-size":"20px","text-align":"center"}},Rt={class:"icon","aria-hidden":"true",style:{height:"24px",width:"29px","vertical-align":"top","margin-right":"8px",display:"inline-block"}},xt={class:"submit-btn-wrapper"},Ot={key:0,class:"premises-tip"};const Et=e({name:"OAuth2Premises",setup(){const e=n(),t=o(""),r=o("专有钉钉"),a=o(!1),s=o(!0),c=o(""),l=o({callback_url:"",state:""}),u=()=>new Promise(((n,o)=>{yt.ready((function(){yt.getAuthCode({corpId:e.query.corpId}).then((e=>{n({errorCode:0,data:e})})).catch((e=>{n({errorCode:1,data:e})}))}))})),d=n=>{const o=[];return n.state=e.query.state||"",Object.keys(n).forEach((e=>{o.push(`${e}=${n[e]}`)})),l.value.callback_url+"?"+o.join("&")},p=()=>{const e=l.value.callback_url+"?state="+l.value.state;return`taurus://taurusclient/action/open_app?type=1&offline=false&url=${encodeURIComponent(e)}`};return i((()=>{(async()=>{s.value=window.self===window.top,a.value=!0;const n=await u();if(a.value=!1,e.query.state){const n=E(e.query.state);l.value=JSON.parse(n),l.value.state=e.query.state}0===n.errorCode?window.location.href=d(n.data):t.value=p()})()})),{url:t,appName:r,loading:a,isAuthState:s,authKey:c,routeHandle:()=>{if(console.log("isAuthState:",s.value,"url:",t.value),s.value||a.value)return;const e={event:"wakeup-app",params:{url:t.value,authKey:c.value}};console.log("peurl",t.value,l.value.state),window.parent.postMessage(e,window.location.origin)}}}},[["render",function(e,n,o,i,f,g){const v=t("base-button");return r(),a("div",St,[s("div",Ct,[s("span",Tt,[(r(),a("svg",Rt,n[0]||(n[0]=[s("use",{"xlink:href":"#icon-auth-zhezhendingmobile"},null,-1)]))),c(" "+l(i.appName),1)])]),s("div",xt,[u(v,{type:"primary",class:"login_submit_button","native-type":"submit",onClick:i.routeHandle},{default:d((()=>[c(l(i.isAuthState?"正在获取授权信息":"授权登录"),1)])),_:1},8,["onClick"])]),i.isAuthState?p("",!0):(r(),a("span",Ot,"若打开失败，请先安装"+l(i.appName)+"App",1))])}]]);export{Et as default};
