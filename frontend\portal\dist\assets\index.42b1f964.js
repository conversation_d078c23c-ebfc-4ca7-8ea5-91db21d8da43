/*! 
 Build based on gin-vue-admin 
 Time : 1754879545000 */
import a from"./header.165b7e39.js";import e from"./menu.bb2c1778.js";import{C as s,h as t,a as i,b as o,j as l,d as r,k as n}from"./index.ab3e73c8.js";import"./logo.b56ac4ae.js";const c={class:"layout-page"},u={class:"layout-wrap"},m={id:"layoutMain",class:"layout-main"},d=Object.assign({name:"Client"},{setup:d=>(s.initIpcClient(),(s,d)=>{const p=t("router-view");return i(),o("div",c,[l(a),r("div",u,[l(e),r("div",m,[(i(),n(p,{key:s.$route.fullPath,style:{height:"100%",display:"flex","flex-direction":"column"}}))])])])})});export{d as default};
