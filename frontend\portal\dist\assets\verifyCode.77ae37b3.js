/*! 
 Build based on gin-vue-admin 
 Time : 1754834760000 */
import{r as e,a2 as a,c as t,f as n,h as s,a as l,b as o,d as r,t as i,n as u,y as c,j as d,w as p,i as y,a3 as f,l as m,k as v,_ as h,p as g,M as _}from"./index.0f69a27d.js";const I={class:"verify-code"},k={class:"title"},x={key:0},C={key:0,class:"message-text"},b={key:1,class:"message-text"},T={key:2,class:"mt-4",style:{"margin-bottom":"25px"}},w={style:{"text-align":"center"}},V={key:1,style:{width:"450px"}},S={key:0},q=["src"],N={key:1},j={style:{"text-align":"center","margin-top":"30px"}},K=h(Object.assign({name:"VerifyCode"},{props:{authInfo:{type:Object,default:function(){return{}}},authId:{type:String,default:function(){return""}},userName:{type:String,default:function(){return""}},lastId:{type:String,default:function(){return""}},secondaryType:{type:String,default:"sms"}},emits:["verification-success","back","cancel"],setup(h,{emit:K}){const O=e(""),A=e(""),P=a("userName"),U=e(!1),B=h,E=()=>{U.value=!0};console.log("verifyCode组件接收到的属性:",{secondaryType:B.secondaryType,authInfo:B.authInfo,canVerify:"email"===B.secondaryType?!1!==B.authInfo.hasEmail:!1!==B.authInfo.notPhone});const G=K,L=t((()=>void 0!==B.authInfo.hasContactInfo?B.authInfo.hasContactInfo:(B.secondaryType,!1!==B.authInfo.hasContactInfo))),M=e(60);let $;const z=()=>{clearInterval($)},D=async()=>{if(!L.value)return;const e={uniq_key:B.authInfo.uniqKey,idp_id:B.authId};try{const a=await g(e);200===a.status&&-1!==a.data.code?(M.value=60,$=setInterval((()=>{M.value--,0===M.value&&z()}),1e3)):(_({showClose:!0,message:a.data.msg,type:"error"}),M.value=0)}catch(a){_({showClose:!0,message:"发送验证码失败",type:"error"}),M.value=0}};D();const F=n(),H=async()=>{const e={uniq_key:B.authInfo.uniqKey,auth_code:A.value,user_name:B.userName||P.value,idp_id:B.authId,redirect_uri:"hello world",grant_type:"implicit",client_id:"client_portal",totp_key:O.value},a=await F.LoginIn(e,"accessory");-1!==a.code&&G("verification-success",a)},J=()=>{G("cancel")},Q=t((()=>{switch(B.secondaryType){case"email":return"邮件认证";case"sms":return"短信认证";case"totp":return"令牌口令"}return"令牌口令"}));return(e,a)=>{const t=s("base-button"),n=s("base-input");return l(),o("div",I,[r("div",{style:u({top:"10px",textAlign:"center",marginBottom:"totp"===h.secondaryType?"0px":"25px"})},[r("span",k,i(Q.value),1)],4),"totp"!==h.secondaryType?(l(),o("div",x,[L.value?(l(),o("div",C,"验证码已发送至您账号("+i(h.userName||c(P))+")关联的"+i("email"===h.secondaryType?"邮箱":"手机")+"，请注意查收",1)):(l(),o("div",b,"您的账号("+i(h.userName||c(P))+")未关联"+i("email"===h.secondaryType?"邮箱":"手机号码")+"，请联系管理员！",1)),L.value?(l(),o("div",T,[d(n,{modelValue:A.value,"onUpdate:modelValue":a[0]||(a[0]=e=>A.value=e),placeholder:"email"===h.secondaryType?"邮箱验证码":"短信验证码",class:"input-with-select",onKeyup:f(H,["enter"])},{append:p((()=>[d(t,{disabled:M.value>0,type:"info",onClick:D},{default:p((()=>[y("重新发送 "+i(M.value>0?`(${M.value}秒)`:""),1)])),_:1},8,["disabled"])])),_:1},8,["modelValue","placeholder"])])):m("",!0),r("div",w,[L.value?(l(),v(t,{key:0,disabled:!A.value,style:{"margin-right":"10px"},type:"primary",onClick:H},{default:p((()=>a[3]||(a[3]=[y("确 定 ")]))),_:1,__:[3]},8,["disabled"])):m("",!0),d(t,{type:"info",onClick:J},{default:p((()=>a[4]||(a[4]=[y("取 消 ")]))),_:1,__:[4]})])])):m("",!0),"totp"===h.secondaryType?(l(),o("div",V,[U.value||!0===h.authInfo.CurrentSecret?m("",!0):(l(),o("div",S,[a[5]||(a[5]=r("p",null,"本系统要求使用令牌生成的口令完成双因子认证。您还没有激活令牌，可以使用第三方应用生成口令，如Google Authenticator、腾讯身份验证器等。",-1)),r("img",{src:"data:image/png;base64,"+h.authInfo.qrCode,alt:"TOTP口令",style:{display:"block",margin:"0 auto"}},null,8,q),r("p",{style:{"text-align":"center","margin-top":"10px"}},[r("a",{style:{cursor:"pointer",color:"#0d84ff"},onClick:E},"已扫描"),r("a",{style:{cursor:"pointer",color:"#0d84ff","margin-left":"20px"},onClick:J},"取消")])])),U.value||h.authInfo.CurrentSecret?(l(),o("div",N,[a[9]||(a[9]=r("p",{style:{"text-align":"center","margin-top":"20px","margin-bottom":"30px"}},"请输入口令",-1)),d(n,{modelValue:O.value,"onUpdate:modelValue":a[1]||(a[1]=e=>O.value=e),class:"input-with-select",placeholder:"请输入令牌口令",onKeyup:f(H,["enter"])},null,8,["modelValue"]),r("div",j,[d(t,{style:{"margin-right":"10px"},type:"primary",onClick:H},{default:p((()=>a[6]||(a[6]=[y("确 定 ")]))),_:1,__:[6]}),!0!==h.authInfo.CurrentSecret?(l(),v(t,{key:0,style:{"margin-right":"10px"},type:"info",onClick:a[2]||(a[2]=e=>U.value=!U.value)},{default:p((()=>a[7]||(a[7]=[y("重 扫 ")]))),_:1,__:[7]})):m("",!0),d(t,{type:"info",onClick:J},{default:p((()=>a[8]||(a[8]=[y("取 消 ")]))),_:1,__:[8]})])])):m("",!0)])):m("",!0)])}}}),[["__scopeId","data-v-1c306988"]]);export{K as default};
