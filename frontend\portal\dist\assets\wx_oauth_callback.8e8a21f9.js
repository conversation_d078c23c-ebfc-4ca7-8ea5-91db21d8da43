/*! 
 Build based on gin-vue-admin 
 Time : 1754834760000 */
import{u as r,E as a,f as e,r as s,o as t,x as i,a as c,b as n,L as o,M as u}from"./index.0f69a27d.js";const l=Object.assign({name:"WxOAuthCallback"},{setup(l){const y=r(),d=a(),p=e(),{code:f,state:h,redirect_url:m}=y.query,x=s(Array.isArray(h)?h[0]:h),b=s("");return t((async()=>{const r=o.service({fullscreen:!0,text:"登录中，请稍候..."});try{const r={clientId:"client_portal",grantType:"implicit",redirect_uri:"",idpId:x.value,authWeb:{authWebCode:Array.isArray(f)?f[0]:f}};!0===await p.LoginIn(r,"qiyewx_oauth",x.value)?await d.push({name:"verify",query:{redirect_url:m}}):u.error("登录失败，请重试")}catch(a){console.error("登录过程出错:",a),u.error("登录过程出错，请重试")}finally{r.close()}})),i("userName",b),(r,a)=>(c(),n("span"))}});export{l as default};
