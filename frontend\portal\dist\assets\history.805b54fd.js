/*! 
 Build based on gin-vue-admin 
 Time : 1754879545000 */
import{u as e,E as a,r as s,f as l,c as t,v as n,P as u,K as o,h as r,a as i,b as v,j as m,w as c,F as d,A as y,k as p,d as g,n as f,y as b,i as h,t as S,Z as I,O as q,I as O,J as w}from"./index.ab3e73c8.js";import{J as k}from"./index-browser-esm.c2d3b5c9.js";const J={class:"router-history"},N=["tab"],x=Object.assign({name:"HistoryComponent"},{setup(x){const C=e(),j=a(),E=e=>e.name+JSON.stringify(e.query)+JSON.stringify(e.params),V=s([]),A=s(""),P=s(!1),T=l(),L=e=>e.name+JSON.stringify(e.query)+JSON.stringify(e.params),R=s(0),$=s(0),_=s(!1),F=s(!1),H=s(""),K=t((()=>k("$..defaultRouter[0]",T.userInfo)[0]||"dashboard")),U=()=>{V.value=[{name:K.value,meta:{title:"总览"},query:{},params:{}}],j.push({name:K.value}),P.value=!1,sessionStorage.setItem("historys",JSON.stringify(V.value))},X=()=>{let e;const a=V.value.findIndex((a=>(E(a)===H.value&&(e=a),E(a)===H.value))),s=V.value.findIndex((e=>E(e)===A.value));V.value.splice(0,a),a>s&&j.push(e),sessionStorage.setItem("historys",JSON.stringify(V.value))},Y=()=>{let e;const a=V.value.findIndex((a=>(E(a)===H.value&&(e=a),E(a)===H.value))),s=V.value.findIndex((e=>E(e)===A.value));V.value.splice(a+1,V.value.length),a<s&&j.push(e),sessionStorage.setItem("historys",JSON.stringify(V.value))},Z=()=>{let e;V.value=V.value.filter((a=>(E(a)===H.value&&(e=a),E(a)===H.value))),j.push(e),sessionStorage.setItem("historys",JSON.stringify(V.value))},z=e=>{if(!V.value.some((a=>((e,a)=>{if(e.name!==a.name)return!1;if(Object.keys(e.query).length!==Object.keys(a.query).length||Object.keys(e.params).length!==Object.keys(a.params).length)return!1;for(const s in e.query)if(e.query[s]!==a.query[s])return!1;for(const s in e.params)if(e.params[s]!==a.params[s])return!1;return!0})(a,e)))){const a={};a.name=e.name,a.meta={...e.meta},delete a.meta.matched,a.query=e.query,a.params=e.params,V.value.push(a)}window.sessionStorage.setItem("activeValue",E(e))},B=s({});n((()=>V.value),(()=>{B.value={},V.value.forEach((e=>{B.value[E(e)]=e}))}));const D=e=>{const a=B.value[e];j.push({name:a.name,query:a.query,params:a.params})},G=e=>{const a=V.value.findIndex((a=>E(a)===e));E(C)===e&&(1===V.value.length?j.push({name:K.value}):a<V.value.length-1?j.push({name:V.value[a+1].name,query:V.value[a+1].query,params:V.value[a+1].params}):j.push({name:V.value[a-1].name,query:V.value[a-1].query,params:V.value[a-1].params})),V.value.splice(a,1)};n((()=>P.value),(()=>{P.value?document.body.addEventListener("click",(()=>{P.value=!1})):document.body.removeEventListener("click",(()=>{P.value=!1}))})),n((()=>C),((e,a)=>{"Login"!==e.name&&"Reload"!==e.name&&(V.value=V.value.filter((e=>!e.meta.closeTab)),z(e),sessionStorage.setItem("historys",JSON.stringify(V.value)),A.value=window.sessionStorage.getItem("activeValue"))}),{deep:!0}),n((()=>V.value),(()=>{sessionStorage.setItem("historys",JSON.stringify(V.value))}),{deep:!0});return(()=>{o.on("closeThisPage",(()=>{G(L(C))})),o.on("closeAllPage",(()=>{U()})),o.on("mobile",(e=>{F.value=e})),o.on("collapse",(e=>{_.value=e}));const e=[{name:K.value,meta:{title:"总览"},query:{},params:{}}];V.value=JSON.parse(sessionStorage.getItem("historys"))||e,window.sessionStorage.getItem("activeValue")?A.value=window.sessionStorage.getItem("activeValue"):A.value=E(C),z(C),"true"===window.sessionStorage.getItem("needCloseAll")&&(U(),window.sessionStorage.removeItem("needCloseAll"))})(),u((()=>{o.off("collapse"),o.off("mobile")})),(e,a)=>{const s=r("base-tab-pane"),l=r("base-tabs");return i(),v("div",J,[m(l,{modelValue:A.value,"onUpdate:modelValue":a[0]||(a[0]=e=>A.value=e),closable:!(1===V.value.length&&e.$route.name===K.value),type:"card",onContextmenu:a[1]||(a[1]=q((e=>(e=>{if(1===V.value.length&&C.name===K.value)return!1;let a="";if(a="SPAN"===e.srcElement.nodeName?e.srcElement.offsetParent.id:e.srcElement.id,a){let s;P.value=!0,s=_.value?54:220,F.value&&(s=0),R.value=e.clientX-s,$.value=e.clientY+10,H.value=a.substring(4)}})(e)),["prevent"])),onTabChange:D,onTabRemove:G},{default:c((()=>[(i(!0),v(d,null,y(V.value,(e=>(i(),p(s,{key:L(e),label:e.meta.title,name:L(e),tab:e,class:"gva-tab"},{label:c((()=>[g("span",{tab:e,style:f({color:A.value===L(e)?b(T).activeColor:"#333"})},[g("i",{class:"dot",style:f({backgroundColor:A.value===L(e)?b(T).activeColor:"#ddd"})},null,4),h(" "+S(b(I)(e.meta.title,e)),1)],12,N)])),_:2},1032,["label","name","tab"])))),128))])),_:1},8,["modelValue","closable"]),O(g("ul",{style:f({left:R.value+"px",top:$.value+"px"}),class:"contextmenu"},[g("li",{onClick:U},"关闭所有"),g("li",{onClick:X},"关闭左侧"),g("li",{onClick:Y},"关闭右侧"),g("li",{onClick:Z},"关闭其他")],4),[[w,P.value]])])}}});export{x as default};
