/*! 
 Build based on gin-vue-admin 
 Time : 1754836337000 */
!function(){function e(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var n,o,r="function"==typeof Symbol?Symbol:{},a=r.iterator||"@@iterator",i=r.toStringTag||"@@toStringTag";function d(e,r,a,i){var d=r&&r.prototype instanceof l?r:l,s=Object.create(d.prototype);return t(s,"_invoke",function(e,t,r){var a,i,d,l=0,s=r||[],u=!1,f={p:0,n:0,v:n,a:h,f:h.bind(n,4),d:function(e,t){return a=e,i=0,d=n,f.n=t,c}};function h(e,t){for(i=e,d=t,o=0;!u&&l&&!r&&o<s.length;o++){var r,a=s[o],h=f.p,g=a[2];e>3?(r=g===t)&&(d=a[(i=a[4])?5:(i=3,3)],a[4]=a[5]=n):a[0]<=h&&((r=e<2&&h<a[1])?(i=0,f.v=t,f.n=a[1]):h<g&&(r=e<3||a[0]>t||t>g)&&(a[4]=e,a[5]=t,f.n=g,i=0))}if(r||e>1)return c;throw u=!0,t}return function(r,s,g){if(l>1)throw TypeError("Generator is already running");for(u&&1===s&&h(s,g),i=s,d=g;(o=i<2?n:d)||!u;){a||(i?i<3?(i>1&&(f.n=-1),h(i,d)):f.n=d:f.v=d);try{if(l=2,a){if(i||(r="next"),o=a[r]){if(!(o=o.call(a,d)))throw TypeError("iterator result is not an object");if(!o.done)return o;d=o.value,i<2&&(i=0)}else 1===i&&(o=a.return)&&o.call(a),i<2&&(d=TypeError("The iterator does not provide a '"+r+"' method"),i=1);a=n}else if((o=(u=f.n<0)?d:e.call(t,f))!==c)break}catch(o){a=n,i=1,d=o}finally{l=1}}return{value:o,done:u}}}(e,a,i),!0),s}var c={};function l(){}function s(){}function u(){}o=Object.getPrototypeOf;var f=[][a]?o(o([][a]())):(t(o={},a,(function(){return this})),o),h=u.prototype=l.prototype=Object.create(f);function g(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,u):(e.__proto__=u,t(e,i,"GeneratorFunction")),e.prototype=Object.create(h),e}return s.prototype=u,t(h,"constructor",u),t(u,"constructor",s),s.displayName="GeneratorFunction",t(u,i,"GeneratorFunction"),t(h),t(h,i,"Generator"),t(h,a,(function(){return this})),t(h,"toString",(function(){return"[object Generator]"})),(e=function(){return{w:d,m:g}})()}function t(e,n,o,r){var a=Object.defineProperty;try{a({},"",{})}catch(e){a=0}t=function(e,n,o,r){if(n)a?a(e,n,{value:o,enumerable:!r,configurable:!r,writable:!r}):e[n]=o;else{var i=function(n,o){t(e,n,(function(e){return this._invoke(n,o,e)}))};i("next",0),i("throw",1),i("return",2)}},t(e,n,o,r)}function n(e,t,n,o,r,a,i){try{var d=e[a](i),c=d.value}catch(e){return void n(e)}d.done?t(c):Promise.resolve(c).then(o,r)}System.register(["./system-legacy.5794454b.js","./index-legacy.aab80689.js"],(function(t,o){"use strict";var r,a,i,d,c,l,s,u,f,h,g,p,v,w,m,x,b,y=document.createElement("style");return y.textContent='@charset "UTF-8";.icon[data-v-73e3a6ee]{width:1em;height:1em;vertical-align:-.15em;fill:currentColor;overflow:hidden}.client[data-v-73e3a6ee]{height:100vh;text-align:center;background:#FFFFFF;max-height:calc(100vh - 84px);border-radius:4px;padding:20px}.client .base-main[data-v-73e3a6ee]{height:100%;padding:0}.client .download-container[data-v-73e3a6ee]{height:100%;display:flex;justify-content:center;align-items:center;flex-wrap:wrap;gap:20px;padding:20px 0}.client .download-card[data-v-73e3a6ee]{width:209px;height:209px;background:#F1F8FF;position:relative;border-radius:8px;cursor:pointer;transition:all .3s ease;box-shadow:0 2px 8px rgba(0,0,0,.1);display:flex;flex-direction:column;align-items:center;justify-content:center}.client .download-card[data-v-73e3a6ee]:hover{transform:translateY(-2px);box-shadow:0 4px 16px rgba(0,0,0,.15)}.client .download-card:hover .window-show[data-v-73e3a6ee]{display:none}.client .download-card:hover .window-hidden[data-v-73e3a6ee]{display:block!important}.client .download-card:hover .window-hidden.qr-container[data-v-73e3a6ee]{display:flex!important;align-items:center;justify-content:center}.client .download-card .download-icon[data-v-73e3a6ee]{font-size:43px;color:#4d70ff;pointer-events:none;margin:0;display:block}.client .download-card .download-icon.window-hidden[data-v-73e3a6ee]{display:none}.client .download-card .download-text[data-v-73e3a6ee]{color:#333;font-size:14px;font-weight:500;text-decoration:none;margin:16px 0 0;display:block;text-align:center}.client .download-card .download-text.window-hidden[data-v-73e3a6ee]{display:none}.client .download-card .download-progress[data-v-73e3a6ee]{margin-top:10px;width:80%}.client .download-card .qr-container[data-v-73e3a6ee]{position:absolute;top:0;left:0;width:100%;height:100%;display:none;align-items:center;justify-content:center;background:#F1F8FF;border-radius:8px}.client .download-card .qr-container .qr-canvas[data-v-73e3a6ee]{width:auto;height:auto;max-width:150px;max-height:150px;display:block;margin:0 auto}.client .mobile-notice[data-v-73e3a6ee]{width:100%;max-width:500px;margin:20px auto 0}.client .mobile-notice .mobile-notice-content[data-v-73e3a6ee]{background:#f8f9fa;border-radius:12px;padding:30px 20px;box-shadow:0 4px 20px rgba(0,0,0,.1)}.client .mobile-notice .mobile-notice-content .mobile-notice-icon[data-v-73e3a6ee]{font-size:48px;color:#6c757d;margin-bottom:16px;display:block}.client .mobile-notice .mobile-notice-content .mobile-notice-title[data-v-73e3a6ee]{font-size:20px;font-weight:600;color:#333;margin-bottom:12px;margin-top:0}.client .mobile-notice .mobile-notice-content .mobile-notice-text[data-v-73e3a6ee]{font-size:14px;color:#666;line-height:1.6;margin-bottom:20px}.client .mobile-notice .mobile-notice-content .mobile-notice-tips[data-v-73e3a6ee]{background:#fff;padding:16px;border-radius:8px;border-left:4px solid #4D70FF;text-align:left}.client .mobile-notice .mobile-notice-content .mobile-notice-tips p[data-v-73e3a6ee]{font-size:13px;font-weight:600;color:#4d70ff;margin-bottom:10px;margin-top:0}.client .mobile-notice .mobile-notice-content .mobile-notice-tips ul[data-v-73e3a6ee]{margin:0;padding-left:18px}.client .mobile-notice .mobile-notice-content .mobile-notice-tips ul li[data-v-73e3a6ee]{font-size:13px;color:#666;line-height:1.5;margin-bottom:6px}.client .mobile-notice .mobile-notice-content .mobile-notice-tips ul li[data-v-73e3a6ee]:last-child{margin-bottom:0}.loading-overlay[data-v-73e3a6ee]{position:absolute;top:0;left:0;right:0;bottom:0;background-color:rgba(241,248,255,.9);display:flex;align-items:center;justify-content:center;z-index:10;border-radius:4px}.loading-spinner[data-v-73e3a6ee]{display:flex;flex-direction:column;align-items:center;gap:8px}.spinner[data-v-73e3a6ee]{width:24px;height:24px;border:2px solid #f3f3f3;border-top:2px solid #536ce6;border-radius:50%;animation:spin-73e3a6ee 1s linear infinite}@keyframes spin-73e3a6ee{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.loading-text[data-v-73e3a6ee]{font-size:12px;color:#606266}@media screen and (max-width: 768px){.client[data-v-73e3a6ee]{padding:10px;max-height:100vh}.client .download-container[data-v-73e3a6ee]{flex-direction:column;align-items:center;justify-content:flex-start;gap:16px;padding:10px 0;overflow-y:auto}.client .download-card[data-v-73e3a6ee]{width:280px;height:200px;margin:0}.client .download-card .download-icon[data-v-73e3a6ee]{font-size:36px}.client .download-card .download-text[data-v-73e3a6ee]{font-size:15px;margin-top:12px}.client .download-card .download-progress[data-v-73e3a6ee]{margin-top:8px;width:85%}.client .download-card .qr-container .qr-canvas[data-v-73e3a6ee]{max-width:120px;max-height:120px}.client .mobile-notice[data-v-73e3a6ee]{margin-top:10px}.client .mobile-notice .mobile-notice-content[data-v-73e3a6ee]{padding:20px 16px}.client .mobile-notice .mobile-notice-content .mobile-notice-icon[data-v-73e3a6ee]{font-size:40px;margin-bottom:12px}.client .mobile-notice .mobile-notice-content .mobile-notice-title[data-v-73e3a6ee]{font-size:18px;margin-bottom:10px}.client .mobile-notice .mobile-notice-content .mobile-notice-text[data-v-73e3a6ee]{font-size:13px;margin-bottom:16px}.client .mobile-notice .mobile-notice-content .mobile-notice-tips[data-v-73e3a6ee]{padding:12px}.client .mobile-notice .mobile-notice-content .mobile-notice-tips p[data-v-73e3a6ee]{font-size:12px;margin-bottom:8px}.client .mobile-notice .mobile-notice-content .mobile-notice-tips ul li[data-v-73e3a6ee]{font-size:12px;margin-bottom:4px}}@media screen and (max-width: 480px){.client[data-v-73e3a6ee]{padding:8px}.client .download-card[data-v-73e3a6ee]{width:260px;height:180px}.client .download-card .download-icon[data-v-73e3a6ee]{font-size:32px}.client .download-card .download-text[data-v-73e3a6ee]{font-size:14px;margin-top:10px}.client .download-card .qr-container .qr-canvas[data-v-73e3a6ee]{max-width:100px;max-height:100px}.client .mobile-notice .mobile-notice-content[data-v-73e3a6ee]{padding:16px 12px}.client .mobile-notice .mobile-notice-content .mobile-notice-icon[data-v-73e3a6ee]{font-size:36px;margin-bottom:10px}.client .mobile-notice .mobile-notice-content .mobile-notice-title[data-v-73e3a6ee]{font-size:16px;margin-bottom:8px}.client .mobile-notice .mobile-notice-content .mobile-notice-text[data-v-73e3a6ee]{font-size:12px;margin-bottom:12px}}@media (hover: none) and (pointer: coarse){.client .download-card[data-v-73e3a6ee]:active{transform:scale(.98);transition:transform .1s ease}.client .download-card[data-v-73e3a6ee]:hover{transform:none;box-shadow:0 2px 8px rgba(0,0,0,.1)}}@media screen and (max-height: 500px) and (orientation: landscape){.client .download-container[data-v-73e3a6ee]{flex-direction:row;flex-wrap:wrap;justify-content:center;gap:12px}.client .download-card[data-v-73e3a6ee]{width:180px;height:160px}.client .download-card .download-icon[data-v-73e3a6ee]{font-size:28px}.client .download-card .download-text[data-v-73e3a6ee]{font-size:12px;margin-top:8px}.client .download-card .qr-container .qr-canvas[data-v-73e3a6ee]{max-width:80px;max-height:80px}.client .mobile-notice[data-v-73e3a6ee]{display:none}}@media screen and (min-width: 769px) and (max-width: 1024px){.client .download-container[data-v-73e3a6ee]{gap:16px}.client .download-card[data-v-73e3a6ee]{width:200px;height:200px}.client .download-card .download-icon[data-v-73e3a6ee]{font-size:40px}.client .download-card .download-text[data-v-73e3a6ee]{font-size:14px;margin-top:14px}.client .download-card .qr-container .qr-canvas[data-v-73e3a6ee]{max-width:130px;max-height:130px}}\n',document.head.appendChild(y),{setters:[function(e){r=e.g},function(e){a=e._,i=e.r,d=e.o,c=e.P,l=e.h,s=e.a,u=e.b,f=e.d,h=e.j,g=e.w,p=e.F,v=e.i,w=e.k,m=e.l,x=e.e,b=e.M}],execute:function(){var o,y={},E={},C={},A=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];C.getSymbolSize=function(e){if(!e)throw new Error('"version" cannot be null or undefined');if(e<1||e>40)throw new Error('"version" should be in range from 1 to 40');return 4*e+17},C.getSymbolTotalCodewords=function(e){return A[e]},C.getBCHDigit=function(e){for(var t=0;0!==e;)t++,e>>>=1;return t},C.setToSJISFunction=function(e){if("function"!=typeof e)throw new Error('"toSJISFunc" is not a valid function.');o=e},C.isKanjiModeEnabled=function(){return void 0!==o},C.toSJIS=function(e){return o(e)};var B={};function M(){this.buffer=[],this.length=0}!function(e){e.L={bit:1},e.M={bit:0},e.Q={bit:3},e.H={bit:2},e.isValid=function(e){return e&&void 0!==e.bit&&e.bit>=0&&e.bit<4},e.from=function(t,n){if(e.isValid(t))return t;try{return function(t){if("string"!=typeof t)throw new Error("Param is not a string");switch(t.toLowerCase()){case"l":case"low":return e.L;case"m":case"medium":return e.M;case"q":case"quartile":return e.Q;case"h":case"high":return e.H;default:throw new Error("Unknown EC Level: "+t)}}(t)}catch(o){return n}}}(B),M.prototype={get:function(e){var t=Math.floor(e/8);return 1==(this.buffer[t]>>>7-e%8&1)},put:function(e,t){for(var n=0;n<t;n++)this.putBit(1==(e>>>t-n-1&1))},getLengthInBits:function(){return this.length},putBit:function(e){var t=Math.floor(this.length/8);this.buffer.length<=t&&this.buffer.push(0),e&&(this.buffer[t]|=128>>>this.length%8),this.length++}};var I=M;function T(e){if(!e||e<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=e,this.data=new Uint8Array(e*e),this.reservedBit=new Uint8Array(e*e)}T.prototype.set=function(e,t,n,o){var r=e*this.size+t;this.data[r]=n,o&&(this.reservedBit[r]=!0)},T.prototype.get=function(e,t){return this.data[e*this.size+t]},T.prototype.xor=function(e,t,n){this.data[e*this.size+t]^=n},T.prototype.isReserved=function(e,t){return this.reservedBit[e*this.size+t]};var P=T,k={};!function(e){var t=C.getSymbolSize;e.getRowColCoords=function(e){if(1===e)return[];for(var n=Math.floor(e/7)+2,o=t(e),r=145===o?26:2*Math.ceil((o-13)/(2*n-2)),a=[o-7],i=1;i<n-1;i++)a[i]=a[i-1]-r;return a.push(6),a.reverse()},e.getPositions=function(t){for(var n=[],o=e.getRowColCoords(t),r=o.length,a=0;a<r;a++)for(var i=0;i<r;i++)0===a&&0===i||0===a&&i===r-1||a===r-1&&0===i||n.push([o[a],o[i]]);return n}}(k);var N={},R=C.getSymbolSize;N.getPositions=function(e){var t=R(e);return[[0,0],[t-7,0],[0,t-7]]};var L={};!function(e){e.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};var t=3,n=3,o=40,r=10;function a(t,n,o){switch(t){case e.Patterns.PATTERN000:return(n+o)%2==0;case e.Patterns.PATTERN001:return n%2==0;case e.Patterns.PATTERN010:return o%3==0;case e.Patterns.PATTERN011:return(n+o)%3==0;case e.Patterns.PATTERN100:return(Math.floor(n/2)+Math.floor(o/3))%2==0;case e.Patterns.PATTERN101:return n*o%2+n*o%3==0;case e.Patterns.PATTERN110:return(n*o%2+n*o%3)%2==0;case e.Patterns.PATTERN111:return(n*o%3+(n+o)%2)%2==0;default:throw new Error("bad maskPattern:"+t)}}e.isValid=function(e){return null!=e&&""!==e&&!isNaN(e)&&e>=0&&e<=7},e.from=function(t){return e.isValid(t)?parseInt(t,10):void 0},e.getPenaltyN1=function(e){for(var n=e.size,o=0,r=0,a=0,i=null,d=null,c=0;c<n;c++){r=a=0,i=d=null;for(var l=0;l<n;l++){var s=e.get(c,l);s===i?r++:(r>=5&&(o+=t+(r-5)),i=s,r=1),(s=e.get(l,c))===d?a++:(a>=5&&(o+=t+(a-5)),d=s,a=1)}r>=5&&(o+=t+(r-5)),a>=5&&(o+=t+(a-5))}return o},e.getPenaltyN2=function(e){for(var t=e.size,o=0,r=0;r<t-1;r++)for(var a=0;a<t-1;a++){var i=e.get(r,a)+e.get(r,a+1)+e.get(r+1,a)+e.get(r+1,a+1);4!==i&&0!==i||o++}return o*n},e.getPenaltyN3=function(e){for(var t=e.size,n=0,r=0,a=0,i=0;i<t;i++){r=a=0;for(var d=0;d<t;d++)r=r<<1&2047|e.get(i,d),d>=10&&(1488===r||93===r)&&n++,a=a<<1&2047|e.get(d,i),d>=10&&(1488===a||93===a)&&n++}return n*o},e.getPenaltyN4=function(e){for(var t=0,n=e.data.length,o=0;o<n;o++)t+=e.data[o];return Math.abs(Math.ceil(100*t/n/5)-10)*r},e.applyMask=function(e,t){for(var n=t.size,o=0;o<n;o++)for(var r=0;r<n;r++)t.isReserved(r,o)||t.xor(r,o,a(e,r,o))},e.getBestMask=function(t,n){for(var o=Object.keys(e.Patterns).length,r=0,a=1/0,i=0;i<o;i++){n(i),e.applyMask(i,t);var d=e.getPenaltyN1(t)+e.getPenaltyN2(t)+e.getPenaltyN3(t)+e.getPenaltyN4(t);e.applyMask(i,t),d<a&&(a=d,r=i)}return r}}(L);var S={},_=B,z=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],U=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];S.getBlocksCount=function(e,t){switch(t){case _.L:return z[4*(e-1)+0];case _.M:return z[4*(e-1)+1];case _.Q:return z[4*(e-1)+2];case _.H:return z[4*(e-1)+3];default:return}},S.getTotalCodewordsCount=function(e,t){switch(t){case _.L:return U[4*(e-1)+0];case _.M:return U[4*(e-1)+1];case _.Q:return U[4*(e-1)+2];case _.H:return U[4*(e-1)+3];default:return}};var F={},j={},q=new Uint8Array(512),D=new Uint8Array(256);!function(){for(var e=1,t=0;t<255;t++)q[t]=e,D[e]=t,256&(e<<=1)&&(e^=285);for(var n=255;n<512;n++)q[n]=q[n-255]}(),j.log=function(e){if(e<1)throw new Error("log("+e+")");return D[e]},j.exp=function(e){return q[e]},j.mul=function(e,t){return 0===e||0===t?0:q[D[e]+D[t]]},function(e){var t=j;e.mul=function(e,n){for(var o=new Uint8Array(e.length+n.length-1),r=0;r<e.length;r++)for(var a=0;a<n.length;a++)o[r+a]^=t.mul(e[r],n[a]);return o},e.mod=function(e,n){for(var o=new Uint8Array(e);o.length-n.length>=0;){for(var r=o[0],a=0;a<n.length;a++)o[a]^=t.mul(n[a],r);for(var i=0;i<o.length&&0===o[i];)i++;o=o.slice(i)}return o},e.generateECPolynomial=function(n){for(var o=new Uint8Array([1]),r=0;r<n;r++)o=e.mul(o,new Uint8Array([1,t.exp(r)]));return o}}(F);var O=F;function H(e){this.genPoly=void 0,this.degree=e,this.degree&&this.initialize(this.degree)}H.prototype.initialize=function(e){this.degree=e,this.genPoly=O.generateECPolynomial(this.degree)},H.prototype.encode=function(e){if(!this.genPoly)throw new Error("Encoder not initialized");var t=new Uint8Array(e.length+this.degree);t.set(e);var n=O.mod(t,this.genPoly),o=this.degree-n.length;if(o>0){var r=new Uint8Array(this.degree);return r.set(n,o),r}return n};var Y=H,J={},K={},V={isValid:function(e){return!isNaN(e)&&e>=1&&e<=40}},Q={},G="[0-9]+",$="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+",W="(?:(?![A-Z0-9 $%*+\\-./:]|"+($=$.replace(/u/g,"\\u"))+")(?:.|[\r\n]))+";Q.KANJI=new RegExp($,"g"),Q.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),Q.BYTE=new RegExp(W,"g"),Q.NUMERIC=new RegExp(G,"g"),Q.ALPHANUMERIC=new RegExp("[A-Z $%*+\\-./:]+","g");var X=new RegExp("^"+$+"$"),Z=new RegExp("^"+G+"$"),ee=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");Q.testKanji=function(e){return X.test(e)},Q.testNumeric=function(e){return Z.test(e)},Q.testAlphanumeric=function(e){return ee.test(e)},function(e){var t=V,n=Q;e.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},e.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},e.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},e.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},e.MIXED={bit:-1},e.getCharCountIndicator=function(e,n){if(!e.ccBits)throw new Error("Invalid mode: "+e);if(!t.isValid(n))throw new Error("Invalid version: "+n);return n>=1&&n<10?e.ccBits[0]:n<27?e.ccBits[1]:e.ccBits[2]},e.getBestModeForData=function(t){return n.testNumeric(t)?e.NUMERIC:n.testAlphanumeric(t)?e.ALPHANUMERIC:n.testKanji(t)?e.KANJI:e.BYTE},e.toString=function(e){if(e&&e.id)return e.id;throw new Error("Invalid mode")},e.isValid=function(e){return e&&e.bit&&e.ccBits},e.from=function(t,n){if(e.isValid(t))return t;try{return function(t){if("string"!=typeof t)throw new Error("Param is not a string");switch(t.toLowerCase()){case"numeric":return e.NUMERIC;case"alphanumeric":return e.ALPHANUMERIC;case"kanji":return e.KANJI;case"byte":return e.BYTE;default:throw new Error("Unknown mode: "+t)}}(t)}catch(o){return n}}}(K),function(e){var t=C,n=S,o=B,r=K,a=V,i=t.getBCHDigit(7973);function d(e,t){return r.getCharCountIndicator(e,t)+4}function c(e,t){var n=0;return e.forEach((function(e){var o=d(e.mode,t);n+=o+e.getBitsLength()})),n}e.from=function(e,t){return a.isValid(e)?parseInt(e,10):t},e.getCapacity=function(e,o,i){if(!a.isValid(e))throw new Error("Invalid QR Code version");void 0===i&&(i=r.BYTE);var c=8*(t.getSymbolTotalCodewords(e)-n.getTotalCodewordsCount(e,o));if(i===r.MIXED)return c;var l=c-d(i,e);switch(i){case r.NUMERIC:return Math.floor(l/10*3);case r.ALPHANUMERIC:return Math.floor(l/11*2);case r.KANJI:return Math.floor(l/13);case r.BYTE:default:return Math.floor(l/8)}},e.getBestVersionForData=function(t,n){var a,i=o.from(n,o.M);if(Array.isArray(t)){if(t.length>1)return function(t,n){for(var o=1;o<=40;o++)if(c(t,o)<=e.getCapacity(o,n,r.MIXED))return o}(t,i);if(0===t.length)return 1;a=t[0]}else a=t;return function(t,n,o){for(var r=1;r<=40;r++)if(n<=e.getCapacity(r,o,t))return r}(a.mode,a.getLength(),i)},e.getEncodedBits=function(e){if(!a.isValid(e)||e<7)throw new Error("Invalid QR Code version");for(var n=e<<12;t.getBCHDigit(n)-i>=0;)n^=7973<<t.getBCHDigit(n)-i;return e<<12|n}}(J);var te={},ne=C,oe=ne.getBCHDigit(1335);te.getEncodedBits=function(e,t){for(var n=e.bit<<3|t,o=n<<10;ne.getBCHDigit(o)-oe>=0;)o^=1335<<ne.getBCHDigit(o)-oe;return 21522^(n<<10|o)};var re={},ae=K;function ie(e){this.mode=ae.NUMERIC,this.data=e.toString()}ie.getBitsLength=function(e){return 10*Math.floor(e/3)+(e%3?e%3*3+1:0)},ie.prototype.getLength=function(){return this.data.length},ie.prototype.getBitsLength=function(){return ie.getBitsLength(this.data.length)},ie.prototype.write=function(e){var t,n,o;for(t=0;t+3<=this.data.length;t+=3)n=this.data.substr(t,3),o=parseInt(n,10),e.put(o,10);var r=this.data.length-t;r>0&&(n=this.data.substr(t),o=parseInt(n,10),e.put(o,3*r+1))};var de=ie,ce=K,le=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function se(e){this.mode=ce.ALPHANUMERIC,this.data=e}se.getBitsLength=function(e){return 11*Math.floor(e/2)+e%2*6},se.prototype.getLength=function(){return this.data.length},se.prototype.getBitsLength=function(){return se.getBitsLength(this.data.length)},se.prototype.write=function(e){var t;for(t=0;t+2<=this.data.length;t+=2){var n=45*le.indexOf(this.data[t]);n+=le.indexOf(this.data[t+1]),e.put(n,11)}this.data.length%2&&e.put(le.indexOf(this.data[t]),6)};var ue=se,fe=K;function he(e){this.mode=fe.BYTE,this.data="string"==typeof e?(new TextEncoder).encode(e):new Uint8Array(e)}he.getBitsLength=function(e){return 8*e},he.prototype.getLength=function(){return this.data.length},he.prototype.getBitsLength=function(){return he.getBitsLength(this.data.length)},he.prototype.write=function(e){for(var t=0,n=this.data.length;t<n;t++)e.put(this.data[t],8)};var ge=he,pe=K,ve=C;function we(e){this.mode=pe.KANJI,this.data=e}we.getBitsLength=function(e){return 13*e},we.prototype.getLength=function(){return this.data.length},we.prototype.getBitsLength=function(){return we.getBitsLength(this.data.length)},we.prototype.write=function(e){var t;for(t=0;t<this.data.length;t++){var n=ve.toSJIS(this.data[t]);if(n>=33088&&n<=40956)n-=33088;else{if(!(n>=57408&&n<=60351))throw new Error("Invalid SJIS character: "+this.data[t]+"\nMake sure your charset is UTF-8");n-=49472}n=192*(n>>>8&255)+(255&n),e.put(n,13)}};var me=we,xe={exports:{}};!function(e){var t={single_source_shortest_paths:function(e,n,o){var r={},a={};a[n]=0;var i,d,c,l,s,u,f,h=t.PriorityQueue.make();for(h.push(n,0);!h.empty();)for(c in d=(i=h.pop()).value,l=i.cost,s=e[d]||{})s.hasOwnProperty(c)&&(u=l+s[c],f=a[c],(void 0===a[c]||f>u)&&(a[c]=u,h.push(c,u),r[c]=d));if(void 0!==o&&void 0===a[o]){var g=["Could not find a path from ",n," to ",o,"."].join("");throw new Error(g)}return r},extract_shortest_path_from_predecessor_list:function(e,t){for(var n=[],o=t;o;)n.push(o),e[o],o=e[o];return n.reverse(),n},find_path:function(e,n,o){var r=t.single_source_shortest_paths(e,n,o);return t.extract_shortest_path_from_predecessor_list(r,o)},PriorityQueue:{make:function(e){var n,o=t.PriorityQueue,r={};for(n in e=e||{},o)o.hasOwnProperty(n)&&(r[n]=o[n]);return r.queue=[],r.sorter=e.sorter||o.default_sorter,r},default_sorter:function(e,t){return e.cost-t.cost},push:function(e,t){var n={value:e,cost:t};this.queue.push(n),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return 0===this.queue.length}}};e.exports=t}(xe),function(e){var t=K,n=de,o=ue,r=ge,a=me,i=Q,d=C,c=xe.exports;function l(e){return unescape(encodeURIComponent(e)).length}function s(e,t,n){for(var o,r=[];null!==(o=e.exec(n));)r.push({data:o[0],index:o.index,mode:t,length:o[0].length});return r}function u(e){var n,o,r=s(i.NUMERIC,t.NUMERIC,e),a=s(i.ALPHANUMERIC,t.ALPHANUMERIC,e);return d.isKanjiModeEnabled()?(n=s(i.BYTE,t.BYTE,e),o=s(i.KANJI,t.KANJI,e)):(n=s(i.BYTE_KANJI,t.BYTE,e),o=[]),r.concat(a,n,o).sort((function(e,t){return e.index-t.index})).map((function(e){return{data:e.data,mode:e.mode,length:e.length}}))}function f(e,i){switch(i){case t.NUMERIC:return n.getBitsLength(e);case t.ALPHANUMERIC:return o.getBitsLength(e);case t.KANJI:return a.getBitsLength(e);case t.BYTE:return r.getBitsLength(e)}}function h(e,i){var c,l=t.getBestModeForData(e);if((c=t.from(i,l))!==t.BYTE&&c.bit<l.bit)throw new Error('"'+e+'" cannot be encoded with mode '+t.toString(c)+".\n Suggested mode is: "+t.toString(l));switch(c!==t.KANJI||d.isKanjiModeEnabled()||(c=t.BYTE),c){case t.NUMERIC:return new n(e);case t.ALPHANUMERIC:return new o(e);case t.KANJI:return new a(e);case t.BYTE:return new r(e)}}e.fromArray=function(e){return e.reduce((function(e,t){return"string"==typeof t?e.push(h(t,null)):t.data&&e.push(h(t.data,t.mode)),e}),[])},e.fromString=function(n,o){for(var r=function(e){for(var n=[],o=0;o<e.length;o++){var r=e[o];switch(r.mode){case t.NUMERIC:n.push([r,{data:r.data,mode:t.ALPHANUMERIC,length:r.length},{data:r.data,mode:t.BYTE,length:r.length}]);break;case t.ALPHANUMERIC:n.push([r,{data:r.data,mode:t.BYTE,length:r.length}]);break;case t.KANJI:n.push([r,{data:r.data,mode:t.BYTE,length:l(r.data)}]);break;case t.BYTE:n.push([{data:r.data,mode:t.BYTE,length:l(r.data)}])}}return n}(u(n,d.isKanjiModeEnabled())),a=function(e,n){for(var o={},r={start:{}},a=["start"],i=0;i<e.length;i++){for(var d=e[i],c=[],l=0;l<d.length;l++){var s=d[l],u=""+i+l;c.push(u),o[u]={node:s,lastCount:0},r[u]={};for(var h=0;h<a.length;h++){var g=a[h];o[g]&&o[g].node.mode===s.mode?(r[g][u]=f(o[g].lastCount+s.length,s.mode)-f(o[g].lastCount,s.mode),o[g].lastCount+=s.length):(o[g]&&(o[g].lastCount=s.length),r[g][u]=f(s.length,s.mode)+4+t.getCharCountIndicator(s.mode,n))}}a=c}for(var p=0;p<a.length;p++)r[a[p]].end=0;return{map:r,table:o}}(r,o),i=c.find_path(a.map,"start","end"),s=[],h=1;h<i.length-1;h++)s.push(a.table[i[h]].node);return e.fromArray(function(e){return e.reduce((function(e,t){var n=e.length-1>=0?e[e.length-1]:null;return n&&n.mode===t.mode?(e[e.length-1].data+=t.data,e):(e.push(t),e)}),[])}(s))},e.rawSplit=function(t){return e.fromArray(u(t,d.isKanjiModeEnabled()))}}(re);var be=C,ye=B,Ee=I,Ce=P,Ae=k,Be=N,Me=L,Ie=S,Te=Y,Pe=J,ke=te,Ne=K,Re=re;function Le(e,t,n){var o,r,a=e.size,i=ke.getEncodedBits(t,n);for(o=0;o<15;o++)r=1==(i>>o&1),o<6?e.set(o,8,r,!0):o<8?e.set(o+1,8,r,!0):e.set(a-15+o,8,r,!0),o<8?e.set(8,a-o-1,r,!0):o<9?e.set(8,15-o-1+1,r,!0):e.set(8,15-o-1,r,!0);e.set(a-8,8,1,!0)}function Se(e,t,n){var o=new Ee;n.forEach((function(t){o.put(t.mode.bit,4),o.put(t.getLength(),Ne.getCharCountIndicator(t.mode,e)),t.write(o)}));var r=8*(be.getSymbolTotalCodewords(e)-Ie.getTotalCodewordsCount(e,t));for(o.getLengthInBits()+4<=r&&o.put(0,4);o.getLengthInBits()%8!=0;)o.putBit(0);for(var a=(r-o.getLengthInBits())/8,i=0;i<a;i++)o.put(i%2?17:236,8);return function(e,t,n){for(var o=be.getSymbolTotalCodewords(t),r=Ie.getTotalCodewordsCount(t,n),a=o-r,i=Ie.getBlocksCount(t,n),d=i-o%i,c=Math.floor(o/i),l=Math.floor(a/i),s=l+1,u=c-l,f=new Te(u),h=0,g=new Array(i),p=new Array(i),v=0,w=new Uint8Array(e.buffer),m=0;m<i;m++){var x=m<d?l:s;g[m]=w.slice(h,h+x),p[m]=f.encode(g[m]),h+=x,v=Math.max(v,x)}var b,y,E=new Uint8Array(o),C=0;for(b=0;b<v;b++)for(y=0;y<i;y++)b<g[y].length&&(E[C++]=g[y][b]);for(b=0;b<u;b++)for(y=0;y<i;y++)E[C++]=p[y][b];return E}(o,e,t)}function _e(e,t,n,o){var r;if(Array.isArray(e))r=Re.fromArray(e);else{if("string"!=typeof e)throw new Error("Invalid data");var a=t;if(!a){var i=Re.rawSplit(e);a=Pe.getBestVersionForData(i,n)}r=Re.fromString(e,a||40)}var d=Pe.getBestVersionForData(r,n);if(!d)throw new Error("The amount of data is too big to be stored in a QR Code");if(t){if(t<d)throw new Error("\nThe chosen QR Code version cannot contain this amount of data.\nMinimum version required to store current data is: "+d+".\n")}else t=d;var c=Se(t,n,r),l=be.getSymbolSize(t),s=new Ce(l);return function(e,t){for(var n=e.size,o=Be.getPositions(t),r=0;r<o.length;r++)for(var a=o[r][0],i=o[r][1],d=-1;d<=7;d++)if(!(a+d<=-1||n<=a+d))for(var c=-1;c<=7;c++)i+c<=-1||n<=i+c||(d>=0&&d<=6&&(0===c||6===c)||c>=0&&c<=6&&(0===d||6===d)||d>=2&&d<=4&&c>=2&&c<=4?e.set(a+d,i+c,!0,!0):e.set(a+d,i+c,!1,!0))}(s,t),function(e){for(var t=e.size,n=8;n<t-8;n++){var o=n%2==0;e.set(n,6,o,!0),e.set(6,n,o,!0)}}(s),function(e,t){for(var n=Ae.getPositions(t),o=0;o<n.length;o++)for(var r=n[o][0],a=n[o][1],i=-2;i<=2;i++)for(var d=-2;d<=2;d++)-2===i||2===i||-2===d||2===d||0===i&&0===d?e.set(r+i,a+d,!0,!0):e.set(r+i,a+d,!1,!0)}(s,t),Le(s,n,0),t>=7&&function(e,t){for(var n,o,r,a=e.size,i=Pe.getEncodedBits(t),d=0;d<18;d++)n=Math.floor(d/3),o=d%3+a-8-3,r=1==(i>>d&1),e.set(n,o,r,!0),e.set(o,n,r,!0)}(s,t),function(e,t){for(var n=e.size,o=-1,r=n-1,a=7,i=0,d=n-1;d>0;d-=2)for(6===d&&d--;;){for(var c=0;c<2;c++)if(!e.isReserved(r,d-c)){var l=!1;i<t.length&&(l=1==(t[i]>>>a&1)),e.set(r,d-c,l),-1===--a&&(i++,a=7)}if((r+=o)<0||n<=r){r-=o,o=-o;break}}}(s,c),isNaN(o)&&(o=Me.getBestMask(s,Le.bind(null,s,n))),Me.applyMask(o,s),Le(s,n,o),{modules:s,version:t,errorCorrectionLevel:n,maskPattern:o,segments:r}}E.create=function(e,t){if(void 0===e||""===e)throw new Error("No input text");var n,o,r=ye.M;return void 0!==t&&(r=ye.from(t.errorCorrectionLevel,ye.M),n=Pe.from(t.version),o=Me.from(t.maskPattern),t.toSJISFunc&&be.setToSJISFunction(t.toSJISFunc)),_e(e,n,r,o)};var ze={},Ue={};!function(e){function t(e){if("number"==typeof e&&(e=e.toString()),"string"!=typeof e)throw new Error("Color should be defined as hex string");var t=e.slice().replace("#","").split("");if(t.length<3||5===t.length||t.length>8)throw new Error("Invalid hex color: "+e);3!==t.length&&4!==t.length||(t=Array.prototype.concat.apply([],t.map((function(e){return[e,e]})))),6===t.length&&t.push("F","F");var n=parseInt(t.join(""),16);return{r:n>>24&255,g:n>>16&255,b:n>>8&255,a:255&n,hex:"#"+t.slice(0,6).join("")}}e.getOptions=function(e){e||(e={}),e.color||(e.color={});var n=void 0===e.margin||null===e.margin||e.margin<0?4:e.margin,o=e.width&&e.width>=21?e.width:void 0,r=e.scale||4;return{width:o,scale:o?4:r,margin:n,color:{dark:t(e.color.dark||"#000000ff"),light:t(e.color.light||"#ffffffff")},type:e.type,rendererOpts:e.rendererOpts||{}}},e.getScale=function(e,t){return t.width&&t.width>=e+2*t.margin?t.width/(e+2*t.margin):t.scale},e.getImageWidth=function(t,n){var o=e.getScale(t,n);return Math.floor((t+2*n.margin)*o)},e.qrToImageData=function(t,n,o){for(var r=n.modules.size,a=n.modules.data,i=e.getScale(r,o),d=Math.floor((r+2*o.margin)*i),c=o.margin*i,l=[o.color.light,o.color.dark],s=0;s<d;s++)for(var u=0;u<d;u++){var f=4*(s*d+u),h=o.color.light;if(s>=c&&u>=c&&s<d-c&&u<d-c)h=l[a[Math.floor((s-c)/i)*r+Math.floor((u-c)/i)]?1:0];t[f++]=h.r,t[f++]=h.g,t[f++]=h.b,t[f]=h.a}}}(Ue),function(e){var t=Ue;e.render=function(e,n,o){var r=o,a=n;void 0!==r||n&&n.getContext||(r=n,n=void 0),n||(a=function(){try{return document.createElement("canvas")}catch(e){throw new Error("You need to specify a canvas element")}}()),r=t.getOptions(r);var i=t.getImageWidth(e.modules.size,r),d=a.getContext("2d"),c=d.createImageData(i,i);return t.qrToImageData(c.data,e,r),function(e,t,n){e.clearRect(0,0,t.width,t.height),t.style||(t.style={}),t.height=n,t.width=n,t.style.height=n+"px",t.style.width=n+"px"}(d,a,i),d.putImageData(c,0,0),a},e.renderToDataURL=function(t,n,o){var r=o;void 0!==r||n&&n.getContext||(r=n,n=void 0),r||(r={});var a=e.render(t,n,r),i=r.type||"image/png",d=r.rendererOpts||{};return a.toDataURL(i,d.quality)}}(ze);var Fe={},je=Ue;function qe(e,t){var n=e.a/255,o=t+'="'+e.hex+'"';return n<1?o+" "+t+'-opacity="'+n.toFixed(2).slice(1)+'"':o}function De(e,t,n){var o=e+t;return void 0!==n&&(o+=" "+n),o}Fe.render=function(e,t,n){var o=je.getOptions(t),r=e.modules.size,a=e.modules.data,i=r+2*o.margin,d=o.color.light.a?"<path "+qe(o.color.light,"fill")+' d="M0 0h'+i+"v"+i+'H0z"/>':"",c="<path "+qe(o.color.dark,"stroke")+' d="'+function(e,t,n){for(var o="",r=0,a=!1,i=0,d=0;d<e.length;d++){var c=Math.floor(d%t),l=Math.floor(d/t);c||a||(a=!0),e[d]?(i++,d>0&&c>0&&e[d-1]||(o+=a?De("M",c+n,.5+l+n):De("m",r,0),r=0,a=!1),c+1<t&&e[d+1]||(o+=De("h",i),i=0)):r++}return o}(a,r,o.margin)+'"/>',l='viewBox="0 0 '+i+" "+i+'"',s='<svg xmlns="http://www.w3.org/2000/svg" '+(o.width?'width="'+o.width+'" height="'+o.width+'" ':"")+l+' shape-rendering="crispEdges">'+d+c+"</svg>\n";return"function"==typeof n&&n(null,s),s};var Oe=function(){return"function"==typeof Promise&&Promise.prototype&&Promise.prototype.then},He=E,Ye=ze,Je=Fe;function Ke(e,t,n,o,r){var a=[].slice.call(arguments,1),i=a.length,d="function"==typeof a[i-1];if(!d&&!Oe())throw new Error("Callback required as last argument");if(!d){if(i<1)throw new Error("Too few arguments provided");return 1===i?(n=t,t=o=void 0):2!==i||t.getContext||(o=n,n=t,t=void 0),new Promise((function(r,a){try{var i=He.create(n,o);r(e(i,t,o))}catch(d){a(d)}}))}if(i<2)throw new Error("Too few arguments provided");2===i?(r=n,n=t,t=o=void 0):3===i&&(t.getContext&&void 0===r?(r=o,o=void 0):(r=o,o=n,n=t,t=void 0));try{var c=He.create(n,o);r(null,e(c,t,o))}catch(l){r(l)}}y.create=He.create,y.toCanvas=Ke.bind(null,Ye.render),y.toDataURL=Ke.bind(null,Ye.renderToDataURL),y.toString=Ke.bind(null,(function(e,t,n){return Je.render(e,n)}));var Ve={class:"client"},Qe={class:"download-container"},Ge={key:0,class:"loading-overlay"},$e={key:0,class:"loading-overlay"},We={__name:"download",setup:function(t){var o=i(!1),a=function(){var e=window.innerWidth,t=navigator.userAgent.toLowerCase(),n=/android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(t);o.value=e<768||n},E=function(){a()};d((function(){a(),window.addEventListener("resize",E)})),c((function(){window.removeEventListener("resize",E)}));var C=i(""),A=i(""),B=i(!1),M=i(!1),I=i(!1),T=i(!1),P=i({windows:0,darwin:0}),k=function(e){return 100===e?"完成":"".concat(e,"%")},N=function(e,t){return new Promise((function(n,o){var r=new XMLHttpRequest;r.open("GET",e,!0),r.responseType="blob",r.onprogress=function(e){if(e.lengthComputable){var n=e.loaded/e.total*100;P.value[t]=Math.round(n)}},r.onload=function(){200===r.status?n(r.response):o(new Error("下载失败"))},r.onerror=function(){o(new Error("网络错误"))},r.send()}))},R=function(e,t){if(window.navigator.msSaveOrOpenBlob)navigator.msSaveBlob(e,t);else{var n=document.createElement("a"),o=document.querySelector("body");n.href=window.URL.createObjectURL(e),n.download=t,n.style.display="none",o.appendChild(n),n.click(),o.removeChild(n),window.URL.revokeObjectURL(n.href)}L()},L=function(){B.value=!1,M.value=!1,I.value=!1,T.value=!1,Object.keys(P.value).forEach((function(e){P.value[e]=0}))},S=i(!1),_=function(){var t,o=(t=e().m((function t(n){var o,a,i,d,c,l,s,u,f,h,g,p,v,w,m,x,E,P,k;return e().w((function(e){for(;;)switch(e.n){case 0:if("android"!==n&&"ios"!==n||!S.value){e.n=1;break}return e.a(2);case 1:return S.value=!0,(o={windows:B,darwin:M,ios:I,android:T}[n]).value=!0,e.p=2,e.n=3,r({platform:n});case 3:if(0!==(a=e.v).data.code){e.n=10;break}if("ios"!==n){e.n=5;break}return e.n=4,y.toDataURL(a.data.data.download_url);case 4:i=e.v,d=document.getElementById("ioscanvas"),A.value=i,d&&(c=d.getContext("2d"),(l=new Image).onload=function(){d.width=l.width,d.height=l.height,c.drawImage(l,0,0)},l.src=i),e.n=9;break;case 5:if("android"!==n){e.n=7;break}return s=window.location.port,u=new URL(a.data.data.download_url),s?u.toString().includes("asec-deploy")?f=a.data.data.download_url:(u.port=s,f=u.toString()):(u.port="",f=u.toString()),e.n=6,y.toDataURL(f);case 6:h=e.v,g=document.getElementById("canvas"),C.value=h,g&&(p=g.getContext("2d"),(v=new Image).onload=function(){g.width=v.width,g.height=v.height,p.drawImage(v,0,0)},v.src=h),e.n=9;break;case 7:return w=window.location.port,m=new URL(a.data.data.download_url),w?(m.toString().includes("asec-deploy")?x=a.data.data.download_url:(m.port=w,x=m.toString()),E=a.data.data.latest_filename.replace(/@(\d+)/,"@".concat(w))):(m.port="",x=m.toString(),E=a.data.data.latest_filename),e.n=8,N(x,n);case 8:P=e.v,R(P,E);case 9:e.n=11;break;case 10:throw new Error(a.data.msg);case 11:e.n=13;break;case 12:e.p=12,k=e.v,b({type:"error",message:k.message||"下载失败，请联系管理员"});case 13:return e.p=13,o.value=!1,e.f(13);case 14:return e.a(2)}}),t,null,[[2,12,13,14]])})),function(){var e=this,o=arguments;return new Promise((function(r,a){var i=t.apply(e,o);function d(e){n(i,r,a,d,c,"next",e)}function c(e){n(i,r,a,d,c,"throw",e)}d(void 0)}))});return function(e){return o.apply(this,arguments)}}();return function(e,t){var n=l("base-link"),r=l("base-progress"),a=l("base-main");return s(),u("div",null,[f("div",Ve,[h(a,null,{default:g((function(){return[f("div",Qe,[o.value?m("",!0):(s(),u(p,{key:0},[f("div",{class:"download-card desktop-only",onClick:t[0]||(t[0]=function(e){return _("windows")})},[t[10]||(t[10]=f("svg",{class:"icon window-show download-icon","aria-hidden":"true"},[f("use",{"xlink:href":"#icon-windows"})],-1)),t[11]||(t[11]=f("svg",{class:"icon window-hidden download-icon","aria-hidden":"true"},[f("use",{"xlink:href":"#icon-xiazai"})],-1)),t[12]||(t[12]=f("br",null,null,-1)),h(n,{class:"window-show download-text",underline:!1},{default:g((function(){return t[8]||(t[8]=[v("Windows客户端")])})),_:1,__:[8]}),h(n,{class:"window-hidden download-text",underline:!1},{default:g((function(){return t[9]||(t[9]=[v("点击下载Windows客户端")])})),_:1,__:[9]}),B.value?(s(),w(r,{key:0,percentage:P.value.windows,format:k,class:"download-progress"},null,8,["percentage"])):m("",!0)]),f("div",{class:"download-card desktop-only",onClick:t[1]||(t[1]=function(e){return _("darwin")})},[t[15]||(t[15]=f("svg",{class:"icon window-show download-icon","aria-hidden":"true"},[f("use",{"xlink:href":"#icon-mac"})],-1)),t[16]||(t[16]=f("svg",{class:"icon window-hidden download-icon","aria-hidden":"true"},[f("use",{"xlink:href":"#icon-xiazai"})],-1)),t[17]||(t[17]=f("br",null,null,-1)),h(n,{class:"window-show download-text",underline:!1},{default:g((function(){return t[13]||(t[13]=[v("Mac客户端")])})),_:1,__:[13]}),h(n,{class:"window-hidden download-text",underline:!1},{default:g((function(){return t[14]||(t[14]=[v("点击下载Mac客户端")])})),_:1,__:[14]}),M.value?(s(),w(r,{key:0,percentage:P.value.darwin,format:k,class:"download-progress"},null,8,["percentage"])):m("",!0)])],64)),f("div",{class:x(["download-card ios-container",{loading:I.value}]),onClick:t[2]||(t[2]=function(e){return o.value?_("ios"):null}),onMousemove:t[3]||(t[3]=function(e){return o.value?null:_("ios")}),onMouseleave:t[4]||(t[4]=function(e){return S.value=!1})},[I.value?(s(),u("div",Ge,t[18]||(t[18]=[f("div",{class:"loading-spinner"},[f("div",{class:"spinner"}),f("div",{class:"loading-text"},"下载码生成中...")],-1)]))):m("",!0),t[20]||(t[20]=f("svg",{class:"icon window-show download-icon","aria-hidden":"true"},[f("use",{"xlink:href":"#icon-ios"})],-1)),t[21]||(t[21]=f("br",null,null,-1)),h(n,{class:"window-show download-text",underline:!1},{default:g((function(){return t[19]||(t[19]=[v("iOS客户端")])})),_:1,__:[19]}),t[22]||(t[22]=f("div",{id:"ios",class:"window-hidden qr-container"},[f("canvas",{id:"ioscanvas",class:"qr-canvas"})],-1))],34),f("div",{class:x(["download-card android-container",{loading:T.value}]),onClick:t[5]||(t[5]=function(e){return o.value?_("android"):null}),onMousemove:t[6]||(t[6]=function(e){return o.value?null:_("android")}),onMouseleave:t[7]||(t[7]=function(e){return S.value=!1})},[T.value?(s(),u("div",$e,t[23]||(t[23]=[f("div",{class:"loading-spinner"},[f("div",{class:"spinner"}),f("div",{class:"loading-text"},"下载码生成中...")],-1)]))):m("",!0),t[25]||(t[25]=f("svg",{class:"icon window-show download-icon","aria-hidden":"true"},[f("use",{"xlink:href":"#icon-android"})],-1)),t[26]||(t[26]=f("br",null,null,-1)),h(n,{class:"window-show download-text",underline:!1},{default:g((function(){return t[24]||(t[24]=[v("Android客户端")])})),_:1,__:[24]}),t[27]||(t[27]=f("div",{id:"android",class:"window-hidden qr-container"},[f("canvas",{id:"canvas",class:"qr-canvas"})],-1))],34)])]})),_:1})])])}}};t("default",a(We,[["__scopeId","data-v-73e3a6ee"]]))}}}))}();
