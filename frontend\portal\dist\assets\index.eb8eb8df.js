/*! 
 Build based on gin-vue-admin 
 Time : 1754834760000 */
import{q as e,r as a,u as l,c as t,f as u,o as n,v as o,x as i,a as s,b as r,d as v,j as c,y as d,t as p,l as m,k as h,z as y,e as g,n as f,F as _,A as w,B as C,L as k,C as x}from"./index.0f69a27d.js";import{g as b}from"./config.fb5c2619.js";const T={class:"login-page"},P={class:"content"},q={class:"right-panel"},O={key:0},L={key:1},I={key:0,class:"login_panel_form"},E={key:0,class:"title"},S={key:1,class:"title"},j={key:1,class:"auth-switcher"},U={class:"auth-switcher-container"},R=["disabled"],A={"aria-hidden":"true",class:"icon",style:{height:"16px",width:"16px"}},D={class:"auth-methods-wrapper"},V=["title","onClick"],$=["data-auth-type"],H={"aria-hidden":"true",class:"icon",style:{height:"18px",width:"18px"}},K=["xlink:href"],N={class:"auth-method-name"},W=["disabled"],J={"aria-hidden":"true",class:"icon",style:{height:"16px",width:"16px"}},M={key:2,class:"auth-waiting"},z={class:"waiting-icon"},B={"aria-hidden":"true",class:"icon",style:{height:"32px",width:"32px",color:"#f4a261"}},F=["xlink:href"],X={class:"waiting-title"},G={class:"security-tips"},Q={"aria-hidden":"true",class:"icon",style:{height:"16px",width:"16px",color:"#67c23a"}},Y=Object.assign({name:"Login"},{setup(Y){const Z=e({loader:()=>C((()=>import("./localLogin.9fbb9590.js")),["./localLogin.9fbb9590.js","./index.0f69a27d.js","./index.c3b165d5.css","./config.fb5c2619.js","./secondaryAuth.1b8a4c74.js","./localLogin.ec82520e.css"],import.meta.url),loadingComponent:k,errorComponent:{template:'<div class="error-component">组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),ee=e({loader:()=>C((()=>import("./wechat.aa88ef52.js")),["./wechat.aa88ef52.js","./index.0f69a27d.js","./index.c3b165d5.css","./secondaryAuth.1b8a4c74.js","./wechat.94fb94a0.css"],import.meta.url),loadingComponent:k,errorComponent:{template:'<div class="error-component">微信组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),ae=e({loader:()=>C((()=>import("./feishu.ee31b0f1.js")),["./feishu.ee31b0f1.js","./index.0f69a27d.js","./index.c3b165d5.css","./secondaryAuth.1b8a4c74.js","./feishu.082a6c41.css"],import.meta.url),loadingComponent:k,errorComponent:{template:'<div class="error-component">飞书组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),le=e({loader:()=>C((()=>import("./dingtalk.98a9e373.js")),["./dingtalk.98a9e373.js","./index.0f69a27d.js","./index.c3b165d5.css","./secondaryAuth.1b8a4c74.js","./dingtalk.99b25b30.css"],import.meta.url),loadingComponent:k,errorComponent:{template:'<div class="error-component">钉钉组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),te=e({loader:()=>C((()=>import("./oauth2.86c6d81d.js")),["./oauth2.86c6d81d.js","./index.0f69a27d.js","./index.c3b165d5.css","./secondaryAuth.1b8a4c74.js","./oauth2.62c69fac.css"],import.meta.url),loadingComponent:k,errorComponent:{template:'<div class="error-component">OAuth2组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),ue=e({loader:()=>C((()=>import("./secondaryAuth.dfd2d2a7.js")),["./secondaryAuth.dfd2d2a7.js","./verifyCode.77ae37b3.js","./index.0f69a27d.js","./index.c3b165d5.css","./verifyCode.af46968b.css","./secondaryAuth.7fb1a277.css"],import.meta.url),loadingComponent:k,errorComponent:{template:'<div class="error-component">二次认证组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),ne=e({loader:()=>C((()=>import("./serverConfig.ff36807c.js")),["./serverConfig.ff36807c.js","./index.0f69a27d.js","./index.c3b165d5.css","./serverConfig.0abff367.css"],import.meta.url),loadingComponent:k,errorComponent:{template:'<div class="error-component">服务器配置组件加载失败，请刷新重试</div>'},delay:200,timeout:3e3}),oe=a({}),ie=l(),se=a(0),re=a([]),ve=a("local"),ce=a(""),de=a(""),pe=a(""),me=a([]),he=a([]),ye=a(!1),ge=a(!1),fe=a(),_e=a(""),we=a(!1),Ce=a(""),ke=a(!1),xe=a(""),be=a(!1),Te=a(""),Pe=a(""),qe=a(""),Oe=a({}),Le=a(0),Ie=a(84),Ee=a(4),Se=a(null),je=t((()=>{const e=ye.value?Te.value:de.value;return re.value.filter((a=>a.id!==e))})),Ue=t((()=>Math.max(0,je.value.length-Ee.value))),Re=u(),Ae=e=>{logger.log("服务器配置完成:",e),ge.value=!1,globalUrlHashParams.set("WebUrl",e.serverUrl),De()},De=async()=>{var e,a,l,t,u,n,o,i,s,r,v,c;try{if(await(async()=>{if(x.isClient()){if(window.self!==window.top)return logger.log("iframe中不展示服务器配置页面"),!1;let a=globalUrlHashParams?globalUrlHashParams.get("WebUrl"):"";try{if(a){const e=new URL(a);a=`${e.protocol}//${e.host}`}}catch(e){a="",console.warn("解析 WebUrl 参数失败:",e)}if(a)return!1;const l=await x.getClientConfig();return oe.value=l||{},!l||!l.ServerUrl}return!1})())return ge.value=!0,void logger.log("显示服务器配置页面");const d=(()=>{const e={};if(ie.query.type&&(e.type=ie.query.type),ie.query.wp&&(e.wp=ie.query.wp),ie.query.redirect&&0===Object.keys(e).length)try{const a=decodeURIComponent(ie.query.redirect);if(a.includes("?")){const l=a.substring(a.indexOf("?")+1),t=new URLSearchParams(l);t.get("type")&&(e.type=t.get("type")),t.get("wp")&&(e.wp=t.get("wp"))}}catch(a){console.warn("解析redirect参数失败:",a)}return e})();Object.keys(d).length>0&&(localStorage.setItem("client_params",JSON.stringify(d)),sessionStorage.setItem("client_params",JSON.stringify(d))),logger.log("登录页初始化开始"),Ve();const p=await b();if(200===p.status){re.value=p.data.idpList;const d=ie.query.idp_id||Re.loginType;if(logger.log("传入或上次记住的认证源ID:",d),d&&"undefined"!==d){let o=!1;for(const e of p.data.idpList)d===e.id&&(o=!0,de.value=e.id,ve.value=e.type,ce.value=e.templateType,me.value=e.attrs,me.value.name=e.name,me.value.authType=e.type,logger.log("传入匹配到认证源"));o||(logger.log("未匹配到认证源"),pe.value=null==(e=re.value[0])?void 0:e.id,de.value=null==(a=re.value[0])?void 0:a.id,ve.value=null==(l=re.value[0])?void 0:l.type,ce.value=null==(t=re.value[0])?void 0:t.templateType,me.value=null==(u=re.value[0])?void 0:u.attrs,me.value.name=re.value[0].name,me.value.authType=null==(n=re.value[0])?void 0:n.type)}else pe.value=null==(o=re.value[0])?void 0:o.id,de.value=null==(i=re.value[0])?void 0:i.id,ve.value=null==(s=re.value[0])?void 0:s.type,ce.value=null==(r=re.value[0])?void 0:r.templateType,me.value=null==(v=re.value[0])?void 0:v.attrs,me.value.name=re.value[0].name,me.value.authType=null==(c=re.value[0])?void 0:c.type,logger.log("未记住和传入使用第一个认证源:",de.value);++se.value}}catch(d){console.error("获取认证列表失败:",d),x.isClient()&&Ve()}},Ve=()=>{logger.log("使用默认本地登录");const e={id:"default-local",type:"local",name:"本地账号登录",templateType:"local",attrs:{name:"本地账号登录",authType:"local",placeholder_username:"请输入用户名",placeholder_password:"请输入密码"}};re.value=[e],de.value=e.id,ve.value=e.type,ce.value=e.templateType,me.value=e.attrs,++se.value,logger.log("已设置默认本地登录认证源")};n((async()=>{try{logger.log("认证页面挂载:",ie.query),await De()}catch(e){console.error(e)}}));const $e=t((()=>{switch(ve.value){case"local":case"msad":case"ldap":case"web":case"email":return Z;case"qiyewx":return ee;case"feishu":return ae;case"dingtalk":return le;case"oauth2":case"cas":return te;default:return"oauth2"===ce.value?te:"local"}})),He=t((()=>[{type:"sms",name:"短信验证",icon:"duanxin",available:"phone"===Ce.value},{type:"email",name:"邮箱验证",icon:"email",available:"email"===Ce.value},{type:"totp",name:"TOTP",icon:"totp",available:"totp"===Ce.value}])),Ke=()=>{ye.value=!1,he.value=[],fe.value="",_e.value="",Ce.value="",ke.value=!1,xe.value="",be.value=!1,Te.value&&(de.value=Te.value,ve.value=Pe.value,ce.value=qe.value,me.value={...Oe.value},Te.value="",Pe.value="",qe.value="",Oe.value={}),++se.value,logger.log("取消后恢复的状态:",{isSecondary:ye.value,auth_id:de.value,auth_type:ve.value})},Ne=async e=>{const a=k.service({fullscreen:!0,text:"认证成功，正在跳转..."});try{let a=ie.query.redirect_url||"/";if(e.clientParams){const l=new URLSearchParams;l.set("type",e.clientParams.type),e.clientParams.wp&&l.set("wp",e.clientParams.wp),a+=(a.includes("?")?"&":"?")+l.toString()}window.location.href=a}finally{null==a||a.close()}},We=t((()=>!["dingtalk","feishu","qiyewx"].includes(ve.value)&&("oauth2"!==ce.value&&"cas"!==ve.value||("cas"===ve.value?1===parseInt(me.value.casOpenType):"oauth2"===ce.value&&1===parseInt(me.value.oauth2OpenType))))),Je=()=>{Le.value>0&&Le.value--},Me=()=>{Le.value<Ue.value&&Le.value++},ze=e=>{pe.value=e.id,me.value=e.attrs||{},me.value.name=e.name,me.value.authType=e.type,ye.value&&(me.value.uniqKey=fe.value,me.value.notPhone=we.value),de.value=e.id,ve.value=e.type,ce.value=e.templateType,++se.value};return o(ye,(async()=>{ye.value&&(Te.value=de.value,Pe.value=ve.value,qe.value=ce.value,Oe.value={...me.value},logger.log("二次认证数据:",{secondary:he.value,secondaryLength:he.value.length}),he.value.length>0&&ze(he.value[0]))})),i("secondary",he),i("isSecondary",ye),i("uniqKey",fe),i("userName",_e),i("notPhone",we),i("last_id",pe),i("contactType",Ce),i("hasContactInfo",ke),i("qrcode",xe),i("CurrentSecret",be),(e,a)=>(s(),r("div",T,[v("div",P,[a[6]||(a[6]=v("div",{class:"left-panel"},null,-1)),v("div",q,[ge.value?(s(),r("div",O,[c(d(ne),{onServerConfigured:Ae})])):ye.value?(s(),r("div",M,[v("div",z,[(s(),r("svg",B,[v("use",{"xlink:href":`#icon-auth-${Pe.value||ve.value}`},null,8,F)]))]),v("h4",X,p(Oe.value.name||me.value.name)+" 登录成功",1),a[5]||(a[5]=v("p",{class:"waiting-message"},"需要进行安全验证以确保账户安全",-1)),v("div",G,[(s(),r("svg",Q,a[3]||(a[3]=[v("use",{"xlink:href":"#icon-shield"},null,-1)]))),a[4]||(a[4]=v("span",null,"为了您的账户安全，请完成二次身份验证",-1))])])):(s(),r("div",L,[de.value?(s(),r("div",I,["local"===ve.value?(s(),r("span",E,"本地账号登录")):We.value?(s(),r("span",S,p(me.value.name),1)):m("",!0),(s(),h(y($e.value),{"auth-id":de.value,"auth-info":me.value},null,8,["auth-id","auth-info"]))])):m("",!0),je.value.length>0?(s(),r("div",j,[a[2]||(a[2]=v("div",{class:"auth-switcher-title"}," 其他登录方式 ",-1)),v("div",U,[je.value.length>4?(s(),r("button",{key:0,disabled:0===Le.value,class:"auth-nav-btn auth-nav-prev",onClick:Je},[(s(),r("svg",A,a[0]||(a[0]=[v("use",{"xlink:href":"#icon-chevron-left"},null,-1)])))],8,R)):m("",!0),v("div",D,[v("div",{ref_key:"authMethodsContainer",ref:Se,class:g(["auth-methods-container",{"auth-methods-centered":je.value.length<=4}]),style:f({transform:`translateX(-${Le.value*Ie.value}px)`})},[(s(!0),r(_,null,w(je.value,(e=>(s(),r("div",{key:e.id,title:e.name,class:"auth-method-item",onClick:a=>ze(e)},[v("div",{"data-auth-type":e.type,class:"auth-method-icon"},[(s(),r("svg",H,[v("use",{"xlink:href":`#icon-auth-${e.type}`},null,8,K)]))],8,$),v("div",N,p(e.name),1)],8,V)))),128))],6)]),je.value.length>4?(s(),r("button",{key:1,disabled:Le.value>=Ue.value,class:"auth-nav-btn auth-nav-next",onClick:Me},[(s(),r("svg",J,a[1]||(a[1]=[v("use",{"xlink:href":"#icon-chevron-right"},null,-1)])))],8,W)):m("",!0)])])):m("",!0)]))])]),ye.value?(s(),h(d(ue),{key:0,"auth-id":de.value,"auth-info":{uniqKey:fe.value,contactType:Ce.value,hasContactInfo:ke.value,qrCode:xe.value,CurrentSecret:be.value},"auth-methods":He.value,"last-id":pe.value,"user-name":_e.value,onCancel:Ke,onVerificationSuccess:Ne},null,8,["auth-id","auth-info","auth-methods","last-id","user-name"])):m("",!0)]))}});export{Y as default};
