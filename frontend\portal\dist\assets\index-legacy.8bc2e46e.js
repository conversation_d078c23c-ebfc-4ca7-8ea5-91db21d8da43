/*! 
 Build based on gin-vue-admin 
 Time : 1754879545000 */
System.register(["./header-legacy.f805cf1a.js","./menu-legacy.aab6cb5a.js","./index-legacy.39a66d41.js","./logo-legacy.17ee3a24.js"],(function(e,t){"use strict";var a,i,n,l,o,u,c,r,d,f=document.createElement("style");return f.textContent='@charset "UTF-8";.layout-page{width:100%!important;height:100%!important;position:relative!important;background:#fff}.layout-page .layout-wrap{width:100%;height:calc(100% - 42px);display:flex}.layout-page .layout-header{width:100%;height:42px;z-index:10}.layout-page .layout-main{width:100%;height:100%;overflow:hidden;flex:1;background:#fff}\n',document.head.appendChild(f),{setters:[function(e){a=e.default},function(e){i=e.default},function(e){n=e.C,l=e.h,o=e.a,u=e.b,c=e.j,r=e.d,d=e.k},function(){}],execute:function(){var t={class:"layout-page"},f={class:"layout-wrap"},s={id:"layoutMain",class:"layout-main"};e("default",Object.assign({name:"Client"},{setup:function(e){return n.initIpcClient(),function(e,n){var h=l("router-view");return o(),u("div",t,[c(a),r("div",f,[c(i),r("div",s,[(o(),d(h,{key:e.$route.fullPath,style:{height:"100%",display:"flex","flex-direction":"column"}}))])])])}}}))}}}));
