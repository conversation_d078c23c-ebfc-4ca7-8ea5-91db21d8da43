/*! 
 Build based on gin-vue-admin 
 Time : 1754879545000 */
System.register(["./index-legacy.39a66d41.js"],(function(e,a){"use strict";var n;return{setters:[function(e){n=e.a2}],execute:function(){e("u",(function(){var e=n("secondary"),a=n("isSecondary"),r=n("uniqKey"),u=n("userName"),t=n("contactType"),o=n("hasContactInfo"),c=n("qrcode"),l=n("CurrentSecret");return{secondary:e,isSecondary:a,uniqKey:r,userName:u,contactType:t,hasContactInfo:o,qrcode:c,CurrentSecret:l,handleSecondaryAuthResponse:function(n){try{return logger.log("处理双因子验证响应:",n),n&&n.isSecondary?(logger.log("触发双因子验证"),a.value=n.isSecondary,e.value=n.secondary,r.value=n.uniqKey,u.value=n.userName,t.value=n.contactType,o.value=n.hasContactInfo,c.value=n.qrcode,l.value=n.CurrentSecret,logger.log("双因子验证状态已设置:",{isSecondary:a.value,secondary:e.value,uniqKey:r.value,userName:u.value,contactType:t.value,hasContactInfo:o.value,qrcode:c.value,CurrentSecret:l.value}),!0):(logger.log("无需双因子验证"),!1)}catch(v){return logger.log("处理双因子验证响应失败:",v),!1}},clearSecondaryAuthState:function(){try{logger.log("清除双因子验证状态"),a.value=!1,e.value=[],r.value="",u.value="",t.value="",o.value=!1,c.value="",l.value="",logger.log("双因子验证状态已清除")}catch(n){logger.log("清除双因子验证状态失败:",n)}},isInSecondaryAuth:function(){return!0===a.value},getSecondaryAuthInfo:function(){return{isSecondary:a.value,secondary:e.value,uniqKey:r.value,userName:u.value,contactType:t.value,hasContactInfo:o.value,qrcode:c.value,CurrentSecret:l.value}},setUserName:function(e){u.value=e,logger.log("设置用户名:",e)},validateInjectedData:function(){var e=["secondary","isSecondary","uniqKey","userName","contactType","hasContactInfo","qrcode","CurrentSecret"].filter((function(e){return null===n(e,null)}));return!(e.length>0&&(logger.log("缺少必要的注入数据:",e),1))}}}))}}}));
