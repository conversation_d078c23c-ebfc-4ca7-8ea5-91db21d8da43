/*! 
 Build based on gin-vue-admin 
 Time : 1754836337000 */
import{X as e}from"./index.57c3624b.js";function o(e,o){for(var t=0;t<o.length;t++){const r=o[t];if("string"!=typeof r&&!Array.isArray(r))for(const o in r)if("default"!==o&&!(o in e)){const t=Object.getOwnPropertyDescriptor(r,o);t&&Object.defineProperty(e,o,t.get?t:{enumerable:!0,get:()=>r[o]})}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}var t={},r=Object.prototype.hasOwnProperty;function n(e){try{return decodeURIComponent(e.replace(/\+/g," "))}catch(o){return null}}function s(e){try{return encodeURIComponent(e)}catch(o){return null}}t.stringify=function(e,o){o=o||"";var t,n,a=[];for(n in"string"!=typeof o&&(o="?"),e)if(r.call(e,n)){if((t=e[n])||null!=t&&!isNaN(t)||(t=""),n=s(n),t=s(t),null===n||null===t)continue;a.push(n+"="+t)}return a.length?o+a.join("&"):""},t.parse=function(e){for(var o,t=/([^=?#&]+)=?([^&]*)/g,r={};o=t.exec(e);){var s=n(o[1]),a=n(o[2]);null===s||null===a||s in r||(r[s]=a)}return r};var a=function(e,o){if(o=o.split(":")[0],!(e=+e))return!1;switch(o){case"http":case"ws":return 80!==e;case"https":case"wss":return 443!==e;case"ftp":return 21!==e;case"gopher":return 70!==e;case"file":return!1}return 0!==e},c=t,p=/^[\x00-\x20\u00a0\u1680\u2000-\u200a\u2028\u2029\u202f\u205f\u3000\ufeff]+/,l=/[\n\r\t]/g,i=/^[A-Za-z][A-Za-z0-9+-.]*:\/\//,u=/:\d+$/,h=/^([a-z][a-z0-9.+-]*:)?(\/\/)?([\\/]+)?([\S\s]*)/i,f=/^[a-zA-Z]:/;function d(e){return(e||"").toString().replace(p,"")}var m=[["#","hash"],["?","query"],function(e,o){return y(o.protocol)?e.replace(/\\/g,"/"):e},["/","pathname"],["@","auth",1],[NaN,"host",void 0,1,1],[/:(\d*)$/,"port",void 0,1],[NaN,"hostname",void 0,1,1]],g={hash:1,query:1};function w(o){var t,r=("undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:{}).location||{},n={},s=typeof(o=o||r);if("blob:"===o.protocol)n=new C(unescape(o.pathname),{});else if("string"===s)for(t in n=new C(o,{}),g)delete n[t];else if("object"===s){for(t in o)t in g||(n[t]=o[t]);void 0===n.slashes&&(n.slashes=i.test(o.href))}return n}function y(e){return"file:"===e||"ftp:"===e||"http:"===e||"https:"===e||"ws:"===e||"wss:"===e}function v(e,o){e=(e=d(e)).replace(l,""),o=o||{};var t,r=h.exec(e),n=r[1]?r[1].toLowerCase():"",s=!!r[2],a=!!r[3],c=0;return s?a?(t=r[2]+r[3]+r[4],c=r[2].length+r[3].length):(t=r[2]+r[4],c=r[2].length):a?(t=r[3]+r[4],c=r[3].length):t=r[4],"file:"===n?c>=2&&(t=t.slice(2)):y(n)?t=r[4]:n?s&&(t=t.slice(2)):c>=2&&y(o.protocol)&&(t=r[4]),{protocol:n,slashes:s||y(n),slashesCount:c,rest:t}}function C(e,o,t){if(e=(e=d(e)).replace(l,""),!(this instanceof C))return new C(e,o,t);var r,n,s,p,i,u,h=m.slice(),g=typeof o,b=this,I=0;for("object"!==g&&"string"!==g&&(t=o,o=null),t&&"function"!=typeof t&&(t=c.parse),r=!(n=v(e||"",o=w(o))).protocol&&!n.slashes,b.slashes=n.slashes||r&&o.slashes,b.protocol=n.protocol||o.protocol||"",e=n.rest,("file:"===n.protocol&&(2!==n.slashesCount||f.test(e))||!n.slashes&&(n.protocol||n.slashesCount<2||!y(b.protocol)))&&(h[3]=[/(.*)/,"pathname"]);I<h.length;I++)"function"!=typeof(p=h[I])?(s=p[0],u=p[1],s!=s?b[u]=e:"string"==typeof s?~(i="@"===s?e.lastIndexOf(s):e.indexOf(s))&&("number"==typeof p[2]?(b[u]=e.slice(0,i),e=e.slice(i+p[2])):(b[u]=e.slice(i),e=e.slice(0,i))):(i=s.exec(e))&&(b[u]=i[1],e=e.slice(0,i.index)),b[u]=b[u]||r&&p[3]&&o[u]||"",p[4]&&(b[u]=b[u].toLowerCase())):e=p(e,b);t&&(b.query=t(b.query)),r&&o.slashes&&"/"!==b.pathname.charAt(0)&&(""!==b.pathname||""!==o.pathname)&&(b.pathname=function(e,o){if(""===e)return o;for(var t=(o||"/").split("/").slice(0,-1).concat(e.split("/")),r=t.length,n=t[r-1],s=!1,a=0;r--;)"."===t[r]?t.splice(r,1):".."===t[r]?(t.splice(r,1),a++):a&&(0===r&&(s=!0),t.splice(r,1),a--);return s&&t.unshift(""),"."!==n&&".."!==n||t.push(""),t.join("/")}(b.pathname,o.pathname)),"/"!==b.pathname.charAt(0)&&y(b.protocol)&&(b.pathname="/"+b.pathname),a(b.port,b.protocol)||(b.host=b.hostname,b.port=""),b.username=b.password="",b.auth&&(~(i=b.auth.indexOf(":"))?(b.username=b.auth.slice(0,i),b.username=encodeURIComponent(decodeURIComponent(b.username)),b.password=b.auth.slice(i+1),b.password=encodeURIComponent(decodeURIComponent(b.password))):b.username=encodeURIComponent(decodeURIComponent(b.auth)),b.auth=b.password?b.username+":"+b.password:b.username),b.origin="file:"!==b.protocol&&y(b.protocol)&&b.host?b.protocol+"//"+b.host:"null",b.href=b.toString()}C.prototype={set:function(e,o,t){var r=this;switch(e){case"query":"string"==typeof o&&o.length&&(o=(t||c.parse)(o)),r[e]=o;break;case"port":r[e]=o,a(o,r.protocol)?o&&(r.host=r.hostname+":"+o):(r.host=r.hostname,r[e]="");break;case"hostname":r[e]=o,r.port&&(o+=":"+r.port),r.host=o;break;case"host":r[e]=o,u.test(o)?(o=o.split(":"),r.port=o.pop(),r.hostname=o.join(":")):(r.hostname=o,r.port="");break;case"protocol":r.protocol=o.toLowerCase(),r.slashes=!t;break;case"pathname":case"hash":if(o){var n="pathname"===e?"/":"#";r[e]=o.charAt(0)!==n?n+o:o}else r[e]=o;break;case"username":case"password":r[e]=encodeURIComponent(o);break;case"auth":var s=o.indexOf(":");~s?(r.username=o.slice(0,s),r.username=encodeURIComponent(decodeURIComponent(r.username)),r.password=o.slice(s+1),r.password=encodeURIComponent(decodeURIComponent(r.password))):r.username=encodeURIComponent(decodeURIComponent(o))}for(var p=0;p<m.length;p++){var l=m[p];l[4]&&(r[l[1]]=r[l[1]].toLowerCase())}return r.auth=r.password?r.username+":"+r.password:r.username,r.origin="file:"!==r.protocol&&y(r.protocol)&&r.host?r.protocol+"//"+r.host:"null",r.href=r.toString(),r},toString:function(e){e&&"function"==typeof e||(e=c.stringify);var o,t=this,r=t.host,n=t.protocol;n&&":"!==n.charAt(n.length-1)&&(n+=":");var s=n+(t.protocol&&t.slashes||y(t.protocol)?"//":"");return t.username?(s+=t.username,t.password&&(s+=":"+t.password),s+="@"):t.password?(s+=":"+t.password,s+="@"):"file:"!==t.protocol&&y(t.protocol)&&!r&&"/"!==t.pathname&&(s+="@"),(":"===r[r.length-1]||u.test(t.hostname)&&!t.port)&&(r+=":"),s+=r+t.pathname,(o="object"==typeof t.query?e(t.query):t.query)&&(s+="?"!==o.charAt(0)?"?"+o:o),t.hash&&(s+=t.hash),s}},C.extractProtocol=v,C.location=w,C.trimLeft=d,C.qs=c;const b=o({__proto__:null,default:C},[C]);export{b as i};
