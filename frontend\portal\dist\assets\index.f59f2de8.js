/*! 
 Build based on gin-vue-admin 
 Time : 1754834760000 */
import e from"./menuItem.a9d1cab1.js";import t from"./asyncSubmenu.edbb23cb.js";import{c as o,h as n,a as r,k as s,w as a,b as l,F as u,A as i,l as f,z as c}from"./index.0f69a27d.js";const d=Object.assign({name:"AsideComponent"},{props:{routerInfo:{type:Object,default:()=>null},isCollapse:{default:function(){return!1},type:Boolean},theme:{default:function(){return{}},type:Object}},setup(d){const m=d,h=o((()=>m.routerInfo.children&&m.routerInfo.children.filter((e=>!e.hidden)).length?t:e));return(e,t)=>{const o=n("AsideComponent");return d.routerInfo.hidden?f("",!0):(r(),s(c(h.value),{key:0,"is-collapse":d.isCollapse,theme:d.theme,"router-info":d.routerInfo},{default:a((()=>[d.routerInfo.children&&d.routerInfo.children.length?(r(!0),l(u,{key:0},i(d.routerInfo.children,(e=>(r(),s(o,{key:e.name,"is-collapse":!1,"router-info":e,theme:d.theme},null,8,["router-info","theme"])))),128)):f("",!0)])),_:1},8,["is-collapse","theme","router-info"]))}}});export{d as default};
