/**
 * WebSocket 工具类
 * 封装项目中所有 WebSocket 连接相关的功能
 */

import { ref } from 'vue'

// WebSocket 连接状态枚举
export const WS_STATUS = {
  CONNECTING: 0,
  OPEN: 1,
  CLOSING: 2,
  CLOSED: 3
}

// WebSocket 动作类型枚举
export const WS_ACTIONS = {
  LOGOUT: 1, // 注销登录
  GET_TOKEN: 2, // 获取token
  LAUNCH_APP: 3, // 启动应用
  LOGIN_SYNC: 4, // 登录同步
  LOGOUT_SYNC: 5 // 退出同步
}

// WebSocket 消息类型枚举
export const WS_MESSAGE_TYPES = {
  REQUEST: 'request',
  RESPONSE: 'response',
  BROADCAST: 'broadcast'
}

// WebSocket 同步动作枚举
export const WS_SYNC_ACTIONS = {
  LOGIN: 'login',
  LOGOUT: 'logout'
}

/**
 * WebSocket 管理器类
 */
class WebSocketManager {
  constructor() {
    this.connection = null // 单个连接
    this.defaultPort = 50001
    this.defaultHost = '127.0.0.1'
    this.messageHandlers = [] // 消息处理器数组
    this.reconnectTimer = null // 重连定时器
    this.reconnectDelay = 3000 // 重连延迟3秒
    this.loginSyncHandlers = [] // 登录同步处理器
    this.logoutSyncHandlers = [] // 退出同步处理器
    this.isAutoSyncEnabled = true // 是否启用自动同步
    this.persistentHandlers = { // 持久化处理器，用于重连后恢复
      loginSyncHandlers: [],
      logoutSyncHandlers: []
    }
  }

  /**
   * 创建或获取 WebSocket 连接（持久连接）
   * @param {Object} options 连接选项
   * @param {number} options.port WebSocket端口，默认50001
   * @param {string} options.host WebSocket主机，默认127.0.0.1
   * @param {boolean} options.useSSL 是否使用SSL，默认根据平台判断
   * @param {number} options.timeout 连接超时时间，默认2000ms
   * @param {boolean} options.persistent 是否为持久连接，默认true
   * @param {Function} options.onOpen 连接打开回调
   * @param {Function} options.onMessage 消息接收回调
   * @param {Function} options.onError 错误回调
   * @param {Function} options.onClose 连接关闭回调
   * @returns {Promise} 返回连接Promise
   */
  createConnection(options = {}) {
    const {
      port = this.defaultPort,
      host = this.defaultHost,
      useSSL = this._shouldUseSSL(),
      timeout = 2000,
      persistent = true,
      onOpen,
      onMessage,
      onError,
      onClose
    } = options

    return new Promise((resolve, reject) => {
      // 如果连接已存在且处于打开状态，直接返回
      if (this.connection) {
        if (this.connection.ws.readyState === WebSocket.OPEN) {
          logger.log('使用现有的打开连接')
          // 如果有新的消息处理器，添加到现有连接
          if (onMessage) {
            this.addMessageHandler(onMessage)
          }
          resolve(this.connection)
          return
        } else if (this.connection.ws.readyState === WebSocket.CONNECTING) {
          logger.log('连接正在建立中，等待完成')
          // 连接正在建立中，等待它完成
          this.connection.ws.addEventListener('open', () => {
            logger.log('等待的连接已打开')
            if (onMessage) {
              this.addMessageHandler(onMessage)
            }
            resolve(this.connection)
          })
          this.connection.ws.addEventListener('error', (error) => {
            logger.log('等待的连接失败', error)
            reject(error)
          })
          return
        } else {
          // 连接已关闭或失败，清除旧连接
          logger.log(`移除无效连接，状态: ${this.connection.ws.readyState}`)
          this.connection = null
          this.messageHandlers = []
        }
      }

      const protocol = useSSL ? 'wss' : 'ws'
      const wsUrl = `${protocol}://${host}:${port}`

      logger.log(`创建新的WebSocket连接: ${wsUrl}`)

      const ws = new WebSocket(wsUrl)
      const connection = {
        ws,
        url: wsUrl,
        connecting: ref(true),
        connected: ref(false),
        persistent,
        port,
        host,
        useSSL
      }

      let timeoutId = null

      // 设置超时
      if (timeout > 0) {
        timeoutId = setTimeout(() => {
          logger.log(`WebSocket连接超时: ${wsUrl}`)
          ws.close()
          connection.connecting.value = false
          reject(new Error('WebSocket connection timeout'))
        }, timeout)
      }

      // 连接打开事件
      ws.onopen = () => {
        logger.log(`WebSocket连接成功: ${wsUrl}`)
        if (timeoutId) clearTimeout(timeoutId)

        connection.connecting.value = false
        connection.connected.value = true
        this.connection = connection
        this._clearReconnectTimer() // 清除重连定时器
        logger.log('连接已建立')

        // 恢复持久化的同步处理器（重连时需要）
        this._restorePersistentHandlers()

        // 判断未登录时，发送获取token消息
        const token = localStorage.getItem('accessToken') || localStorage.getItem('token')
        if (!token) {
          setTimeout(() => {
            this.sendMessage({
              action: 2,
              msg: {
                type: 'auto_sync_request',
                timestamp: Date.now()
              },
              platform: document.location.hostname
            })
          }, 1000)
        }

        // 添加消息处理器
        if (onMessage) {
          this.addMessageHandler(onMessage)
        }

        if (onOpen) onOpen(connection)
        resolve(connection)
      }

      // 消息接收事件 - 支持多个处理器
      ws.onmessage = (event) => {
        logger.log(`WebSocket收到消息: ${event.data}`)

        // 处理登录同步消息
        this._handleSyncMessage(event, connection)

        // 调用所有注册的消息处理器
        this.messageHandlers.forEach(handler => {
          try {
            handler(event, connection)
          } catch (error) {
            logger.log(`消息处理器执行失败:`, error)
          }
        })
      }

      // 错误事件
      ws.onerror = (error) => {
        logger.log(`WebSocket连接错误: ${wsUrl}`, error)
        if (timeoutId) clearTimeout(timeoutId)

        connection.connecting.value = false
        connection.connected.value = false

        if (onError) onError(error, connection)

        // 如果是持久连接，尝试重连
        if (persistent) {
          this._attemptReconnect(connection)
        } else {
          reject(error)
        }
      }

      // 连接关闭事件
      ws.onclose = (event) => {
        logger.log(`WebSocket连接关闭: ${wsUrl}`, event.code, event.reason)
        if (timeoutId) clearTimeout(timeoutId)

        connection.connecting.value = false
        connection.connected.value = false

        if (onClose) onClose(connection)

        // 如果是持久连接且不是主动关闭，尝试重连
        if (persistent && event.code !== 1000) {
          this._attemptReconnect(connection)
        } else {
          this.connection = null
          this.messageHandlers = []
          this._clearReconnectTimer()
        }
      }
    })
  }

  /**
   * 添加消息处理器
   * @param {Function} handler 消息处理函数
   */
  addMessageHandler(handler) {
    this.messageHandlers.push(handler)
  }

  /**
   * 移除消息处理器
   * @param {Function} handler 要移除的处理函数
   */
  removeMessageHandler(handler) {
    const index = this.messageHandlers.indexOf(handler)
    if (index > -1) {
      this.messageHandlers.splice(index, 1)
    }
  }

  /**
   * 尝试重连
   * @param {Object} connectionInfo 连接信息
   * @private
   */
  _attemptReconnect(connectionInfo) {
    // 清除之前的重连定时器
    this._clearReconnectTimer()

    logger.log('WebSocket开始重连')

    this.reconnectTimer = setTimeout(() => {
      this.createConnection({
        port: connectionInfo.port,
        host: connectionInfo.host,
        useSSL: connectionInfo.useSSL,
        persistent: connectionInfo.persistent
      }).catch(error => {
        logger.log('WebSocket重连失败', error)
        // 重连失败后继续尝试重连
        this._attemptReconnect(connectionInfo)
      })
    }, this.reconnectDelay) // 固定3秒延迟
  }

  /**
   * 清除重连定时器
   * @private
   */
  _clearReconnectTimer() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
  }

  /**
   * 发送消息
   * @param {Object|string} message 要发送的消息
   * @returns {boolean} 发送是否成功
   */
  sendMessage(message) {
    if (!this.connection || this.connection.ws.readyState !== WebSocket.OPEN) {
      logger.log('WebSocket连接不可用')
      return false
    }

    try {
      const messageStr = typeof message === 'string' ? message : JSON.stringify(message)
      this.connection.ws.send(messageStr)
      logger.log(`WebSocket发送消息: ${messageStr}`)
      return true
    } catch (error) {
      logger.log('WebSocket发送消息失败:', error)
      return false
    }
  }

  /**
   * 关闭连接
   */
  closeConnection() {
    if (this.connection) {
      logger.log('关闭WebSocket连接')
      this.connection.ws.close()
      this.connection = null
    }
  }

  /**
   * 关闭所有连接
   */
  closeAllConnections() {
    logger.log('关闭所有WebSocket连接')

    // 清除重连定时器
    this._clearReconnectTimer()

    // 关闭连接
    if (this.connection) {
      this.connection.ws.close()
    }

    // 清理所有资源
    this.connection = null
    this.messageHandlers = []
  }

  /**
   * 获取连接
   * @returns {Object|null} 连接对象
   */
  getConnection() {
    return this.connection
  }

  /**
   * 获取连接状态
   * @returns {number} WebSocket状态
   */
  getConnectionStatus() {
    return this.connection ? this.connection.ws.readyState : WS_STATUS.CLOSED
  }

  /**
   * 处理登录同步消息
   * @param {MessageEvent} event 消息事件
   * @param {Object} connection 连接对象
   * @private
   */
  _handleSyncMessage(event, connection) {
    try {
      const message = JSON.parse(event.data)

      // 检查是否是同步消息（action 0 为登录同步，action 1 为退出同步）
      if (message.action === 0 || message.action === 1) {
        // 检查msg中是否包含同步字段
        if (message.msg) {
          // 检查是否是自己发送的消息，如果是则忽略
          if (message.msg.type === WS_MESSAGE_TYPES.BROADCAST && message.msg.senderId === PAGE_CONNECTION_ID) {
            logger.log('忽略自己发送的同步消息:', message)
            return
          }

          if (message.action === 0 && message.msg.action === WS_SYNC_ACTIONS.LOGIN) {
            logger.log('收到登录同步消息:', message)
            // 构造登录数据
            const loginData = {
              token: message.msg.token,
              refreshToken: message.msg.refreshToken,
              realm: 'default',
              timestamp: message.msg.timestamp || Date.now(),
              source: message.msg.source || 'client'
            }
            this._triggerLoginSync(loginData)
          } else if (message.action === 1 && message.msg.action === WS_SYNC_ACTIONS.LOGOUT) {
            logger.log('收到退出同步消息:', message)
            // 构造退出数据
            const logoutData = {
              reason: message.msg.reason,
              timestamp: message.msg.timestamp || Date.now(),
              source: message.msg.source || 'client'
            }
            this._triggerLogoutSync(logoutData)
          }
        }
      } else {
        // 不是同步消息，忽略
        logger.log('忽略非同步消息:', message)
      }
    } catch (error) {
      // 不是JSON格式或不是同步消息，忽略
      logger.log('不是JSON格式或不是同步消息，忽略:', event.data)
    }
  }

  /**
   * 触发登录同步
   * @param {Object} loginData 登录数据
   * @private
   */
  _triggerLoginSync(loginData) {
    this.loginSyncHandlers.forEach(handler => {
      try {
        handler(loginData)
      } catch (error) {
        logger.log('登录同步处理器执行失败:', error)
      }
    })
  }

  /**
   * 触发退出同步
   * @param {Object} logoutData 退出数据
   * @private
   */
  _triggerLogoutSync(logoutData) {
    this.logoutSyncHandlers.forEach(handler => {
      try {
        handler(logoutData)
      } catch (error) {
        logger.log('退出同步处理器执行失败:', error)
      }
    })
  }

  /**
   * 添加登录同步处理器
   * @param {Function} handler 处理函数
   */
  addLoginSyncHandler(handler) {
    this.loginSyncHandlers.push(handler)
    // 同时保存到持久化数组，用于重连后恢复
    if (!this.persistentHandlers.loginSyncHandlers.includes(handler)) {
      this.persistentHandlers.loginSyncHandlers.push(handler)
      logger.log('登录同步处理器已添加并持久化，当前数量:', this.loginSyncHandlers.length)
    }
  }

  /**
   * 移除登录同步处理器
   * @param {Function} handler 处理函数
   */
  removeLoginSyncHandler(handler) {
    const index = this.loginSyncHandlers.indexOf(handler)
    if (index > -1) {
      this.loginSyncHandlers.splice(index, 1)
    }

    // 同时从持久化数组中移除
    const persistentIndex = this.persistentHandlers.loginSyncHandlers.indexOf(handler)
    if (persistentIndex > -1) {
      this.persistentHandlers.loginSyncHandlers.splice(persistentIndex, 1)
      logger.log('登录同步处理器已移除，当前数量:', this.loginSyncHandlers.length)
    }
  }

  /**
   * 添加退出同步处理器
   * @param {Function} handler 处理函数
   */
  addLogoutSyncHandler(handler) {
    this.logoutSyncHandlers.push(handler)
    // 同时保存到持久化数组，用于重连后恢复
    if (!this.persistentHandlers.logoutSyncHandlers.includes(handler)) {
      this.persistentHandlers.logoutSyncHandlers.push(handler)
      logger.log('退出同步处理器已添加并持久化，当前数量:', this.logoutSyncHandlers.length)
    }
  }

  /**
   * 移除退出同步处理器
   * @param {Function} handler 处理函数
   */
  removeLogoutSyncHandler(handler) {
    const index = this.logoutSyncHandlers.indexOf(handler)
    if (index > -1) {
      this.logoutSyncHandlers.splice(index, 1)
    }

    // 同时从持久化数组中移除
    const persistentIndex = this.persistentHandlers.logoutSyncHandlers.indexOf(handler)
    if (persistentIndex > -1) {
      this.persistentHandlers.logoutSyncHandlers.splice(persistentIndex, 1)
      logger.log('退出同步处理器已移除，当前数量:', this.logoutSyncHandlers.length)
    }
  }

  /**
   * 恢复持久化的同步处理器
   * 在重连成功后调用，重新注册所有持久化的处理器
   * @private
   */
  _restorePersistentHandlers() {
    logger.log('开始恢复持久化的同步处理器')

    // 清空当前处理器数组
    this.loginSyncHandlers = []
    this.logoutSyncHandlers = []

    // 恢复登录同步处理器
    this.persistentHandlers.loginSyncHandlers.forEach(handler => {
      this.loginSyncHandlers.push(handler)
    })

    // 恢复退出同步处理器
    this.persistentHandlers.logoutSyncHandlers.forEach(handler => {
      this.logoutSyncHandlers.push(handler)
    })

    logger.log(`恢复完成 - 登录处理器: ${this.loginSyncHandlers.length}, 退出处理器: ${this.logoutSyncHandlers.length}`)
  }

  /**
   * 发送登录同步消息
   * @param {Object} loginData 登录数据
   */
  sendLoginSync(loginData) {
    const message = {
      action: 0, // 登录同步动作
      msg: {
        token: loginData.token || loginData.accessToken,
        refreshToken: loginData.refreshToken,
        realm: 'default',
        // 新增的同步字段（首字母小写）
        type: WS_MESSAGE_TYPES.BROADCAST,
        action: WS_SYNC_ACTIONS.LOGIN,
        senderId: PAGE_CONNECTION_ID,
        timestamp: Date.now(),
        source: loginData.source || 'browser'
      },
      platform: document.location.hostname
    }

    return this.sendMessage(message)
  }

  /**
   * 发送退出同步消息
   * @param {Object} logoutData 退出数据
   */
  sendLogoutSync(logoutData) {
    const message = {
      action: 1, // 退出登录动作
      msg: {
        // 新增的同步字段（首字母小写）
        type: WS_MESSAGE_TYPES.BROADCAST,
        action: WS_SYNC_ACTIONS.LOGOUT,
        senderId: PAGE_CONNECTION_ID,
        timestamp: Date.now(),
        reason: logoutData.reason || 'user_logout',
        source: logoutData.source || 'browser'
      },
      platform: document.location.hostname
    }

    return this.sendMessage(message)
  }

  /**
   * 判断是否应该使用SSL
   * @returns {boolean}
   * @private
   */
  _shouldUseSSL() {
    const platform = navigator.platform
    return platform.indexOf('Mac') === 0 || platform === 'MacIntel'
  }
}

// 创建全局WebSocket管理器实例
const wsManager = new WebSocketManager()

// 页面级别的共享连接配置
const DEFAULT_PORT = 50001

/**
 * 生成唯一的页面连接ID
 * @returns {string} 唯一的连接ID
 */
const generateUniqueId = () => {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2, 11)
  return `page-${timestamp}-${random}`
}

// 每个页面使用唯一的连接ID
const PAGE_CONNECTION_ID = generateUniqueId()

// 连接创建锁，防止并发创建多个连接
let connectionPromise = null

/**
 * 获取页面级别的共享WebSocket连接
 * @param {number} port WebSocket端口
 * @returns {Promise} 返回页面共享连接
 */
const getPageSharedConnection = async(port = DEFAULT_PORT) => {
  // 如果已经有连接创建中，等待它完成
  if (connectionPromise) {
    logger.log(`等待现有连接创建完成，连接ID: ${PAGE_CONNECTION_ID}`)
    try {
      return await connectionPromise
    } catch (error) {
      // 如果之前的连接创建失败，清除promise并重新尝试
      connectionPromise = null
    }
  }

  // 检查是否已有可用连接
  if (wsManager.connection) {
    if (wsManager.connection.ws.readyState === WebSocket.OPEN) {
      logger.log('使用现有的打开连接')
      return wsManager.connection
    } else if (wsManager.connection.ws.readyState === WebSocket.CONNECTING) {
      logger.log('连接正在建立中，等待完成')
      // 等待连接完成
      return new Promise((resolve, reject) => {
        wsManager.connection.ws.addEventListener('open', () => {
          logger.log('等待的连接已打开')
          resolve(wsManager.connection)
        })
        wsManager.connection.ws.addEventListener('error', (error) => {
          logger.log('等待的连接失败', error)
          reject(error)
        })
      })
    } else {
      // 连接已关闭或失败，清理并重新创建
      logger.log(`清理无效连接，状态: ${wsManager.connection.ws.readyState}`)
      wsManager.connection = null
      wsManager.messageHandlers = []
    }
  }

  // 创建新连接
  logger.log('创建新的页面共享连接')
  connectionPromise = wsManager.createConnection({
    port,
    persistent: true,
    timeout: 5000
  }).then(connection => {
    logger.log('页面共享连接创建成功')
    connectionPromise = null // 清除promise
    return connection
  }).catch(error => {
    logger.log('页面共享连接创建失败:', error)
    connectionPromise = null // 清除promise
    throw error
  })

  return connectionPromise
}

/**
 * 应用启动相关的WebSocket连接
 * @param {string} appName 应用名称
 * @param {number} port WebSocket端口
 * @returns {Promise}
 */
export const connectForAppLaunch = (appName, port = DEFAULT_PORT) => {
  return new Promise((resolve, reject) => {
    let messageReceived = false
    let timeoutId

    // 创建一次性消息处理器
    const messageHandler = (event) => {
      try {
        const response = event.data
        logger.log('应用启动响应:', response)
        messageReceived = true
        if (timeoutId) clearTimeout(timeoutId)

        // 移除这个一次性处理器
        wsManager.removeMessageHandler(messageHandler)

        if (response.startsWith('Ok')) {
          resolve(response)
        } else if (response.startsWith('Failed')) {
          reject(new Error(response))
        } else {
          resolve(response)
        }
      } catch (error) {
        logger.log('解析应用启动响应失败:', error)
        if (timeoutId) clearTimeout(timeoutId)
        wsManager.removeMessageHandler(messageHandler)
        reject(error)
      }
    }

    const connectAndSend = async() => {
      try {
        // 使用页面级别的共享连接
        await getPageSharedConnection(port)

        // 添加消息处理器
        wsManager.addMessageHandler(messageHandler)

        // 发送启动应用消息
        const message = {
          action: WS_ACTIONS.LAUNCH_APP,
          msg: appName
        }

        if (!wsManager.sendMessage(message)) {
          wsManager.removeMessageHandler(messageHandler)
          reject(new Error('发送启动应用消息失败'))
          return
        }

        // 设置响应超时
        timeoutId = setTimeout(() => {
          if (!messageReceived) {
            logger.log('应用启动响应超时')
            wsManager.removeMessageHandler(messageHandler)
            reject(new Error('应用启动响应超时'))
          }
        }, 10000)
      } catch (error) {
        if (timeoutId) clearTimeout(timeoutId)
        wsManager.removeMessageHandler(messageHandler)
        reject(error)
      }
    }

    connectAndSend()
  })
}

/**
 * 获取Token的WebSocket连接
 * @param {number} port WebSocket端口
 * @param {number} timeout 超时时间（毫秒），默认2000ms
 * @returns {Promise}
 */
export const connectForToken = (port = DEFAULT_PORT, timeout = 2000) => {
  return new Promise((resolve) => {
    const clineData = {
      action: WS_ACTIONS.GET_TOKEN,
      msg: '',
      platform: document.location.hostname
    }

    let timeoutId = null
    let messageReceived = false

    // 创建一次性消息处理器
    const messageHandler = async(event) => {
      try {
        const data = JSON.parse(event.data)
        logger.log('Token响应:', data)

        messageReceived = true
        if (timeoutId) clearTimeout(timeoutId)

        // 移除这个一次性处理器
        wsManager.removeMessageHandler(messageHandler)

        // 根据原代码的数据结构处理
        if (data?.msg?.token) {
          const tokenInfo = {
            accessToken: data.msg.token,
            refreshToken: data.msg.refreshToken,
            expiresAt: new Date(new Date().getTime() + 7 * 24 * 60 * 60 * 1000),
            tokenType: 'Bearer'
          }
          resolve(tokenInfo)
        } else {
          resolve(null)
        }
      } catch (error) {
        logger.log('解析Token响应失败:', error)
        messageReceived = true
        if (timeoutId) clearTimeout(timeoutId)
        wsManager.removeMessageHandler(messageHandler)
        resolve(null)
      }
    }

    // 设置超时处理
    timeoutId = setTimeout(() => {
      if (!messageReceived) {
        logger.log(`Token获取超时 (${timeout}ms)，认为没有获取到`)
        wsManager.removeMessageHandler(messageHandler)
        resolve(null)
      }
    }, timeout)

    // 使用页面级别的共享连接
    getPageSharedConnection(port).then(() => {
      // 添加消息处理器
      wsManager.addMessageHandler(messageHandler)
      wsManager.sendMessage(clineData)
    }).catch(() => {
      logger.log('Token获取连接错误')
      if (timeoutId) clearTimeout(timeoutId)
      wsManager.removeMessageHandler(messageHandler)
      resolve(null)
    })
  })
}

/**
 * 注销登录的WebSocket连接
 * @param {number} port WebSocket端口
 * @returns {Promise}
 */
export const connectForLogout = async(port = DEFAULT_PORT) => {
  const clineData = {
    action: WS_ACTIONS.LOGOUT,
    msg: '',
    platform: document.location.hostname
  }

  try {
    // 使用页面级别的共享连接
    const connection = await getPageSharedConnection(port)

    // 直接发送注销消息，不需要等待响应
    wsManager.sendMessage(clineData)
    logger.log('注销消息已发送')

    return connection
  } catch (error) {
    logger.log('注销连接失败:', error)
    throw error
  }
}

/**
 * 发送WebSocket消息到页面共享连接
 * @param {Object|string} message 消息内容
 * @returns {boolean}
 */
export const sendWebSocketMessage = (message) => {
  return wsManager.sendMessage(message)
}

/**
 * 关闭页面共享WebSocket连接
 */
export const closeWebSocketConnection = () => {
  wsManager.closeConnection()
}

/**
 * 关闭所有WebSocket连接
 */
export const closeAllWebSocketConnections = () => {
  wsManager.closeAllConnections()
}

/**
 * 获取页面共享WebSocket连接状态
 * @returns {number}
 */
export const getWebSocketStatus = () => {
  return wsManager.getConnectionStatus()
}

/**
 * 检查页面共享WebSocket连接是否可用
 * @returns {boolean}
 */
export const isWebSocketConnected = () => {
  return wsManager.getConnectionStatus() === WS_STATUS.OPEN
}

/**
 * 通用的WebSocket连接方法（兼容性保留）
 * @param {Object} options 连接选项
 * @returns {Promise}
 */
export const createWebSocketConnection = (options) => {
  return wsManager.createConnection(options)
}

/**
 * 获取页面级别的共享WebSocket连接
 * @param {number} port WebSocket端口
 * @returns {Promise}
 */
export const getPersistentConnection = async(port = DEFAULT_PORT) => {
  return getPageSharedConnection(port)
}

/**
 * 添加消息监听器到页面共享连接
 * @param {Function} handler 消息处理函数
 */
export const addPersistentMessageHandler = (handler) => {
  wsManager.addMessageHandler(handler)
}

/**
 * 移除消息监听器
 * @param {Function} handler 要移除的处理函数
 */
export const removePersistentMessageHandler = (handler) => {
  wsManager.removeMessageHandler(handler)
}

/**
 * 向页面共享连接发送消息
 * @param {Object|string} message 消息内容
 * @returns {boolean}
 */
export const sendToPersistentConnection = (message) => {
  return wsManager.sendMessage(message)
}

/**
 * 检查页面共享连接是否可用
 * @returns {boolean}
 */
export const isPageConnectionAvailable = () => {
  return wsManager.getConnectionStatus() === WS_STATUS.OPEN
}

/**
 * 获取当前页面的连接ID
 * @returns {string} 页面连接ID
 */
export const getPageConnectionId = () => {
  return PAGE_CONNECTION_ID
}

/**
 * 发送登录同步消息
 * @param {Object} loginData 登录数据
 * @returns {boolean} 发送是否成功
 */
export const sendLoginSync = (loginData) => {
  return wsManager.sendLoginSync(loginData)
}

/**
 * 发送退出同步消息
 * @param {Object} logoutData 退出数据
 * @returns {boolean} 发送是否成功
 */
export const sendLogoutSync = (logoutData = {}) => {
  return wsManager.sendLogoutSync(logoutData)
}

/**
 * 添加登录同步处理器
 * @param {Function} handler 处理函数，接收登录数据作为参数
 */
export const addLoginSyncHandler = (handler) => {
  wsManager.addLoginSyncHandler(handler)
}

/**
 * 移除登录同步处理器
 * @param {Function} handler 要移除的处理函数
 */
export const removeLoginSyncHandler = (handler) => {
  wsManager.removeLoginSyncHandler(handler)
}

/**
 * 添加退出同步处理器
 * @param {Function} handler 处理函数，接收退出数据作为参数
 */
export const addLogoutSyncHandler = (handler) => {
  wsManager.addLogoutSyncHandler(handler)
}

/**
 * 移除退出同步处理器
 * @param {Function} handler 要移除的处理函数
 */
export const removeLogoutSyncHandler = (handler) => {
  wsManager.removeLogoutSyncHandler(handler)
}

/**
 * 初始化登录同步功能
 * 自动建立WebSocket连接并设置同步处理器
 * @param {Object} options 配置选项
 * @param {Function} options.onLogin 登录同步回调
 * @param {Function} options.onLogout 退出同步回调
 * @param {number} options.port WebSocket端口
 * @returns {Promise} 返回连接Promise
 */
export const initLoginSync = async(options = {}) => {
  const {
    onLogin,
    onLogout,
    port = DEFAULT_PORT
  } = options

  try {
    // 建立页面共享连接
    await getPageSharedConnection(port)
    logger.log('登录同步连接已建立')

    // 添加同步处理器
    if (onLogin) {
      addLoginSyncHandler(onLogin)
    }

    if (onLogout) {
      addLogoutSyncHandler(onLogout)
    }

    return true
  } catch (error) {
    logger.log('初始化登录同步失败:', error)
    throw error
  }
}

export { wsManager }
export default wsManager
