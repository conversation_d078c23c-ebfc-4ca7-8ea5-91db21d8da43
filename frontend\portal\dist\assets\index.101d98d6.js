/*! 
 Build based on gin-vue-admin 
 Time : 1754834760000 */
import a from"./header.42dfb3cd.js";import s from"./menu.19c43ef8.js";import{C as e,h as t,a as i,b as o,j as l,d as r,k as n}from"./index.0f69a27d.js";import"./logo.b56ac4ae.js";const d={class:"layout-page"},c={class:"layout-wrap"},u={id:"layoutMain",class:"layout-main"},m=Object.assign({name:"Client"},{setup:m=>(e.initIpcClient(),(e,m)=>{const f=t("router-view");return i(),o("div",d,[l(a),r("div",c,[l(s),r("div",u,[(i(),n(f,{key:e.$route.fullPath,style:{height:"100%",display:"flex","flex-direction":"column"}}))])])])})});export{m as default};
