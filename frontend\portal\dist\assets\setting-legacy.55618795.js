/*! 
 Build based on gin-vue-admin 
 Time : 1754834760000 */
!function(){function e(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var a,n,r="function"==typeof Symbol?Symbol:{},o=r.iterator||"@@iterator",i=r.toStringTag||"@@toStringTag";function s(e,r,o,i){var s=r&&r.prototype instanceof c?r:c,u=Object.create(s.prototype);return t(u,"_invoke",function(e,t,r){var o,i,s,c=0,u=r||[],d=!1,p={p:0,n:0,v:a,a:v,f:v.bind(a,4),d:function(e,t){return o=e,i=0,s=a,p.n=t,l}};function v(e,t){for(i=e,s=t,n=0;!d&&c&&!r&&n<u.length;n++){var r,o=u[n],v=p.p,f=o[2];e>3?(r=f===t)&&(s=o[(i=o[4])?5:(i=3,3)],o[4]=o[5]=a):o[0]<=v&&((r=e<2&&v<o[1])?(i=0,p.v=t,p.n=o[1]):v<f&&(r=e<3||o[0]>t||t>f)&&(o[4]=e,o[5]=t,p.n=f,i=0))}if(r||e>1)return l;throw d=!0,t}return function(r,u,f){if(c>1)throw TypeError("Generator is already running");for(d&&1===u&&v(u,f),i=u,s=f;(n=i<2?a:s)||!d;){o||(i?i<3?(i>1&&(p.n=-1),v(i,s)):p.n=s:p.v=s);try{if(c=2,o){if(i||(r="next"),n=o[r]){if(!(n=n.call(o,s)))throw TypeError("iterator result is not an object");if(!n.done)return n;s=n.value,i<2&&(i=0)}else 1===i&&(n=o.return)&&n.call(o),i<2&&(s=TypeError("The iterator does not provide a '"+r+"' method"),i=1);o=a}else if((n=(d=p.n<0)?s:e.call(t,p))!==l)break}catch(n){o=a,i=1,s=n}finally{c=1}}return{value:n,done:d}}}(e,o,i),!0),u}var l={};function c(){}function u(){}function d(){}n=Object.getPrototypeOf;var p=[][o]?n(n([][o]())):(t(n={},o,(function(){return this})),n),v=d.prototype=c.prototype=Object.create(p);function f(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,d):(e.__proto__=d,t(e,i,"GeneratorFunction")),e.prototype=Object.create(v),e}return u.prototype=d,t(v,"constructor",d),t(d,"constructor",u),u.displayName="GeneratorFunction",t(d,i,"GeneratorFunction"),t(v),t(v,i,"Generator"),t(v,o,(function(){return this})),t(v,"toString",(function(){return"[object Generator]"})),(e=function(){return{w:s,m:f}})()}function t(e,a,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}t=function(e,a,n,r){if(a)o?o(e,a,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[a]=n;else{var i=function(a,n){t(e,a,(function(e){return this._invoke(a,n,e)}))};i("next",0),i("throw",1),i("return",2)}},t(e,a,n,r)}function a(e,t,a,n,r,o,i){try{var s=e[o](i),l=s.value}catch(e){return void a(e)}s.done?t(l):Promise.resolve(l).then(n,r)}function n(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var i=e.apply(t,n);function s(e){a(i,r,o,s,l,"next",e)}function l(e){a(i,r,o,s,l,"throw",e)}s(void 0)}))}}System.register(["./index-legacy.7db06653.js"],(function(t,a){"use strict";var r,o,i,s,l,c,u,d,p,v,f,g,b,m,x,h,y,k,w,_,C,S=document.createElement("style");return S.textContent='@charset "UTF-8";.setting-page[data-v-a035f85e]{display:flex;height:100%;font-family:PingFang SC,PingFang SC-Medium,Microsoft YaHei,\\5fae\\8f6f\\96c5\\9ed1}.sidebar[data-v-a035f85e]{width:60px;background:#667eea;display:flex;flex-direction:column;align-items:center;padding:20px 0;box-shadow:2px 0 8px rgba(0,0,0,.1)}.sidebar .sidebar-menu[data-v-a035f85e]{display:flex;flex-direction:column}.sidebar .sidebar-menu .menu-item[data-v-a035f85e]{width:48px;height:48px;display:flex;flex-direction:column;align-items:center;justify-content:center;border-radius:8px;cursor:pointer;transition:all .3s ease;color:rgba(255,255,255,.7);font-size:10px;margin-bottom:16px}.sidebar .sidebar-menu .menu-item[data-v-a035f85e]:last-child{margin-bottom:0}.sidebar .sidebar-menu .menu-item[data-v-a035f85e]:hover{background:rgba(255,255,255,.1);color:#fff}.sidebar .sidebar-menu .menu-item.active[data-v-a035f85e]{background:rgba(255,255,255,.2);color:#fff}.sidebar .sidebar-menu .menu-item .menu-icon[data-v-a035f85e]{width:16px;height:16px;margin-bottom:4px}.sidebar .sidebar-menu .menu-item .menu-icon[data-v-a035f85e]:before{content:"";display:block;width:100%;height:100%;background:currentColor;border-radius:2px}.main-content[data-v-a035f85e]{flex:1;padding:16px 32px 32px;overflow-y:auto}.setting-container[data-v-a035f85e]{height:calc(100% - 38px);background:white;border-radius:8px;overflow:hidden;margin:0 auto}.tabs-header[data-v-a035f85e]{display:flex;border-bottom:1px solid #e4e7ed}.tabs-header .tab-item[data-v-a035f85e]{padding:16px 24px 16px 0;cursor:pointer;color:#686e84;font-size:16px;font-family:PingFang SC,PingFang SC-Medium,Microsoft YaHei,\\5fae\\8f6f\\96c5\\9ed1;font-weight:400;line-height:22px;border-bottom:2px solid transparent;transition:all .3s ease}.tabs-header .tab-item[data-v-a035f85e]:hover{color:#536ce6}.tabs-header .tab-item.active[data-v-a035f85e]{color:#536ce6;font-family:PingFang SC,PingFang SC-Medium,Microsoft YaHei,\\5fae\\8f6f\\96c5\\9ed1;font-weight:600;background:white;position:relative}.tabs-header .tab-item.active .tab-item-text[data-v-a035f85e]{border-bottom:2px solid #536ce6;padding-top:20px;padding-bottom:20px}.tabs-header .tab-item.active[data-v-a035f85e]:after{content:"";position:absolute;bottom:-1px;left:0;right:0;height:1px;background:white}.tabs-content[data-v-a035f85e]{min-height:400px}.tabs-content .tab-panel[data-v-a035f85e]{padding:24px 0}.tabs-content .tab-panel .setting-update[data-v-a035f85e]{padding-bottom:24px;margin-bottom:24px;border-bottom:1px solid #f0f0f0}.loading-placeholder[data-v-a035f85e]{padding:32px 0;min-height:200px}.setting-section .setting-item[data-v-a035f85e]{margin-bottom:24px}.setting-section .setting-item[data-v-a035f85e]:last-child{margin-bottom:0}.setting-section .setting-platformAddress[data-v-a035f85e]{padding-bottom:24px;border-bottom:1px solid #f0f0f0}.setting-section .setting-label[data-v-a035f85e]{display:block;font-size:14px;font-weight:550;color:#303133;margin-bottom:12px}.setting-section .spa-label[data-v-a035f85e]{margin-left:10px;width:34px;height:34px;border:1px solid #dcdfe6;border-radius:4px;display:flex;flex-direction:column;align-items:center;justify-content:center;cursor:pointer}.setting-section .spa-label .icon[data-v-a035f85e]{width:16px;height:16px}.setting-section .setting-input[data-v-a035f85e]{width:320px}.setting-section .setting-input[data-v-a035f85e] .el-input__inner{height:40px;border-radius:6px}.setting-section .setting-input[data-v-a035f85e] .el-input__inner:focus{border-color:#536ce6}.setting-section .setting-select[data-v-a035f85e]{width:160px}.setting-section .setting-select[data-v-a035f85e] .el-select__wrapper{height:40px;border-radius:6px}.setting-section .checkbox-group[data-v-a035f85e]{display:flex;flex-direction:column;width:200px}.setting-section .checkbox-group .setting-checkbox[data-v-a035f85e]{margin-bottom:16px}.setting-section .checkbox-group .setting-checkbox[data-v-a035f85e]:last-child{margin-bottom:0}.setting-section .checkbox-group .setting-checkbox[data-v-a035f85e] .el-checkbox__label{font-size:14px;color:#606266}.setting-section .checkbox-group .setting-checkbox[data-v-a035f85e] .el-checkbox__input.is-checked .el-checkbox__inner{background-color:#536ce6;border-color:#536ce6}.setting-section .version-item[data-v-a035f85e]{display:flex;align-items:center;padding:0 0 8px}.setting-section .version-item[data-v-a035f85e]:last-child{border-bottom:none}.setting-section .version-item .version-label[data-v-a035f85e]{font-size:14px;color:#686e84;flex-shrink:0}.setting-section .version-item .version-value[data-v-a035f85e]{font-size:14px;color:#3c404d;font-weight:500}.setting-section .version-item .version-value-group[data-v-a035f85e]{display:flex;align-items:center}.setting-section .version-item .version-value-group .version-value[data-v-a035f85e]{font-size:14px;color:#3c404d;font-weight:500;margin-right:12px}.setting-section .version-item .version-value-group .version-update-button[data-v-a035f85e]{width:64px;height:24px;background:#f5f5f7;font-size:12px;color:#686e84;padding:2px 7px 5px;border-radius:4px;border:none;transition:all .3s ease;cursor:pointer}.setting-section .version-item .version-value-group .version-update-button[data-v-a035f85e]:hover{transform:scale(1.05) translateY(-2px);box-shadow:0 4px 8px rgba(0,0,0,.1)}.copyright[data-v-a035f85e]{margin-top:0;display:none}.copyright p[data-v-a035f85e]{font-size:12px;color:#909399;margin:0}.setting-footer[data-v-a035f85e]{padding:20px 0;border-top:1px solid #f0f0f0;background:#ffffff;display:flex;justify-content:flex-end;margin-top:32px}.setting-footer .base-button[data-v-a035f85e]{padding:8px 20px;border-radius:6px;font-size:14px;min-width:100px;height:36px;margin-left:12px}.setting-footer .base-button[data-v-a035f85e]:first-child{margin-left:0}@media (max-width: 768px){.setting-page[data-v-a035f85e]{flex-direction:column}.sidebar[data-v-a035f85e]{width:100%;height:auto;flex-direction:row;padding:16px}.sidebar .sidebar-menu[data-v-a035f85e]{flex-direction:row;justify-content:center}.sidebar .sidebar-menu .menu-item[data-v-a035f85e]{margin-bottom:0;margin-right:16px}.sidebar .sidebar-menu .menu-item[data-v-a035f85e]:last-child{margin-right:0}.main-content[data-v-a035f85e]{padding:24px 32px}.setting-container[data-v-a035f85e]{margin:0;border-radius:0}.tabs-content .tab-panel[data-v-a035f85e]{padding:26px 0}.setting-footer[data-v-a035f85e]{padding:16px 20px}.tabs-header .tab-item[data-v-a035f85e]{padding:12px 16px 16px 0;font-size:16px}.setting-section .setting-select[data-v-a035f85e]{width:100%}}.spa-code-input-wrapper[data-v-a035f85e]{position:relative;display:flex;align-items:center}.spa-code-input-wrapper .spa-code-input[data-v-a035f85e]{letter-spacing:2px}.spa-code-input-wrapper .spa-code-input[type=password][data-v-a035f85e]{font-family:Courier New,monospace;letter-spacing:4px}.spa-code-input-wrapper .input-icon-right[data-v-a035f85e]{position:absolute;right:12px;top:50%;transform:translateY(-50%);width:18px;height:18px;color:#909399;cursor:pointer;z-index:2;transition:color .3s ease}.spa-code-input-wrapper .input-icon-right[data-v-a035f85e]:hover,.spa-code-input-wrapper .spa-code-toggle[data-v-a035f85e]:hover{color:#409eff}\n',document.head.appendChild(S),{setters:[function(e){r=e._,o=e.r,i=e.f,s=e.o,l=e.S,c=e.C,u=e.M,d=e.v,p=e.h,v=e.a,f=e.b,g=e.d,b=e.e,m=e.j,x=e.l,h=e.w,y=e.t,k=e.U,w=e.G,_=e.L,C=e.i}],execute:function(){var a={class:"setting-page"},S={class:"main-content"},T={class:"setting-container"},A={class:"tabs-header"},U={class:"tabs-content"},P={key:0,class:"tab-panel"},V={key:0,class:"loading-placeholder"},j={key:1,class:"setting-section"},E={class:"setting-item setting-platformAddress"},z={style:{display:"flex","align-items":"center"}},B={key:0,style:{"margin-top":"12px",width:"320px"}},F={class:"spa-code-input-wrapper"},M={key:0,d:"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z",stroke:"currentColor","stroke-width":"2"},O={key:1,cx:"12",cy:"12",r:"3",stroke:"currentColor","stroke-width":"2"},G={key:2,d:"M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24",stroke:"currentColor","stroke-width":"2"},I={key:3,stroke:"currentColor","stroke-width":"2",x1:"1",x2:"23",y1:"1",y2:"23"},Y={class:"setting-item"},H={class:"checkbox-group"},L={key:1,class:"tab-panel"},q={key:0,class:"loading-placeholder"},N={key:1},$={class:"setting-section setting-update"},R={class:"setting-item"},W={class:"checkbox-group"},D={key:0,class:"setting-item"},J={class:"setting-section"},K={class:"setting-item",style:{"margin-bottom":"8px"}},Q={class:"version-item"},X={class:"version-value-group"},Z={class:"version-value",style:{"margin-left":"68px"}},ee={class:"version-item"},te={class:"version-value",style:{"margin-left":"68px"}},ae={key:0,class:"version-item"},ne={class:"version-value",style:{"margin-left":"40px"}},re={__name:"setting",setup:function(t){var r=o("general"),re=o({}),oe=o(""),ie=i(),se=o(""),le=o(!1),ce=o(!1),ue=o(!0),de=o(!0),pe=o("daily"),ve=o(!1),fe=o(""),ge=o(""),be=o(!1),me=o(!1),xe=o(""),he=o(""),ye=o(""),ke=o(0),we=o(null),_e=o(!0);s(n(e().m((function t(){return e().w((function(e){for(;;)switch(e.n){case 0:return e.n=1,Ce();case 1:return e.n=2,l();case 2:_e.value=!1;case 3:return e.a(2)}}),t)}))));var Ce=function(){var t=n(e().m((function t(){var a,n,r,o;return e().w((function(e){for(;;)switch(e.n){case 0:return e.p=0,logger.log("开始加载客户端设置..."),e.n=1,c.getClientConfig();case 1:a=e.v,logger.log("加载到的设置:",a),a&&(oe.value=a.ServerUrl||"",se.value=a.ServerUrl||"",ce.value=a.IsAutoStartEnabled||!1,ue.value=void 0===a.AutoConnectAfterStartup||a.AutoConnectAfterStartup,de.value=void 0===a.IsAutoUpdateEnabled||a.IsAutoUpdateEnabled,n=["1800","3600","86400","604800","2592000"],r=a.UpdateFrequency||"",pe.value=n.includes(r)?r:"1800",xe.value=a.CurrentVersion||"",he.value=a.BuildTimestamp||"",ye.value=a.LastUpdatedTime||"",re.value=a,fe.value=a.activation_code||"",ge.value=fe.value,ve.value=!!fe.value),logger.log("设置加载完成"),e.n=3;break;case 2:e.p=2,o=e.v,console.error("加载设置失败:",o),u.warning("加载设置失败，使用默认设置");case 3:return e.a(2)}}),t,null,[[0,2]])})));return function(){return t.apply(this,arguments)}}(),Se=function(){var t=n(e().m((function t(){var a,n,r,o=arguments;return e().w((function(e){for(;;)switch(e.n){case 0:if(a=o.length>0&&void 0!==o[0]&&o[0],!_e.value){e.n=1;break}return e.a(2);case 1:if(a||!ve.value||/^[0-9]{6}$/.test(fe.value)){e.n=2;break}return u.error("请输入6位数字访问码"),e.a(2);case 2:return e.p=2,(n=re.value).IsAutoStartEnabled=ce.value,n.AutoConnectAfterStartup=ue.value,n.IsAutoUpdateEnabled=de.value,n.UpdateFrequency=pe.value,n.activation_code=ve.value?fe.value:"",logger.log("保存其他设置:",n),e.n=3,c.setClientConfig(n);case 3:logger.log("其他设置保存成功"),u.success("设置已保存"),e.n=5;break;case 4:e.p=4,r=e.v,console.error("保存其他设置失败:",r),u.error("保存设置失败，请重试");case 5:return e.a(2)}}),t,null,[[2,4]])})));return function(){return t.apply(this,arguments)}}(),Te=function(){var t=n(e().m((function t(){var a,n,r,o,i,s,l,c,d,p;return e().w((function(e){for(;;)switch(e.n){case 0:if(!le.value){e.n=1;break}return e.a(2);case 1:if(oe.value!==se.value){e.n=2;break}return e.a(2);case 2:return le.value=!0,e.p=3,e.n=4,k(oe.value);case 4:if(e.v){e.n=5;break}throw new Error("服务器地址格式错误");case 5:return e.n=6,w.confirm("确定要将平台地址修改为：".concat(oe.value," 吗？"),"确认修改平台地址",{type:"warning",confirmButtonText:"确定",cancelButtonText:"取消"});case 6:if(a=_.service({fullscreen:!0,text:"正在连接服务器..."}),e.p=7,n=new URL(oe.value),r="".concat(n.protocol,"//").concat(n.host),oe.value=r,!ve.value){e.n=9;break}return console.log("SPA模式已启用，跳过连接验证，直接保存配置"),a.updateText("正在保存配置..."),e.n=8,Ae();case 8:a.close(),u.success("服务器地址已保存（SPA模式）"),e.n=20;break;case 9:return a.updateText("正在验证服务器连接..."),e.p=10,o=new AbortController,i=setTimeout((function(){o.abort()}),5e3),e.n=11,fetch("".concat(oe.value,"/auth/login/v1/user/main_idp/list"),{method:"GET",headers:{"Content-Type":"application/json"},signal:o.signal});case 11:if(s=e.v,clearTimeout(i),!s.ok||200!==s.status){e.n=13;break}return a.updateText("正在保存配置..."),e.n=12,Ae();case 12:a.close(),u.success("服务器连接成功！"),e.n=14;break;case 13:throw new Error("服务器响应错误: ".concat(s.status));case 14:e.n=20;break;case 15:return e.p=15,c=e.v,console.warn("服务器连接测试失败:",c),a.close(),l="连接测试失败","AbortError"===c.name?l="连接超时，请检查网络或服务器状态":"TypeError"===c.name&&(l="网络错误，请检查服务器地址是否正确"),e.p=16,e.n=17,w.confirm("".concat(l,"，是否仍要保存此服务器地址？\n\n注意：前端将通过本地代理访问，后台会处理实际连接。"),"连接测试失败",{type:"warning",confirmButtonText:"仍要保存",cancelButtonText:"取消"});case 17:return console.log("用户选择保存服务器地址（将由后台代理使用）:",oe.value),e.n=18,Ae();case 18:u.success("服务器地址已保存（将由后台代理处理连接）"),e.n=20;break;case 19:throw e.p=19,e.v,new Error("已取消保存服务器地址");case 20:e.n=22;break;case 21:e.p=21,d=e.v,a&&a.close(),oe.value=se.value,u.error(d.message||"配置服务器地址失败");case 22:e.n=24;break;case 23:e.p=23,p=e.v,logger.log("用户取消修改平台地址，还原原值"),oe.value=se.value,"cancel"===p?u.info("已取消修改"):u.error(p.message);case 24:return e.p=24,le.value=!1,e.f(24);case 25:return e.a(2)}}),t,null,[[16,19],[10,15],[7,21],[3,23,24,25]])})));return function(){return t.apply(this,arguments)}}(),Ae=function(){var t=n(e().m((function t(){var a,n;return e().w((function(e){for(;;)switch(e.n){case 0:return e.p=0,(a=re.value).ServerUrl=oe.value,logger.log("保存平台地址:",a),e.n=1,c.setClientConfig(a);case 1:se.value=oe.value,globalUrlHashParams.set("WebUrl",oe.value),logger.log("平台地址保存成功"),u.success("平台地址已保存"),e.n=3;break;case 2:throw e.p=2,n=e.v,console.error("保存平台地址失败:",n),oe.value=se.value,new Error("保存平台地址失败，请重试");case 3:return e.a(2)}}),t,null,[[0,2]])})));return function(){return t.apply(this,arguments)}}(),Ue=function(){var t=n(e().m((function t(){return e().w((function(e){for(;;)switch(e.n){case 0:if(!ve.value){e.n=5;break}return e.p=1,e.n=2,w.confirm("确定要关闭访问码吗？这将清空当前的访问码配置。","确认关闭访问码",{type:"warning",confirmButtonText:"确定",cancelButtonText:"取消"});case 2:ve.value=!ve.value,e.n=4;break;case 3:e.p=3,e.v,logger.log("用户取消");case 4:e.n=6;break;case 5:ve.value=!ve.value,be.value=!0;case 6:return e.a(2)}}),t,null,[[1,3]])})));return function(){return t.apply(this,arguments)}}(),Pe=function(){var t=n(e().m((function t(a){return e().w((function(e){for(;;)switch(e.n){case 0:if(!_e.value){e.n=1;break}return e.a(2);case 1:if(a||!ge.value){e.n=4;break}return fe.value="",ge.value="",e.n=2,Se();case 2:return u.success("访问码已关闭"),e.n=3,ie.ClearStorage();case 3:logger.log("访问码已关闭，用户状态已刷新"),e.n=6;break;case 4:if(!a){e.n=6;break}if(fe.value){e.n=5;break}u.info("请在下方输入6位数字访问码，输入完成后会自动保存"),e.n=6;break;case 5:return e.n=6,Se();case 6:return e.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}(),Ve=function(e){var t=e.replace(/[^0-9]/g,"");t!==e&&(fe.value=t)},je=function(){be.value=!be.value},Ee=function(){var t=n(e().m((function t(){var a,n;return e().w((function(e){for(;;)switch(e.n){case 0:if(!me.value){e.n=1;break}return e.a(2);case 1:if(fe.value!==ge.value){e.n=2;break}return e.a(2);case 2:if(me.value=!0,a=!ge.value&&ve.value,e.p=3,/^[0-9]{6}$/.test(fe.value)){e.n=4;break}return u.error("请输入6位数字访问码"),fe.value=ge.value,e.a(2);case 4:if(!a){e.n=7;break}return re.value.activation_code=fe.value,e.n=5,c.setClientConfig(re.value);case 5:return ge.value=fe.value,u.success("访问码已保存"),e.n=6,ie.ClearStorage();case 6:console.log("访问码已保存，用户状态已刷新"),e.n=11;break;case 7:return e.n=8,w.confirm("确定要将访问码修改为：".concat(fe.value," 吗？"),"确认修改访问码",{type:"warning",confirmButtonText:"确定",cancelButtonText:"取消"});case 8:return re.value.activation_code=fe.value,e.n=9,c.setClientConfig(re.value);case 9:return ge.value=fe.value,u.success("访问码已保存"),e.n=10,ie.ClearStorage();case 10:console.log("访问码已修改，用户状态已刷新");case 11:be.value=!1,e.n=13;break;case 12:e.p=12,n=e.v,fe.value=ge.value,"cancel"===n?u.info("已取消修改"):u.error(n.message||"保存访问码失败");case 13:return e.p=13,me.value=!1,e.f(13);case 14:return e.a(2)}}),t,null,[[3,12,13,14]])})));return function(){return t.apply(this,arguments)}}();d(ve,Pe);var ze=function(){if(logger.log("构建时间被点击"),ke.value++,1===ke.value&&(we.value=setTimeout((function(){ke.value=0,we.value=null,logger.log("构建时间点击计数已重置")}),1e3)),ke.value>=5){logger.log("检测到1秒内连续点击构建时间，调用 agentApi.openAsecPage"),we.value&&(clearTimeout(we.value),we.value=null),ke.value=0;try{c.openAsecPage("http://127.0.0.1:19998/"),logger.log("成功调用 agentApi.openAsecPage")}catch(e){logger.error("调用 agentApi.openAsecPage 失败:",e)}}},Be=function(){var t=n(e().m((function t(){return e().w((function(e){for(;;)switch(e.n){case 0:try{u.info("正在检查更新..."),setTimeout((function(){u.success("当前已是最新版本")}),1e3)}catch(t){console.error("检查更新失败:",t),u.error("检查更新失败，请稍后重试")}case 1:return e.a(2)}}),t)})));return function(){return t.apply(this,arguments)}}();return d([ce,ue,de,pe],(function(){_e.value||(logger.log("检测到其他设置变化，立即保存..."),Se(!0))}),{deep:!0}),function(e,t){var n=p("base-input"),o=p("base-checkbox"),i=p("base-option"),s=p("base-select"),l=p("base-button");return v(),f("div",a,[g("div",S,[g("div",T,[g("div",A,[g("div",{class:b(["tab-item",{active:"general"===r.value}]),onClick:t[0]||(t[0]=function(e){return r.value="general"})},t[8]||(t[8]=[g("span",{class:"tab-item-text"},"通用设置",-1)]),2),g("div",{class:b(["tab-item",{active:"version"===r.value}]),onClick:t[1]||(t[1]=function(e){return r.value="version"})},t[9]||(t[9]=[g("span",{class:"tab-item-text"},"版本信息",-1)]),2)]),g("div",U,["general"===r.value?(v(),f("div",P,[_e.value?(v(),f("div",V)):(v(),f("div",j,[g("div",E,[t[11]||(t[11]=g("label",{class:"setting-label"},"平台地址",-1)),g("div",z,[m(n,{modelValue:oe.value,"onUpdate:modelValue":t[2]||(t[2]=function(e){return oe.value=e}),placeholder:"输入您连接的平台服务器地址",class:"setting-input",clearable:"",onBlur:Te},null,8,["modelValue"]),g("span",{class:"spa-label",onClick:Ue},t[10]||(t[10]=[g("svg",{class:"icon","aria-hidden":"true"},[g("use",{"xlink:href":"#icon-spa"})],-1)]))]),ve.value?(v(),f("div",B,[g("div",F,[m(n,{modelValue:fe.value,"onUpdate:modelValue":t[3]||(t[3]=function(e){return fe.value=e}),type:be.value?"text":"password",maxlength:"6",placeholder:"请输入6位数字访问码",class:"setting-input spa-code-input",onBlur:Ee,onInput:Ve},null,8,["modelValue","type"]),(v(),f("svg",{class:"input-icon-right spa-code-toggle",fill:"none",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",onClick:je},[be.value?x("",!0):(v(),f("path",M)),be.value?x("",!0):(v(),f("circle",O)),be.value?(v(),f("path",G)):x("",!0),be.value?(v(),f("line",I)):x("",!0)]))])])):x("",!0)]),g("div",Y,[t[14]||(t[14]=g("label",{class:"setting-label"},"启动选项",-1)),g("div",H,[m(o,{modelValue:ce.value,"onUpdate:modelValue":t[4]||(t[4]=function(e){return ce.value=e}),class:"setting-checkbox"},{default:h((function(){return t[12]||(t[12]=[C(" 开机自启动 ")])})),_:1,__:[12]},8,["modelValue"]),m(o,{modelValue:ue.value,"onUpdate:modelValue":t[5]||(t[5]=function(e){return ue.value=e}),class:"setting-checkbox"},{default:h((function(){return t[13]||(t[13]=[C(" 启动后自动连接 ")])})),_:1,__:[13]},8,["modelValue"])])])]))])):x("",!0),"version"===r.value?(v(),f("div",L,[_e.value?(v(),f("div",q)):(v(),f("div",N,[g("div",$,[g("div",R,[t[16]||(t[16]=g("label",{class:"setting-label"},"更新选项",-1)),g("div",W,[m(o,{modelValue:de.value,"onUpdate:modelValue":t[6]||(t[6]=function(e){return de.value=e}),class:"setting-checkbox"},{default:h((function(){return t[15]||(t[15]=[C(" 自动检查更新 ")])})),_:1,__:[15]},8,["modelValue"])])]),de.value?(v(),f("div",D,[t[17]||(t[17]=g("label",{class:"setting-label"},"更新检查频率",-1)),m(s,{modelValue:pe.value,"onUpdate:modelValue":t[7]||(t[7]=function(e){return pe.value=e}),class:"setting-select",placeholder:"请选择"},{default:h((function(){return[m(i,{label:"每30分钟",value:"1800"}),m(i,{label:"每小时",value:"3600"}),m(i,{label:"每天",value:"86400"}),m(i,{label:"每周",value:"604800"}),m(i,{label:"每月",value:"2592000"})]})),_:1},8,["modelValue"])])):x("",!0)]),g("div",J,[g("div",K,[t[22]||(t[22]=g("label",{class:"setting-label"},"关于安全客户端",-1)),g("div",Q,[t[19]||(t[19]=g("span",{class:"version-label"},"当前版本",-1)),g("div",X,[g("span",Z,y(xe.value),1),m(l,{class:"version-update-button",style:{display:"none"},text:"",type:"primary",size:"small",onClick:Be},{default:h((function(){return t[18]||(t[18]=[C(" 检查更新 ")])})),_:1,__:[18]})])]),g("div",ee,[t[20]||(t[20]=g("span",{class:"version-label"},"构建时间",-1)),g("span",te,y(he.value),1),g("span",{class:"version-value",style:{width:"50px",height:"20px"},onClick:ze})]),""!==ye.value?(v(),f("div",ae,[t[21]||(t[21]=g("span",{class:"version-label"},"上次更新时间",-1)),g("span",ne,y(ye.value),1)])):x("",!0)]),t[23]||(t[23]=g("div",{class:"copyright"},[g("p",null,"© 2025 Security Systems Inc. 保留所有权利")],-1))])]))])):x("",!0)])])])])}}};t("default",r(re,[["__scopeId","data-v-a035f85e"]]))}}}))}();
