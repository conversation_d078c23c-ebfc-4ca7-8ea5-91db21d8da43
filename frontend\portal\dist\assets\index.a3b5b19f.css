/*! 
 Build based on gin-vue-admin 
 Time : 1754879545000 */
@charset "UTF-8";@media screen and (min-width: 320px) and (max-width: 750px){.el-header{padding:0 5px}.layout-cont .main-cont .breadcrumb{padding:0 5px;width:100%}.layout-cont .right-box{margin-right:5px}.el-main .admin-box{margin-left:0;margin-right:0}.el-main .big.admin-box{padding:0}.el-main .big .bottom .chart-player{height:auto!important;margin-bottom:15px}.el-main .big .bottom .todoapp{background-color:#fff;padding-bottom:10px}.card .car-left,.card .car-right{width:100%;height:100%}.card{padding-left:5px;padding-right:5px}.card .text{width:100%}.card .text h4{white-space:break-spaces}.shadow{margin-left:4px;margin-right:4px}.shadow .grid-content{margin-bottom:10px;padding:0}.el-dialog{width:90%}.el-transfer .el-transfer-panel{width:40%;display:inline-block}.el-transfer .el-transfer__buttons{padding:0 5px;display:inline-block}}.dark{background-color:#273444!important;color:#fff!important}.light{background-color:#fff!important;color:#000!important}.icon-rizhi1 span{margin-left:5px}.day-select{height:23px;width:88px;margin-left:15px}.day-select div{height:23px;width:88px}.day-select div input{height:23px;width:50px;font-size:12px;color:#536ce6}*,*:before,*:after{box-sizing:border-box}.layout-wrapper{display:flex;min-height:100vh;width:100%}.shadow-overlay{position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,.5);z-index:999;display:none}.shadowBg{display:block!important}.header-row{display:flex;width:100%}.header-col{flex:1}.header-content{display:flex;align-items:center;padding:0;height:44px;min-height:44px}.header-menu-col{flex:0 0 auto}.breadcrumb-col{flex:1}.user-col{flex:0 0 auto;min-width:200px}.breadcrumb{display:flex;align-items:center}.breadcrumb-item{display:flex;align-items:center;color:#606266;font-size:14px}.breadcrumb-item:not(:last-child):after{content:"/";margin:0 8px;color:#c0c4cc}.dropdown{position:relative;display:inline-block}.dropdown-menu{position:absolute;top:100%;right:0;background-color:#fff;border:1px solid #e4e7ed;border-radius:4px;box-shadow:0 2px 12px rgba(0,0,0,.1);z-index:1000;min-width:120px;padding:4px 0}.dropdown-item{display:flex;align-items:center;padding:8px 16px;cursor:pointer;transition:background-color .3s;font-size:14px;color:#606266}.dropdown-item .dropdown-item-icon{margin-right:4px;width:14px;height:14px}.dropdown-item:hover{background-color:#f5f7fa}.dropdown-item .icon{margin-right:8px;font-size:14px}.router-view-container{position:relative;flex:1;display:flex;flex-direction:column;height:calc(100vh - 52px);overflow:hidden}.loading-overlay{position:absolute;top:0;left:0;right:0;bottom:0;background-color:rgba(255,255,255,.9);display:flex;align-items:center;justify-content:center;z-index:9999}.loading-spinner{display:flex;flex-direction:column;align-items:center}.loading-spinner>*{margin-bottom:12px}.loading-spinner>*:last-child{margin-bottom:0}.spinner{width:32px;height:32px;border:3px solid #f3f3f3;border-top:3px solid #536ce6;border-radius:50%;animation:spin 1s linear infinite}.menu-footer-icon{color:#fff;font-size:16px;width:16px;height:16px;display:flex;align-items:center;justify-content:center}.footer{position:relative;bottom:0;left:0;right:0;height:48px;border-top:1px solid rgba(255,255,255,.1);flex-shrink:0}.menu-total{display:flex;align-items:center;width:100%;height:100%;cursor:pointer;transition:background-color .3s;user-select:none}.collapse-btn{background:transparent;border:none;color:#fff;cursor:pointer;padding:8px;border-radius:4px;transition:background-color .3s;display:flex;align-items:center;justify-content:center;width:32px;height:32px;margin-left:12px}.collapse-btn:hover{background-color:rgba(255,255,255,.1)}.collapse-btn:focus{outline:none}.gva-aside{position:relative;width:100%;height:calc(100vh - 60px)!important;display:flex;flex-direction:column;overflow:hidden}.gva-aside .aside{flex:1;overflow-y:auto;height:calc(100vh - 60px)!important;min-height:0}.gva-aside .footer{position:relative;bottom:0;left:0;right:0;height:48px;flex-shrink:0;z-index:9999}.sidebar-container{display:flex;flex-direction:column;height:100vh;width:180px;transition:width .3s ease;position:relative}.sidebar-container.collapsed{width:54px}.sidebar-container .gva-aside{flex:1;height:100%;overflow:hidden}@keyframes spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.loading-text{font-size:14px;color:#606266}.header-avatar-text{margin-right:9px;color:#252631}
