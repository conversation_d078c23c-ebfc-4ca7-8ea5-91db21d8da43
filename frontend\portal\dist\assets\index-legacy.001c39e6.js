/*! 
 Build based on gin-vue-admin 
 Time : 1754834760000 */
System.register(["./header-legacy.efa9e46e.js","./menu-legacy.10c6de32.js","./index-legacy.7db06653.js","./logo-legacy.17ee3a24.js"],(function(e,t){"use strict";var a,i,n,l,o,u,c,d,r,s=document.createElement("style");return s.textContent='@charset "UTF-8";.layout-page{width:100%!important;height:100%!important;position:relative!important;background:#fff}.layout-page .layout-wrap{width:100%;height:calc(100% - 42px);display:flex}.layout-page .layout-header{width:100%;height:42px;z-index:10}.layout-page .layout-main{width:100%;height:100%;overflow:hidden;flex:1;background:#fff}\n',document.head.appendChild(s),{setters:[function(e){a=e.default},function(e){i=e.default},function(e){n=e.C,l=e.h,o=e.a,u=e.b,c=e.j,d=e.d,r=e.k},function(){}],execute:function(){var t={class:"layout-page"},s={class:"layout-wrap"},f={id:"layoutMain",class:"layout-main"};e("default",Object.assign({name:"Client"},{setup:function(e){return n.initIpcClient(),function(e,n){var h=l("router-view");return o(),u("div",t,[c(a),d("div",s,[c(i),d("div",f,[(o(),r(h,{key:e.$route.fullPath,style:{height:"100%",display:"flex","flex-direction":"column"}}))])])])}}}))}}}));
