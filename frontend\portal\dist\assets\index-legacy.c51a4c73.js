/*! 
 Build based on gin-vue-admin 
 Time : 1754836337000 */
System.register(["./index-legacy.aab80689.js"],(function(e,r){"use strict";var o,t,n,a,s,i,c,l=document.createElement("style");return l.textContent='@charset "UTF-8";.error-page[data-v-0976c458]{width:100%;height:100vh;background-color:#fff;display:flex;align-items:center;justify-content:center;font-family:PingFang SC,PingFang SC-Regular,Microsoft YaHei,\\5fae\\8f6f\\96c5\\9ed1,HarmonyOS_Medium,Helvetica Neue,sans-serif}.error-content[data-v-0976c458]{max-width:700px;width:90%;text-align:center}.error-content .error-icon-container[data-v-0976c458]{margin-bottom:30px}.error-content .error-icon[data-v-0976c458]{width:296px;height:160px;object-fit:contain}.error-content .error-svg[data-v-0976c458]{width:64px;height:64px}.error-content .error-info-display[data-v-0976c458]{margin-bottom:30px;max-width:500px;margin-left:auto;margin-right:auto;position:relative}.error-content .error-info-display .main-error-message[data-v-0976c458]{font-size:18px;color:#333;line-height:1.5;margin-bottom:20px;text-align:center;width:100%}.error-content .error-info-display .error-reason-display[data-v-0976c458]{text-align:left;width:100%}.error-content .error-info-display .error-reason-display.aligned[data-v-0976c458]{padding-left:var(--text-align-offset, 0)}.error-content .error-info-display .error-reason-display .reason-title[data-v-0976c458]{font-size:16px;font-weight:400;color:#666;margin-bottom:8px}.error-content .error-info-display .error-reason-display .reason-text[data-v-0976c458]{font-size:16px;color:#666;margin-bottom:8px}.error-content .error-info-display .error-reason-display .reason-detail[data-v-0976c458]{font-size:16px;color:#666;line-height:1.5}.error-content .error-actions[data-v-0976c458]{display:flex;gap:16px;justify-content:center}.error-content .error-actions .btn[data-v-0976c458]{padding:12px 24px;border-radius:6px;border:none;font-size:14px;cursor:pointer;transition:all .3s ease}.error-content .error-actions .btn.btn-primary[data-v-0976c458]{background-color:#007bff;color:#fff}.error-content .error-actions .btn.btn-primary[data-v-0976c458]:hover{background-color:#0056b3}.error-content .error-actions .btn.btn-secondary[data-v-0976c458]{background-color:#6c757d;color:#fff}.error-content .error-actions .btn.btn-secondary[data-v-0976c458]:hover{background-color:#545b62}.loading-container[data-v-0976c458]{display:flex;flex-direction:column;align-items:center;justify-content:center;min-height:200px}.loading-container .loading-spinner[data-v-0976c458]{width:40px;height:40px;border:4px solid #f3f3f3;border-top:4px solid #007bff;border-radius:50%;animation:spin-0976c458 1s linear infinite;margin-bottom:16px}.loading-container .loading-text[data-v-0976c458]{font-size:14px;color:#666;margin:0}@keyframes spin-0976c458{0%{transform:rotate(0)}to{transform:rotate(360deg)}}\n',document.head.appendChild(l),{setters:[function(e){o=e._,t=e.a,n=e.b,a=e.d,s=e.t,i=e.l,c=e.e}],execute:function(){var l=""+new URL("400.162e6142.png",r.meta.url).href,d=""+new URL("401.0a3a9fbb.png",r.meta.url).href,g=""+new URL("403.7a85b4fb.png",r.meta.url).href,h=""+new URL("404.79989b51.png",r.meta.url).href,f={key:0,class:"error-content"},p={class:"error-icon-container"},u=["src"],m={key:1,class:"error-icon error-svg",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},v={class:"error-info-display"},x={class:"main-error-message"},y={key:0,class:"error-reason-display"},b={class:"reason-detail"},w={class:"error-actions"},C={key:1,class:"loading-container"};e("default",o({name:"ErrorPage",data:function(){return{errorCode:"",errorMessage:"",errorReason:"",state:"",requestUrl:"",isLoading:!0}},computed:{errorIconPath:function(){return{400:l,401:d,403:g,404:h}[this.errorCode]||null}},mounted:function(){var e=this;this.parseUrlParams(),this.$nextTick((function(){e.calculateTextAlignment()}))},methods:{parseUrlParams:function(){var e,r,o,t=this,n=window.location.hash;if(n.includes("?")){var a=n.split("?")[1];o=new URLSearchParams(a)}else o=new URLSearchParams(window.location.search);if(console.log("当前URL:",window.location.href),console.log("Hash:",n),console.log("当前路由名称:",null===(e=this.$route)||void 0===e?void 0:e.name),console.log("解析到的参数:",Object.fromEntries(o)),this.errorCode=o.get("code")||o.get("error")||o.get("status")||"",this.errorMessage=o.get("message")||o.get("error_description")||o.get("msg")||o.get("description")||"",this.errorReason=o.get("errorreason")||o.get("reason")||"",this.state=o.get("state")||"",this.requestUrl=o.get("url")||"","Error"!==(null===(r=this.$route)||void 0===r?void 0:r.name)||this.errorCode||o.get("error")||o.get("code")||(this.errorCode="404",this.errorMessage||(this.errorMessage="您访问的页面不存在")),console.log("解析结果:",{errorCode:this.errorCode,errorMessage:this.errorMessage,errorReason:this.errorReason,state:this.state,requestUrl:this.requestUrl}),!this.errorMessage){var s={400:"很抱歉，您的请求存在错误，无法处理",401:"很抱歉，您需要登录后才能访问此页面",403:"很抱歉，您无权限访问此页面",404:"很抱歉，您访问的页面不存在",500:"很抱歉，服务器遇到了内部错误",network_error:"网络连接失败，请检查您的网络设置",timeout:"请求超时，请稍后重试",permission_denied:"您没有执行此操作的权限",invalid_request:"请求参数无效或缺失",unauthorized_client:"客户端未经授权",access_denied:"用户拒绝了授权请求"};this.errorCode&&s[this.errorCode]?this.errorMessage=s[this.errorCode]:this.errorMessage="页面访问出现错误，请联系管理员或稍后重试"}!this.errorReason&&this.errorCode&&(this.errorReason={403:"您未经授权或未登录，无法访问所请求的页面。",401:"您的身份验证已过期，需要重新登录。",404:"请求的资源不存在或已被移动。"}[this.errorCode]||""),this.decodeBase64Params(),this.isLoading=!1,this.$nextTick((function(){t.calculateTextAlignment()}))},decodeBase64Params:function(){try{this.errorMessage&&(console.log("原始message:",this.errorMessage),this.errorMessage=this.decodeUTF8Base64(this.errorMessage),console.log("解码后message:",this.errorMessage))}catch(e){console.warn("Message base64解码失败，使用原值",e)}try{this.errorReason&&(console.log("原始reason:",this.errorReason),this.errorReason=this.decodeUTF8Base64(this.errorReason),console.log("解码后reason:",this.errorReason))}catch(e){console.warn("Reason base64解码失败，使用原值",e)}},decodeUTF8Base64:function(e){if(!e)return e;console.log("尝试URL-safe base64解码:",e);for(var r=e.replace(/-/g,"+").replace(/_/g,"/");r.length%4;)r+="=";console.log("转换为标准base64:",r);try{var o=Uint8Array.from(atob(r),(function(e){return e.charCodeAt(0)})),t=new TextDecoder("utf-8").decode(o);return console.log("URL-safe base64解码结果:",t),t}catch(n){throw console.warn("URL-safe base64解码失败:",n),n}},onImageLoad:function(){console.log("错误图标加载完成")},onImageError:function(){console.warn("错误图标加载失败，使用默认图标")},calculateTextAlignment:function(){var e=this.$el.querySelector(".main-error-message"),r=this.$el.querySelector(".error-reason-display");if(e&&r){var o=document.createElement("div");o.style.cssText="\n          position: absolute;\n          visibility: hidden;\n          white-space: nowrap;\n          font-size: 18px;\n          font-family: ".concat(window.getComputedStyle(e).fontFamily,";\n          line-height: 1.5;\n        "),o.textContent=e.textContent,document.body.appendChild(o);var t=o.offsetWidth,n=e.offsetWidth,a=Math.max(0,(n-t)/2);r.style.setProperty("--text-align-offset","".concat(a,"px")),r.classList.add("aligned"),document.body.removeChild(o)}},goBack:function(){window.history.length>1?this.$router.go(-1):this.goHome()},goHome:function(){this.$router.push("/login")}}},[["render",function(e,r,o,l,d,g){return t(),n("div",{class:c(["error-page",{loading:d.isLoading}])},[d.isLoading?(t(),n("div",C,r[7]||(r[7]=[a("div",{class:"loading-spinner"},null,-1),a("p",{class:"loading-text"},"页面加载中...",-1)]))):(t(),n("div",f,[a("div",p,[g.errorIconPath?(t(),n("img",{key:0,src:g.errorIconPath,alt:"错误图标",class:"error-icon",loading:"lazy",onLoad:r[0]||(r[0]=function(){return g.onImageLoad&&g.onImageLoad.apply(g,arguments)}),onError:r[1]||(r[1]=function(){return g.onImageError&&g.onImageError.apply(g,arguments)})},null,40,u)):(t(),n("svg",m,r[4]||(r[4]=[a("path",{d:"M512 0a512 512 0 1 1 0 1024A512 512 0 0 1 512 0zM297.6 297.6c-12.8 12.8-12.8 33.472 0 46.272L465.664 512 297.6 680.064a32.768 32.768 0 0 0 46.272 46.336L512 558.272l168.064 168.128a32.704 32.704 0 1 0 46.336-46.336L558.336 512.064l168.128-168.128a32.768 32.768 0 0 0-46.336-46.272L512.064 465.728 343.872 297.6c-12.8-12.8-33.472-12.8-46.272 0z",fill:"#FF4D4D"},null,-1)])))]),a("div",v,[a("div",x,s(d.errorMessage)+"，错误代码："+s(d.errorCode||"未知"),1),d.errorReason?(t(),n("div",y,[r[5]||(r[5]=a("div",{class:"reason-title"},"访问阻断：",-1)),r[6]||(r[6]=a("div",{class:"reason-text"},"您可能遇到了以下情况之一：",-1)),a("div",b,s(d.errorReason),1)])):i("",!0)]),a("div",w,[a("button",{class:"btn btn-secondary",onClick:r[2]||(r[2]=function(){return g.goBack&&g.goBack.apply(g,arguments)})},"返回上页"),a("button",{class:"btn btn-primary",onClick:r[3]||(r[3]=function(){return g.goHome&&g.goHome.apply(g,arguments)})},"回到首页")])]))],2)}],["__scopeId","data-v-0976c458"]]))}}}));
