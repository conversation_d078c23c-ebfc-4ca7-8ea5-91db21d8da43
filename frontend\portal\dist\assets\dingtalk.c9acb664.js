/*! 
 Build based on gin-vue-admin 
 Time : 1754879545000 */
import{_ as e,f as o,r as t,o as a,P as l,v as n,a as r,b as i,d as g,i as d,t as s,Q as c}from"./index.ab3e73c8.js";import{u}from"./secondaryAuth.466a8088.js";const p={style:{"text-align":"center"}},v={class:"title",style:{height:"24px","line-height":"24px",margin:"0 auto",color:"#0082ef","font-size":"20px","text-align":"center"}},h={"aria-hidden":"true",class:"icon",style:{height:"24px",width:"29px","vertical-align":"top","margin-right":"8px",display:"inline-block"}},m=e(Object.assign({name:"Dingtalk"},{props:{authInfo:{type:Array,default:function(){return[]}},authId:{type:String,default:function(){return""}}},setup(e){const m=o(),{handleSecondaryAuthResponse:y}=u(),f=t(0),w=t("https://login.dingtalk.com/login/qrcode.htm"),k=t("https://oapi.dingtalk.com/connect/oauth2/sns_authorize"),b=t(""),x=t(null),_=t({}),I=t(!1),E=t(null),A=t(0),C=e,L=async e=>{logger.log("收到消息:",e);if("https://login.dingtalk.com"===e.origin){logger.log("钉钉loginTmpCode:",e.data);const o=e.data;logger.log("收到钉钉loginTmpCode:",o);const t=C.authInfo.dingtalkAppKey,a=`${c()}/auth_callback/`;_.value={appid:t,response_type:"code",scope:"snsapi_login",state:C.authId,redirect_uri:encodeURIComponent(a),loginTmpCode:o};const l=k.value+"?"+new URLSearchParams(_.value).toString();logger.log("钉钉认证URL:",l),T(l)}else if(e.data&&"dingtalk_auth_callback"===e.data.type){logger.log("收到index.html认证结果:",e.data);const{code:o,state:t,error:a}=e.data;if(a)return console.error("钉钉认证失败:",a),void F("认证失败: "+a);if(o&&t){const e=Array.isArray(o)?o[0]:o;if(I.value)return void logger.log("钉钉认证正在进行中，忽略callback消息");if(E.value===e)return void logger.log("钉钉认证重复的callback消息，忽略");logger.log("钉钉认证成功，code:",o,"state:",t),await R(o,t)}else console.error("认证结果缺少必要参数"),F("认证结果无效")}},T=e=>{logger.log("创建认证回调iframe:",e);const o=document.getElementById("dingtalk-callback-iframe");o&&o.remove();const t=document.createElement("iframe");t.id="dingtalk-callback-iframe",t.src=e,t.style.display="none",t.style.width="0",t.style.height="0",t.style.border="none",document.body.appendChild(t),logger.log("认证回调iframe已创建")},R=async(e,o)=>{try{const t=Date.now(),a=Array.isArray(e)?e[0]:e;if(logger.log("钉钉认证回调触发:",{code:a,state:o,currentTime:t}),I.value)return void logger.log("钉钉认证正在进行中，忽略重复调用 - 状态检查");if(E.value===a)return void logger.log("钉钉认证重复的authCode，忽略重复调用 - 认证码检查");if(t-A.value<2e3)return void logger.log("钉钉认证时间间隔过短，忽略重复调用 - 时间检查");I.value=!0,E.value=a,A.value=t,logger.log("钉钉认证成功，开始处理:",{code:a,state:o});const l={clientId:"client_portal",grantType:"implicit",redirect_uri:`${c()}/`,idpId:Array.isArray(o)?o[0]:o,authWeb:{authWebCode:a}};logger.log("调用登录接口，参数:",l);const n=await m.LoginIn(l,"dingtalk",C.authId);if(n&&-1!==n.code){if(await y(n))return void logger.log("钉钉登录成功，进入双因子验证");logger.log("钉钉登录成功")}else console.error("钉钉登录失败"),F("登录失败，请重试"),U();setTimeout((()=>{I.value=!1,logger.log("钉钉认证状态已重置")}),5e3)}catch(t){console.error("钉钉认证处理失败:",t),F("认证处理失败: "+t.message),U(),I.value=!1,E.value=null}},U=()=>{logger.log("开始绘制钉钉二维码"),I.value=!1,E.value=null,A.value=0,b.value=(new Date).getTime();const e=C.authInfo.dingtalkAppKey;if(!e)return logger.log("钉钉配置缺失（）:",{appId:e}),void F("钉钉配置缺失");const o=`${c()}/auth_callback/`;logger.log("钉钉认证参数（）:",{appId:e,callbackUrl:o,time:b.value}),_.value={appid:e,response_type:"code",scope:"snsapi_login",state:C.authId,redirect_uri:o};const t=encodeURIComponent(k.value+"?"+new URLSearchParams(_.value).toString());logger.log("钉钉goto参数（）:",t),(e=>{logger.log("创建钉钉登录iframe（）:",e);const o=w.value,t=document.createElement("iframe");let a=o+"?goto="+e.goto;a+=e.style?"&style="+encodeURIComponent(e.style):"",a+=e.href?"&href="+e.href:"",t.src=a,t.style.border="0",t.style.width=e.width?e.width+"px":"365px",t.style.height=e.height?e.height+"px":"400px",t.id="dingiframe";const l=document.getElementById(e.id);l&&(l.innerHTML="",l.appendChild(t)),logger.log("钉钉iframe创建完成（）:",a),x.value=t})({id:"ding_qrcode_login",goto:t,style:"border:none;background-color:#FFFFFF;",width:260,height:300}),void 0!==window.addEventListener?window.addEventListener("message",L,!1):void 0!==window.attachEvent&&window.attachEvent("onmessage",L)},F=e=>{const o=document.getElementById("ding_qrcode_login");o&&(o.innerHTML=`\n      <div style="text-align: center; padding: 20px; color: #f56c6c;">\n        <div style="margin-bottom: 10px;">钉钉认证失败</div>\n        <div style="font-size: 12px; color: #909399;">${e}</div>\n        <button onclick="location.reload()" style="margin-top: 10px; padding: 5px 15px; background: #409eff; color: white; border: none; border-radius: 4px; cursor: pointer;">\n          重试\n        </button>\n      </div>\n    `)};return a((()=>{logger.log("钉钉认证组件挂载（）"),U()})),l((()=>{logger.log("钉钉认证组件卸载"),I.value=!1,E.value=null,A.value=0;const e=document.getElementById("dingtalk-callback-iframe");e&&e.remove(),void 0!==window.addEventListener?window.removeEventListener("message",L,!1):void 0!==window.attachEvent&&window.detachEvent("onmessage",L)})),n(C,(()=>{logger.log("钉钉认证props变化，重新绘制二维码（）"),f.value++,U()})),(o,t)=>(r(),i("div",{key:f.value},[g("div",p,[g("span",v,[(r(),i("svg",h,t[0]||(t[0]=[g("use",{"xlink:href":"#icon-auth-dingtalk"},null,-1)]))),d(" "+s(e.authInfo.name),1)])]),t[1]||(t[1]=g("div",{id:"ding_qrcode_login",class:"dingtalk-qrcode-container"},null,-1))]))}}),[["__scopeId","data-v-e9e3234f"]]);export{m as default};
