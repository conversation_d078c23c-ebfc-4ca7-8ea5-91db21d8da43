<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>钉钉认证回调</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f5f5f5;
        }
        .container {
            text-align: center;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .loading {
            width: 40px;
            height: 40px;
            margin: 0 auto 20px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #1890ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .message {
            color: #666;
            font-size: 14px;
        }
        .error {
            color: #f56c6c;
        }
        .success {
            color: #67c23a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="loading" id="loading"></div>
        <div class="message" id="message">正在处理钉钉认证...</div>
    </div>

    <script>
        (function() {
            console.log('钉钉认证回调页面加载')

            const loadingEl = document.getElementById('loading')
            const messageEl = document.getElementById('message')

            function showMessage(text, type = 'normal') {
                messageEl.textContent = text
                messageEl.className = 'message ' + (type === 'error' ? 'error' : type === 'success' ? 'success' : '')

                if (type !== 'normal') {
                    loadingEl.style.display = 'none'
                }
            }

            function getUrlParams() {
                const params = new URLSearchParams(window.location.search)
                const result = {}
                for (const [key, value] of params) {
                    result[key] = value
                }
                return result
            }

            function sendMessageToParent(data) {
                try {
                    console.log('向父窗口发送消息:', data)

                    // 尝试向父窗口发送消息
                    if (window.parent && window.parent !== window) {
                        window.parent.postMessage(data, '*')
                        console.log('已向parent发送消息')
                    }

                    // 也尝试向opener发送消息（如果是弹窗打开的）
                    if (window.opener) {
                        window.opener.postMessage(data, '*')
                        console.log('已向opener发送消息')
                    }

                    // 尝试向顶层窗口发送消息
                    if (window.top && window.top !== window) {
                        window.top.postMessage(data, '*')
                        console.log('已向top发送消息')
                    }
                } catch (error) {
                    console.error('发送消息失败:', error)
                }
            }

            function processCallback() {
                try {
                    const params = getUrlParams()
                    console.log('URL参数:', params)

                    const { code, state, auth_type, error, error_description } = params
                    let curr_auth_type = auth_type || 'dingtalk'

                    if (error) {
                        console.error('认证失败:', error, error_description)
                        showMessage('认证失败: ' + (error_description || error), 'error')

                        // 发送错误消息
                        sendMessageToParent({
                            type: curr_auth_type + '_auth_callback',
                            error: error_description || error
                        })
                        return
                    }

                    if (code && state) {
                        console.log('认证成功，code:', code, 'state:', state)
                        showMessage('认证成功，正在跳转...', 'success')

                        // 发送成功消息
                        sendMessageToParent({
                            type: curr_auth_type + '_auth_callback',
                            code: code,
                            state: state
                        })

                        // 延迟关闭或隐藏
                        setTimeout(() => {
                            try {
                                // 如果是iframe，尝试隐藏自己
                                if (window.parent !== window) {
                                    const iframe = window.parent.document.getElementById(curr_auth_type + '-callback-iframe')
                                    if (iframe) {
                                        iframe.style.display = 'none'
                                    }
                                }

                                // 如果是弹窗，关闭自己
                                if (window.opener) {
                                    window.close()
                                }
                            } catch (e) {
                                console.log('无法自动关闭/隐藏窗口:', e.message)
                            }
                        }, 1000)

                    } else {
                        console.error('缺少必要的认证参数')
                        showMessage('认证参数不完整', 'error')

                        sendMessageToParent({
                            type: curr_auth_type + '_auth_callback',
                            error: '认证参数不完整'
                        })
                    }
                } catch (error) {
                    console.error('处理回调失败:', error)
                    showMessage('处理认证结果时出错', 'error')

                    sendMessageToParent({
                        type: curr_auth_type + '_auth_callback',
                        error: '处理认证结果时出错: ' + error.message
                    })
                }
            }

            // 页面加载完成后处理回调
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', processCallback)
            } else {
                processCallback()
            }

            // 也可以立即处理（防止DOMContentLoaded已经触发）
            setTimeout(processCallback, 100)
        })()
    </script>
</body>
</html>
