/*! 
 Build based on gin-vue-admin 
 Time : 1754879545000 */
System.register(["./index-legacy.39a66d41.js"],(function(e,n){"use strict";var t,u,r,i,a,o,l,c,d,f,s;return{setters:[function(e){t=e.$,u=e.h,r=e.a,i=e.b,a=e.j,o=e.w,l=e.W,c=e.k,d=e.a0,f=e.y,s=e.z}],execute:function(){e("default",Object.assign({name:"RouterHolder"},{setup:function(e){var n=t();return function(e,t){var v=u("router-view");return r(),i("div",null,[a(v,null,{default:o((function(e){var t=e.Component;return[a(l,{mode:"out-in",name:"el-fade-in-linear"},{default:o((function(){return[(r(),c(d,{include:f(n).keepAliveRouters},[(r(),c(s(t)))],1032,["include"]))]})),_:2},1024)]})),_:1})])}}}))}}}));
