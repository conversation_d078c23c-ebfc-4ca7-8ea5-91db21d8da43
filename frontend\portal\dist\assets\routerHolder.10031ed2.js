/*! 
 Build based on gin-vue-admin 
 Time : 1754834760000 */
import{$ as e,h as a,a as s,b as n,j as t,w as o,W as u,k as l,a0 as r,y as d,z as i}from"./index.0f69a27d.js";const c=Object.assign({name:"RouterHolder"},{setup(c){const f=e();return(e,c)=>{const m=a("router-view");return s(),n("div",null,[t(m,null,{default:o((({Component:e})=>[t(u,{mode:"out-in",name:"el-fade-in-linear"},{default:o((()=>[(s(),l(r,{include:d(f).keepAliveRouters},[(s(),l(i(e)))],1032,["include"]))])),_:2},1024)])),_:1})])}}});export{c as default};
