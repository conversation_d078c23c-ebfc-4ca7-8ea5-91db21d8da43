/*! 
 Build based on gin-vue-admin 
 Time : 1754834760000 */
import{_ as e,r as s,N as a,h as o,a as r,b as l,d as n,j as t,w as i,l as c,i as u,a3 as g,n as d,t as m,M as v,U as p,a5 as f,C as h}from"./index.0f69a27d.js";const w={class:"server-config"},b={class:"config-form"},y={class:"input-wrapper"},U={key:0,style:{"margin-top":"12px",width:"288px"}},_={class:"config-tips"},k={key:0,class:"tip-item error-tip"},C={class:"error-message"},$={key:1,class:"tip-item error-reasons"},x={class:"reasons-text"},S=e(Object.assign({name:"ServerConfig"},{emits:["server-configured"],setup(e,{emit:S}){const E=S,P=s(null),T=s(!1),V=a({serverUrl:""}),A=s(!1),j=s(""),I=a({icon1:"info-circle",color1:"#e6a23c",message1:"",icon2:"check",color2:"#67c23a",message2:""}),N={serverUrl:[{required:!0,message:"请输入服务器地址",trigger:"blur"},{pattern:/^https?:\/\/.+/,message:"请输入有效的服务器地址（需包含 http:// 或 https://）",trigger:"blur"}]},O=async()=>{A.value=!A.value},R=e=>{const s=e.replace(/[^0-9]/g,"");s!==e&&(j.value=s)},q=async()=>{if(P.value)try{if(!(await P.value.validate()))return;if(A.value){if(!j.value)return void v.error("请输入访问码");if(6!==j.value.length)return void v.error("访问码必须是6位数字");if(!/^\d{6}$/.test(j.value))return void v.error("访问码只能包含数字")}T.value=!0;if(!(await p(V.serverUrl)))return void v.error("服务器地址格式错误");const s=new URL(V.serverUrl),a=`${s.protocol}//${s.host}`;V.serverUrl=a;const o={serverUrl:V.serverUrl,spaEnabled:A.value,spaCode:A.value?j.value:""};try{I.icon1="loading",I.color1="#409eff",I.message1="正在测试服务器连接...",I.icon2="warning",I.color2="#e6a23c",I.message2="请稍候，正在验证服务器配置",await(async e=>{try{if(h.isClient()){const s={...await h.getClientConfig()||{},ServerUrl:e.serverUrl,SpaEnabled:e.spaEnabled,activation_code:e.spaEnabled?e.spaCode:""};console.log("SPA码将保存为activation_code:",s.activation_code),await h.setClientConfig(s),globalUrlHashParams.set("WebUrl",e.serverUrl),console.log("配置已保存到客户端:",s)}else{const s={serverUrl:e.serverUrl,spaEnabled:e.spaEnabled,spaCode:e.spaCode};localStorage.setItem("server_config",JSON.stringify(s)),console.log("配置已保存到本地存储:",s)}}catch(s){throw console.error("保存配置失败:",s),new Error("保存配置失败")}})(o),console.info("配置已保存，正在测试连接..."),I.message1="正在启动服务，请稍候...",I.color1="#409eff",I.icon1="loading",await new Promise((e=>setTimeout(e,5e3)));let s=28080;try{s=await f(),console.log("获取到的代理端口:",s)}catch(e){console.warn("获取动态端口失败，使用默认端口28080:",e),s=28080}const a=`http://127.0.0.1:${s}/auth/login/v1/user/main_idp/list`;console.log("测试连接地址:",a);const r=5,l=2e3;let n=null;for(let o=1;o<=r;o++)try{console.log(`连接测试第${o}次尝试...`),o>1&&(I.message1=`正在重试连接... (${o}/${r})`);const e=new AbortController,s=setTimeout((()=>{e.abort()}),1e3),l=await fetch(a,{method:"GET",headers:{"Content-Type":"application/json"},signal:e.signal});if(clearTimeout(s),l.ok&&200===l.status){console.log(`连接测试第${o}次尝试成功`);break}throw new Error(`服务器响应错误: ${l.status}`)}catch(e){if(n=e,console.warn(`连接测试第${o}次尝试失败:`,e.message),!(o<r))throw console.error(`连接测试失败，已重试${r}次`),n;console.log(`等待${l}ms后进行第${o+1}次重试...`),await new Promise((e=>setTimeout(e,l)))}const t={ok:!0,status:200};t.ok&&200===t.status&&(I.icon1="check",I.color1="#67c23a",I.message1="服务器连接成功！",I.icon2="check",I.color2="#67c23a",I.message2="配置成功后将自动跳转到登录页面",v.success("服务器连接成功！"),E("server-configured",o))}catch(e){console.warn("服务器连接测试失败，清除已保存的配置:",e),I.icon1="close",I.color1="#f56c6c",I.icon2="warning",I.color2="#e6a23c",I.message1="连接失败，可能存在以下原因：",A.value?I.message2="1.网络异常或服务器地址不可达\n2.已启用SPA安全防护，未输入SPA访问码\n3.SPA访问码无效（无效原因可能为：电脑系统时间存在偏差、未区分字母大小写、访问码已过期），请重新输入":I.message2="1.网络异常或服务器地址不可达\n2.服务器服务未启动或配置异常"}}catch(e){console.error("配置服务器失败:",e),I.icon1="close",I.color1="#f56c6c",I.message1="配置失败，请检查输入格式",A.value?(I.icon2="warning",I.color2="#e6a23c",I.message2="请检查服务器地址和访问码格式"):(I.icon2="warning",I.color2="#e6a23c",I.message2="请检查服务器地址格式是否正确")}finally{T.value=!1}};return(e,s)=>{const a=o("base-input"),v=o("base-form-item"),p=o("base-button"),f=o("base-form"),h=o("base-icon");return r(),l("div",w,[s[5]||(s[5]=n("div",{class:"config-header"},[n("span",{class:"title"},"平台地址")],-1)),n("div",b,[t(f,{ref_key:"serverForm",ref:P,model:V,rules:N,onKeyup:g(q,["enter"])},{default:i((()=>[t(v,{prop:"serverUrl"},{default:i((()=>[n("div",y,[t(a,{modelValue:V.serverUrl,"onUpdate:modelValue":s[0]||(s[0]=e=>V.serverUrl=e),placeholder:"输入您连接的平台服务器地址","suffix-icon":"link",class:"setting-input"},null,8,["modelValue"]),n("span",{class:"spa-label",onClick:O},s[2]||(s[2]=[n("svg",{class:"icon","aria-hidden":"true"},[n("use",{"xlink:href":"#icon-spa"})],-1)]))]),A.value?(r(),l("div",U,[t(a,{modelValue:j.value,"onUpdate:modelValue":s[1]||(s[1]=e=>j.value=e),maxlength:"6",placeholder:"请输入6位数字访问码",style:{width:"100%"},onInput:R},null,8,["modelValue"])])):c("",!0),s[3]||(s[3]=n("div",{class:"input-tip"}," 请输入平台地址，如：https://************* ",-1))])),_:1,__:[3]}),t(v,null,{default:i((()=>[t(p,{type:"primary",class:"submit-button",loading:T.value,onClick:q},{default:i((()=>s[4]||(s[4]=[u(" 连接服务器 ")]))),_:1,__:[4]},8,["loading"])])),_:1})])),_:1},8,["model"])]),n("div",_,[I.message1?(r(),l("div",k,[t(h,{name:I.icon1,style:d({color:I.color1,marginRight:"6px",fontSize:"14px",flexShrink:0,marginTop:"1px"})},null,8,["name","style"]),n("span",C,m(I.message1),1)])):c("",!0),I.message2?(r(),l("div",$,[n("span",x,m(I.message2),1)])):c("",!0)])])}}}),[["__scopeId","data-v-8cd5b5d6"]]);export{S as default};
