/*! 
 Build based on gin-vue-admin 
 Time : 1754834760000 */
System.register(["./menuItem-legacy.e4a28572.js","./asyncSubmenu-legacy.e2e633cf.js","./index-legacy.7db06653.js"],(function(e,n){"use strict";var t,r,u,o,i,f,l,c,a,s,d,h;return{setters:[function(e){t=e.default},function(e){r=e.default},function(e){u=e.c,o=e.h,i=e.a,f=e.k,l=e.w,c=e.b,a=e.F,s=e.A,d=e.l,h=e.z}],execute:function(){e("default",Object.assign({name:"AsideComponent"},{props:{routerInfo:{type:Object,default:function(){return null}},isCollapse:{default:function(){return!1},type:Boolean},theme:{default:function(){return{}},type:Object}},setup:function(e){var n=e,m=u((function(){return n.routerInfo.children&&n.routerInfo.children.filter((function(e){return!e.hidden})).length?r:t}));return function(n,t){var r=o("AsideComponent");return e.routerInfo.hidden?d("",!0):(i(),f(h(m.value),{key:0,"is-collapse":e.isCollapse,theme:e.theme,"router-info":e.routerInfo},{default:l((function(){return[e.routerInfo.children&&e.routerInfo.children.length?(i(!0),c(a,{key:0},s(e.routerInfo.children,(function(n){return i(),f(r,{key:n.name,"is-collapse":!1,"router-info":n,theme:e.theme},null,8,["router-info","theme"])})),128)):d("",!0)]})),_:1},8,["is-collapse","theme","router-info"]))}}}))}}}));
