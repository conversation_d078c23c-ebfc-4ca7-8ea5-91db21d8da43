/*! 
 Build based on gin-vue-admin 
 Time : 1754834760000 */
!function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,n,o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",i=o.toStringTag||"@@toStringTag";function u(t,o,a,i){var u=o&&o.prototype instanceof s?o:s,l=Object.create(u.prototype);return r(l,"_invoke",function(t,r,o){var a,i,u,s=0,l=o||[],f=!1,d={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(t,r){return a=t,i=0,u=e,d.n=r,c}};function p(t,r){for(i=t,u=r,n=0;!f&&s&&!o&&n<l.length;n++){var o,a=l[n],p=d.p,g=a[2];t>3?(o=g===r)&&(u=a[(i=a[4])?5:(i=3,3)],a[4]=a[5]=e):a[0]<=p&&((o=t<2&&p<a[1])?(i=0,d.v=r,d.n=a[1]):p<g&&(o=t<3||a[0]>r||r>g)&&(a[4]=t,a[5]=r,d.n=g,i=0))}if(o||t>1)return c;throw f=!0,r}return function(o,l,g){if(s>1)throw TypeError("Generator is already running");for(f&&1===l&&p(l,g),i=l,u=g;(n=i<2?e:u)||!f;){a||(i?i<3?(i>1&&(d.n=-1),p(i,u)):d.n=u:d.v=u);try{if(s=2,a){if(i||(o="next"),n=a[o]){if(!(n=n.call(a,u)))throw TypeError("iterator result is not an object");if(!n.done)return n;u=n.value,i<2&&(i=0)}else 1===i&&(n=a.return)&&n.call(a),i<2&&(u=TypeError("The iterator does not provide a '"+o+"' method"),i=1);a=e}else if((n=(f=d.n<0)?u:t.call(r,d))!==c)break}catch(n){a=e,i=1,u=n}finally{s=1}}return{value:n,done:f}}}(t,a,i),!0),l}var c={};function s(){}function l(){}function f(){}n=Object.getPrototypeOf;var d=[][a]?n(n([][a]())):(r(n={},a,(function(){return this})),n),p=f.prototype=s.prototype=Object.create(d);function g(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,r(e,i,"GeneratorFunction")),e.prototype=Object.create(p),e}return l.prototype=f,r(p,"constructor",f),r(f,"constructor",l),l.displayName="GeneratorFunction",r(f,i,"GeneratorFunction"),r(p),r(p,i,"Generator"),r(p,a,(function(){return this})),r(p,"toString",(function(){return"[object Generator]"})),(t=function(){return{w:u,m:g}})()}function r(e,t,n,o){var a=Object.defineProperty;try{a({},"",{})}catch(e){a=0}r=function(e,t,n,o){if(t)a?a(e,t,{value:n,enumerable:!o,configurable:!o,writable:!o}):e[t]=n;else{var i=function(t,n){r(e,t,(function(e){return this._invoke(t,n,e)}))};i("next",0),i("throw",1),i("return",2)}},r(e,t,n,o)}function n(e){return function(e){if(Array.isArray(e))return o(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return o(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?o(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function a(e,t,r,n,o,a,i){try{var u=e[a](i),c=u.value}catch(e){return void r(e)}u.done?t(c):Promise.resolve(c).then(n,o)}function i(e){return function(){var t=this,r=arguments;return new Promise((function(n,o){var i=e.apply(t,r);function u(e){a(i,n,o,u,c,"next",e)}function c(e){a(i,n,o,u,c,"throw",e)}u(void 0)}))}}System.register(["./index-legacy.7db06653.js","./secondaryAuth-legacy.124024d6.js"],(function(r,o){"use strict";var a,u,c,s,l,f,d,p,g,h,y,b,m,w,v=document.createElement("style");return v.textContent='@charset "UTF-8";.sso-warpper[data-v-23638b1a]{padding:30px 5px 0;overflow:visible;position:relative;text-align:center;display:flex;flex-direction:column;align-items:center;justify-content:center}.sso-warpper .sso-title[data-v-23638b1a]{width:64px;height:64px;line-height:64px;padding:12px 15px 12px 13px;margin:0 auto 34px;background:#ffffff;border-radius:12px;box-shadow:0 2px 20px rgba(46,60,128,.1);color:#0082ef;font-size:20px;text-align:center;display:flex;align-items:center;justify-content:center}.sso-warpper .icon[data-v-23638b1a]{height:40px;width:36px;vertical-align:top;display:inline-block}.sso-warpper .sso-img[data-v-23638b1a]{width:120px;height:120px;display:block;margin:0 auto}.sso-warpper .sso-callback-iframe[data-v-23638b1a]{display:none;width:0;height:0;border:none}.sso-warpper .sso-iframe[data-v-23638b1a]{transform:translateZ(0);backface-visibility:hidden;height:320px;width:100%}.sso-warpper .login_submit_button[data-v-23638b1a]{width:100%;height:40px;font-size:16px;margin-top:20px;margin-bottom:30px;display:flex;align-items:center;justify-content:center}\n',document.head.appendChild(v),{setters:[function(e){a=e._,u=e.h,c=e.a,s=e.b,l=e.d,f=e.j,d=e.w,p=e.i,g=e.u,h=e.f,y=e.M,b=e.Q,m=e.C},function(e){w=e.u}],execute:function(){function o(e){var t=0;if(0===e.length)return t.toString(16).padStart(8,"0");for(var r=0;r<e.length;r++){t=(t<<5)-t+e.charCodeAt(r),t&=t}var n=Math.abs(t),o=Date.now(),a=Math.floor(65535*Math.random());return(n.toString(16)+o.toString(16)+a.toString(16)).padStart(64,"0").substring(0,64)}function v(e){return k.apply(this,arguments)}function k(){return(k=i(t().m((function e(r){var n,a,i,u,c,s,l,f,d,p,g,h;return t().w((function(e){for(;;)switch(e.n){case 0:if(e.p=0,"undefined"==typeof window||!window.crypto||!window.crypto.subtle||"function"!=typeof window.crypto.subtle.digest){e.n=2;break}return logger.log("使用Web Crypto API计算SHA-256"),n=new TextEncoder,a=n.encode(r),e.n=1,window.crypto.subtle.digest("SHA-256",a);case 1:return e.a(2,e.v);case 2:for(logger.log("QT环境：使用简化哈希算法"),i=o(r),u=new Uint8Array(32),c=0;c<32;c++)s=i.substring(2*c,2*c+2),u[c]=parseInt(s,16);return e.a(2,u.buffer);case 3:e.n=5;break;case 4:for(e.p=4,h=e.v,console.error("SHA-256计算失败:",h),logger.log("使用最终回退方案"),l=r+"_"+Date.now()+"_"+Math.random(),f=o(l),d=new Uint8Array(32),p=0;p<32;p++)g=f.substring(2*p,2*p+2),d[p]=parseInt(g,16);return e.a(2,d.buffer);case 5:return e.a(2)}}),e,null,[[0,4]])})))).apply(this,arguments)}function _(e){return S.apply(this,arguments)}function S(){return(S=i(t().m((function e(r){var o,a,i;return t().w((function(e){for(;;)switch(e.n){case 0:return e.p=0,e.n=1,v(r);case 1:return o=e.v,e.a(2,(t=void 0,u=void 0,t=new Uint8Array(o),u=String.fromCharCode.apply(String,n(t)),btoa(u).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")));case 2:return e.p=2,i=e.v,console.error("生成code_challenge失败:",i),logger.log("使用最终回退方案生成code_challenge"),a=r+"_"+Date.now(),e.a(2,btoa(a).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,""))}var t,u}),e,null,[[0,2]])})))).apply(this,arguments)}function A(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:128,t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",r="";if("undefined"!=typeof window&&window.crypto&&window.crypto.getRandomValues){var n=new Uint8Array(e);window.crypto.getRandomValues(n);for(var o=0;o<e;o++)r+=t[n[o]%66]}else for(var a=0;a<e;a++)r+=t[Math.floor(66*Math.random())];return r}var x=function(e){var t="";if(e&&e.includes("message =")){var r=e.match(/message\s*=\s*(.*?)(?=\s+metadata\s*=|$)/);r&&r[1]&&(t=r[1].trim())}return t},I={key:0,class:"sso-warpper"},O={class:"sso-title"},C={"aria-hidden":"true",class:"icon"},T=["xlink:href"],j={key:1,class:"sso-warpper"},E=["src"],L=["src"],M={props:{authId:{type:String,default:function(){return""}},authInfo:{type:Object,default:function(){return{}}}},data:function(){return{iframeSrc:"",callbackIframeSrc:"",isListen:!1,isThirdBack:!1,isAutoLogin:!1,isListenShowApp:!1,route:g(),userStore:h(),secondaryAuth:w(),loading:!1}},computed:{isForceBrowser:function(){return"cas"===this.authInfo.authType?1===parseInt(this.authInfo.casOpenType):1===parseInt(this.authInfo.oauth2OpenType)},isCallbackMode:function(){return this.oauth_callbak||this.route.query.oauth_callbak}},watch:{authId:{handler:function(){this.init()},deep:!0,immediate:!0}},mounted:function(){this.isCallbackMode&&this.handleOAuth2Callback()},destroyed:function(){this.unListenGoBack(),this.clearLoading(),this.isListen&&this.removeEvent(window,"message",this.listenHandle)},methods:{isInIframe:function(){try{return window.self!==window.top}catch(e){return!0}},sendMessageToParent:function(e){try{logger.log("向父页面发送消息:",e),window.parent&&window.parent!==window&&(window.parent.postMessage(e,"*"),logger.log("已向parent发送消息")),window.top&&window.top!==window&&(window.top.postMessage(e,"*"),logger.log("已向top发送消息"))}catch(t){console.error("发送消息失败:",t)}},handleOAuth2Callback:function(){var e=this;return i(t().m((function r(){var n,o;return t().w((function(t){for(;;)switch(t.n){case 0:if(t.p=0,logger.log("开始处理OAuth2回调"),!e.isInIframe()){t.n=1;break}return logger.log("在iframe中，发送消息给父窗口"),n={idp_id:e.route.query.idp_id,redirect:e.route.query.redirect,auth_token:e.route.query.auth_token,login_type:e.route.query.login_type,auth_error:e.route.query.auth_error,state:e.route.query.state},e.sendMessageToParent({type:"oauth2_auth_callback",code:n.auth_token,state:n.state,auth_error:n.auth_error}),t.a(2);case 1:return t.n=2,e.processOAuth2Callback();case 2:t.n=4;break;case 3:t.p=3,o=t.v,logger.log("OAuth2回调处理失败:",o),y({type:"error",message:"认证失败，请重试",showClose:!0});case 4:return t.a(2)}}),r,null,[[0,3]])})))()},processOAuth2Callback:function(){var r=this;return i(t().m((function n(){var o,a,i,u,c;return t().w((function(t){for(;;)switch(t.n){case 0:if(!(o=r.route.query.auth_error)){t.n=1;break}return logger.log("OAuth2认证错误:",o),a=x(o),y({type:"error",message:"认证失败: "+a,showClose:!0}),t.a(2);case 1:if(i=r.route.query.auth_token){t.n=2;break}return logger.log("缺少认证令牌"),y({type:"error",message:"认证失败: 缺少认证令牌",showClose:!0}),t.a(2);case 2:return u={clientId:"client_portal",grantType:"implicit",redirect_uri:"",idpId:r.authId,authWeb:{authWebToken:i}},logger.log("调用登录接口，参数:",u),t.n=3,r.userStore.LoginIn(u,"oauth2",r.authId);case 3:if(c=t.v,logger.log("登录结果:",c),!0!==c&&("object"!==e(c)||null===c||-1===c.code)){t.n=6;break}return t.n=4,r.secondaryAuth.handleSecondaryAuthResponse(c);case 4:if(!t.v){t.n=5;break}return logger.log("企业微信登录成功，进入双因子验证"),t.a(2);case 5:logger.log("OAuth2登录成功"),t.n=7;break;case 6:logger.log("OAuth2登录失败:",c);case 7:return t.a(2)}}),n)})))()},init:function(){this.isForceBrowser||this.clickSubmit()},clickSubmit:function(){var e=this;return i(t().m((function r(){var n,o;return t().w((function(t){for(;;)switch(t.n){case 0:return n=A(),sessionStorage.setItem("oauth2_code_verifier",n),t.n=1,_(n);case 1:o=t.v,e.submit({code_challenge:encodeURIComponent(o),code_challenge_method:"S256"});case 2:return t.a(2)}}),r)})))()},submit:function(e){var r=this;return i(t().m((function n(){var o,a,i,u,c,s;return t().w((function(t){for(;;)switch(t.n){case 0:if(o=b()+"/auth/login/v1/callback/"+r.authId,e){for(u in i=[],e)i.push(u+"="+encodeURIComponent(e[u]));o+="?"+i.join("&"),null!==(a=r.route.query)&&void 0!==a&&a.redirect&&(s=null===(c=r.route.query)||void 0===c?void 0:c.redirect,o+="&redirect=/"+encodeURIComponent(s))}if(!r.isForceBrowser){t.n=4;break}if(logger.log("强制浏览器授权认证URL:",o),!m.isClient()){t.n=2;break}return t.n=1,m.openAsecPage(o);case 1:t.n=3;break;case 2:window.location.href=o;case 3:t.n=6;break;case 4:if(logger.log("iframe授权认证URL:",o),!r.isListen){t.n=5;break}return logger.log("iframe授权监听:"),o.includes("code=")||o.includes("token=")||o.includes("auth_success=true")?(logger.log("iframe授权回调"),r.callbackIframeSrc=o):r.iframeSrc=o,t.a(2);case 5:r.iframeSrc=o,logger.log("iframe初始地址",r.iframeSrc),r.isListen=!0,r.addEvent(window,"message",r.listenHandle);case 6:return t.a(2)}}),n)})))()},listenHandle:function(e){var r=this;return i(t().m((function n(){var o;return t().w((function(t){for(;;)switch(t.n){case 0:if(logger.log("sso触发监听：",e.data),"oauth2_auth_callback"!==e.data.type){t.n=1;break}return r.handleOAuth2Message(e.data.code,e.data.auth_error),t.a(2);case 1:if(o=e.data.event,!r.isThirdAppWakeup(o)){t.n=2;break}return r.wakeupApp(e),t.a(2);case 2:e.data&&r.submit(e.data);case 3:return t.a(2)}}),n)})))()},handleOAuth2Message:function(r,n){var o=this;return i(t().m((function a(){var i,u,c,s;return t().w((function(t){for(;;)switch(t.n){case 0:if(t.p=0,logger.log("收到oauth2_result页面的消息:",{code:r}),r){t.n=1;break}return n&&n.includes("message =")&&(i=x(n),y({type:"error",message:"认证失败: "+i,showClose:!0}),o.init()),logger.log("消息缺少必要参数:",{code:r}),t.a(2);case 1:return u={clientId:"client_portal",grantType:"implicit",redirect_uri:"",idpId:o.authId,authWeb:{authWebToken:r}},logger.log("调用登录接口，参数:",u),t.n=2,o.userStore.LoginIn(u,"oauth2",o.authId);case 2:if(c=t.v,logger.log("登录结果:",c),!0!==c&&("object"!==e(c)||null===c||-1===c.code)){t.n=5;break}return t.n=3,o.secondaryAuth.handleSecondaryAuthResponse(c);case 3:if(!t.v){t.n=4;break}return logger.log("企业微信登录成功，进入双因子验证"),t.a(2);case 4:logger.log("OAuth2登录成功"),t.n=6;break;case 5:logger.log("OAuth2登录失败:",c);case 6:t.n=8;break;case 7:t.p=7,s=t.v,logger.log("处理OAuth2消息失败:",s);case 8:return t.a(2)}}),a,null,[[0,7]])})))()},addEvent:function(e,t,r){e.addEventListener?e.addEventListener(t,r,!1):e.attachEvent&&e.attachEvent("on"+t,(function(){r.call(e,window.event)}))},removeEvent:function(e,t,r){e.removeEventListener?e.removeEventListener(t,r):e.detachEvent&&e.detachEvent("on"+t,r)},isThirdAppWakeup:function(e){return"wakeup-app"===e},wakeupApp:function(e){var t=e.data.params.url;t&&(window.location.href=t)},clearLoading:function(){this.loading&&(this.loading.clear(),this.loading=!1)}}},q=Object.assign(M,{__name:"oauth2",setup:function(e){return function(t,r){var n=u("base-button");return c(),s("div",null,[t.isForceBrowser?(c(),s("div",I,[l("span",O,[(c(),s("svg",C,[l("use",{"xlink:href":"#icon-auth-"+e.authInfo.authType},null,8,T)]))]),f(n,{class:"login_submit_button",type:"primary",onClick:t.clickSubmit},{default:d((function(){return r[0]||(r[0]=[p("授权登录")])})),_:1,__:[0]},8,["onClick"])])):(c(),s("div",j,[l("iframe",{src:t.iframeSrc,class:"sso-iframe",frameborder:"0"},null,8,E),l("iframe",{src:t.callbackIframeSrc,class:"sso-callback-iframe"},null,8,L)]))])}}});r("default",a(q,[["__scopeId","data-v-23638b1a"]]))}}}))}();
