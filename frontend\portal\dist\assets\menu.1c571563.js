/*! 
 Build based on gin-vue-admin 
 Time : 1754836337000 */
import{_ as e,C as t,a as n,b as u,d as a,F as o,A as i,e as c,t as r}from"./index.57c3624b.js";const s=[{path:"/client/main",name:"access",meta:{code:"101",menu:{name:"接入",icon:"icon-jieru",moduleName:"接入",uiId:"ui-menu-client-access"}}},{path:"/client/setting",name:"setting",meta:{code:"102",menu:{name:"设置",icon:"icon-shezhi",moduleName:"设置",uiId:"ui-menu-client-setting"}}}],m={class:"layout-aside"},d={class:"menu-wrapper"},l=["onClick"],h={class:"icon menu-item-icon","aria-hidden":"true"},g=["xlink:href"],p={class:"menu-item-title"};const C=e({name:"ClientMenu",data:()=>({currentRouteCode:"101"}),computed:{computedMenu(){return this.computedMenuFun()}},watch:{$route:{handler(e,t){if(logger.log("路由变化",e,t),"ClientNewLogin"===e.name)return logger.log("跳转到登录页面，菜单切换到接入"),void(this.currentRouteCode="101");if(e.meta&&e.meta.code){if(!_.get(e.meta,"code"))return;if(e.meta.code===this.currentRouteCode)return;this.currentRouteCode=this.cutOut(e.meta.code)}},immediate:!0}},methods:{computedMenuFun(){const e=[];return s&&s.forEach((t=>{if(t.meta&&t.meta.menu){const{name:n,icon:u,uiId:a}=t.meta.menu,o={name:n,icon:u,code:t.meta.code,requiresAuth:t.meta.requiresAuth,url:t.path,params:t.params||[],uiId:a};e.push(o)}})),e},changeMenu(e,n={},u=0){logger.log("切换菜单:",e,n);const a=t.getClientParams(),o={...n,...a};logger.log("切换菜单携带客户端参数:",o),this.$router.push({path:e,query:o}),this.currentRouteCode=this.cutOut(u)},routerInterceptor(e){const t={next:!1,stateMsg:"您好，系统正在检测您的网络环境，请稍候......"};return t.next=!0,t},cutOut:e=>e&&e.length?e.substr(0,3):e}},[["render",function(e,t,s,C,f,M){return n(),u("div",m,[a("ul",d,[(n(!0),u(o,null,i(M.computedMenu,(e=>(n(),u("li",{key:e.code,class:c(["menu-item",M.cutOut(e.code)===f.currentRouteCode?"active-menu-item":""]),onClick:t=>M.changeMenu(e.url,e.params,e.code)},[(n(),u("svg",h,[a("use",{"xlink:href":"#"+e.icon},null,8,g)])),a("div",p,r(e.name),1)],10,l)))),128))])])}],["__scopeId","data-v-eea213d2"]]);export{C as default};
