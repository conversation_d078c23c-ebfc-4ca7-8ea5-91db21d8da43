/*! 
 Build based on gin-vue-admin 
 Time : ************* */
import{D as e,r as a,N as l,c as s,v as n,h as o,a as t,b as i,d as p,t as r,j as c,w as d,n as u,O as g,l as m,_ as A,M as f,i as v,P as h,o as y,f as b,k as w,y as _,F as C,A as k,e as E,C as I,Q as F,R as U,K as L,S as M,T as D}from"./index.57c3624b.js";const N=""+new URL("fault_compact.3c013a62.png",import.meta.url).href,R=""+new URL("maintenance_compact.52fb32ae.png",import.meta.url).href,P=a=>e({url:"/console/v1/form-fill/account",method:"get",params:{app_id:a}}),W={key:0,class:"form-fill-dialog"},x={class:"dialog-wrapper"},V={class:"dialog-container"},B={class:"dialog-header"},q={class:"dialog-title"},z={class:"dialog-body"},S={class:"form-content"},O={class:"form-header"},Q={class:"app-info"},X={class:"app-icon"},j={class:"app-details"},G={class:"app-name"},Y={class:"app-desc"},K={class:"form-tip"},H={class:"dialog-footer"},J=A(Object.assign({name:"FormFillDialog"},{props:{visible:{type:Boolean,default:!1},appInfo:{type:Object,required:!0,default:()=>({})}},emits:["update:visible","success"],setup(A,{emit:h}){const y=A,b=h,w=a(!1),_=a(),C=a(!1),k=a(!1),E=l({username:"",password:""}),I={username:[{required:!0,message:"请输入账户名称",trigger:"blur"},{min:1,max:100,message:"账户名称长度应在1-100字符之间",trigger:"blur"}],password:[{required:!0,message:"请输入账户密码",trigger:"blur"},{min:1,max:100,message:"账户密码长度应在1-100字符之间",trigger:"blur"}]},F=s((()=>k.value?"编辑表单代填账户":"创建表单代填账户"));n((()=>y.visible),(e=>{w.value=e,e&&U()}),{immediate:!0}),n(w,(e=>{e||(b("update:visible",!1),D())}));const U=async()=>{if(y.appInfo.id){C.value=!0;try{const e=String(y.appInfo.id),a=await P(e);if(a.data&&"0"===a.data.errcode&&a.data.credentials){const e=atob(a.data.credentials),[l,s]=e.split(":");E.username=l,E.password=s,k.value=!0}else k.value=!1,E.username="",E.password=""}catch(e){console.error("加载表单代填账户失败:",e),f.error("加载账户信息失败")}finally{C.value=!1}}},L=async()=>{var a,l;if(await(null==(a=_.value)?void 0:a.validate())){C.value=!0;try{const a=btoa(`${E.username}:${E.password}`),n={app_id:String(y.appInfo.id),credentials:a},o=await(s=n,e({url:"/console/v1/form-fill/account",method:"put",data:s}));200===o.status||"0"===o.data.errcode?(f.success(k.value?"账户信息已更新":"账户信息已创建"),b("success"),M()):f.error((null==(l=o.data)?void 0:l.message)||"操作失败")}catch(n){console.error("提交表单失败:",n),f.error("操作失败，请重试")}finally{C.value=!1}var s}},M=()=>{w.value=!1},D=()=>{var e;E.username="",E.password="",k.value=!1,null==(e=_.value)||e.resetFields()},N=e=>{const a=["#71BDDF","#8AB05D","#9571DF","#DF7171","#DFC271","#71DFA7","#B05D8A","#5D8AB0"];let l=0;for(let s=0;s<e.length;s++)l+=e.charCodeAt(s);return a[l%a.length]};return(e,a)=>{const l=o("base-icon"),s=o("base-avatar"),n=o("base-input"),f=o("base-form-item"),h=o("base-form"),y=o("base-button");return w.value?(t(),i("div",W,[p("div",{class:"dialog-mask",onClick:M}),p("div",x,[p("div",V,[p("div",B,[p("span",q,r(F.value),1),c(l,{class:"dialog-close",name:"close",onClick:M})]),p("div",z,[p("div",S,[p("div",O,[p("div",Q,[p("div",X,[c(s,{shape:"square",size:36,src:A.appInfo.iconError?"":A.appInfo.icon,style:u(!A.appInfo.icon||A.appInfo.iconError?`background-color: ${N(A.appInfo.app_name)} !important`:"background-color: #f7f7fa !important"),onError:a[0]||(a[0]=()=>{A.appInfo.iconError=!0})},{default:d((()=>[v(r(!A.appInfo.icon||A.appInfo.iconError?A.appInfo.app_name.slice(0,1):""),1)])),_:1},8,["src","style"])]),p("div",j,[p("div",G,r(A.appInfo.app_name),1),p("div",Y,r(A.appInfo.app_desc||"设置表单代填账户信息"),1)])])]),c(h,{ref_key:"formRef",ref:_,model:E,rules:I,"label-width":"80px",onSubmit:g(L,["prevent"])},{default:d((()=>[c(f,{label:"账户名称",prop:"username"},{default:d((()=>[c(n,{modelValue:E.username,"onUpdate:modelValue":a[1]||(a[1]=e=>E.username=e),placeholder:"请输入账户名称",maxlength:100,clearable:""},null,8,["modelValue"])])),_:1}),c(f,{label:"账户密码",prop:"password"},{default:d((()=>[c(n,{modelValue:E.password,"onUpdate:modelValue":a[2]||(a[2]=e=>E.password=e),type:"password",placeholder:"请输入账户密码",maxlength:100,clearable:"","show-password":""},null,8,["modelValue"])])),_:1}),p("div",K,[c(l,{name:"info",color:"#536ce6"}),a[3]||(a[3]=p("span",null,"此账户信息将用于应用的自动登录，请确保账户信息准确无误。",-1))])])),_:1},8,["model"])])]),p("div",H,[c(y,{onClick:M},{default:d((()=>a[4]||(a[4]=[v("取消")]))),_:1,__:[4]}),c(y,{type:"primary",loading:C.value,onClick:L},{default:d((()=>[v(r(k.value?"保存":"创建"),1)])),_:1},8,["loading"])])])])])):m("",!0)}}}),[["__scopeId","data-v-99b414e6"]]),T=""+new URL("no_power.31f14e62.png",import.meta.url).href,Z=""+new URL("no_result.d0219d8d.png",import.meta.url).href,$={class:"app-page-root"},ee={class:"person"},ae={class:"header-right"},le={class:"search-controls"},se={class:"el-row"},ne={class:"el-recent-data"},oe=["onClick"],te={key:0,class:"el-recent-empty"},ie={key:1,class:"connected-content"},pe={class:"category-menu-text"},re={key:0,class:"loading-wrapper"},ce={key:1},de=["onClick"],ue={key:0,class:"status-badge status-badge-compact"},ge={key:1,class:"status-badge status-badge-compact"},me={class:"app-content"},Ae={class:"app-icon"},fe={class:"tooltip-content"},ve={key:0},he={key:1},ye={key:2},be={class:"app-details"},we=["title"],_e={class:"app-name-text"},Ce={key:0,class:"status-badge-inline"},ke={key:1,class:"status-badge-inline"},Ee={key:0,class:"app-desc"},Ie={class:"app-desc-text"},Fe={key:2,class:"no-apps-wrapper"},Ue={class:"no-apps-content"},Le={class:"no-apps-image"},Me=["src"],De={class:"no-apps-text"},Ne={key:2,class:"disconnected-content"},Re={class:"no-connection-wrapper"},Pe={class:"no-connection-image"},We=["src"],xe={name:"AppPage",props:{isConnected:{type:Boolean,default:!1}}},Ve=A(Object.assign(xe,{props:{isConnected:{type:Boolean,default:!1}},setup(A){const W=A,x=s((()=>!I.isClient()||(!(!sa.token||!sa.token.accessToken)||W.isConnected))),V=s((()=>!(!j.value&&G.value)||!(!S.value||0===S.value.length)&&S.value.some((e=>e.apps&&e.apps.length>0)))),B=a(""),q=a(null),z=a([]),S=a([]),O=a("0"),Q=a("standard"),X=l([{key:"standard",label:"标准视图"},{key:"compact",label:"紧凑视图"}]),j=a(!1),G=a(!1),Y=a([]),K=a([]),H=a(!1),xe=a(null),Ve=e=>{var a,l;return`${e}_${(null==(a=sa.userInfo)?void 0:a.id)||(null==(l=sa.userInfo)?void 0:l.name)||"anonymous"}`},Be="app_favorites",qe="app_recent",ze=(e,a="success",l=3e3)=>{f({message:e,type:a,duration:l})},Se=(e,a=[])=>{try{const l=Ve(e),s=localStorage.getItem(l);return s?JSON.parse(s):a}catch(l){return console.error(`加载${e}失败:`,l),a}},Oe=(e,a)=>{try{const l=Ve(e);localStorage.setItem(l,JSON.stringify(a))}catch(l){console.error(`保存${e}失败:`,l)}},Qe=e=>Y.value.some((a=>a.id===e.id)),Xe=()=>{K.value=[],Oe(qe,[]),ze("已清空最近访问","info")},je=e=>{1===e.form_fill_enabled?(xe.value=e,H.value=!0):ze("该应用未启用表单代填功能","warning")},Ge=()=>{console.log("表单代填设置成功")},Ye=async e=>{if(!(!e.WebUrl&&"portal"!==e.app_type||!e.WebUrl&&"portal"===e.app_type&&!1===e.open_config.enabled||e.maint))if("portal"!==e.app_type||!0!==e.open_config.enabled||(e.WebUrl="",e.form_fill_enabled=0,I.isClient())){if(1===e.form_fill_enabled)try{const a=await P(String(e.id));if(!a.data||"0"!==a.data.errcode||!a.data.credentials)return ze("请先设置表单代填账户信息","warning"),void je(e)}catch(a){return ze("请先设置表单代填账户信息","warning"),void je(e)}if((e=>{const a=K.value.findIndex((a=>a.id===e.id));a>-1&&K.value.splice(a,1),K.value.unshift({id:e.id,app_name:e.app_name,app_desc:e.app_desc,icon:e.icon,WebUrl:e.WebUrl,maint:e.maint,app_type:e.app_type,open_config:e.open_config,health_status:e.health_status,form_fill_enabled:e.form_fill_enabled,accessTime:Date.now()}),K.value.length>8&&(K.value=K.value.slice(0,8)),Oe(qe,K.value)})(e),e.WebUrl.toLowerCase().startsWith("cs:")){const l=e.WebUrl.substring(3);try{ze("正在启动爱尔企业浏览器...","info"),await(async e=>{try{const a=await D(e);return logger.log("应用启动响应:",a),a&&a.startsWith&&a.startsWith("Ok")?Promise.resolve():a&&a.startsWith&&a.startsWith("Failed")?(ze(a,"error"),Promise.reject(new Error(a))):Promise.resolve()}catch(a){return logger.log("应用启动失败:",a),Promise.reject(a)}})(l),ze("启动成功","success")}catch(a){ze("启动企业浏览器失败：\n      检查是否已安装企业浏览器，\n      如仍然无法启动，请手动运行企业浏览器访问该应用！","warning",8e3)}}else if(I.isClient()){const a=await Ke(e);await I.openResource(a)}else window.open(e.WebUrl,"_blank")}else ze("请在客户端访问","warning")},Ke=async e=>{const a={Type:"URL",Data:{URL:e.WebUrl,OpenExplorer:[]}};if(e.open_config.enabled)if("browser"===e.open_config.open_type){const l=[];for(let a=0;a<e.open_config.browser_configs.length;a++)l.push({ExplorerName:e.open_config.browser_configs[a].type,ExplorerParam:e.open_config.browser_configs[a].params});a.Data.OpenExplorer=l}else if(a.Type="APP","client"===e.open_config.open_type){const l={Windows:{AppName:"",AppPath:"",AppParam:"",NotFoundMsg:""},MacOS:{AppName:"",AppPath:"",AppParam:"",NotFoundMsg:""},Linux:{AppName:"",AppPath:"",AppParam:"",NotFoundMsg:""}};for(let a=0;a<e.open_config.program_configs.length;a++)switch(e.open_config.program_configs[a].os){case"windows":l.Windows.AppName=e.open_config.program_configs[a].name,l.Windows.AppPath=e.open_config.program_configs[a].path,l.Windows.AppParam=e.open_config.program_configs[a].params,l.Windows.NotFoundMsg=e.open_config.program_configs[a].notFoundMessage;break;case"macos":l.MacOS.AppName=e.open_config.program_configs[a].bundleId,l.MacOS.AppParam=e.open_config.program_configs[a].params,l.MacOS.NotFoundMsg=e.open_config.program_configs[a].notFoundMessage;break;case"linux":l.Linux.AppName=e.open_config.program_configs[a].name,l.Linux.AppPath=e.open_config.program_configs[a].path,l.Linux.AppParam=e.open_config.program_configs[a].params,l.Linux.NotFoundMsg=e.open_config.program_configs[a].notFoundMessage}a.Data=l}else if("system"===e.open_config.open_type){const l={Windows:{AppName:"",AppPath:""},MacOS:{AppName:"",AppPath:""},Linux:{AppName:"",AppPath:""}};for(let a=0;a<e.open_config.system_app_configs.length;a++)switch(e.open_config.system_app_configs[a].os){case"windows":l.Windows.AppName=e.open_config.system_app_configs[a].type,l.Windows.AppPath=e.open_config.system_app_configs[a].type;break;case"macos":l.MacOS.AppName=e.open_config.system_app_configs[a].type,l.MacOS.AppPath=e.open_config.system_app_configs[a].type;break;case"linux":l.Linux.AppName=e.open_config.system_app_configs[a].type,l.Linux.AppPath=e.open_config.system_app_configs[a].type}a.Data=l}return a},He=async()=>{I.isClient()&&(await U(sa),logger.log("客户端模式：发送隧道状态刷新事件"),L.emit("refreshTunnelStatus",{timestamp:Date.now(),source:"app-refresh-btn"})),await aa()};h((()=>{}));const Je=e=>{const a=["#71BDDF","#8AB05D","#9571DF","#DF7171","#DFC271","#71DFA7","#B05D8A","#5D8AB0"];let l=0;for(let s=0;s<e.length;s++)l+=e.charCodeAt(s);return a[l%a.length]},Te=s((()=>-1===q.value)),Ze=()=>{if(-1===q.value)Y.value.length>0?S.value=[{id:-1,name:"我的收藏",apps:Y.value}]:S.value=[{id:-1,name:"我的收藏",apps:[]}];else if(null===q.value||0===q.value){const e=[],a=new Set;z.value.forEach((l=>{l.apps&&l.apps.length>0&&l.apps.forEach((l=>{a.has(l.id)||(a.add(l.id),e.push(l))}))})),S.value=[{id:0,name:"全部应用",apps:e}]}else{const e=z.value.filter((e=>e.id===q.value));if(0===e.length)return console.warn("当前选中的分类不存在，回退到全部分类"),q.value=null,O.value="0",void Ze();S.value=e}},$e=e=>{-1===e?(q.value=-1,O.value="-1"):null===e||0===e?(q.value=null,O.value="0"):(q.value=parseInt(e),O.value=e.toString()),Ze()},ea=()=>{if(!B.value)return void Ze();const e=B.value.toLowerCase().trim();if(-1===q.value){const a=Y.value.filter((a=>a.app_name.toLowerCase().includes(e)));S.value=[{id:-1,name:"我的收藏",apps:a}]}else if(null===q.value||0===q.value){const a=[],l=new Set;z.value.forEach((s=>{if(s.apps&&s.apps.length>0){s.apps.filter((a=>a.app_name.toLowerCase().includes(e))).forEach((e=>{l.has(e.id)||(l.add(e.id),a.push(e))}))}})),S.value=[{id:0,name:"全部应用",apps:a}]}else{const a=z.value.filter((e=>e.id===q.value));S.value=a.map((a=>({...a,apps:a.apps.filter((a=>a.app_name.toLowerCase().includes(e)))}))).filter((e=>e.apps.length>0))}},aa=async()=>{j.value=!0;try{const{data:a}=await e({url:"/console/v1/application/getuserapp",method:"get"});if(logger.log("API返回数据:",a),0===a.code&&a.data){const e=a.data.map(((e,a)=>({id:a+1,name:e.category,apps:e.apps.map((e=>({id:e.id,app_name:e.portal_show_name?e.portal_show_name:e.app_name,app_desc:e.portal_desc?e.portal_desc:"web"===e.app_type?"Web应用":"tun"===e.app_type?"隧道应用":"应用程序",icon:e.icon,maint:2===e.app_status,app_type:e.app_type,app_sites:e.app_sites,WebUrl:e.WebUrl,form_fill_enabled:e.form_fill_enabled,open_config:e.open_config,health_status:e.health_status})))})));if(logger.log("格式化后的数据:",e),z.value=e,null!==q.value&&-1!==q.value&&0!==q.value){e.some((e=>e.id===q.value))||(console.warn("当前选中的分类不存在于新数据中，重置为全部分类"),q.value=null,O.value="0")}Ze()}else 0===a.code&&null===a.data&&(z.value=[],Ze())}catch(a){console.error("API调用出错:",a)}finally{j.value=!1,G.value=!0}},la=()=>{var e,a,l,s;(null==(e=sa.userInfo)?void 0:e.id)||(null==(a=sa.userInfo)?void 0:a.name)?(Y.value=Se(Be,[]),K.value=Se(qe,[]),logger.log("加载用户收藏应用:",Y.value.length,"个"),logger.log("加载用户最近访问:",K.value.length,"个"),logger.log("当前用户ID:",(null==(l=sa.userInfo)?void 0:l.id)||(null==(s=sa.userInfo)?void 0:s.name))):console.warn("用户信息未加载，跳过存储数据初始化")};y((async()=>{const e=localStorage.getItem("appViewType");e&&["standard","compact"].includes(e)&&(Q.value=e),q.value=null,O.value="0",await aa(),la()}));const sa=b();n((()=>Q.value),(e=>{localStorage.setItem("appViewType",e)})),n((()=>{var e;return null==(e=sa.userInfo)?void 0:e.id}),((e,a)=>{e&&e!==a&&(logger.log("用户切换，重新加载存储数据:",{oldUserId:a,newUserId:e}),la(),Ze())}),{immediate:!1}),n((()=>sa.token),((e,a)=>{a&&!e&&(logger.log("用户注销，清理存储数据"),Y.value=[],K.value=[],Ze())}),{immediate:!1});const na=e=>{const a=e.apps?e.apps.length:0;if("compact"===Q.value){if(a<=5)return"apps-grid-limited"}else if(a<=2)return"apps-grid-limited";return""};return(e,a)=>{const l=o("base-icon"),s=o("base-input"),n=o("base-button"),A=o("base-option"),f=o("base-select"),h=o("base-header"),y=o("base-menu-item"),b=o("base-tooltip"),I=o("base-menu"),U=o("base-aside"),L=o("base-avatar"),D=o("base-main"),P=o("base-container");return t(),i("div",$,[p("div",ee,[c(h,{class:"app-header",height:"44px",padding:"0 16px"},{default:d((()=>[a[6]||(a[6]=p("div",{class:"header-left"},[p("h1",{class:"page-title"},"应用门户")],-1)),p("div",ae,[p("div",le,[c(l,{class:"search-icon",name:"search"}),c(s,{modelValue:B.value,"onUpdate:modelValue":a[0]||(a[0]=e=>B.value=e),class:"search-input",clearable:"",placeholder:"搜索应用","prefix-icon":"Search",onInput:ea},null,8,["modelValue"]),c(n,{class:"refresh-btn",icon:"Refresh",size:"small",onClick:He},{default:d((()=>a[5]||(a[5]=[p("svg",{"aria-hidden":"true",class:"icon refresh-btn-icon"},[p("use",{"xlink:href":"#icon-search"})],-1)]))),_:1,__:[5]}),c(f,{modelValue:Q.value,"onUpdate:modelValue":a[1]||(a[1]=e=>Q.value=e),class:"view-select",size:"small"},{default:d((()=>[(t(!0),i(C,null,k(X,(e=>(t(),w(A,{key:e.key,label:e.label,value:e.key},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])])])),_:1,__:[6]}),x.value?(t(),w(h,{key:0,class:"app-header",height:"36px",padding:"12px 16px 8px 16px"},{default:d((()=>[p("div",se,[a[8]||(a[8]=p("span",{class:"el-recent-access"},[p("span",{class:"el-recent-text"},"最近访问")],-1)),p("span",ne,[(t(!0),i(C,null,k(K.value,((e,a)=>(t(),i("span",{key:e.id,class:"el-recent-item",onClick:a=>Ye(e)},[v(r(e.app_name)+" ",1),c(l,{class:"el-recent-icon",name:"close",size:"8px",onClick:g((e=>(e=>{const a=K.value.splice(e,1)[0];a&&(ze(`已从最近访问中移除 ${a.app_name}`,"info"),Oe(qe,K.value))})(a)),["stop"])},null,8,["onClick"])],8,oe)))),128)),0===K.value.length?(t(),i("span",te," 暂无最近访问记录 ")):m("",!0),K.value.length>0?(t(),i("svg",{key:1,"aria-hidden":"true",class:"icon el-recent-clear",title:"清空最近访问",onClick:Xe},a[7]||(a[7]=[p("use",{"xlink:href":"#icon-qingkong"},null,-1)]))):m("",!0)])])])),_:1})):m("",!0),x.value?(t(),i("div",ie,[c(P,{class:"flex-container flex-row"},{default:d((()=>[c(U,{class:"category-aside",width:"104px"},{default:d((()=>[c(I,{"default-active":O.value,"background-color":"#ffffff",class:"category-menu",mode:"vertical",onSelect:$e},{default:d((()=>[c(y,{class:"category-menu-item",index:"-1",onClick:a[2]||(a[2]=e=>$e(-1))},{default:d((()=>a[9]||(a[9]=[v(" 收藏 ")]))),_:1,__:[9]}),c(y,{class:"category-menu-item",index:"0",onClick:a[3]||(a[3]=e=>$e(null))},{default:d((()=>a[10]||(a[10]=[v(" 全部 ")]))),_:1,__:[10]}),(t(!0),i(C,null,k(z.value,(e=>(t(),w(y,{key:e.id,index:e.id.toString()},{default:d((()=>[c(b,{content:e.name,disabled:e.name.length<=5,effect:"light",placement:"right"},{default:d((()=>{return[p("span",pe,r((a=e.name,a.length<=5?a:a.substring(0,4)+"...")),1)];var a})),_:2},1032,["content","disabled"])])),_:2},1032,["index"])))),128))])),_:1},8,["default-active"])])),_:1}),c(D,{class:"app-main custom-scrollbar"},{default:d((()=>[j.value?(t(),i("div",re)):V.value?(t(),i("div",ce,[(t(!0),i(C,null,k(S.value,(e=>(t(),i("div",{key:e.id,class:"category-section"},[p("div",{class:E([[`view-${Q.value}`,na(e),{"favorite-category":Te.value}],"apps-grid"])},[(t(!0),i(C,null,k(e.apps,(e=>(t(),i("div",{key:e.id,class:E([{disabled:!e.WebUrl&&"portal"!==e.app_type||!e.WebUrl&&"portal"===e.app_type&&!1===e.open_config.enabled||e.maint},"app-item transition transform-hover"]),onClick:a=>Ye(e)},[2===e.health_status&&"compact"===Q.value?(t(),i("div",ue,a[11]||(a[11]=[p("img",{alt:"故障",class:"status-maint",src:N},null,-1)]))):e.maint&&"compact"===Q.value?(t(),i("div",ge,a[12]||(a[12]=[p("img",{alt:"维护中",class:"status-maint",src:R},null,-1)]))):m("",!0),p("div",me,[p("div",Ae,[c(b,{effect:"light",placement:"bottom"},{content:d((()=>[p("div",fe,[e.WebUrl?(t(),i("span",ve,r(e.WebUrl),1)):"portal"===e.app_type&&e.open_config.enabled?(t(),i("span",he,"打开方式："+r("browser"===e.open_config.open_type?"浏览器":"client"===e.open_config.open_type?"指定程序":"系统应用"),1)):(t(),i("span",ye,"暂无访问地址"))])])),default:d((()=>{return[c(L,{size:"compact"===Q.value?20:28,src:e.iconError?"":(a=e.icon,a?/^(https?:)?\/\//.test(a)||a.startsWith("data:")||a.startsWith("blob:")||a.endsWith(".svg")?a:F()+a:""),style:u(!e.icon||e.iconError?`background-color: ${Je(e.app_name)} !important`:"background-color: #f7f7fa !important"),shape:"square",onError:()=>(async e=>{e.iconError=!0,await M()})(e)},{default:d((()=>[v(r(!e.icon||e.iconError?e.app_name.slice(0,1):""),1)])),_:2},1032,["size","src","style","onError"])];var a})),_:2},1024)]),p("div",be,[p("div",{title:e.app_name,class:"app-name"},[p("span",_e,r(e.app_name),1),2===e.health_status&&"standard"===Q.value?(t(),i("span",Ce,a[13]||(a[13]=[p("img",{alt:"故障",class:"status-maint",src:"data:image/png;base64,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"},null,-1)]))):e.maint&&"standard"===Q.value?(t(),i("span",ke,a[14]||(a[14]=[p("img",{alt:"维护中",class:"status-maint",src:"data:image/png;base64,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"},null,-1)]))):m("",!0)],8,we),"standard"===Q.value?(t(),i("div",Ee,[c(b,{content:e.app_desc||"应用程序",disabled:!(e.app_desc&&e.app_desc.length>8),effect:"light",placement:"bottom"},{default:d((()=>[p("span",Ie,r(e.app_desc||"应用程序"),1)])),_:2},1032,["content","disabled"])])):m("",!0)]),c(l,{name:Qe(e)?"yishoucang":"shoucang",title:Qe(e)?"取消收藏":"收藏",class:"app-collect-icon",onClick:g((a=>(e=>{const a=Y.value.findIndex((a=>a.id===e.id));a>-1?(Y.value.splice(a,1),ze(`已取消收藏 ${e.app_name}`,"info")):(Y.value.push({id:e.id,app_name:e.app_name,app_desc:e.app_desc,icon:e.icon,WebUrl:e.WebUrl,maint:e.maint,app_type:e.app_type,open_config:e.open_config,health_status:e.health_status,form_fill_enabled:e.form_fill_enabled,favoriteTime:Date.now()}),ze(`已收藏 ${e.app_name}`,"success")),Oe(Be,Y.value),Ze()})(e)),["stop"])},null,8,["name","title","onClick"]),1===e.form_fill_enabled||Te.value&&0!==e.form_fill_enabled?(t(),w(l,{key:0,class:"app-form-fill-icon",name:"bianji",title:"编辑表单代填",onClick:g((a=>je(e)),["stop"])},null,8,["onClick"])):m("",!0)])],10,de)))),128))],2)])))),128))])):(t(),i("div",Fe,[p("div",Ue,[p("div",Le,[p("img",{src:_(Z),alt:"暂无搜索结果"},null,8,Me)]),p("div",De,r(B.value?"暂无搜索结果":"暂无应用数据，请添加应用"),1)])]))])),_:1})])),_:1})])):(t(),i("div",Ne,[p("div",Re,[p("div",Pe,[p("img",{src:_(T),alt:"请先建立安全连接"},null,8,We)]),a[15]||(a[15]=p("div",{class:"no-connection-text"},[p("h3",{style:{"font-size":"16px"}},"请先建立安全连接"),p("p",{style:{"font-size":"12px"}},"成功连接后可查看授权的应用")],-1))])]))]),c(J,{"app-info":xe.value,visible:H.value,onSuccess:Ge,"onUpdate:visible":a[4]||(a[4]=e=>H.value=e)},null,8,["app-info","visible"])])}}}),[["__scopeId","data-v-d155529e"]]);export{Ve as default};
