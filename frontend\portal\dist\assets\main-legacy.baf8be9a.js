/*! 
 Build based on gin-vue-admin 
 Time : 1754836337000 */
!function(){function t(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var n,r,o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",i=o.toStringTag||"@@toStringTag";function c(t,o,a,i){var c=o&&o.prototype instanceof f?o:f,s=Object.create(c.prototype);return e(s,"_invoke",function(t,e,o){var a,i,c,f=0,s=o||[],p=!1,l={p:0,n:0,v:n,a:d,f:d.bind(n,4),d:function(t,e){return a=t,i=0,c=n,l.n=e,u}};function d(t,e){for(i=t,c=e,r=0;!p&&f&&!o&&r<s.length;r++){var o,a=s[r],d=l.p,h=a[2];t>3?(o=h===e)&&(c=a[(i=a[4])?5:(i=3,3)],a[4]=a[5]=n):a[0]<=d&&((o=t<2&&d<a[1])?(i=0,l.v=e,l.n=a[1]):d<h&&(o=t<3||a[0]>e||e>h)&&(a[4]=t,a[5]=e,l.n=h,i=0))}if(o||t>1)return u;throw p=!0,e}return function(o,s,h){if(f>1)throw TypeError("Generator is already running");for(p&&1===s&&d(s,h),i=s,c=h;(r=i<2?n:c)||!p;){a||(i?i<3?(i>1&&(l.n=-1),d(i,c)):l.n=c:l.v=c);try{if(f=2,a){if(i||(o="next"),r=a[o]){if(!(r=r.call(a,c)))throw TypeError("iterator result is not an object");if(!r.done)return r;c=r.value,i<2&&(i=0)}else 1===i&&(r=a.return)&&r.call(a),i<2&&(c=TypeError("The iterator does not provide a '"+o+"' method"),i=1);a=n}else if((r=(p=l.n<0)?c:t.call(e,l))!==u)break}catch(r){a=n,i=1,c=r}finally{f=1}}return{value:r,done:p}}}(t,a,i),!0),s}var u={};function f(){}function s(){}function p(){}r=Object.getPrototypeOf;var l=[][a]?r(r([][a]())):(e(r={},a,(function(){return this})),r),d=p.prototype=f.prototype=Object.create(l);function h(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,p):(t.__proto__=p,e(t,i,"GeneratorFunction")),t.prototype=Object.create(d),t}return s.prototype=p,e(d,"constructor",p),e(p,"constructor",s),s.displayName="GeneratorFunction",e(p,i,"GeneratorFunction"),e(d),e(d,i,"Generator"),e(d,a,(function(){return this})),e(d,"toString",(function(){return"[object Generator]"})),(t=function(){return{w:c,m:h}})()}function e(t,n,r,o){var a=Object.defineProperty;try{a({},"",{})}catch(t){a=0}e=function(t,n,r,o){if(n)a?a(t,n,{value:r,enumerable:!o,configurable:!o,writable:!o}):t[n]=r;else{var i=function(n,r){e(t,n,(function(t){return this._invoke(n,r,t)}))};i("next",0),i("throw",1),i("return",2)}},e(t,n,r,o)}function n(t,e,n,r,o,a,i){try{var c=t[a](i),u=c.value}catch(t){return void n(t)}c.done?e(u):Promise.resolve(u).then(r,o)}System.register(["./index-legacy.49b01aa0.js","./index-legacy.aab80689.js"],(function(e,r){"use strict";var o,a,i,c,u,f,s,p=document.createElement("style");return p.textContent='@charset "UTF-8";.access-main[data-v-550f2b74]{width:100%;flex:1;min-height:0;display:flex;flex-direction:column}.access-main .content-wrapper[data-v-550f2b74]{margin:0;padding:0;list-style:none;width:100%;flex:1;min-height:0;display:flex;flex-direction:column}.access-main .content-wrapper .access-app[data-v-550f2b74]{flex:1;min-height:0;font-size:13px;font-weight:400;display:flex;flex-direction:column;background-color:#fff;border-radius:4px;overflow:hidden}.access-main .content-wrapper .access-app .access-app-page[data-v-550f2b74]{width:100%;height:100%;flex:1;min-height:0}\n',document.head.appendChild(p),{setters:[function(t){o=t.default},function(t){a=t._,i=t.h,c=t.a,u=t.b,f=t.d,s=t.j}],execute:function(){var r={name:"BowserAccess",components:{AppPage:o},data:function(){return{}},mounted:function(){return(e=t().m((function e(){return t().w((function(t){for(;;)if(0===t.n)return t.a(2)}),e)})),function(){var t=this,r=arguments;return new Promise((function(o,a){var i=e.apply(t,r);function c(t){n(i,o,a,c,u,"next",t)}function u(t){n(i,o,a,c,u,"throw",t)}c(void 0)}))})();var e},methods:{}},p={class:"access-main"},l={class:"content-wrapper"},d={class:"access-app"};e("default",a(r,[["render",function(t,e,n,r,o,a){var h=i("AppPage");return c(),u("div",p,[f("ul",l,[f("li",d,[s(h,{class:"access-app-page"})])])])}],["__scopeId","data-v-550f2b74"]]))}}}))}();
