/*! 
 Build based on gin-vue-admin 
 Time : 1754879545000 */
!function(){function e(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var n,r,o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",i=o.toStringTag||"@@toStringTag";function l(e,o,a,i){var l=o&&o.prototype instanceof u?o:u,g=Object.create(l.prototype);return t(g,"_invoke",function(e,t,o){var a,i,l,u=0,g=o||[],d=!1,s={p:0,n:0,v:n,a:f,f:f.bind(n,4),d:function(e,t){return a=e,i=0,l=n,s.n=t,c}};function f(e,t){for(i=e,l=t,r=0;!d&&u&&!o&&r<g.length;r++){var o,a=g[r],f=s.p,p=a[2];e>3?(o=p===t)&&(l=a[(i=a[4])?5:(i=3,3)],a[4]=a[5]=n):a[0]<=f&&((o=e<2&&f<a[1])?(i=0,s.v=t,s.n=a[1]):f<p&&(o=e<3||a[0]>t||t>p)&&(a[4]=e,a[5]=t,s.n=p,i=0))}if(o||e>1)return c;throw d=!0,t}return function(o,g,p){if(u>1)throw TypeError("Generator is already running");for(d&&1===g&&f(g,p),i=g,l=p;(r=i<2?n:l)||!d;){a||(i?i<3?(i>1&&(s.n=-1),f(i,l)):s.n=l:s.v=l);try{if(u=2,a){if(i||(o="next"),r=a[o]){if(!(r=r.call(a,l)))throw TypeError("iterator result is not an object");if(!r.done)return r;l=r.value,i<2&&(i=0)}else 1===i&&(r=a.return)&&r.call(a),i<2&&(l=TypeError("The iterator does not provide a '"+o+"' method"),i=1);a=n}else if((r=(d=s.n<0)?l:e.call(t,s))!==c)break}catch(r){a=n,i=1,l=r}finally{u=1}}return{value:r,done:d}}}(e,a,i),!0),g}var c={};function u(){}function g(){}function d(){}r=Object.getPrototypeOf;var s=[][a]?r(r([][a]())):(t(r={},a,(function(){return this})),r),f=d.prototype=u.prototype=Object.create(s);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,d):(e.__proto__=d,t(e,i,"GeneratorFunction")),e.prototype=Object.create(f),e}return g.prototype=d,t(f,"constructor",d),t(d,"constructor",g),g.displayName="GeneratorFunction",t(d,i,"GeneratorFunction"),t(f),t(f,i,"Generator"),t(f,a,(function(){return this})),t(f,"toString",(function(){return"[object Generator]"})),(e=function(){return{w:l,m:p}})()}function t(e,n,r,o){var a=Object.defineProperty;try{a({},"",{})}catch(e){a=0}t=function(e,n,r,o){if(n)a?a(e,n,{value:r,enumerable:!o,configurable:!o,writable:!o}):e[n]=r;else{var i=function(n,r){t(e,n,(function(e){return this._invoke(n,r,e)}))};i("next",0),i("throw",1),i("return",2)}},t(e,n,r,o)}function n(e,t,n,r,o,a,i){try{var l=e[a](i),c=l.value}catch(e){return void n(e)}l.done?t(c):Promise.resolve(c).then(r,o)}function r(e){return function(){var t=this,r=arguments;return new Promise((function(o,a){var i=e.apply(t,r);function l(e){n(i,o,a,l,c,"next",e)}function c(e){n(i,o,a,l,c,"throw",e)}l(void 0)}))}}System.register(["./index-legacy.39a66d41.js","./secondaryAuth-legacy.1191bf79.js"],(function(t,n){"use strict";var o,a,i,l,c,u,g,d,s,f,p,v,h,y=document.createElement("style");return y.textContent='@charset "UTF-8";.dingtalk-qrcode-container[data-v-e9e3234f]{padding-top:15px;overflow:hidden;text-align:center}\n',document.head.appendChild(y),{setters:[function(e){o=e._,a=e.f,i=e.r,l=e.o,c=e.P,u=e.v,g=e.a,d=e.b,s=e.d,f=e.i,p=e.t,v=e.Q},function(e){h=e.u}],execute:function(){var n={style:{"text-align":"center"}},y={class:"title",style:{height:"24px","line-height":"24px",margin:"0 auto",color:"#0082ef","font-size":"20px","text-align":"center"}},m={"aria-hidden":"true",class:"icon",style:{height:"24px",width:"29px","vertical-align":"top","margin-right":"8px",display:"inline-block"}},b=Object.assign({name:"Dingtalk"},{props:{authInfo:{type:Array,default:function(){return[]}},authId:{type:String,default:function(){return""}}},setup:function(t){var o=a(),b=h().handleSecondaryAuthResponse,k=i(0),w=i("https://login.dingtalk.com/login/qrcode.htm"),x=i("https://oapi.dingtalk.com/connect/oauth2/sns_authorize"),_=i(""),I=i(null),E=i({}),T=i(!1),j=i(null),C=i(0),S=t,A=function(){var t=r(e().m((function t(n){var r,o,a,i,l,c,u,g,d,s;return e().w((function(e){for(;;)switch(e.n){case 0:if(logger.log("收到消息:",n),"https://login.dingtalk.com"!==n.origin){e.n=1;break}logger.log("钉钉loginTmpCode:",n.data),r=n.data,logger.log("收到钉钉loginTmpCode:",r),o=S.authInfo.dingtalkAppKey,a=v(),i="".concat(a,"/auth_callback/"),E.value={appid:o,response_type:"code",scope:"snsapi_login",state:S.authId,redirect_uri:encodeURIComponent(i),loginTmpCode:r},l=x.value+"?"+new URLSearchParams(E.value).toString(),logger.log("钉钉认证URL:",l),O(l),e.n=7;break;case 1:if(!n.data||"dingtalk_auth_callback"!==n.data.type){e.n=7;break}if(logger.log("收到index.html认证结果:",n.data),c=n.data,u=c.code,g=c.state,!(d=c.error)){e.n=2;break}return console.error("钉钉认证失败:",d),P("认证失败: "+d),e.a(2);case 2:if(!u||!g){e.n=6;break}if(s=Array.isArray(u)?u[0]:u,!T.value){e.n=3;break}return logger.log("钉钉认证正在进行中，忽略callback消息"),e.a(2);case 3:if(j.value!==s){e.n=4;break}return logger.log("钉钉认证重复的callback消息，忽略"),e.a(2);case 4:return logger.log("钉钉认证成功，code:",u,"state:",g),e.n=5,F(u,g);case 5:e.n=7;break;case 6:console.error("认证结果缺少必要参数"),P("认证结果无效");case 7:return e.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}(),O=function(e){logger.log("创建认证回调iframe:",e);var t=document.getElementById("dingtalk-callback-iframe");t&&t.remove();var n=document.createElement("iframe");n.id="dingtalk-callback-iframe",n.src=e,n.style.display="none",n.style.width="0",n.style.height="0",n.style.border="none",document.body.appendChild(n),logger.log("认证回调iframe已创建")},F=function(){var t=r(e().m((function t(n,r){var a,i,l,c,u;return e().w((function(e){for(;;)switch(e.n){case 0:if(e.p=0,a=Date.now(),i=Array.isArray(n)?n[0]:n,logger.log("钉钉认证回调触发:",{code:i,state:r,currentTime:a}),!T.value){e.n=1;break}return logger.log("钉钉认证正在进行中，忽略重复调用 - 状态检查"),e.a(2);case 1:if(j.value!==i){e.n=2;break}return logger.log("钉钉认证重复的authCode，忽略重复调用 - 认证码检查"),e.a(2);case 2:if(!(a-C.value<2e3)){e.n=3;break}return logger.log("钉钉认证时间间隔过短，忽略重复调用 - 时间检查"),e.a(2);case 3:return T.value=!0,j.value=i,C.value=a,logger.log("钉钉认证成功，开始处理:",{code:i,state:r}),l={clientId:"client_portal",grantType:"implicit",redirect_uri:"".concat(v(),"/"),idpId:Array.isArray(r)?r[0]:r,authWeb:{authWebCode:i}},logger.log("调用登录接口，参数:",l),e.n=4,o.LoginIn(l,"dingtalk",S.authId);case 4:if(!(c=e.v)||-1===c.code){e.n=7;break}return e.n=5,b(c);case 5:if(!e.v){e.n=6;break}return logger.log("钉钉登录成功，进入双因子验证"),e.a(2);case 6:logger.log("钉钉登录成功"),e.n=8;break;case 7:console.error("钉钉登录失败"),P("登录失败，请重试"),L();case 8:setTimeout((function(){T.value=!1,logger.log("钉钉认证状态已重置")}),5e3),e.n=10;break;case 9:e.p=9,u=e.v,console.error("钉钉认证处理失败:",u),P("认证处理失败: "+u.message),L(),T.value=!1,j.value=null;case 10:return e.a(2)}}),t,null,[[0,9]])})));return function(e,n){return t.apply(this,arguments)}}(),L=function(){logger.log("开始绘制钉钉二维码"),T.value=!1,j.value=null,C.value=0,_.value=(new Date).getTime();var e=S.authInfo.dingtalkAppKey;if(!e)return logger.log("钉钉配置缺失（）:",{appId:e}),void P("钉钉配置缺失");var t=v(),n="".concat(t,"/auth_callback/");logger.log("钉钉认证参数（）:",{appId:e,callbackUrl:n,time:_.value}),E.value={appid:e,response_type:"code",scope:"snsapi_login",state:S.authId,redirect_uri:n};var r=encodeURIComponent(x.value+"?"+new URLSearchParams(E.value).toString());logger.log("钉钉goto参数（）:",r),function(e){logger.log("创建钉钉登录iframe（）:",e);var t=w.value,n=document.createElement("iframe"),r=t+"?goto="+e.goto;r+=e.style?"&style="+encodeURIComponent(e.style):"",r+=e.href?"&href="+e.href:"",n.src=r,n.style.border="0",n.style.width=e.width?e.width+"px":"365px",n.style.height=e.height?e.height+"px":"400px",n.id="dingiframe";var o=document.getElementById(e.id);o&&(o.innerHTML="",o.appendChild(n)),logger.log("钉钉iframe创建完成（）:",r),I.value=n}({id:"ding_qrcode_login",goto:r,style:"border:none;background-color:#FFFFFF;",width:260,height:300}),void 0!==window.addEventListener?window.addEventListener("message",A,!1):void 0!==window.attachEvent&&window.attachEvent("onmessage",A)},P=function(e){var t=document.getElementById("ding_qrcode_login");t&&(t.innerHTML='\n      <div style="text-align: center; padding: 20px; color: #f56c6c;">\n        <div style="margin-bottom: 10px;">钉钉认证失败</div>\n        <div style="font-size: 12px; color: #909399;">'.concat(e,'</div>\n        <button onclick="location.reload()" style="margin-top: 10px; padding: 5px 15px; background: #409eff; color: white; border: none; border-radius: 4px; cursor: pointer;">\n          重试\n        </button>\n      </div>\n    '))};return l((function(){logger.log("钉钉认证组件挂载（）"),L()})),c((function(){logger.log("钉钉认证组件卸载"),T.value=!1,j.value=null,C.value=0;var e=document.getElementById("dingtalk-callback-iframe");e&&e.remove(),void 0!==window.addEventListener?window.removeEventListener("message",A,!1):void 0!==window.attachEvent&&window.detachEvent("onmessage",A)})),u(S,(function(){logger.log("钉钉认证props变化，重新绘制二维码（）"),k.value++,L()})),function(e,r){return g(),d("div",{key:k.value},[s("div",n,[s("span",y,[(g(),d("svg",m,r[0]||(r[0]=[s("use",{"xlink:href":"#icon-auth-dingtalk"},null,-1)]))),f(" "+p(t.authInfo.name),1)])]),r[1]||(r[1]=s("div",{id:"ding_qrcode_login",class:"dingtalk-qrcode-container"},null,-1))])}}});t("default",o(b,[["__scopeId","data-v-e9e3234f"]]))}}}))}();
