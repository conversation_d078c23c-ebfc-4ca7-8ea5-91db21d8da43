/*! 
 Build based on gin-vue-admin 
 Time : 1754879545000 */
!function(){function t(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var r,i,n="function"==typeof Symbol?Symbol:{},s=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function a(t,n,s,o){var a=n&&n.prototype instanceof u?n:u,c=Object.create(a.prototype);return e(c,"_invoke",function(t,e,n){var s,o,a,u=0,c=n||[],f=!1,l={p:0,n:0,v:r,a:p,f:p.bind(r,4),d:function(t,e){return s=t,o=0,a=r,l.n=e,h}};function p(t,e){for(o=t,a=e,i=0;!f&&u&&!n&&i<c.length;i++){var n,s=c[i],p=l.p,g=s[2];t>3?(n=g===e)&&(a=s[(o=s[4])?5:(o=3,3)],s[4]=s[5]=r):s[0]<=p&&((n=t<2&&p<s[1])?(o=0,l.v=e,l.n=s[1]):p<g&&(n=t<3||s[0]>e||e>g)&&(s[4]=t,s[5]=e,l.n=g,o=0))}if(n||t>1)return h;throw f=!0,e}return function(n,c,g){if(u>1)throw TypeError("Generator is already running");for(f&&1===c&&p(c,g),o=c,a=g;(i=o<2?r:a)||!f;){s||(o?o<3?(o>1&&(l.n=-1),p(o,a)):l.n=a:l.v=a);try{if(u=2,s){if(o||(n="next"),i=s[n]){if(!(i=i.call(s,a)))throw TypeError("iterator result is not an object");if(!i.done)return i;a=i.value,o<2&&(o=0)}else 1===o&&(i=s.return)&&i.call(s),o<2&&(a=TypeError("The iterator does not provide a '"+n+"' method"),o=1);s=r}else if((i=(f=l.n<0)?a:t.call(e,l))!==h)break}catch(i){s=r,o=1,a=i}finally{u=1}}return{value:i,done:f}}}(t,s,o),!0),c}var h={};function u(){}function c(){}function f(){}i=Object.getPrototypeOf;var l=[][s]?i(i([][s]())):(e(i={},s,(function(){return this})),i),p=f.prototype=u.prototype=Object.create(l);function g(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,f):(t.__proto__=f,e(t,o,"GeneratorFunction")),t.prototype=Object.create(p),t}return c.prototype=f,e(p,"constructor",f),e(f,"constructor",c),c.displayName="GeneratorFunction",e(f,o,"GeneratorFunction"),e(p),e(p,o,"Generator"),e(p,s,(function(){return this})),e(p,"toString",(function(){return"[object Generator]"})),(t=function(){return{w:a,m:g}})()}function e(t,r,i,n){var s=Object.defineProperty;try{s({},"",{})}catch(t){s=0}e=function(t,r,i,n){if(r)s?s(t,r,{value:i,enumerable:!n,configurable:!n,writable:!n}):t[r]=i;else{var o=function(r,i){e(t,r,(function(t){return this._invoke(r,i,t)}))};o("next",0),o("throw",1),o("return",2)}},e(t,r,i,n)}function r(t,e,r,i,n,s,o){try{var a=t[s](o),h=a.value}catch(t){return void r(t)}a.done?e(h):Promise.resolve(h).then(i,n)}function i(t){return function(){var e=this,i=arguments;return new Promise((function(n,s){var o=t.apply(e,i);function a(t){r(o,n,s,a,h,"next",t)}function h(t){r(o,n,s,a,h,"throw",t)}a(void 0)}))}}System.register(["./index-legacy.39a66d41.js","./config-legacy.6e7b5c15.js","./secondaryAuth-legacy.1191bf79.js"],(function(e,r){"use strict";var n,s,o,a,h,u,c,f,l,p,g,d,m,v,y,b,T,S,w,x=document.createElement("style");return x.textContent='@charset "UTF-8";.login-page[data-v-50674125]{width:100%;height:100%;background-image:url('+new URL("login_background.4576f25d.png",r.meta.url).href+");background-size:cover;background-position:center;min-height:100vh}.header[data-v-50674125]{height:60px;display:flex;align-items:center;background-color:rgba(255,255,255,.8)}.logo[data-v-50674125]{height:20px;margin-left:50px;margin-right:10px}.separator[data-v-50674125]{width:1px;height:14px;background-color:#ccc;margin:0 10px}.company-name[data-v-50674125]{font-size:24px}.header-text[data-v-50674125]{font-size:12px;opacity:.6}.content[data-v-50674125]{display:flex;height:calc(100% - 60px)}.left-panel[data-v-50674125]{flex:1;display:flex;flex-direction:column;justify-content:center;padding:20px;margin-left:310px}.slogan[data-v-50674125]{font-size:36px;margin-bottom:20px}.image[data-v-50674125]{width:718px;height:470px;margin-bottom:20px}.icons[data-v-50674125]{display:flex;justify-content:space-between;width:150px}.icons img[data-v-50674125]{width:30px;height:30px}.right-panel[data-v-50674125]{width:auto;height:auto;min-height:560px;max-height:560px;box-sizing:border-box;min-width:450px;max-width:450px;margin-right:310px;margin-bottom:auto;padding:40px;background-color:#fff;border-radius:8px;box-shadow:0 2px 16px rgba(16,36,66,.1);backdrop-filter:blur(2px);display:flex;flex-direction:column;justify-content:center;position:absolute;z-index:2;top:50%;left:76%;transform:translate(-50%,-50%)}.title[data-v-50674125]{height:57px;font-size:18px;text-align:center}.login_panel[data-v-50674125]{display:flex;flex-direction:column}.form-group[data-v-50674125]{display:flex;flex-direction:column;margin-bottom:20px}.label[data-v-50674125]{font-size:16px;margin-bottom:5px}.input-field[data-v-50674125]{height:40px;padding:5px;font-size:16px;border:1px solid #ccc;border-radius:5px}.login-submit-button[data-v-50674125]{width:100%;height:40px;margin-top:12px;font-size:16px;color:#fff;background-color:#536ce6;border:none;border-radius:5px;cursor:pointer;margin-bottom:60px}.submit-button[data-v-50674125]:hover,.submit-button[data-v-50674125]:active{background-color:#536ce6}.input-icon-left[data-v-50674125]{position:absolute;left:16px;top:50%;transform:translateY(-50%);width:15px;height:15px;color:#999;z-index:2;pointer-events:none}.input-icon-right[data-v-50674125]{position:absolute;right:16px;top:50%;transform:translateY(-50%);width:15px;height:15px;color:#999;z-index:2;cursor:pointer;transition:color .3s ease}.input-icon-right[data-v-50674125]:hover{color:#666}.password-toggle[data-v-50674125]:hover{color:#626aef}[data-v-50674125] .base-input{padding-left:40px!important;height:40px}.login-form[data-v-50674125]{margin-left:5px;margin-right:5px}\n",document.head.appendChild(x),{setters:[function(t){n=t._,s=t.r,o=t.N,a=t.f,h=t.h,u=t.a,c=t.k,f=t.w,l=t.j,p=t.d,g=t.b,d=t.l,m=t.i,v=t.a3,y=t.M,b=t.L,T=t.a4},function(t){S=t.g},function(t){w=t.u}],execute:function(){function r(t){return"0123456789abcdefghijklmnopqrstuvwxyz".charAt(t)}function x(t,e){return t&e}function E(t,e){return t|e}function D(t,e){return t^e}function B(t,e){return t&~e}function O(t){if(0==t)return-1;var e=0;return 65535&t||(t>>=16,e+=16),255&t||(t>>=8,e+=8),15&t||(t>>=4,e+=4),3&t||(t>>=2,e+=2),1&t||++e,e}function R(t){for(var e=0;0!=t;)t&=t-1,++e;return e}var A,V="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function I(t){var e,r,i="";for(e=0;e+3<=t.length;e+=3)r=parseInt(t.substring(e,e+3),16),i+=V.charAt(r>>6)+V.charAt(63&r);for(e+1==t.length?(r=parseInt(t.substring(e,e+1),16),i+=V.charAt(r<<2)):e+2==t.length&&(r=parseInt(t.substring(e,e+2),16),i+=V.charAt(r>>2)+V.charAt((3&r)<<4));(3&i.length)>0;)i+="=";return i}function N(t){var e,i="",n=0,s=0;for(e=0;e<t.length&&"="!=t.charAt(e);++e){var o=V.indexOf(t.charAt(e));o<0||(0==n?(i+=r(o>>2),s=3&o,n=1):1==n?(i+=r(s<<2|o>>4),s=15&o,n=2):2==n?(i+=r(s),i+=r(o>>2),s=3&o,n=3):(i+=r(s<<2|o>>4),i+=r(15&o),n=0))}return 1==n&&(i+=r(s<<2)),i}var P,j=function(t){var e;if(void 0===A){var r="0123456789ABCDEF",i=" \f\n\r\t \u2028\u2029";for(A={},e=0;e<16;++e)A[r.charAt(e)]=e;for(r=r.toLowerCase(),e=10;e<16;++e)A[r.charAt(e)]=e;for(e=0;e<8;++e)A[i.charAt(e)]=-1}var n=[],s=0,o=0;for(e=0;e<t.length;++e){var a=t.charAt(e);if("="==a)break;if(-1!=(a=A[a])){if(void 0===a)throw new Error("Illegal character at offset "+e);s|=a,++o>=2?(n[n.length]=s,s=0,o=0):s<<=4}}if(o)throw new Error("Hex encoding incomplete: 4 bits missing");return n},M={decode:function(t){var e;if(void 0===P){var r="= \f\n\r\t \u2028\u2029";for(P=Object.create(null),e=0;e<64;++e)P["ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(e)]=e;for(P["-"]=62,P._=63,e=0;e<9;++e)P[r.charAt(e)]=-1}var i=[],n=0,s=0;for(e=0;e<t.length;++e){var o=t.charAt(e);if("="==o)break;if(-1!=(o=P[o])){if(void 0===o)throw new Error("Illegal character at offset "+e);n|=o,++s>=4?(i[i.length]=n>>16,i[i.length]=n>>8&255,i[i.length]=255&n,n=0,s=0):n<<=6}}switch(s){case 1:throw new Error("Base64 encoding incomplete: at least 2 bits missing");case 2:i[i.length]=n>>10;break;case 3:i[i.length]=n>>16,i[i.length]=n>>8&255}return i},re:/-----BEGIN [^-]+-----([A-Za-z0-9+\/=\s]+)-----END [^-]+-----|begin-base64[^\n]+\n([A-Za-z0-9+\/=\s]+)====/,unarmor:function(t){var e=M.re.exec(t);if(e)if(e[1])t=e[1];else{if(!e[2])throw new Error("RegExp out of sync");t=e[2]}return M.decode(t)}},L=1e13,q=function(){function t(t){this.buf=[+t||0]}return t.prototype.mulAdd=function(t,e){var r,i,n=this.buf,s=n.length;for(r=0;r<s;++r)(i=n[r]*t+e)<L?e=0:i-=(e=0|i/L)*L,n[r]=i;e>0&&(n[r]=e)},t.prototype.sub=function(t){var e,r,i=this.buf,n=i.length;for(e=0;e<n;++e)(r=i[e]-t)<0?(r+=L,t=1):t=0,i[e]=r;for(;0===i[i.length-1];)i.pop()},t.prototype.toString=function(t){if(10!=(t||10))throw new Error("only base 10 is supported");for(var e=this.buf,r=e[e.length-1].toString(),i=e.length-2;i>=0;--i)r+=(L+e[i]).toString().substring(1);return r},t.prototype.valueOf=function(){for(var t=this.buf,e=0,r=t.length-1;r>=0;--r)e=e*L+t[r];return e},t.prototype.simplify=function(){var t=this.buf;return 1==t.length?t[0]:this},t}(),k=/^(\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/,C=/^(\d\d\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/;function _(t,e){return t.length>e&&(t=t.substring(0,e)+"…"),t}var H,F=function(){function t(e,r){this.hexDigits="0123456789ABCDEF",e instanceof t?(this.enc=e.enc,this.pos=e.pos):(this.enc=e,this.pos=r)}return t.prototype.get=function(t){if(void 0===t&&(t=this.pos++),t>=this.enc.length)throw new Error("Requesting byte offset ".concat(t," on a stream of length ").concat(this.enc.length));return"string"==typeof this.enc?this.enc.charCodeAt(t):this.enc[t]},t.prototype.hexByte=function(t){return this.hexDigits.charAt(t>>4&15)+this.hexDigits.charAt(15&t)},t.prototype.hexDump=function(t,e,r){for(var i="",n=t;n<e;++n)if(i+=this.hexByte(this.get(n)),!0!==r)switch(15&n){case 7:i+="  ";break;case 15:i+="\n";break;default:i+=" "}return i},t.prototype.isASCII=function(t,e){for(var r=t;r<e;++r){var i=this.get(r);if(i<32||i>176)return!1}return!0},t.prototype.parseStringISO=function(t,e){for(var r="",i=t;i<e;++i)r+=String.fromCharCode(this.get(i));return r},t.prototype.parseStringUTF=function(t,e){for(var r="",i=t;i<e;){var n=this.get(i++);r+=n<128?String.fromCharCode(n):n>191&&n<224?String.fromCharCode((31&n)<<6|63&this.get(i++)):String.fromCharCode((15&n)<<12|(63&this.get(i++))<<6|63&this.get(i++))}return r},t.prototype.parseStringBMP=function(t,e){for(var r,i,n="",s=t;s<e;)r=this.get(s++),i=this.get(s++),n+=String.fromCharCode(r<<8|i);return n},t.prototype.parseTime=function(t,e,r){var i=this.parseStringISO(t,e),n=(r?k:C).exec(i);return n?(r&&(n[1]=+n[1],n[1]+=+n[1]<70?2e3:1900),i=n[1]+"-"+n[2]+"-"+n[3]+" "+n[4],n[5]&&(i+=":"+n[5],n[6]&&(i+=":"+n[6],n[7]&&(i+="."+n[7]))),n[8]&&(i+=" UTC","Z"!=n[8]&&(i+=n[8],n[9]&&(i+=":"+n[9]))),i):"Unrecognized time: "+i},t.prototype.parseInteger=function(t,e){for(var r,i=this.get(t),n=i>127,s=n?255:0,o="";i==s&&++t<e;)i=this.get(t);if(0===(r=e-t))return n?-1:0;if(r>4){for(o=i,r<<=3;!(128&(+o^s));)o=+o<<1,--r;o="("+r+" bit)\n"}n&&(i-=256);for(var a=new q(i),h=t+1;h<e;++h)a.mulAdd(256,this.get(h));return o+a.toString()},t.prototype.parseBitString=function(t,e,r){for(var i=this.get(t),n="("+((e-t-1<<3)-i)+" bit)\n",s="",o=t+1;o<e;++o){for(var a=this.get(o),h=o==e-1?i:0,u=7;u>=h;--u)s+=a>>u&1?"1":"0";if(s.length>r)return n+_(s,r)}return n+s},t.prototype.parseOctetString=function(t,e,r){if(this.isASCII(t,e))return _(this.parseStringISO(t,e),r);var i=e-t,n="("+i+" byte)\n";i>(r/=2)&&(e=t+r);for(var s=t;s<e;++s)n+=this.hexByte(this.get(s));return i>r&&(n+="…"),n},t.prototype.parseOID=function(t,e,r){for(var i="",n=new q,s=0,o=t;o<e;++o){var a=this.get(o);if(n.mulAdd(128,127&a),s+=7,!(128&a)){if(""===i)if((n=n.simplify())instanceof q)n.sub(80),i="2."+n.toString();else{var h=n<80?n<40?0:1:2;i=h+"."+(n-40*h)}else i+="."+n.toString();if(i.length>r)return _(i,r);n=new q,s=0}}return s>0&&(i+=".incomplete"),i},t}(),U=function(){function t(t,e,r,i,n){if(!(i instanceof K))throw new Error("Invalid tag value.");this.stream=t,this.header=e,this.length=r,this.tag=i,this.sub=n}return t.prototype.typeName=function(){switch(this.tag.tagClass){case 0:switch(this.tag.tagNumber){case 0:return"EOC";case 1:return"BOOLEAN";case 2:return"INTEGER";case 3:return"BIT_STRING";case 4:return"OCTET_STRING";case 5:return"NULL";case 6:return"OBJECT_IDENTIFIER";case 7:return"ObjectDescriptor";case 8:return"EXTERNAL";case 9:return"REAL";case 10:return"ENUMERATED";case 11:return"EMBEDDED_PDV";case 12:return"UTF8String";case 16:return"SEQUENCE";case 17:return"SET";case 18:return"NumericString";case 19:return"PrintableString";case 20:return"TeletexString";case 21:return"VideotexString";case 22:return"IA5String";case 23:return"UTCTime";case 24:return"GeneralizedTime";case 25:return"GraphicString";case 26:return"VisibleString";case 27:return"GeneralString";case 28:return"UniversalString";case 30:return"BMPString"}return"Universal_"+this.tag.tagNumber.toString();case 1:return"Application_"+this.tag.tagNumber.toString();case 2:return"["+this.tag.tagNumber.toString()+"]";case 3:return"Private_"+this.tag.tagNumber.toString()}},t.prototype.content=function(t){if(void 0===this.tag)return null;void 0===t&&(t=1/0);var e=this.posContent(),r=Math.abs(this.length);if(!this.tag.isUniversal())return null!==this.sub?"("+this.sub.length+" elem)":this.stream.parseOctetString(e,e+r,t);switch(this.tag.tagNumber){case 1:return 0===this.stream.get(e)?"false":"true";case 2:return this.stream.parseInteger(e,e+r);case 3:return this.sub?"("+this.sub.length+" elem)":this.stream.parseBitString(e,e+r,t);case 4:return this.sub?"("+this.sub.length+" elem)":this.stream.parseOctetString(e,e+r,t);case 6:return this.stream.parseOID(e,e+r,t);case 16:case 17:return null!==this.sub?"("+this.sub.length+" elem)":"(no elem)";case 12:return _(this.stream.parseStringUTF(e,e+r),t);case 18:case 19:case 20:case 21:case 22:case 26:return _(this.stream.parseStringISO(e,e+r),t);case 30:return _(this.stream.parseStringBMP(e,e+r),t);case 23:case 24:return this.stream.parseTime(e,e+r,23==this.tag.tagNumber)}return null},t.prototype.toString=function(){return this.typeName()+"@"+this.stream.pos+"[header:"+this.header+",length:"+this.length+",sub:"+(null===this.sub?"null":this.sub.length)+"]"},t.prototype.toPrettyString=function(t){void 0===t&&(t="");var e=t+this.typeName()+" @"+this.stream.pos;if(this.length>=0&&(e+="+"),e+=this.length,this.tag.tagConstructed?e+=" (constructed)":!this.tag.isUniversal()||3!=this.tag.tagNumber&&4!=this.tag.tagNumber||null===this.sub||(e+=" (encapsulates)"),e+="\n",null!==this.sub){t+="  ";for(var r=0,i=this.sub.length;r<i;++r)e+=this.sub[r].toPrettyString(t)}return e},t.prototype.posStart=function(){return this.stream.pos},t.prototype.posContent=function(){return this.stream.pos+this.header},t.prototype.posEnd=function(){return this.stream.pos+this.header+Math.abs(this.length)},t.prototype.toHexString=function(){return this.stream.hexDump(this.posStart(),this.posEnd(),!0)},t.decodeLength=function(t){var e=t.get(),r=127&e;if(r==e)return r;if(r>6)throw new Error("Length over 48 bits not supported at position "+(t.pos-1));if(0===r)return null;e=0;for(var i=0;i<r;++i)e=256*e+t.get();return e},t.prototype.getHexStringValue=function(){var t=this.toHexString(),e=2*this.header,r=2*this.length;return t.substr(e,r)},t.decode=function(e){var r;r=e instanceof F?e:new F(e,0);var i=new F(r),n=new K(r),s=t.decodeLength(r),o=r.pos,a=o-i.pos,h=null,u=function(){var e=[];if(null!==s){for(var i=o+s;r.pos<i;)e[e.length]=t.decode(r);if(r.pos!=i)throw new Error("Content size is not correct for container starting at offset "+o)}else try{for(;;){var n=t.decode(r);if(n.tag.isEOC())break;e[e.length]=n}s=o-r.pos}catch(a){throw new Error("Exception while decoding undefined length content: "+a)}return e};if(n.tagConstructed)h=u();else if(n.isUniversal()&&(3==n.tagNumber||4==n.tagNumber))try{if(3==n.tagNumber&&0!=r.get())throw new Error("BIT STRINGs with unused bits cannot encapsulate.");h=u();for(var c=0;c<h.length;++c)if(h[c].tag.isEOC())throw new Error("EOC is not supposed to be actual content.")}catch(f){h=null}if(null===h){if(null===s)throw new Error("We can't skip over an invalid tag with undefined length at offset "+o);r.pos=o+Math.abs(s)}return new t(i,a,s,n,h)},t}(),K=function(){function t(t){var e=t.get();if(this.tagClass=e>>6,this.tagConstructed=!!(32&e),this.tagNumber=31&e,31==this.tagNumber){var r=new q;do{e=t.get(),r.mulAdd(128,127&e)}while(128&e);this.tagNumber=r.simplify()}}return t.prototype.isUniversal=function(){return 0===this.tagClass},t.prototype.isEOC=function(){return 0===this.tagClass&&0===this.tagNumber},t}(),z=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],G=(1<<26)/z[z.length-1],Z=function(){function t(t,e,r){null!=t&&("number"==typeof t?this.fromNumber(t,e,r):null==e&&"string"!=typeof t?this.fromString(t,256):this.fromString(t,e))}return t.prototype.toString=function(t){if(this.s<0)return"-"+this.negate().toString(t);var e;if(16==t)e=4;else if(8==t)e=3;else if(2==t)e=1;else if(32==t)e=5;else{if(4!=t)return this.toRadix(t);e=2}var i,n=(1<<e)-1,s=!1,o="",a=this.t,h=this.DB-a*this.DB%e;if(a-- >0)for(h<this.DB&&(i=this[a]>>h)>0&&(s=!0,o=r(i));a>=0;)h<e?(i=(this[a]&(1<<h)-1)<<e-h,i|=this[--a]>>(h+=this.DB-e)):(i=this[a]>>(h-=e)&n,h<=0&&(h+=this.DB,--a)),i>0&&(s=!0),s&&(o+=r(i));return s?o:"0"},t.prototype.negate=function(){var e=X();return t.ZERO.subTo(this,e),e},t.prototype.abs=function(){return this.s<0?this.negate():this},t.prototype.compareTo=function(t){var e=this.s-t.s;if(0!=e)return e;var r=this.t;if(0!=(e=r-t.t))return this.s<0?-e:e;for(;--r>=0;)if(0!=(e=this[r]-t[r]))return e;return 0},t.prototype.bitLength=function(){return this.t<=0?0:this.DB*(this.t-1)+ot(this[this.t-1]^this.s&this.DM)},t.prototype.mod=function(e){var r=X();return this.abs().divRemTo(e,null,r),this.s<0&&r.compareTo(t.ZERO)>0&&e.subTo(r,r),r},t.prototype.modPowInt=function(t,e){var r;return r=t<256||e.isEven()?new Q(e):new $(e),this.exp(t,r)},t.prototype.clone=function(){var t=X();return this.copyTo(t),t},t.prototype.intValue=function(){if(this.s<0){if(1==this.t)return this[0]-this.DV;if(0==this.t)return-1}else{if(1==this.t)return this[0];if(0==this.t)return 0}return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]},t.prototype.byteValue=function(){return 0==this.t?this.s:this[0]<<24>>24},t.prototype.shortValue=function(){return 0==this.t?this.s:this[0]<<16>>16},t.prototype.signum=function(){return this.s<0?-1:this.t<=0||1==this.t&&this[0]<=0?0:1},t.prototype.toByteArray=function(){var t=this.t,e=[];e[0]=this.s;var r,i=this.DB-t*this.DB%8,n=0;if(t-- >0)for(i<this.DB&&(r=this[t]>>i)!=(this.s&this.DM)>>i&&(e[n++]=r|this.s<<this.DB-i);t>=0;)i<8?(r=(this[t]&(1<<i)-1)<<8-i,r|=this[--t]>>(i+=this.DB-8)):(r=this[t]>>(i-=8)&255,i<=0&&(i+=this.DB,--t)),128&r&&(r|=-256),0==n&&(128&this.s)!=(128&r)&&++n,(n>0||r!=this.s)&&(e[n++]=r);return e},t.prototype.equals=function(t){return 0==this.compareTo(t)},t.prototype.min=function(t){return this.compareTo(t)<0?this:t},t.prototype.max=function(t){return this.compareTo(t)>0?this:t},t.prototype.and=function(t){var e=X();return this.bitwiseTo(t,x,e),e},t.prototype.or=function(t){var e=X();return this.bitwiseTo(t,E,e),e},t.prototype.xor=function(t){var e=X();return this.bitwiseTo(t,D,e),e},t.prototype.andNot=function(t){var e=X();return this.bitwiseTo(t,B,e),e},t.prototype.not=function(){for(var t=X(),e=0;e<this.t;++e)t[e]=this.DM&~this[e];return t.t=this.t,t.s=~this.s,t},t.prototype.shiftLeft=function(t){var e=X();return t<0?this.rShiftTo(-t,e):this.lShiftTo(t,e),e},t.prototype.shiftRight=function(t){var e=X();return t<0?this.lShiftTo(-t,e):this.rShiftTo(t,e),e},t.prototype.getLowestSetBit=function(){for(var t=0;t<this.t;++t)if(0!=this[t])return t*this.DB+O(this[t]);return this.s<0?this.t*this.DB:-1},t.prototype.bitCount=function(){for(var t=0,e=this.s&this.DM,r=0;r<this.t;++r)t+=R(this[r]^e);return t},t.prototype.testBit=function(t){var e=Math.floor(t/this.DB);return e>=this.t?0!=this.s:!!(this[e]&1<<t%this.DB)},t.prototype.setBit=function(t){return this.changeBit(t,E)},t.prototype.clearBit=function(t){return this.changeBit(t,B)},t.prototype.flipBit=function(t){return this.changeBit(t,D)},t.prototype.add=function(t){var e=X();return this.addTo(t,e),e},t.prototype.subtract=function(t){var e=X();return this.subTo(t,e),e},t.prototype.multiply=function(t){var e=X();return this.multiplyTo(t,e),e},t.prototype.divide=function(t){var e=X();return this.divRemTo(t,e,null),e},t.prototype.remainder=function(t){var e=X();return this.divRemTo(t,null,e),e},t.prototype.divideAndRemainder=function(t){var e=X(),r=X();return this.divRemTo(t,e,r),[e,r]},t.prototype.modPow=function(t,e){var r,i,n=t.bitLength(),s=st(1);if(n<=0)return s;r=n<18?1:n<48?3:n<144?4:n<768?5:6,i=n<8?new Q(e):e.isEven()?new W(e):new $(e);var o=[],a=3,h=r-1,u=(1<<r)-1;if(o[1]=i.convert(this),r>1){var c=X();for(i.sqrTo(o[1],c);a<=u;)o[a]=X(),i.mulTo(c,o[a-2],o[a]),a+=2}var f,l,p=t.t-1,g=!0,d=X();for(n=ot(t[p])-1;p>=0;){for(n>=h?f=t[p]>>n-h&u:(f=(t[p]&(1<<n+1)-1)<<h-n,p>0&&(f|=t[p-1]>>this.DB+n-h)),a=r;!(1&f);)f>>=1,--a;if((n-=a)<0&&(n+=this.DB,--p),g)o[f].copyTo(s),g=!1;else{for(;a>1;)i.sqrTo(s,d),i.sqrTo(d,s),a-=2;a>0?i.sqrTo(s,d):(l=s,s=d,d=l),i.mulTo(d,o[f],s)}for(;p>=0&&!(t[p]&1<<n);)i.sqrTo(s,d),l=s,s=d,d=l,--n<0&&(n=this.DB-1,--p)}return i.revert(s)},t.prototype.modInverse=function(e){var r=e.isEven();if(this.isEven()&&r||0==e.signum())return t.ZERO;for(var i=e.clone(),n=this.clone(),s=st(1),o=st(0),a=st(0),h=st(1);0!=i.signum();){for(;i.isEven();)i.rShiftTo(1,i),r?(s.isEven()&&o.isEven()||(s.addTo(this,s),o.subTo(e,o)),s.rShiftTo(1,s)):o.isEven()||o.subTo(e,o),o.rShiftTo(1,o);for(;n.isEven();)n.rShiftTo(1,n),r?(a.isEven()&&h.isEven()||(a.addTo(this,a),h.subTo(e,h)),a.rShiftTo(1,a)):h.isEven()||h.subTo(e,h),h.rShiftTo(1,h);i.compareTo(n)>=0?(i.subTo(n,i),r&&s.subTo(a,s),o.subTo(h,o)):(n.subTo(i,n),r&&a.subTo(s,a),h.subTo(o,h))}return 0!=n.compareTo(t.ONE)?t.ZERO:h.compareTo(e)>=0?h.subtract(e):h.signum()<0?(h.addTo(e,h),h.signum()<0?h.add(e):h):h},t.prototype.pow=function(t){return this.exp(t,new Y)},t.prototype.gcd=function(t){var e=this.s<0?this.negate():this.clone(),r=t.s<0?t.negate():t.clone();if(e.compareTo(r)<0){var i=e;e=r,r=i}var n=e.getLowestSetBit(),s=r.getLowestSetBit();if(s<0)return e;for(n<s&&(s=n),s>0&&(e.rShiftTo(s,e),r.rShiftTo(s,r));e.signum()>0;)(n=e.getLowestSetBit())>0&&e.rShiftTo(n,e),(n=r.getLowestSetBit())>0&&r.rShiftTo(n,r),e.compareTo(r)>=0?(e.subTo(r,e),e.rShiftTo(1,e)):(r.subTo(e,r),r.rShiftTo(1,r));return s>0&&r.lShiftTo(s,r),r},t.prototype.isProbablePrime=function(t){var e,r=this.abs();if(1==r.t&&r[0]<=z[z.length-1]){for(e=0;e<z.length;++e)if(r[0]==z[e])return!0;return!1}if(r.isEven())return!1;for(e=1;e<z.length;){for(var i=z[e],n=e+1;n<z.length&&i<G;)i*=z[n++];for(i=r.modInt(i);e<n;)if(i%z[e++]==0)return!1}return r.millerRabin(t)},t.prototype.copyTo=function(t){for(var e=this.t-1;e>=0;--e)t[e]=this[e];t.t=this.t,t.s=this.s},t.prototype.fromInt=function(t){this.t=1,this.s=t<0?-1:0,t>0?this[0]=t:t<-1?this[0]=t+this.DV:this.t=0},t.prototype.fromString=function(e,r){var i;if(16==r)i=4;else if(8==r)i=3;else if(256==r)i=8;else if(2==r)i=1;else if(32==r)i=5;else{if(4!=r)return void this.fromRadix(e,r);i=2}this.t=0,this.s=0;for(var n=e.length,s=!1,o=0;--n>=0;){var a=8==i?255&+e[n]:nt(e,n);a<0?"-"==e.charAt(n)&&(s=!0):(s=!1,0==o?this[this.t++]=a:o+i>this.DB?(this[this.t-1]|=(a&(1<<this.DB-o)-1)<<o,this[this.t++]=a>>this.DB-o):this[this.t-1]|=a<<o,(o+=i)>=this.DB&&(o-=this.DB))}8==i&&128&+e[0]&&(this.s=-1,o>0&&(this[this.t-1]|=(1<<this.DB-o)-1<<o)),this.clamp(),s&&t.ZERO.subTo(this,this)},t.prototype.clamp=function(){for(var t=this.s&this.DM;this.t>0&&this[this.t-1]==t;)--this.t},t.prototype.dlShiftTo=function(t,e){var r;for(r=this.t-1;r>=0;--r)e[r+t]=this[r];for(r=t-1;r>=0;--r)e[r]=0;e.t=this.t+t,e.s=this.s},t.prototype.drShiftTo=function(t,e){for(var r=t;r<this.t;++r)e[r-t]=this[r];e.t=Math.max(this.t-t,0),e.s=this.s},t.prototype.lShiftTo=function(t,e){for(var r=t%this.DB,i=this.DB-r,n=(1<<i)-1,s=Math.floor(t/this.DB),o=this.s<<r&this.DM,a=this.t-1;a>=0;--a)e[a+s+1]=this[a]>>i|o,o=(this[a]&n)<<r;for(a=s-1;a>=0;--a)e[a]=0;e[s]=o,e.t=this.t+s+1,e.s=this.s,e.clamp()},t.prototype.rShiftTo=function(t,e){e.s=this.s;var r=Math.floor(t/this.DB);if(r>=this.t)e.t=0;else{var i=t%this.DB,n=this.DB-i,s=(1<<i)-1;e[0]=this[r]>>i;for(var o=r+1;o<this.t;++o)e[o-r-1]|=(this[o]&s)<<n,e[o-r]=this[o]>>i;i>0&&(e[this.t-r-1]|=(this.s&s)<<n),e.t=this.t-r,e.clamp()}},t.prototype.subTo=function(t,e){for(var r=0,i=0,n=Math.min(t.t,this.t);r<n;)i+=this[r]-t[r],e[r++]=i&this.DM,i>>=this.DB;if(t.t<this.t){for(i-=t.s;r<this.t;)i+=this[r],e[r++]=i&this.DM,i>>=this.DB;i+=this.s}else{for(i+=this.s;r<t.t;)i-=t[r],e[r++]=i&this.DM,i>>=this.DB;i-=t.s}e.s=i<0?-1:0,i<-1?e[r++]=this.DV+i:i>0&&(e[r++]=i),e.t=r,e.clamp()},t.prototype.multiplyTo=function(e,r){var i=this.abs(),n=e.abs(),s=i.t;for(r.t=s+n.t;--s>=0;)r[s]=0;for(s=0;s<n.t;++s)r[s+i.t]=i.am(0,n[s],r,s,0,i.t);r.s=0,r.clamp(),this.s!=e.s&&t.ZERO.subTo(r,r)},t.prototype.squareTo=function(t){for(var e=this.abs(),r=t.t=2*e.t;--r>=0;)t[r]=0;for(r=0;r<e.t-1;++r){var i=e.am(r,e[r],t,2*r,0,1);(t[r+e.t]+=e.am(r+1,2*e[r],t,2*r+1,i,e.t-r-1))>=e.DV&&(t[r+e.t]-=e.DV,t[r+e.t+1]=1)}t.t>0&&(t[t.t-1]+=e.am(r,e[r],t,2*r,0,1)),t.s=0,t.clamp()},t.prototype.divRemTo=function(e,r,i){var n=e.abs();if(!(n.t<=0)){var s=this.abs();if(s.t<n.t)return null!=r&&r.fromInt(0),void(null!=i&&this.copyTo(i));null==i&&(i=X());var o=X(),a=this.s,h=e.s,u=this.DB-ot(n[n.t-1]);u>0?(n.lShiftTo(u,o),s.lShiftTo(u,i)):(n.copyTo(o),s.copyTo(i));var c=o.t,f=o[c-1];if(0!=f){var l=f*(1<<this.F1)+(c>1?o[c-2]>>this.F2:0),p=this.FV/l,g=(1<<this.F1)/l,d=1<<this.F2,m=i.t,v=m-c,y=null==r?X():r;for(o.dlShiftTo(v,y),i.compareTo(y)>=0&&(i[i.t++]=1,i.subTo(y,i)),t.ONE.dlShiftTo(c,y),y.subTo(o,o);o.t<c;)o[o.t++]=0;for(;--v>=0;){var b=i[--m]==f?this.DM:Math.floor(i[m]*p+(i[m-1]+d)*g);if((i[m]+=o.am(0,b,i,v,0,c))<b)for(o.dlShiftTo(v,y),i.subTo(y,i);i[m]<--b;)i.subTo(y,i)}null!=r&&(i.drShiftTo(c,r),a!=h&&t.ZERO.subTo(r,r)),i.t=c,i.clamp(),u>0&&i.rShiftTo(u,i),a<0&&t.ZERO.subTo(i,i)}}},t.prototype.invDigit=function(){if(this.t<1)return 0;var t=this[0];if(!(1&t))return 0;var e=3&t;return(e=(e=(e=(e=e*(2-(15&t)*e)&15)*(2-(255&t)*e)&255)*(2-((65535&t)*e&65535))&65535)*(2-t*e%this.DV)%this.DV)>0?this.DV-e:-e},t.prototype.isEven=function(){return 0==(this.t>0?1&this[0]:this.s)},t.prototype.exp=function(e,r){if(e>4294967295||e<1)return t.ONE;var i=X(),n=X(),s=r.convert(this),o=ot(e)-1;for(s.copyTo(i);--o>=0;)if(r.sqrTo(i,n),(e&1<<o)>0)r.mulTo(n,s,i);else{var a=i;i=n,n=a}return r.revert(i)},t.prototype.chunkSize=function(t){return Math.floor(Math.LN2*this.DB/Math.log(t))},t.prototype.toRadix=function(t){if(null==t&&(t=10),0==this.signum()||t<2||t>36)return"0";var e=this.chunkSize(t),r=Math.pow(t,e),i=st(r),n=X(),s=X(),o="";for(this.divRemTo(i,n,s);n.signum()>0;)o=(r+s.intValue()).toString(t).substr(1)+o,n.divRemTo(i,n,s);return s.intValue().toString(t)+o},t.prototype.fromRadix=function(e,r){this.fromInt(0),null==r&&(r=10);for(var i=this.chunkSize(r),n=Math.pow(r,i),s=!1,o=0,a=0,h=0;h<e.length;++h){var u=nt(e,h);u<0?"-"==e.charAt(h)&&0==this.signum()&&(s=!0):(a=r*a+u,++o>=i&&(this.dMultiply(n),this.dAddOffset(a,0),o=0,a=0))}o>0&&(this.dMultiply(Math.pow(r,o)),this.dAddOffset(a,0)),s&&t.ZERO.subTo(this,this)},t.prototype.fromNumber=function(e,r,i){if("number"==typeof r)if(e<2)this.fromInt(1);else for(this.fromNumber(e,i),this.testBit(e-1)||this.bitwiseTo(t.ONE.shiftLeft(e-1),E,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(r);)this.dAddOffset(2,0),this.bitLength()>e&&this.subTo(t.ONE.shiftLeft(e-1),this);else{var n=[],s=7&e;n.length=1+(e>>3),r.nextBytes(n),s>0?n[0]&=(1<<s)-1:n[0]=0,this.fromString(n,256)}},t.prototype.bitwiseTo=function(t,e,r){var i,n,s=Math.min(t.t,this.t);for(i=0;i<s;++i)r[i]=e(this[i],t[i]);if(t.t<this.t){for(n=t.s&this.DM,i=s;i<this.t;++i)r[i]=e(this[i],n);r.t=this.t}else{for(n=this.s&this.DM,i=s;i<t.t;++i)r[i]=e(n,t[i]);r.t=t.t}r.s=e(this.s,t.s),r.clamp()},t.prototype.changeBit=function(e,r){var i=t.ONE.shiftLeft(e);return this.bitwiseTo(i,r,i),i},t.prototype.addTo=function(t,e){for(var r=0,i=0,n=Math.min(t.t,this.t);r<n;)i+=this[r]+t[r],e[r++]=i&this.DM,i>>=this.DB;if(t.t<this.t){for(i+=t.s;r<this.t;)i+=this[r],e[r++]=i&this.DM,i>>=this.DB;i+=this.s}else{for(i+=this.s;r<t.t;)i+=t[r],e[r++]=i&this.DM,i>>=this.DB;i+=t.s}e.s=i<0?-1:0,i>0?e[r++]=i:i<-1&&(e[r++]=this.DV+i),e.t=r,e.clamp()},t.prototype.dMultiply=function(t){this[this.t]=this.am(0,t-1,this,0,0,this.t),++this.t,this.clamp()},t.prototype.dAddOffset=function(t,e){if(0!=t){for(;this.t<=e;)this[this.t++]=0;for(this[e]+=t;this[e]>=this.DV;)this[e]-=this.DV,++e>=this.t&&(this[this.t++]=0),++this[e]}},t.prototype.multiplyLowerTo=function(t,e,r){var i=Math.min(this.t+t.t,e);for(r.s=0,r.t=i;i>0;)r[--i]=0;for(var n=r.t-this.t;i<n;++i)r[i+this.t]=this.am(0,t[i],r,i,0,this.t);for(n=Math.min(t.t,e);i<n;++i)this.am(0,t[i],r,i,0,e-i);r.clamp()},t.prototype.multiplyUpperTo=function(t,e,r){--e;var i=r.t=this.t+t.t-e;for(r.s=0;--i>=0;)r[i]=0;for(i=Math.max(e-this.t,0);i<t.t;++i)r[this.t+i-e]=this.am(e-i,t[i],r,0,0,this.t+i-e);r.clamp(),r.drShiftTo(1,r)},t.prototype.modInt=function(t){if(t<=0)return 0;var e=this.DV%t,r=this.s<0?t-1:0;if(this.t>0)if(0==e)r=this[0]%t;else for(var i=this.t-1;i>=0;--i)r=(e*r+this[i])%t;return r},t.prototype.millerRabin=function(e){var r=this.subtract(t.ONE),i=r.getLowestSetBit();if(i<=0)return!1;var n=r.shiftRight(i);(e=e+1>>1)>z.length&&(e=z.length);for(var s=X(),o=0;o<e;++o){s.fromInt(z[Math.floor(Math.random()*z.length)]);var a=s.modPow(n,this);if(0!=a.compareTo(t.ONE)&&0!=a.compareTo(r)){for(var h=1;h++<i&&0!=a.compareTo(r);)if(0==(a=a.modPowInt(2,this)).compareTo(t.ONE))return!1;if(0!=a.compareTo(r))return!1}}return!0},t.prototype.square=function(){var t=X();return this.squareTo(t),t},t.prototype.gcda=function(t,e){var r=this.s<0?this.negate():this.clone(),i=t.s<0?t.negate():t.clone();if(r.compareTo(i)<0){var n=r;r=i,i=n}var s=r.getLowestSetBit(),o=i.getLowestSetBit();if(o<0)e(r);else{s<o&&(o=s),o>0&&(r.rShiftTo(o,r),i.rShiftTo(o,i));var a=function(){(s=r.getLowestSetBit())>0&&r.rShiftTo(s,r),(s=i.getLowestSetBit())>0&&i.rShiftTo(s,i),r.compareTo(i)>=0?(r.subTo(i,r),r.rShiftTo(1,r)):(i.subTo(r,i),i.rShiftTo(1,i)),r.signum()>0?setTimeout(a,0):(o>0&&i.lShiftTo(o,i),setTimeout((function(){e(i)}),0))};setTimeout(a,10)}},t.prototype.fromNumberAsync=function(e,r,i,n){if("number"==typeof r)if(e<2)this.fromInt(1);else{this.fromNumber(e,i),this.testBit(e-1)||this.bitwiseTo(t.ONE.shiftLeft(e-1),E,this),this.isEven()&&this.dAddOffset(1,0);var s=this,o=function(){s.dAddOffset(2,0),s.bitLength()>e&&s.subTo(t.ONE.shiftLeft(e-1),s),s.isProbablePrime(r)?setTimeout((function(){n()}),0):setTimeout(o,0)};setTimeout(o,0)}else{var a=[],h=7&e;a.length=1+(e>>3),r.nextBytes(a),h>0?a[0]&=(1<<h)-1:a[0]=0,this.fromString(a,256)}},t}(),Y=function(){function t(){}return t.prototype.convert=function(t){return t},t.prototype.revert=function(t){return t},t.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r)},t.prototype.sqrTo=function(t,e){t.squareTo(e)},t}(),Q=function(){function t(t){this.m=t}return t.prototype.convert=function(t){return t.s<0||t.compareTo(this.m)>=0?t.mod(this.m):t},t.prototype.revert=function(t){return t},t.prototype.reduce=function(t){t.divRemTo(this.m,null,t)},t.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},t.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},t}(),$=function(){function t(t){this.m=t,this.mp=t.invDigit(),this.mpl=32767&this.mp,this.mph=this.mp>>15,this.um=(1<<t.DB-15)-1,this.mt2=2*t.t}return t.prototype.convert=function(t){var e=X();return t.abs().dlShiftTo(this.m.t,e),e.divRemTo(this.m,null,e),t.s<0&&e.compareTo(Z.ZERO)>0&&this.m.subTo(e,e),e},t.prototype.revert=function(t){var e=X();return t.copyTo(e),this.reduce(e),e},t.prototype.reduce=function(t){for(;t.t<=this.mt2;)t[t.t++]=0;for(var e=0;e<this.m.t;++e){var r=32767&t[e],i=r*this.mpl+((r*this.mph+(t[e]>>15)*this.mpl&this.um)<<15)&t.DM;for(t[r=e+this.m.t]+=this.m.am(0,i,t,e,0,this.m.t);t[r]>=t.DV;)t[r]-=t.DV,t[++r]++}t.clamp(),t.drShiftTo(this.m.t,t),t.compareTo(this.m)>=0&&t.subTo(this.m,t)},t.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},t.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},t}(),W=function(){function t(t){this.m=t,this.r2=X(),this.q3=X(),Z.ONE.dlShiftTo(2*t.t,this.r2),this.mu=this.r2.divide(t)}return t.prototype.convert=function(t){if(t.s<0||t.t>2*this.m.t)return t.mod(this.m);if(t.compareTo(this.m)<0)return t;var e=X();return t.copyTo(e),this.reduce(e),e},t.prototype.revert=function(t){return t},t.prototype.reduce=function(t){for(t.drShiftTo(this.m.t-1,this.r2),t.t>this.m.t+1&&(t.t=this.m.t+1,t.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);t.compareTo(this.r2)<0;)t.dAddOffset(1,this.m.t+1);for(t.subTo(this.r2,t);t.compareTo(this.m)>=0;)t.subTo(this.m,t)},t.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},t.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},t}();function X(){return new Z(null)}function J(t,e){return new Z(t,e)}var tt="undefined"!=typeof navigator;tt&&"Microsoft Internet Explorer"==navigator.appName?(Z.prototype.am=function(t,e,r,i,n,s){for(var o=32767&e,a=e>>15;--s>=0;){var h=32767&this[t],u=this[t++]>>15,c=a*h+u*o;n=((h=o*h+((32767&c)<<15)+r[i]+(1073741823&n))>>>30)+(c>>>15)+a*u+(n>>>30),r[i++]=1073741823&h}return n},H=30):tt&&"Netscape"!=navigator.appName?(Z.prototype.am=function(t,e,r,i,n,s){for(;--s>=0;){var o=e*this[t++]+r[i]+n;n=Math.floor(o/67108864),r[i++]=67108863&o}return n},H=26):(Z.prototype.am=function(t,e,r,i,n,s){for(var o=16383&e,a=e>>14;--s>=0;){var h=16383&this[t],u=this[t++]>>14,c=a*h+u*o;n=((h=o*h+((16383&c)<<14)+r[i]+n)>>28)+(c>>14)+a*u,r[i++]=268435455&h}return n},H=28),Z.prototype.DB=H,Z.prototype.DM=(1<<H)-1,Z.prototype.DV=1<<H;Z.prototype.FV=Math.pow(2,52),Z.prototype.F1=52-H,Z.prototype.F2=2*H-52;var et,rt,it=[];for(et="0".charCodeAt(0),rt=0;rt<=9;++rt)it[et++]=rt;for(et="a".charCodeAt(0),rt=10;rt<36;++rt)it[et++]=rt;for(et="A".charCodeAt(0),rt=10;rt<36;++rt)it[et++]=rt;function nt(t,e){var r=it[t.charCodeAt(e)];return null==r?-1:r}function st(t){var e=X();return e.fromInt(t),e}function ot(t){var e,r=1;return 0!=(e=t>>>16)&&(t=e,r+=16),0!=(e=t>>8)&&(t=e,r+=8),0!=(e=t>>4)&&(t=e,r+=4),0!=(e=t>>2)&&(t=e,r+=2),0!=(e=t>>1)&&(t=e,r+=1),r}Z.ZERO=st(0),Z.ONE=st(1);var at=function(){function t(){this.i=0,this.j=0,this.S=[]}return t.prototype.init=function(t){var e,r,i;for(e=0;e<256;++e)this.S[e]=e;for(r=0,e=0;e<256;++e)r=r+this.S[e]+t[e%t.length]&255,i=this.S[e],this.S[e]=this.S[r],this.S[r]=i;this.i=0,this.j=0},t.prototype.next=function(){var t;return this.i=this.i+1&255,this.j=this.j+this.S[this.i]&255,t=this.S[this.i],this.S[this.i]=this.S[this.j],this.S[this.j]=t,this.S[t+this.S[this.i]&255]},t}();var ht,ut,ct=null;if(null==ct){ct=[],ut=0;var ft=void 0;if("undefined"!=typeof window&&window.crypto&&window.crypto.getRandomValues){var lt=new Uint32Array(256);for(window.crypto.getRandomValues(lt),ft=0;ft<lt.length;++ft)ct[ut++]=255&lt[ft]}var pt=0,gt=function(t){if((pt=pt||0)>=256||ut>=256)window.removeEventListener?window.removeEventListener("mousemove",gt,!1):window.detachEvent&&window.detachEvent("onmousemove",gt);else try{var e=t.x+t.y;ct[ut++]=255&e,pt+=1}catch(r){}};"undefined"!=typeof window&&(window.addEventListener?window.addEventListener("mousemove",gt,!1):window.attachEvent&&window.attachEvent("onmousemove",gt))}function dt(){if(null==ht){for(ht=new at;ut<256;){var t=Math.floor(65536*Math.random());ct[ut++]=255&t}for(ht.init(ct),ut=0;ut<ct.length;++ut)ct[ut]=0;ut=0}return ht.next()}var mt=function(){function t(){}return t.prototype.nextBytes=function(t){for(var e=0;e<t.length;++e)t[e]=dt()},t}();var vt=function(){function t(){this.n=null,this.e=0,this.d=null,this.p=null,this.q=null,this.dmp1=null,this.dmq1=null,this.coeff=null}return t.prototype.doPublic=function(t){return t.modPowInt(this.e,this.n)},t.prototype.doPrivate=function(t){if(null==this.p||null==this.q)return t.modPow(this.d,this.n);for(var e=t.mod(this.p).modPow(this.dmp1,this.p),r=t.mod(this.q).modPow(this.dmq1,this.q);e.compareTo(r)<0;)e=e.add(this.p);return e.subtract(r).multiply(this.coeff).mod(this.p).multiply(this.q).add(r)},t.prototype.setPublic=function(t,e){null!=t&&null!=e&&t.length>0&&e.length>0?(this.n=J(t,16),this.e=parseInt(e,16)):console.error("Invalid RSA public key")},t.prototype.encrypt=function(t){var e=this.n.bitLength()+7>>3,r=function(t,e){if(e<t.length+11)return console.error("Message too long for RSA"),null;for(var r=[],i=t.length-1;i>=0&&e>0;){var n=t.charCodeAt(i--);n<128?r[--e]=n:n>127&&n<2048?(r[--e]=63&n|128,r[--e]=n>>6|192):(r[--e]=63&n|128,r[--e]=n>>6&63|128,r[--e]=n>>12|224)}r[--e]=0;for(var s=new mt,o=[];e>2;){for(o[0]=0;0==o[0];)s.nextBytes(o);r[--e]=o[0]}return r[--e]=2,r[--e]=0,new Z(r)}(t,e);if(null==r)return null;var i=this.doPublic(r);if(null==i)return null;for(var n=i.toString(16),s=n.length,o=0;o<2*e-s;o++)n="0"+n;return n},t.prototype.setPrivate=function(t,e,r){null!=t&&null!=e&&t.length>0&&e.length>0?(this.n=J(t,16),this.e=parseInt(e,16),this.d=J(r,16)):console.error("Invalid RSA private key")},t.prototype.setPrivateEx=function(t,e,r,i,n,s,o,a){null!=t&&null!=e&&t.length>0&&e.length>0?(this.n=J(t,16),this.e=parseInt(e,16),this.d=J(r,16),this.p=J(i,16),this.q=J(n,16),this.dmp1=J(s,16),this.dmq1=J(o,16),this.coeff=J(a,16)):console.error("Invalid RSA private key")},t.prototype.generate=function(t,e){var r=new mt,i=t>>1;this.e=parseInt(e,16);for(var n=new Z(e,16);;){for(;this.p=new Z(t-i,1,r),0!=this.p.subtract(Z.ONE).gcd(n).compareTo(Z.ONE)||!this.p.isProbablePrime(10););for(;this.q=new Z(i,1,r),0!=this.q.subtract(Z.ONE).gcd(n).compareTo(Z.ONE)||!this.q.isProbablePrime(10););if(this.p.compareTo(this.q)<=0){var s=this.p;this.p=this.q,this.q=s}var o=this.p.subtract(Z.ONE),a=this.q.subtract(Z.ONE),h=o.multiply(a);if(0==h.gcd(n).compareTo(Z.ONE)){this.n=this.p.multiply(this.q),this.d=n.modInverse(h),this.dmp1=this.d.mod(o),this.dmq1=this.d.mod(a),this.coeff=this.q.modInverse(this.p);break}}},t.prototype.decrypt=function(t){var e=J(t,16),r=this.doPrivate(e);return null==r?null:function(t,e){var r=t.toByteArray(),i=0;for(;i<r.length&&0==r[i];)++i;if(r.length-i!=e-1||2!=r[i])return null;++i;for(;0!=r[i];)if(++i>=r.length)return null;var n="";for(;++i<r.length;){var s=255&r[i];s<128?n+=String.fromCharCode(s):s>191&&s<224?(n+=String.fromCharCode((31&s)<<6|63&r[i+1]),++i):(n+=String.fromCharCode((15&s)<<12|(63&r[i+1])<<6|63&r[i+2]),i+=2)}return n}(r,this.n.bitLength()+7>>3)},t.prototype.generateAsync=function(t,e,r){var i=new mt,n=t>>1;this.e=parseInt(e,16);var s=new Z(e,16),o=this,a=function(){var e=function(){if(o.p.compareTo(o.q)<=0){var t=o.p;o.p=o.q,o.q=t}var e=o.p.subtract(Z.ONE),i=o.q.subtract(Z.ONE),n=e.multiply(i);0==n.gcd(s).compareTo(Z.ONE)?(o.n=o.p.multiply(o.q),o.d=s.modInverse(n),o.dmp1=o.d.mod(e),o.dmq1=o.d.mod(i),o.coeff=o.q.modInverse(o.p),setTimeout((function(){r()}),0)):setTimeout(a,0)},h=function(){o.q=X(),o.q.fromNumberAsync(n,1,i,(function(){o.q.subtract(Z.ONE).gcda(s,(function(t){0==t.compareTo(Z.ONE)&&o.q.isProbablePrime(10)?setTimeout(e,0):setTimeout(h,0)}))}))},u=function(){o.p=X(),o.p.fromNumberAsync(t-n,1,i,(function(){o.p.subtract(Z.ONE).gcda(s,(function(t){0==t.compareTo(Z.ONE)&&o.p.isProbablePrime(10)?setTimeout(h,0):setTimeout(u,0)}))}))};setTimeout(u,0)};setTimeout(a,0)},t.prototype.sign=function(t,e,r){var i=function(t,e){if(e<t.length+22)return console.error("Message too long for RSA"),null;for(var r=e-t.length-6,i="",n=0;n<r;n+=2)i+="ff";return J("0001"+i+"00"+t,16)}((yt[r]||"")+e(t).toString(),this.n.bitLength()/4);if(null==i)return null;var n=this.doPrivate(i);if(null==n)return null;var s=n.toString(16);return 1&s.length?"0"+s:s},t.prototype.verify=function(t,e,r){var i=J(e,16),n=this.doPublic(i);return null==n?null:function(t){for(var e in yt)if(yt.hasOwnProperty(e)){var r=yt[e],i=r.length;if(t.substr(0,i)==r)return t.substr(i)}return t}
/*!
            Copyright (c) 2011, Yahoo! Inc. All rights reserved.
            Code licensed under the BSD License:
            http://developer.yahoo.com/yui/license.html
            version: 2.9.0
            */(n.toString(16).replace(/^1f+00/,""))==r(t).toString()},t}();var yt={md2:"3020300c06082a864886f70d020205000410",md5:"3020300c06082a864886f70d020505000410",sha1:"3021300906052b0e03021a05000414",sha224:"302d300d06096086480165030402040500041c",sha256:"3031300d060960864801650304020105000420",sha384:"3041300d060960864801650304020205000430",sha512:"3051300d060960864801650304020305000440",ripemd160:"3021300906052b2403020105000414"};var bt={};bt.lang={extend:function(t,e,r){if(!e||!t)throw new Error("YAHOO.lang.extend failed, please check that all dependencies are included.");var i=function(){};if(i.prototype=e.prototype,t.prototype=new i,t.prototype.constructor=t,t.superclass=e.prototype,e.prototype.constructor==Object.prototype.constructor&&(e.prototype.constructor=e),r){var n;for(n in r)t.prototype[n]=r[n];var s=function(){},o=["toString","valueOf"];try{/MSIE/.test(navigator.userAgent)&&(s=function(t,e){for(n=0;n<o.length;n+=1){var r=o[n],i=e[r];"function"==typeof i&&i!=Object.prototype[r]&&(t[r]=i)}})}catch(a){}s(t.prototype,r)}}};
/**
             * @fileOverview
             * @name asn1-1.0.js
             * <AUTHOR>
             * @version asn1 1.0.13 (2017-Jun-02)
             * @since jsrsasign 2.1
             * @license <a href="https://kjur.github.io/jsrsasign/license/">MIT License</a>
             */
var Tt={};void 0!==Tt.asn1&&Tt.asn1||(Tt.asn1={}),Tt.asn1.ASN1Util=new function(){this.integerToByteHex=function(t){var e=t.toString(16);return e.length%2==1&&(e="0"+e),e},this.bigIntToMinTwosComplementsHex=function(t){var e=t.toString(16);if("-"!=e.substr(0,1))e.length%2==1?e="0"+e:e.match(/^[0-7]/)||(e="00"+e);else{var r=e.substr(1).length;r%2==1?r+=1:e.match(/^[0-7]/)||(r+=2);for(var i="",n=0;n<r;n++)i+="f";e=new Z(i,16).xor(t).add(Z.ONE).toString(16).replace(/^-/,"")}return e},this.getPEMStringFromHex=function(t,e){return hextopem(t,e)},this.newObject=function(t){var e=Tt.asn1,r=e.DERBoolean,i=e.DERInteger,n=e.DERBitString,s=e.DEROctetString,o=e.DERNull,a=e.DERObjectIdentifier,h=e.DEREnumerated,u=e.DERUTF8String,c=e.DERNumericString,f=e.DERPrintableString,l=e.DERTeletexString,p=e.DERIA5String,g=e.DERUTCTime,d=e.DERGeneralizedTime,m=e.DERSequence,v=e.DERSet,y=e.DERTaggedObject,b=e.ASN1Util.newObject,T=Object.keys(t);if(1!=T.length)throw"key of param shall be only one.";var S=T[0];if(-1==":bool:int:bitstr:octstr:null:oid:enum:utf8str:numstr:prnstr:telstr:ia5str:utctime:gentime:seq:set:tag:".indexOf(":"+S+":"))throw"undefined key: "+S;if("bool"==S)return new r(t[S]);if("int"==S)return new i(t[S]);if("bitstr"==S)return new n(t[S]);if("octstr"==S)return new s(t[S]);if("null"==S)return new o(t[S]);if("oid"==S)return new a(t[S]);if("enum"==S)return new h(t[S]);if("utf8str"==S)return new u(t[S]);if("numstr"==S)return new c(t[S]);if("prnstr"==S)return new f(t[S]);if("telstr"==S)return new l(t[S]);if("ia5str"==S)return new p(t[S]);if("utctime"==S)return new g(t[S]);if("gentime"==S)return new d(t[S]);if("seq"==S){for(var w=t[S],x=[],E=0;E<w.length;E++){var D=b(w[E]);x.push(D)}return new m({array:x})}if("set"==S){for(w=t[S],x=[],E=0;E<w.length;E++){D=b(w[E]);x.push(D)}return new v({array:x})}if("tag"==S){var B=t[S];if("[object Array]"===Object.prototype.toString.call(B)&&3==B.length){var O=b(B[2]);return new y({tag:B[0],explicit:B[1],obj:O})}var R={};if(void 0!==B.explicit&&(R.explicit=B.explicit),void 0!==B.tag&&(R.tag=B.tag),void 0===B.obj)throw"obj shall be specified for 'tag'.";return R.obj=b(B.obj),new y(R)}},this.jsonToASN1HEX=function(t){return this.newObject(t).getEncodedHex()}},Tt.asn1.ASN1Util.oidHexToInt=function(t){for(var e="",r=parseInt(t.substr(0,2),16),i=(e=Math.floor(r/40)+"."+r%40,""),n=2;n<t.length;n+=2){var s=("00000000"+parseInt(t.substr(n,2),16).toString(2)).slice(-8);if(i+=s.substr(1,7),"0"==s.substr(0,1))e=e+"."+new Z(i,2).toString(10),i=""}return e},Tt.asn1.ASN1Util.oidIntToHex=function(t){var e=function(t){var e=t.toString(16);return 1==e.length&&(e="0"+e),e},r=function(t){var r="",i=new Z(t,10).toString(2),n=7-i.length%7;7==n&&(n=0);for(var s="",o=0;o<n;o++)s+="0";i=s+i;for(o=0;o<i.length-1;o+=7){var a=i.substr(o,7);o!=i.length-7&&(a="1"+a),r+=e(parseInt(a,2))}return r};if(!t.match(/^[0-9.]+$/))throw"malformed oid string: "+t;var i="",n=t.split("."),s=40*parseInt(n[0])+parseInt(n[1]);i+=e(s),n.splice(0,2);for(var o=0;o<n.length;o++)i+=r(n[o]);return i},Tt.asn1.ASN1Object=function(){this.getLengthHexFromValue=function(){if(void 0===this.hV||null==this.hV)throw"this.hV is null or undefined.";if(this.hV.length%2==1)throw"value hex must be even length: n=0,v="+this.hV;var t=this.hV.length/2,e=t.toString(16);if(e.length%2==1&&(e="0"+e),t<128)return e;var r=e.length/2;if(r>15)throw"ASN.1 length too long to represent by 8x: n = "+t.toString(16);return(128+r).toString(16)+e},this.getEncodedHex=function(){return(null==this.hTLV||this.isModified)&&(this.hV=this.getFreshValueHex(),this.hL=this.getLengthHexFromValue(),this.hTLV=this.hT+this.hL+this.hV,this.isModified=!1),this.hTLV},this.getValueHex=function(){return this.getEncodedHex(),this.hV},this.getFreshValueHex=function(){return""}},Tt.asn1.DERAbstractString=function(t){Tt.asn1.DERAbstractString.superclass.constructor.call(this),this.getString=function(){return this.s},this.setString=function(t){this.hTLV=null,this.isModified=!0,this.s=t,this.hV=stohex(this.s)},this.setStringHex=function(t){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=t},this.getFreshValueHex=function(){return this.hV},void 0!==t&&("string"==typeof t?this.setString(t):void 0!==t.str?this.setString(t.str):void 0!==t.hex&&this.setStringHex(t.hex))},bt.lang.extend(Tt.asn1.DERAbstractString,Tt.asn1.ASN1Object),Tt.asn1.DERAbstractTime=function(t){Tt.asn1.DERAbstractTime.superclass.constructor.call(this),this.localDateToUTC=function(t){return utc=t.getTime()+6e4*t.getTimezoneOffset(),new Date(utc)},this.formatDate=function(t,e,r){var i=this.zeroPadding,n=this.localDateToUTC(t),s=String(n.getFullYear());"utc"==e&&(s=s.substr(2,2));var o=s+i(String(n.getMonth()+1),2)+i(String(n.getDate()),2)+i(String(n.getHours()),2)+i(String(n.getMinutes()),2)+i(String(n.getSeconds()),2);if(!0===r){var a=n.getMilliseconds();if(0!=a){var h=i(String(a),3);o=o+"."+(h=h.replace(/[0]+$/,""))}}return o+"Z"},this.zeroPadding=function(t,e){return t.length>=e?t:new Array(e-t.length+1).join("0")+t},this.getString=function(){return this.s},this.setString=function(t){this.hTLV=null,this.isModified=!0,this.s=t,this.hV=stohex(t)},this.setByDateValue=function(t,e,r,i,n,s){var o=new Date(Date.UTC(t,e-1,r,i,n,s,0));this.setByDate(o)},this.getFreshValueHex=function(){return this.hV}},bt.lang.extend(Tt.asn1.DERAbstractTime,Tt.asn1.ASN1Object),Tt.asn1.DERAbstractStructured=function(t){Tt.asn1.DERAbstractString.superclass.constructor.call(this),this.setByASN1ObjectArray=function(t){this.hTLV=null,this.isModified=!0,this.asn1Array=t},this.appendASN1Object=function(t){this.hTLV=null,this.isModified=!0,this.asn1Array.push(t)},this.asn1Array=new Array,void 0!==t&&void 0!==t.array&&(this.asn1Array=t.array)},bt.lang.extend(Tt.asn1.DERAbstractStructured,Tt.asn1.ASN1Object),Tt.asn1.DERBoolean=function(){Tt.asn1.DERBoolean.superclass.constructor.call(this),this.hT="01",this.hTLV="0101ff"},bt.lang.extend(Tt.asn1.DERBoolean,Tt.asn1.ASN1Object),Tt.asn1.DERInteger=function(t){Tt.asn1.DERInteger.superclass.constructor.call(this),this.hT="02",this.setByBigInteger=function(t){this.hTLV=null,this.isModified=!0,this.hV=Tt.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t)},this.setByInteger=function(t){var e=new Z(String(t),10);this.setByBigInteger(e)},this.setValueHex=function(t){this.hV=t},this.getFreshValueHex=function(){return this.hV},void 0!==t&&(void 0!==t.bigint?this.setByBigInteger(t.bigint):void 0!==t.int?this.setByInteger(t.int):"number"==typeof t?this.setByInteger(t):void 0!==t.hex&&this.setValueHex(t.hex))},bt.lang.extend(Tt.asn1.DERInteger,Tt.asn1.ASN1Object),Tt.asn1.DERBitString=function(t){if(void 0!==t&&void 0!==t.obj){var e=Tt.asn1.ASN1Util.newObject(t.obj);t.hex="00"+e.getEncodedHex()}Tt.asn1.DERBitString.superclass.constructor.call(this),this.hT="03",this.setHexValueIncludingUnusedBits=function(t){this.hTLV=null,this.isModified=!0,this.hV=t},this.setUnusedBitsAndHexValue=function(t,e){if(t<0||7<t)throw"unused bits shall be from 0 to 7: u = "+t;var r="0"+t;this.hTLV=null,this.isModified=!0,this.hV=r+e},this.setByBinaryString=function(t){var e=8-(t=t.replace(/0+$/,"")).length%8;8==e&&(e=0);for(var r=0;r<=e;r++)t+="0";var i="";for(r=0;r<t.length-1;r+=8){var n=t.substr(r,8),s=parseInt(n,2).toString(16);1==s.length&&(s="0"+s),i+=s}this.hTLV=null,this.isModified=!0,this.hV="0"+e+i},this.setByBooleanArray=function(t){for(var e="",r=0;r<t.length;r++)1==t[r]?e+="1":e+="0";this.setByBinaryString(e)},this.newFalseArray=function(t){for(var e=new Array(t),r=0;r<t;r++)e[r]=!1;return e},this.getFreshValueHex=function(){return this.hV},void 0!==t&&("string"==typeof t&&t.toLowerCase().match(/^[0-9a-f]+$/)?this.setHexValueIncludingUnusedBits(t):void 0!==t.hex?this.setHexValueIncludingUnusedBits(t.hex):void 0!==t.bin?this.setByBinaryString(t.bin):void 0!==t.array&&this.setByBooleanArray(t.array))},bt.lang.extend(Tt.asn1.DERBitString,Tt.asn1.ASN1Object),Tt.asn1.DEROctetString=function(t){if(void 0!==t&&void 0!==t.obj){var e=Tt.asn1.ASN1Util.newObject(t.obj);t.hex=e.getEncodedHex()}Tt.asn1.DEROctetString.superclass.constructor.call(this,t),this.hT="04"},bt.lang.extend(Tt.asn1.DEROctetString,Tt.asn1.DERAbstractString),Tt.asn1.DERNull=function(){Tt.asn1.DERNull.superclass.constructor.call(this),this.hT="05",this.hTLV="0500"},bt.lang.extend(Tt.asn1.DERNull,Tt.asn1.ASN1Object),Tt.asn1.DERObjectIdentifier=function(t){var e=function(t){var e=t.toString(16);return 1==e.length&&(e="0"+e),e},r=function(t){var r="",i=new Z(t,10).toString(2),n=7-i.length%7;7==n&&(n=0);for(var s="",o=0;o<n;o++)s+="0";i=s+i;for(o=0;o<i.length-1;o+=7){var a=i.substr(o,7);o!=i.length-7&&(a="1"+a),r+=e(parseInt(a,2))}return r};Tt.asn1.DERObjectIdentifier.superclass.constructor.call(this),this.hT="06",this.setValueHex=function(t){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=t},this.setValueOidString=function(t){if(!t.match(/^[0-9.]+$/))throw"malformed oid string: "+t;var i="",n=t.split("."),s=40*parseInt(n[0])+parseInt(n[1]);i+=e(s),n.splice(0,2);for(var o=0;o<n.length;o++)i+=r(n[o]);this.hTLV=null,this.isModified=!0,this.s=null,this.hV=i},this.setValueName=function(t){var e=Tt.asn1.x509.OID.name2oid(t);if(""===e)throw"DERObjectIdentifier oidName undefined: "+t;this.setValueOidString(e)},this.getFreshValueHex=function(){return this.hV},void 0!==t&&("string"==typeof t?t.match(/^[0-2].[0-9.]+$/)?this.setValueOidString(t):this.setValueName(t):void 0!==t.oid?this.setValueOidString(t.oid):void 0!==t.hex?this.setValueHex(t.hex):void 0!==t.name&&this.setValueName(t.name))},bt.lang.extend(Tt.asn1.DERObjectIdentifier,Tt.asn1.ASN1Object),Tt.asn1.DEREnumerated=function(t){Tt.asn1.DEREnumerated.superclass.constructor.call(this),this.hT="0a",this.setByBigInteger=function(t){this.hTLV=null,this.isModified=!0,this.hV=Tt.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t)},this.setByInteger=function(t){var e=new Z(String(t),10);this.setByBigInteger(e)},this.setValueHex=function(t){this.hV=t},this.getFreshValueHex=function(){return this.hV},void 0!==t&&(void 0!==t.int?this.setByInteger(t.int):"number"==typeof t?this.setByInteger(t):void 0!==t.hex&&this.setValueHex(t.hex))},bt.lang.extend(Tt.asn1.DEREnumerated,Tt.asn1.ASN1Object),Tt.asn1.DERUTF8String=function(t){Tt.asn1.DERUTF8String.superclass.constructor.call(this,t),this.hT="0c"},bt.lang.extend(Tt.asn1.DERUTF8String,Tt.asn1.DERAbstractString),Tt.asn1.DERNumericString=function(t){Tt.asn1.DERNumericString.superclass.constructor.call(this,t),this.hT="12"},bt.lang.extend(Tt.asn1.DERNumericString,Tt.asn1.DERAbstractString),Tt.asn1.DERPrintableString=function(t){Tt.asn1.DERPrintableString.superclass.constructor.call(this,t),this.hT="13"},bt.lang.extend(Tt.asn1.DERPrintableString,Tt.asn1.DERAbstractString),Tt.asn1.DERTeletexString=function(t){Tt.asn1.DERTeletexString.superclass.constructor.call(this,t),this.hT="14"},bt.lang.extend(Tt.asn1.DERTeletexString,Tt.asn1.DERAbstractString),Tt.asn1.DERIA5String=function(t){Tt.asn1.DERIA5String.superclass.constructor.call(this,t),this.hT="16"},bt.lang.extend(Tt.asn1.DERIA5String,Tt.asn1.DERAbstractString),Tt.asn1.DERUTCTime=function(t){Tt.asn1.DERUTCTime.superclass.constructor.call(this,t),this.hT="17",this.setByDate=function(t){this.hTLV=null,this.isModified=!0,this.date=t,this.s=this.formatDate(this.date,"utc"),this.hV=stohex(this.s)},this.getFreshValueHex=function(){return void 0===this.date&&void 0===this.s&&(this.date=new Date,this.s=this.formatDate(this.date,"utc"),this.hV=stohex(this.s)),this.hV},void 0!==t&&(void 0!==t.str?this.setString(t.str):"string"==typeof t&&t.match(/^[0-9]{12}Z$/)?this.setString(t):void 0!==t.hex?this.setStringHex(t.hex):void 0!==t.date&&this.setByDate(t.date))},bt.lang.extend(Tt.asn1.DERUTCTime,Tt.asn1.DERAbstractTime),Tt.asn1.DERGeneralizedTime=function(t){Tt.asn1.DERGeneralizedTime.superclass.constructor.call(this,t),this.hT="18",this.withMillis=!1,this.setByDate=function(t){this.hTLV=null,this.isModified=!0,this.date=t,this.s=this.formatDate(this.date,"gen",this.withMillis),this.hV=stohex(this.s)},this.getFreshValueHex=function(){return void 0===this.date&&void 0===this.s&&(this.date=new Date,this.s=this.formatDate(this.date,"gen",this.withMillis),this.hV=stohex(this.s)),this.hV},void 0!==t&&(void 0!==t.str?this.setString(t.str):"string"==typeof t&&t.match(/^[0-9]{14}Z$/)?this.setString(t):void 0!==t.hex?this.setStringHex(t.hex):void 0!==t.date&&this.setByDate(t.date),!0===t.millis&&(this.withMillis=!0))},bt.lang.extend(Tt.asn1.DERGeneralizedTime,Tt.asn1.DERAbstractTime),Tt.asn1.DERSequence=function(t){Tt.asn1.DERSequence.superclass.constructor.call(this,t),this.hT="30",this.getFreshValueHex=function(){for(var t="",e=0;e<this.asn1Array.length;e++){t+=this.asn1Array[e].getEncodedHex()}return this.hV=t,this.hV}},bt.lang.extend(Tt.asn1.DERSequence,Tt.asn1.DERAbstractStructured),Tt.asn1.DERSet=function(t){Tt.asn1.DERSet.superclass.constructor.call(this,t),this.hT="31",this.sortFlag=!0,this.getFreshValueHex=function(){for(var t=new Array,e=0;e<this.asn1Array.length;e++){var r=this.asn1Array[e];t.push(r.getEncodedHex())}return 1==this.sortFlag&&t.sort(),this.hV=t.join(""),this.hV},void 0!==t&&void 0!==t.sortflag&&0==t.sortflag&&(this.sortFlag=!1)},bt.lang.extend(Tt.asn1.DERSet,Tt.asn1.DERAbstractStructured),Tt.asn1.DERTaggedObject=function(t){Tt.asn1.DERTaggedObject.superclass.constructor.call(this),this.hT="a0",this.hV="",this.isExplicit=!0,this.asn1Object=null,this.setASN1Object=function(t,e,r){this.hT=e,this.isExplicit=t,this.asn1Object=r,this.isExplicit?(this.hV=this.asn1Object.getEncodedHex(),this.hTLV=null,this.isModified=!0):(this.hV=null,this.hTLV=r.getEncodedHex(),this.hTLV=this.hTLV.replace(/^../,e),this.isModified=!1)},this.getFreshValueHex=function(){return this.hV},void 0!==t&&(void 0!==t.tag&&(this.hT=t.tag),void 0!==t.explicit&&(this.isExplicit=t.explicit),void 0!==t.obj&&(this.asn1Object=t.obj,this.setASN1Object(this.isExplicit,this.hT,this.asn1Object)))},bt.lang.extend(Tt.asn1.DERTaggedObject,Tt.asn1.ASN1Object);var St,wt,xt=globalThis&&globalThis.__extends||(St=function(t,e){return St=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},St(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}St(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),Et=function(t){function e(r){var i=t.call(this)||this;return r&&("string"==typeof r?i.parseKey(r):(e.hasPrivateKeyProperty(r)||e.hasPublicKeyProperty(r))&&i.parsePropertiesFrom(r)),i}return xt(e,t),e.prototype.parseKey=function(t){try{var e=0,r=0,i=/^\s*(?:[0-9A-Fa-f][0-9A-Fa-f]\s*)+$/.test(t)?j(t):M.unarmor(t),n=U.decode(i);if(3===n.sub.length&&(n=n.sub[2].sub[0]),9===n.sub.length){e=n.sub[1].getHexStringValue(),this.n=J(e,16),r=n.sub[2].getHexStringValue(),this.e=parseInt(r,16);var s=n.sub[3].getHexStringValue();this.d=J(s,16);var o=n.sub[4].getHexStringValue();this.p=J(o,16);var a=n.sub[5].getHexStringValue();this.q=J(a,16);var h=n.sub[6].getHexStringValue();this.dmp1=J(h,16);var u=n.sub[7].getHexStringValue();this.dmq1=J(u,16);var c=n.sub[8].getHexStringValue();this.coeff=J(c,16)}else{if(2!==n.sub.length)return!1;if(n.sub[0].sub){var f=n.sub[1].sub[0];e=f.sub[0].getHexStringValue(),this.n=J(e,16),r=f.sub[1].getHexStringValue(),this.e=parseInt(r,16)}else e=n.sub[0].getHexStringValue(),this.n=J(e,16),r=n.sub[1].getHexStringValue(),this.e=parseInt(r,16)}return!0}catch(l){return!1}},e.prototype.getPrivateBaseKey=function(){var t={array:[new Tt.asn1.DERInteger({int:0}),new Tt.asn1.DERInteger({bigint:this.n}),new Tt.asn1.DERInteger({int:this.e}),new Tt.asn1.DERInteger({bigint:this.d}),new Tt.asn1.DERInteger({bigint:this.p}),new Tt.asn1.DERInteger({bigint:this.q}),new Tt.asn1.DERInteger({bigint:this.dmp1}),new Tt.asn1.DERInteger({bigint:this.dmq1}),new Tt.asn1.DERInteger({bigint:this.coeff})]};return new Tt.asn1.DERSequence(t).getEncodedHex()},e.prototype.getPrivateBaseKeyB64=function(){return I(this.getPrivateBaseKey())},e.prototype.getPublicBaseKey=function(){var t=new Tt.asn1.DERSequence({array:[new Tt.asn1.DERObjectIdentifier({oid:"1.2.840.113549.1.1.1"}),new Tt.asn1.DERNull]}),e=new Tt.asn1.DERSequence({array:[new Tt.asn1.DERInteger({bigint:this.n}),new Tt.asn1.DERInteger({int:this.e})]}),r=new Tt.asn1.DERBitString({hex:"00"+e.getEncodedHex()});return new Tt.asn1.DERSequence({array:[t,r]}).getEncodedHex()},e.prototype.getPublicBaseKeyB64=function(){return I(this.getPublicBaseKey())},e.wordwrap=function(t,e){if(!t)return t;var r="(.{1,"+(e=e||64)+"})( +|$\n?)|(.{1,"+e+"})";return t.match(RegExp(r,"g")).join("\n")},e.prototype.getPrivateKey=function(){var t="-----BEGIN RSA PRIVATE KEY-----\n";return t+=e.wordwrap(this.getPrivateBaseKeyB64())+"\n",t+="-----END RSA PRIVATE KEY-----"},e.prototype.getPublicKey=function(){var t="-----BEGIN PUBLIC KEY-----\n";return t+=e.wordwrap(this.getPublicBaseKeyB64())+"\n",t+="-----END PUBLIC KEY-----"},e.hasPublicKeyProperty=function(t){return(t=t||{}).hasOwnProperty("n")&&t.hasOwnProperty("e")},e.hasPrivateKeyProperty=function(t){return(t=t||{}).hasOwnProperty("n")&&t.hasOwnProperty("e")&&t.hasOwnProperty("d")&&t.hasOwnProperty("p")&&t.hasOwnProperty("q")&&t.hasOwnProperty("dmp1")&&t.hasOwnProperty("dmq1")&&t.hasOwnProperty("coeff")},e.prototype.parsePropertiesFrom=function(t){this.n=t.n,this.e=t.e,t.hasOwnProperty("d")&&(this.d=t.d,this.p=t.p,this.q=t.q,this.dmp1=t.dmp1,this.dmq1=t.dmq1,this.coeff=t.coeff)},e}(vt),Dt="undefined"!=typeof process?null===(wt={})||void 0===wt?void 0:wt.npm_package_version:void 0,Bt=function(){function t(t){void 0===t&&(t={}),t=t||{},this.default_key_size=t.default_key_size?parseInt(t.default_key_size,10):1024,this.default_public_exponent=t.default_public_exponent||"010001",this.log=t.log||!1,this.key=null}return t.prototype.setKey=function(t){this.log&&this.key&&console.warn("A key was already set, overriding existing."),this.key=new Et(t)},t.prototype.setPrivateKey=function(t){this.setKey(t)},t.prototype.setPublicKey=function(t){this.setKey(t)},t.prototype.decrypt=function(t){try{return this.getKey().decrypt(N(t))}catch(e){return!1}},t.prototype.encrypt=function(t){try{return I(this.getKey().encrypt(t))}catch(e){return!1}},t.prototype.sign=function(t,e,r){try{return I(this.getKey().sign(t,e,r))}catch(i){return!1}},t.prototype.verify=function(t,e,r){try{return this.getKey().verify(t,N(e),r)}catch(i){return!1}},t.prototype.getKey=function(t){if(!this.key){if(this.key=new Et,t&&"[object Function]"==={}.toString.call(t))return void this.key.generateAsync(this.default_key_size,this.default_public_exponent,t);this.key.generate(this.default_key_size,this.default_public_exponent)}return this.key},t.prototype.getPrivateKey=function(){return this.getKey().getPrivateKey()},t.prototype.getPrivateKeyB64=function(){return this.getKey().getPrivateBaseKeyB64()},t.prototype.getPublicKey=function(){return this.getKey().getPublicKey()},t.prototype.getPublicKeyB64=function(){return this.getKey().getPublicBaseKeyB64()},t.version=Dt,t}(),Ot={class:"input-wrapper"},Rt={class:"input-wrapper"},At=Object.assign({name:"LocalLogin"},{props:{authId:{type:String,default:function(){return""}},authInfo:{type:Object,default:function(){return[]}}},setup:function(e){var r=w().handleSecondaryAuthResponse,n=e,x=s(null),E=s(!1),D=o({user_name:"",password:"",idp_id:n.authId,redirect_uri:"hello world",grant_type:"implicit",client_id:"client_portal"}),B=o({user_name:[{required:!0,trigger:"change",message:"用户名不能为空"}],password:[{required:!0,trigger:"change",message:"密码不能为空"}]}),O=function(){E.value=!E.value},R=a(),A=function(){var e=i(t().m((function e(){var i,s,o,a,h,u,c,f;return t().w((function(t){for(;;)switch(t.n){case 0:if(logger.log({idp_id:n.authId}),i=n.authId,"default-local"!==n.authId){t.n=9;break}return s=b.service({fullscreen:!0,text:""}),logger.log("检测到默认本地认证源，尝试获取真实的认证源ID"),t.p=1,t.n=2,S();case 2:if(!(200===(o=t.v).status&&o.data&&o.data.idpList.length>0)){t.n=5;break}if(!(a=o.data.idpList.find((function(t){return"local"===t.type})))){t.n=3;break}i=a.id,logger.log("获取到真实的本地认证源ID:",i),t.n=4;break;case 3:throw new Error("服务器配置错误，未找到本地认证源！");case 4:t.n=6;break;case 5:throw new Error("服务器不通，请检查网络！");case 6:t.n=8;break;case 7:return t.p=7,f=t.v,logger.log("获取认证源列表失败:",f),y.error(f.message),t.a(2);case 8:return t.p=8,null==s||s.close(),t.f(8);case 9:return D.idp_id=i,(h=new Bt).setPublicKey("-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA52nU2J3CmT/UsKy2oKYp\ng7GyY/wn6T/cymNFrHFGjwpdzYQ0W+wZS75JNPOVvUPYu5zLFsr3FnfddXrBpxo7\nctNYaPAO9maCqo8WfmE5lA04av4trueA0Qd31OVVeBOfxvSkZxMevOneioxFqVh5\nyO9meOc01oKzpQ6m8qLYh3Ru4/GUus9XABkV1ue7Ll1Owxj4h0ovXTZN2rVpyrNU\nvr+OZeaKA+aMqv2t4woehMuj9hDU9t79mjmVCEJVTPjf051cBFpQawAPUzmMIDWU\nEz3OalPwD03+pHubn80+x+FN94wNK2VV5KtXxwx2g7ZfHGWfY3AwPaJ/uh7cDg/z\nWQIDAQAB\n-----END PUBLIC KEY-----"),(u=T.cloneDeep(D)).password=h.encrypt(D.password),u.user_name=h.encrypt(D.user_name),u.encryption="rsa","msad"!==n.authInfo.authType&&"ldap"!==n.authInfo.authType||(u.ad_pwd=u.password,u.ad_username=u.user_name,delete u.password,delete u.user_name),t.n=10,R.LoginIn(u,n.authInfo.authType,i);case 10:return c=t.v,t.n=11,r(c);case 11:if(!t.v){t.n=12;break}return logger.log("用户名密码登录成功，进入双因子验证"),t.a(2);case 12:return t.a(2)}}),e,null,[[1,7,8,9]])})));return function(){return e.apply(this,arguments)}}(),V=function(){x.value.validate(function(){var e=i(t().m((function e(r){return t().w((function(t){for(;;)switch(t.n){case 0:if(!r){t.n=2;break}return t.n=1,A();case 1:t.n=3;break;case 2:return y({type:"error",message:"用户名密码不能为空",showClose:!0}),t.a(2,!1);case 3:return t.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}())};return function(t,e){var r=h("base-input"),i=h("base-form-item"),n=h("base-button"),s=h("base-form");return u(),c(s,{ref_key:"loginForm",ref:x,model:D,rules:B,"validate-on-rule-change":!1,class:"login-form",onKeyup:v(V,["enter"])},{default:f((function(){return[l(i,{class:"form-item",prop:"user_name"},{default:f((function(){return[p("div",Ot,[e[2]||(e[2]=p("svg",{class:"input-icon-left","aria-hidden":"true"},[p("use",{"xlink:href":"#icon-username"})],-1)),l(r,{id:"ui-fake-username",modelValue:D.user_name,"onUpdate:modelValue":e[0]||(e[0]=function(t){return D.user_name=t}),autocapitalize:"off",autocomplete:"off",autocorrect:"off",class:"login-input",name:"fake-username",placeholder:"请输入用户名",spellcheck:"false"},null,8,["modelValue"])])]})),_:1}),l(i,{class:"form-item",prop:"password"},{default:f((function(){return[p("div",Rt,[e[5]||(e[5]=p("svg",{class:"input-icon-left","aria-hidden":"true"},[p("use",{"xlink:href":"#icon-password"})],-1)),l(r,{id:"ui-fake-password",modelValue:D.password,"onUpdate:modelValue":e[1]||(e[1]=function(t){return D.password=t}),type:E.value?"text":"password",autocapitalize:"off",autocomplete:"new-password",autocorrect:"off",class:"login-input password-input",name:"fake-password",placeholder:"请输入密码",spellcheck:"false"},null,8,["modelValue","type"]),E.value?d("",!0):(u(),g("svg",{key:0,class:"input-icon-right password-toggle","aria-hidden":"true",onClick:O},e[3]||(e[3]=[p("use",{"xlink:href":"#icon-password-show"},null,-1)]))),E.value?(u(),g("svg",{key:1,class:"input-icon-right password-toggle","aria-hidden":"true",onClick:O},e[4]||(e[4]=[p("use",{"xlink:href":"#icon-password-hidden"},null,-1)]))):d("",!0)])]})),_:1}),l(i,{class:"form-item"},{default:f((function(){return[l(n,{id:"ui-fake-submit-login",class:"login-submit-button",type:"primary",onClick:V},{default:f((function(){return e[6]||(e[6]=[m(" 安全登录 ")])})),_:1,__:[6]})]})),_:1})]})),_:1},8,["model","rules"])}}});e("default",n(At,[["__scopeId","data-v-50674125"]]))}}}))}();
