/*! 
 Build based on gin-vue-admin 
 Time : 1754879545000 */
import{u as e,E as r,f as a,r as s,o as t,x as i,a as c,b as n,L as o,M as u}from"./index.ab3e73c8.js";const l=Object.assign({name:"WxOAuthCallback"},{setup(l){const y=e(),d=r(),p=a(),{code:b,state:f,redirect_url:h}=y.query,m=s(Array.isArray(f)?f[0]:f),x=s("");return t((async()=>{const e=o.service({fullscreen:!0,text:"登录中，请稍候..."});try{const e={clientId:"client_portal",grantType:"implicit",redirect_uri:"",idpId:m.value,authWeb:{authWebCode:Array.isArray(b)?b[0]:b}};!0===await p.LoginIn(e,"qiyewx_oauth",m.value)?await d.push({name:"verify",query:{redirect_url:h}}):u.error("登录失败，请重试")}catch(r){console.error("登录过程出错:",r),u.error("登录过程出错，请重试")}finally{e.close()}})),i("userName",x),(e,r)=>(c(),n("span"))}});export{l as default};
