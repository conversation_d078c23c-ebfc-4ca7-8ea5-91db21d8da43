/*! 
 Build based on gin-vue-admin 
 Time : 1754879545000 */
!function(){function e(r){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(r)}function r(r){if(null!=r){var n=r["function"==typeof Symbol&&Symbol.iterator||"@@iterator"],t=0;if(n)return n.call(r);if("function"==typeof r.next)return r;if(!isNaN(r.length))return{next:function(){return r&&t>=r.length&&(r=void 0),{value:r&&r[t++],done:!r}}}}throw new TypeError(e(r)+" is not iterable")}function n(e,r){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);r&&(t=t.filter((function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable}))),n.push.apply(n,t)}return n}function t(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?n(Object(t),!0).forEach((function(r){o(e,r,t[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):n(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}))}return e}function o(r,n,t){return(n=function(r){var n=function(r,n){if("object"!=e(r)||!r)return r;var t=r[Symbol.toPrimitive];if(void 0!==t){var o=t.call(r,n||"default");if("object"!=e(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(r)}(r,"string");return"symbol"==e(n)?n:n+""}(n))in r?Object.defineProperty(r,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):r[n]=t,r}function a(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,r,n="function"==typeof Symbol?Symbol:{},t=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function c(n,t,o,a){var c=t&&t.prototype instanceof l?t:l,u=Object.create(c.prototype);return i(u,"_invoke",function(n,t,o){var a,i,c,l=0,u=o||[],f=!1,p={p:0,n:0,v:e,a:d,f:d.bind(e,4),d:function(r,n){return a=r,i=0,c=e,p.n=n,s}};function d(n,t){for(i=n,c=t,r=0;!f&&l&&!o&&r<u.length;r++){var o,a=u[r],d=p.p,g=a[2];n>3?(o=g===t)&&(c=a[(i=a[4])?5:(i=3,3)],a[4]=a[5]=e):a[0]<=d&&((o=n<2&&d<a[1])?(i=0,p.v=t,p.n=a[1]):d<g&&(o=n<3||a[0]>t||t>g)&&(a[4]=n,a[5]=t,p.n=g,i=0))}if(o||n>1)return s;throw f=!0,t}return function(o,u,g){if(l>1)throw TypeError("Generator is already running");for(f&&1===u&&d(u,g),i=u,c=g;(r=i<2?e:c)||!f;){a||(i?i<3?(i>1&&(p.n=-1),d(i,c)):p.n=c:p.v=c);try{if(l=2,a){if(i||(o="next"),r=a[o]){if(!(r=r.call(a,c)))throw TypeError("iterator result is not an object");if(!r.done)return r;c=r.value,i<2&&(i=0)}else 1===i&&(r=a.return)&&r.call(a),i<2&&(c=TypeError("The iterator does not provide a '"+o+"' method"),i=1);a=e}else if((r=(f=p.n<0)?c:n.call(t,p))!==s)break}catch(r){a=e,i=1,c=r}finally{l=1}}return{value:r,done:f}}}(n,o,a),!0),u}var s={};function l(){}function u(){}function f(){}r=Object.getPrototypeOf;var p=[][t]?r(r([][t]())):(i(r={},t,(function(){return this})),r),d=f.prototype=l.prototype=Object.create(p);function g(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,i(e,o,"GeneratorFunction")),e.prototype=Object.create(d),e}return u.prototype=f,i(d,"constructor",f),i(f,"constructor",u),u.displayName="GeneratorFunction",i(f,o,"GeneratorFunction"),i(d),i(d,o,"Generator"),i(d,t,(function(){return this})),i(d,"toString",(function(){return"[object Generator]"})),(a=function(){return{w:c,m:g}})()}function i(e,r,n,t){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}i=function(e,r,n,t){if(r)o?o(e,r,{value:n,enumerable:!t,configurable:!t,writable:!t}):e[r]=n;else{var a=function(r,n){i(e,r,(function(e){return this._invoke(r,n,e)}))};a("next",0),a("throw",1),a("return",2)}},i(e,r,n,t)}function c(e,r,n,t,o,a,i){try{var c=e[a](i),s=c.value}catch(e){return void n(e)}c.done?r(s):Promise.resolve(s).then(t,o)}function s(e){return function(){var r=this,n=arguments;return new Promise((function(t,o){var a=e.apply(r,n);function i(e){c(a,t,o,i,s,"next",e)}function s(e){c(a,t,o,i,s,"throw",e)}i(void 0)}))}}System.register(["./index-legacy.39a66d41.js"],(function(e,n){"use strict";var o,i,c,l,u,f,p,d,g,v,m,b,h,y,w,x,k,S,P=document.createElement("style");return P.textContent='@charset "UTF-8";.server-config[data-v-8cd5b5d6]{width:100%;max-width:400px;margin:0 auto;padding:20px}.server-config .config-header[data-v-8cd5b5d6]{display:flex;align-items:center;justify-content:center}.server-config .config-header .title[data-v-8cd5b5d6]{font-size:20px;font-weight:600;color:#333;font-family:PingFang SC,PingFang SC-Regular,Microsoft YaHei,\\5fae\\8f6f\\96c5\\9ed1}.server-config .config-form[data-v-8cd5b5d6]{margin-bottom:30px}.server-config .config-form .label[data-v-8cd5b5d6]{display:block;margin-bottom:8px;font-weight:500;color:#333;font-family:PingFang SC,PingFang SC-Regular,Microsoft YaHei,\\5fae\\8f6f\\96c5\\9ed1}.server-config .config-form .input-tip[data-v-8cd5b5d6]{margin-top:6px;font-size:12px;color:#999;line-height:1.4;font-family:PingFang SC,Microsoft YaHei,sans-serif}.server-config .config-form .submit-button[data-v-8cd5b5d6]{width:100%;height:40px;font-size:16px;font-weight:500;padding:0;font-family:PingFang SC,PingFang SC-Regular,Microsoft YaHei,\\5fae\\8f6f\\96c5\\9ed1}.server-config .config-form .submit-button[data-v-8cd5b5d6] .loading{width:16px!important;height:16px!important;border-width:2px!important;margin-right:8px!important;align-items:center;justify-content:center}.server-config .input-wrapper[data-v-8cd5b5d6]{display:flex}.server-config .setting-input[data-v-8cd5b5d6]{width:288px}.server-config .setting-input[data-v-8cd5b5d6] .el-input__inner{height:40px;border-radius:6px}.server-config .setting-input[data-v-8cd5b5d6] .el-input__inner:focus{border-color:#536ce6}.server-config .spa-label[data-v-8cd5b5d6]{margin-left:10px;width:34px;height:34px;border:1px solid #dcdfe6;border-radius:4px;display:flex;flex-direction:column;align-items:center;justify-content:center;cursor:pointer}.server-config .spa-label .icon[data-v-8cd5b5d6]{width:16px;height:16px}.server-config .config-tips .tip-item[data-v-8cd5b5d6]{display:flex;align-items:flex-start;margin-bottom:12px;font-size:14px;color:#666;font-family:PingFang SC,PingFang SC-Regular,Microsoft YaHei,\\5fae\\8f6f\\96c5\\9ed1}.server-config .config-tips .tip-item[data-v-8cd5b5d6]:last-child{margin-bottom:0}.server-config .config-tips .tip-item.error-tip[data-v-8cd5b5d6]{align-items:center}.server-config .config-tips .tip-item.error-tip .error-message[data-v-8cd5b5d6]{color:#e6a23c;font-weight:500;font-size:14px;line-height:1.4}.server-config .config-tips .tip-item.error-reasons[data-v-8cd5b5d6]{margin-left:20px;margin-top:6px}.server-config .config-tips .tip-item.error-reasons .reasons-text[data-v-8cd5b5d6]{color:#606266;font-size:13px;line-height:1.5;white-space:pre-line;word-break:break-word}\n',document.head.appendChild(P),{setters:[function(e){o=e._,i=e.r,c=e.N,l=e.h,u=e.a,f=e.b,p=e.d,d=e.j,g=e.w,v=e.l,m=e.i,b=e.a3,h=e.n,y=e.t,w=e.M,x=e.U,k=e.a5,S=e.C}],execute:function(){var n={class:"server-config"},P={class:"config-form"},j={class:"input-wrapper"},O={key:0,style:{"margin-top":"12px",width:"288px"}},_={class:"config-tips"},C={key:0,class:"tip-item error-tip"},U={class:"error-message"},E={key:1,class:"tip-item error-reasons"},T={class:"reasons-text"},F=Object.assign({name:"ServerConfig"},{emits:["server-configured"],setup:function(e,o){var F=o.emit,z=i(null),G=i(!1),H=c({serverUrl:""}),M=i(!1),N=i(""),R=c({icon1:"info-circle",color1:"#e6a23c",message1:"",icon2:"check",color2:"#67c23a",message2:""}),V={serverUrl:[{required:!0,message:"请输入服务器地址",trigger:"blur"},{pattern:/^https?:\/\/.+/,message:"请输入有效的服务器地址（需包含 http:// 或 https://）",trigger:"blur"}]},A=function(){var e=s(a().m((function e(){return a().w((function(e){for(;;)switch(e.n){case 0:M.value=!M.value;case 1:return e.a(2)}}),e)})));return function(){return e.apply(this,arguments)}}(),Y=function(e){var r=e.replace(/[^0-9]/g,"");r!==e&&(N.value=r)},D=function(){var e=s(a().m((function e(r){var n,o,i,c;return a().w((function(e){for(;;)switch(e.n){case 0:if(e.p=0,!S.isClient()){e.n=4;break}return e.n=1,S.getClientConfig();case 1:if(i=e.v){e.n=2;break}i={};case 2:return n=t(t({},i),{},{ServerUrl:r.serverUrl,SpaEnabled:r.spaEnabled,activation_code:r.spaEnabled?r.spaCode:""}),console.log("SPA码将保存为activation_code:",n.activation_code),e.n=3,S.setClientConfig(n);case 3:globalUrlHashParams.set("WebUrl",r.serverUrl),console.log("配置已保存到客户端:",n),e.n=5;break;case 4:o={serverUrl:r.serverUrl,spaEnabled:r.spaEnabled,spaCode:r.spaCode},localStorage.setItem("server_config",JSON.stringify(o)),console.log("配置已保存到本地存储:",o);case 5:e.n=7;break;case 6:throw e.p=6,c=e.v,console.error("保存配置失败:",c),new Error("保存配置失败");case 7:return e.a(2)}}),e,null,[[0,6]])})));return function(r){return e.apply(this,arguments)}}(),I=function(){var e=s(a().m((function e(){var n,t,o,i,c,s,l,u,f,p,d,g,v;return a().w((function(e){for(;;)switch(e.n){case 0:if(z.value){e.n=1;break}return e.a(2);case 1:return e.p=1,e.n=2,z.value.validate();case 2:if(e.v){e.n=3;break}return e.a(2);case 3:if(!M.value){e.n=6;break}if(N.value){e.n=4;break}return w.error("请输入访问码"),e.a(2);case 4:if(6===N.value.length){e.n=5;break}return w.error("访问码必须是6位数字"),e.a(2);case 5:if(/^\d{6}$/.test(N.value)){e.n=6;break}return w.error("访问码只能包含数字"),e.a(2);case 6:return G.value=!0,e.n=7,x(H.serverUrl);case 7:if(e.v){e.n=8;break}return w.error("服务器地址格式错误"),e.a(2);case 8:return n=new URL(H.serverUrl),t="".concat(n.protocol,"//").concat(n.host),H.serverUrl=t,o={serverUrl:H.serverUrl,spaEnabled:M.value,spaCode:M.value?N.value:""},e.p=9,R.icon1="loading",R.color1="#409eff",R.message1="正在测试服务器连接...",R.icon2="warning",R.color2="#e6a23c",R.message2="请稍候，正在验证服务器配置",e.n=10,D(o);case 10:return console.info("配置已保存，正在测试连接..."),R.message1="正在启动服务，请稍候...",R.color1="#409eff",R.icon1="loading",e.n=11,new Promise((function(e){return setTimeout(e,5e3)}));case 11:return i=28080,e.p=12,e.n=13,k();case 13:i=e.v,console.log("获取到的代理端口:",i),e.n=15;break;case 14:e.p=14,d=e.v,console.warn("获取动态端口失败，使用默认端口28080:",d),i=28080;case 15:c="http://127.0.0.1:".concat(i,"/auth/login/v1/user/main_idp/list"),console.log("测试连接地址:",c),s=5,l=null,u=a().m((function e(){var r,n,t,o;return a().w((function(e){for(;;)switch(e.n){case 0:return e.p=0,console.log("连接测试第".concat(f,"次尝试...")),f>1&&(R.message1="正在重试连接... (".concat(f,"/").concat(s,")")),r=new AbortController,n=setTimeout((function(){r.abort()}),1e3),e.n=1,fetch(c,{method:"GET",headers:{"Content-Type":"application/json"},signal:r.signal});case 1:if(t=e.v,clearTimeout(n),!t.ok||200!==t.status){e.n=2;break}return console.log("连接测试第".concat(f,"次尝试成功")),e.a(2,1);case 2:throw new Error("服务器响应错误: ".concat(t.status));case 3:case 5:e.n=7;break;case 4:if(e.p=4,o=e.v,l=o,console.warn("连接测试第".concat(f,"次尝试失败:"),o.message),!(f<s)){e.n=6;break}return console.log("等待".concat(2e3,"ms后进行第").concat(f+1,"次重试...")),e.n=5,new Promise((function(e){return setTimeout(e,2e3)}));case 6:throw console.error("连接测试失败，已重试".concat(s,"次")),l;case 7:return e.a(2)}}),e,null,[[0,4]])})),f=1;case 16:if(!(f<=s)){e.n=19;break}return e.d(r(u()),17);case 17:if(!e.v){e.n=18;break}return e.a(3,19);case 18:f++,e.n=16;break;case 19:(p={ok:!0,status:200}).ok&&200===p.status&&(R.icon1="check",R.color1="#67c23a",R.message1="服务器连接成功！",R.icon2="check",R.color2="#67c23a",R.message2="配置成功后将自动跳转到登录页面",w.success("服务器连接成功！"),F("server-configured",o)),e.n=21;break;case 20:e.p=20,g=e.v,console.warn("服务器连接测试失败，清除已保存的配置:",g),R.icon1="close",R.color1="#f56c6c",R.icon2="warning",R.color2="#e6a23c",R.message1="连接失败，可能存在以下原因：",M.value?R.message2="1.网络异常或服务器地址不可达\n2.已启用SPA安全防护，未输入SPA访问码\n3.SPA访问码无效（无效原因可能为：电脑系统时间存在偏差、未区分字母大小写、访问码已过期），请重新输入":R.message2="1.网络异常或服务器地址不可达\n2.服务器服务未启动或配置异常";case 21:e.n=23;break;case 22:e.p=22,v=e.v,console.error("配置服务器失败:",v),R.icon1="close",R.color1="#f56c6c",R.message1="配置失败，请检查输入格式",M.value?(R.icon2="warning",R.color2="#e6a23c",R.message2="请检查服务器地址和访问码格式"):(R.icon2="warning",R.color2="#e6a23c",R.message2="请检查服务器地址格式是否正确");case 23:return e.p=23,G.value=!1,e.f(23);case 24:return e.a(2)}}),e,null,[[12,14],[9,20],[1,22,23,24]])})));return function(){return e.apply(this,arguments)}}();return function(e,r){var t=l("base-input"),o=l("base-form-item"),a=l("base-button"),i=l("base-form"),c=l("base-icon");return u(),f("div",n,[r[5]||(r[5]=p("div",{class:"config-header"},[p("span",{class:"title"},"平台地址")],-1)),p("div",P,[d(i,{ref_key:"serverForm",ref:z,model:H,rules:V,onKeyup:b(I,["enter"])},{default:g((function(){return[d(o,{prop:"serverUrl"},{default:g((function(){return[p("div",j,[d(t,{modelValue:H.serverUrl,"onUpdate:modelValue":r[0]||(r[0]=function(e){return H.serverUrl=e}),placeholder:"输入您连接的平台服务器地址","suffix-icon":"link",class:"setting-input"},null,8,["modelValue"]),p("span",{class:"spa-label",onClick:A},r[2]||(r[2]=[p("svg",{class:"icon","aria-hidden":"true"},[p("use",{"xlink:href":"#icon-spa"})],-1)]))]),M.value?(u(),f("div",O,[d(t,{modelValue:N.value,"onUpdate:modelValue":r[1]||(r[1]=function(e){return N.value=e}),maxlength:"6",placeholder:"请输入6位数字访问码",style:{width:"100%"},onInput:Y},null,8,["modelValue"])])):v("",!0),r[3]||(r[3]=p("div",{class:"input-tip"}," 请输入平台地址，如：https://************* ",-1))]})),_:1,__:[3]}),d(o,null,{default:g((function(){return[d(a,{type:"primary",class:"submit-button",loading:G.value,onClick:I},{default:g((function(){return r[4]||(r[4]=[m(" 连接服务器 ")])})),_:1,__:[4]},8,["loading"])]})),_:1})]})),_:1},8,["model"])]),p("div",_,[R.message1?(u(),f("div",C,[d(c,{name:R.icon1,style:h({color:R.color1,marginRight:"6px",fontSize:"14px",flexShrink:0,marginTop:"1px"})},null,8,["name","style"]),p("span",U,y(R.message1),1)])):v("",!0),R.message2?(u(),f("div",E,[p("span",T,y(R.message2),1)])):v("",!0)])])}}});e("default",o(F,[["__scopeId","data-v-8cd5b5d6"]]))}}}))}();
