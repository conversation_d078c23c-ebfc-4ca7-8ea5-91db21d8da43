/*! 
 Build based on gin-vue-admin 
 Time : 1754834760000 */
System.register(["./index-legacy.7db06653.js"],(function(e,r){"use strict";var o,t,c,n;return{setters:[function(e){o=e.f,t=e.g,c=e.a,n=e.b}],execute:function(){e("default",Object.assign({name:"Verify"},{setup:function(e){var r=o(),i=document.location.protocol+"//"+document.location.host,l=function(){var e=window.location.href;console.log("verify页面当前URL:",e);var r=new URLSearchParams(window.location.search),o=r.get("redirect_url");if(o)return console.log("直接解析的redirect_url:",o),o;var t=window.location.hash;if(t&&t.includes("redirect_url=")){console.log("从hash中检测到redirect_url，开始解析:",t);var c=t.split("?");if(c.length>1){var n=c.slice(1).join("?");if(o=new URLSearchParams(n).get("redirect_url")){console.log("从hash解析的redirect_url（原始）:",o);try{var i=decodeURIComponent(o);return console.log("从hash解析的redirect_url（解码后）:",i),i}catch(f){return console.log("解码失败，使用原始值:",o),o}}}}var l=r.get("redirect");if(l){if(console.log("从login页面redirect参数解析:",l),l.startsWith("http"))return l;if(l.startsWith("#")&&l.includes("redirect_url=")){console.log("检测到hash格式的redirect参数");try{var a=l.substring(1).split("?");if(a.length>1){var s=a.slice(1).join("?");console.log("redirect参数中的查询字符串:",s);var u=new URLSearchParams(s).get("redirect_url");if(u){console.log("从redirect参数中提取的URL（原始）:",u);try{var d=decodeURIComponent(u);return console.log("从redirect参数中提取的URL（解码后）:",d),d}catch(f){return console.log("解码失败，使用原始值:",u),u}}}}catch(f){console.error("解析redirect参数失败:",f)}}}try{var h=window.location.search.slice(1)||window.location.hash.split("?")[1]||"";if(console.log("尝试解析的URL字符串:",h),o=new URLSearchParams(h).get("redirect_url"))try{var g=decodeURIComponent(o);return console.log("解码后的redirect_url:",g),g}catch(f){return console.log("解码失败，使用原始redirect_url:",o),o}}catch(v){console.error("解析redirect_url失败:",v)}return console.warn("未能解析到redirect_url"),null}();if(l){console.log("最终使用的redirect_url:",l);var a=new URLSearchParams(window.location.search),s=new URLSearchParams;"client"===a.get("type")&&(s.set("type","client"),a.get("wp")&&s.set("wp",a.get("wp")));var u={method:"GET",url:"".concat(i,"/auth/user/v1/redirect_verify?redirect_url=").concat(encodeURIComponent(l)),headers:{Accept:"application/json, text/plain, */*",Authorization:"".concat(r.token.tokenType," ").concat(r.token.accessToken)}};console.log("调用redirect_verify接口:",u.url),t.request(u).then((function(e){if(200===e.status){var r=e.data.url;if(console.log("认证服务返回的URL:",r),s.toString()){var o=r.includes("?")?"&":"?";r+=o+s.toString(),console.log("添加客户端参数后的URL:",r)}console.log("最终跳转URL:",r),window.location.href=r}})).catch((function(e){console.error("redirect_verify调用失败:",e)}))}else console.error("缺少redirect_url参数");return function(e,r){return c(),n("div")}}}))}}}));
