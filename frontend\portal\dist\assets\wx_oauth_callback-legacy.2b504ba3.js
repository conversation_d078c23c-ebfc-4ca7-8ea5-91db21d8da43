/*! 
 Build based on gin-vue-admin 
 Time : 1754836337000 */
!function(){function r(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */var e,n,o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",u=o.toStringTag||"@@toStringTag";function a(r,o,i,u){var a=o&&o.prototype instanceof f?o:f,s=Object.create(a.prototype);return t(s,"_invoke",function(r,t,o){var i,u,a,f=0,s=o||[],l=!1,p={p:0,n:0,v:e,a:y,f:y.bind(e,4),d:function(r,t){return i=r,u=0,a=e,p.n=t,c}};function y(r,t){for(u=r,a=t,n=0;!l&&f&&!o&&n<s.length;n++){var o,i=s[n],y=p.p,v=i[2];r>3?(o=v===t)&&(a=i[(u=i[4])?5:(u=3,3)],i[4]=i[5]=e):i[0]<=y&&((o=r<2&&y<i[1])?(u=0,p.v=t,p.n=i[1]):y<v&&(o=r<3||i[0]>t||t>v)&&(i[4]=r,i[5]=t,p.n=v,u=0))}if(o||r>1)return c;throw l=!0,t}return function(o,s,v){if(f>1)throw TypeError("Generator is already running");for(l&&1===s&&y(s,v),u=s,a=v;(n=u<2?e:a)||!l;){i||(u?u<3?(u>1&&(p.n=-1),y(u,a)):p.n=a:p.v=a);try{if(f=2,i){if(u||(o="next"),n=i[o]){if(!(n=n.call(i,a)))throw TypeError("iterator result is not an object");if(!n.done)return n;a=n.value,u<2&&(u=0)}else 1===u&&(n=i.return)&&n.call(i),u<2&&(a=TypeError("The iterator does not provide a '"+o+"' method"),u=1);i=e}else if((n=(l=p.n<0)?a:r.call(t,p))!==c)break}catch(n){i=e,u=1,a=n}finally{f=1}}return{value:n,done:l}}}(r,i,u),!0),s}var c={};function f(){}function s(){}function l(){}n=Object.getPrototypeOf;var p=[][i]?n(n([][i]())):(t(n={},i,(function(){return this})),n),y=l.prototype=f.prototype=Object.create(p);function v(r){return Object.setPrototypeOf?Object.setPrototypeOf(r,l):(r.__proto__=l,t(r,u,"GeneratorFunction")),r.prototype=Object.create(y),r}return s.prototype=l,t(y,"constructor",l),t(l,"constructor",s),s.displayName="GeneratorFunction",t(l,u,"GeneratorFunction"),t(y),t(y,u,"Generator"),t(y,i,(function(){return this})),t(y,"toString",(function(){return"[object Generator]"})),(r=function(){return{w:a,m:v}})()}function t(r,e,n,o){var i=Object.defineProperty;try{i({},"",{})}catch(r){i=0}t=function(r,e,n,o){if(e)i?i(r,e,{value:n,enumerable:!o,configurable:!o,writable:!o}):r[e]=n;else{var u=function(e,n){t(r,e,(function(r){return this._invoke(e,n,r)}))};u("next",0),u("throw",1),u("return",2)}},t(r,e,n,o)}function e(r,t,e,n,o,i,u){try{var a=r[i](u),c=a.value}catch(r){return void e(r)}a.done?t(c):Promise.resolve(c).then(n,o)}System.register(["./index-legacy.aab80689.js"],(function(t,n){"use strict";var o,i,u,a,c,f,s,l,p,y;return{setters:[function(r){o=r.u,i=r.E,u=r.f,a=r.r,c=r.o,f=r.x,s=r.a,l=r.b,p=r.L,y=r.M}],execute:function(){t("default",Object.assign({name:"WxOAuthCallback"},{setup:function(t){var n=o(),v=i(),b=u(),d=n.query,h=d.code,g=d.state,m=d.redirect_url,O=a(Array.isArray(g)?g[0]:g),j=a(""),w=function(){var t,n=(t=r().m((function t(){var e,n,o;return r().w((function(r){for(;;)switch(r.n){case 0:return e=p.service({fullscreen:!0,text:"登录中，请稍候..."}),r.p=1,n={clientId:"client_portal",grantType:"implicit",redirect_uri:"",idpId:O.value,authWeb:{authWebCode:Array.isArray(h)?h[0]:h}},r.n=2,b.LoginIn(n,"qiyewx_oauth",O.value);case 2:if(!0!==r.v){r.n=4;break}return r.n=3,v.push({name:"verify",query:{redirect_url:m}});case 3:r.n=5;break;case 4:y.error("登录失败，请重试");case 5:r.n=7;break;case 6:r.p=6,o=r.v,console.error("登录过程出错:",o),y.error("登录过程出错，请重试");case 7:return r.p=7,e.close(),r.f(7);case 8:return r.a(2)}}),t,null,[[1,6,7,8]])})),function(){var r=this,n=arguments;return new Promise((function(o,i){var u=t.apply(r,n);function a(r){e(u,o,i,a,c,"next",r)}function c(r){e(u,o,i,a,c,"throw",r)}a(void 0)}))});return function(){return n.apply(this,arguments)}}();return c(w),f("userName",j),function(r,t){return s(),l("span")}}}))}}}))}();
