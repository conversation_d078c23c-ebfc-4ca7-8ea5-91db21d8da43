/*! 
 Build based on gin-vue-admin 
 Time : 1754836337000 */
import{f as e,g as o,a as r,b as t}from"./index.57c3624b.js";const c=Object.assign({name:"Verify"},{setup(c){const n=e(),l=document.location.protocol+"//"+document.location.host,s=function(){const e=window.location.href;console.log("verify页面当前URL:",e);const o=new URLSearchParams(window.location.search);let r=o.get("redirect_url");if(r)return console.log("直接解析的redirect_url:",r),r;const t=window.location.hash;if(t&&t.includes("redirect_url=")){console.log("从hash中检测到redirect_url，开始解析:",t);const e=t.split("?");if(e.length>1){const o=e.slice(1).join("?");if(r=new URLSearchParams(o).get("redirect_url"),r){console.log("从hash解析的redirect_url（原始）:",r);try{const e=decodeURIComponent(r);return console.log("从hash解析的redirect_url（解码后）:",e),e}catch(n){return console.log("解码失败，使用原始值:",r),r}}}}const c=o.get("redirect");if(c){if(console.log("从login页面redirect参数解析:",c),c.startsWith("http"))return c;if(c.startsWith("#")&&c.includes("redirect_url=")){console.log("检测到hash格式的redirect参数");try{const e=c.substring(1).split("?");if(e.length>1){const o=e.slice(1).join("?");console.log("redirect参数中的查询字符串:",o);const r=new URLSearchParams(o).get("redirect_url");if(r){console.log("从redirect参数中提取的URL（原始）:",r);try{const e=decodeURIComponent(r);return console.log("从redirect参数中提取的URL（解码后）:",e),e}catch(n){return console.log("解码失败，使用原始值:",r),r}}}}catch(n){console.error("解析redirect参数失败:",n)}}}try{const e=window.location.search.slice(1)||window.location.hash.split("?")[1]||"";console.log("尝试解析的URL字符串:",e);if(r=new URLSearchParams(e).get("redirect_url"),r)try{const e=decodeURIComponent(r);return console.log("解码后的redirect_url:",e),e}catch(n){return console.log("解码失败，使用原始redirect_url:",r),r}}catch(l){console.error("解析redirect_url失败:",l)}return console.warn("未能解析到redirect_url"),null}();if(s){console.log("最终使用的redirect_url:",s);const e=new URLSearchParams(window.location.search),r=new URLSearchParams;"client"===e.get("type")&&(r.set("type","client"),e.get("wp")&&r.set("wp",e.get("wp")));const t={method:"GET",url:`${l}/auth/user/v1/redirect_verify?redirect_url=${encodeURIComponent(s)}`,headers:{Accept:"application/json, text/plain, */*",Authorization:`${n.token.tokenType} ${n.token.accessToken}`}};console.log("调用redirect_verify接口:",t.url),o.request(t).then((function(e){if(200===e.status){let o=e.data.url;if(console.log("认证服务返回的URL:",o),r.toString()){const e=o.includes("?")?"&":"?";o+=e+r.toString(),console.log("添加客户端参数后的URL:",o)}console.log("最终跳转URL:",o),window.location.href=o}})).catch((function(e){console.error("redirect_verify调用失败:",e)}))}else console.error("缺少redirect_url参数");return(e,o)=>(r(),t("div"))}});export{c as default};
