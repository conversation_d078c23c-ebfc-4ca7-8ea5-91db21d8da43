/*! 
 Build based on gin-vue-admin 
 Time : 1754879545000 */
import{f as e,E as a,u as l,$ as s,r as o,c as i,o as n,K as t,P as d,S as c,x as u,h as r,a as v,k as m,w as p,d as g,e as f,j as h,n as y,b,W as w,F as k,A as x,t as _,y as j,Z as C,l as I,a0 as L,z,a1 as S}from"./index.ab3e73c8.js";import A from"./index.859b3a39.js";import{l as E}from"./logo.b56ac4ae.js";import"./index.842f4476.js";import"./menuItem.2d18ac6b.js";import"./asyncSubmenu.f9ede66e.js";const M=""+new URL("logo_Infogo_white.cf29bfc3.svg",import.meta.url).href,R=["src"],T={class:"collapse-btn",type:"button"},N={key:0},O={key:1},W={class:"header-row"},$={class:"header-col"},q={class:"header-cont"},B={class:"header-content pd-0"},F={class:"breadcrumb-col"},K={class:"breadcrumb"},P={class:"user-col"},U={class:"right-box"},Z={class:"dp-flex justify-content-center align-items height-full width-full"},D={class:"header-avatar",style:{cursor:"pointer"}},G={class:"header-avatar-text"},H={key:0,class:"dropdown-menu"},J={key:0,class:"loading-overlay"},Q={style:{height:"100%",display:"flex","flex-direction":"column"}},V=Object.assign({name:"Layout"},{setup(V){const X=e(),Y=a(),ee=l(),ae=s(),le=o(!1),se=o(!0),oe=o(!1),ie=o("7"),ne=i((()=>le.value?E:M)),te=()=>{const e=document.body.clientWidth;e<1e3||e>=1e3&&e<1200?(oe.value=!1,se.value=!1,le.value=!0):(oe.value=!1,se.value=!0,le.value=!1)};te();const de=o(!1);n((()=>{t.emit("collapse",le.value),t.emit("mobile",oe.value),t.on("reload",pe),t.on("showLoading",(()=>{de.value=!0})),t.on("closeLoading",(()=>{de.value=!1})),window.onresize=()=>(te(),t.emit("collapse",le.value),void t.emit("mobile",oe.value)),X.loadingInstance&&X.loadingInstance.close(),document.addEventListener("click",ce)})),d((()=>{document.removeEventListener("click",ce)}));const ce=e=>{if(fe.value){const a=document.querySelector(".dropdown");a&&!a.contains(e.target)&&(fe.value=!1)}},ue=i((()=>"dark"===X.sideMode?"#273444":"light"===X.sideMode?"#fff":X.sideMode)),re=i((()=>ee.meta.matched)),ve=o(!0);let me=null;const pe=async()=>{me&&window.clearTimeout(me),me=window.setTimeout((async()=>{if(ee.meta.keepAlive)ve.value=!1,await c(),ve.value=!0;else{const e=ee.meta.title;Y.push({name:"Reload",params:{title:e}})}}),400)},ge=o(!1),fe=o(!1),he=()=>{console.log("totalCollapse 被调用, 当前 isCollapse:",le.value),le.value=!le.value,se.value=!le.value,ge.value=!1,t.emit("collapse",le.value),console.log("新状态 isCollapse:",le.value,"isSider:",se.value)},ye=()=>{fe.value=!fe.value},be=()=>{Y.push({name:"person"})};return u("day",ie),(e,a)=>{const l=r("base-aside"),s=r("base-icon"),o=r("router-view"),i=r("base-main"),n=r("base-container");return v(),m(n,{class:"layout-cont"},{default:p((()=>[g("div",{class:f([[se.value?"openside":"hideside",oe.value?"mobile":""],"layout-wrapper"])},[g("div",{class:f([[ge.value?"shadowBg":""],"shadow-overlay"]),onClick:a[0]||(a[0]=e=>(ge.value=!ge.value,void(ge.value||le.value||(le.value=!0,se.value=!1,t.emit("collapse",le.value)))))},null,2),g("div",{class:f(["sidebar-container",{collapsed:le.value}])},[h(l,{class:"main-cont main-left gva-aside",collapsed:le.value},{default:p((()=>[g("div",{class:f(["tilte",[se.value?"openlogoimg":"hidelogoimg"]]),style:y({background:ue.value})},[g("img",{alt:"",class:"logoimg",src:ne.value},null,8,R)],6),h(A,{class:"aside"}),g("div",{class:"footer",style:y({background:ue.value})},[g("div",{class:"menu-total",onClick:he},[g("button",T,[le.value?(v(),b("span",N,"☰")):(v(),b("span",O,"☰"))])])],4)])),_:1},8,["collapsed"])],2),h(i,{class:"main-cont main-right"},{default:p((()=>[h(w,{duration:{enter:800,leave:100},mode:"out-in",name:"el-fade-in-linear"},{default:p((()=>[g("div",{style:y({width:`calc(100% - ${oe.value?"0px":le.value?"54px":"180px"})`}),class:"topfix"},[g("div",W,[g("div",$,[g("header",q,[g("div",B,[a[4]||(a[4]=g("div",{class:"header-menu-col",style:{"z-index":"100"}},null,-1)),g("div",F,[g("nav",K,[(v(!0),b(k,null,x(re.value.slice(1,re.value.length),(e=>(v(),b("div",{key:e.path,class:"breadcrumb-item"},_(j(C)(e.meta.topTitle||"",j(ee))),1)))),128))])]),g("div",P,[g("div",U,[g("div",{class:"dropdown",onClick:ye},[g("div",Z,[g("span",D,[g("span",G,_(j(X).userInfo.displayName?j(X).userInfo.displayName:j(X).userInfo.name),1),h(s,{name:"zhankai",size:"10px"})])]),fe.value?(v(),b("div",H,[g("div",{class:"dropdown-item",onClick:be},a[2]||(a[2]=[g("svg",{class:"icon dropdown-item-icon","aria-hidden":"true"},[g("use",{"xlink:href":"#icon-person"})],-1),g("span",null,"个人信息",-1)])),g("div",{class:"dropdown-item",onClick:a[1]||(a[1]=e=>(async()=>{try{await X.LoginOut(),S.remove("asce_sms"),logger.log("注销登录完成")}catch(e){logger.log("注销登录过程中出现错误:",e),S.remove("asce_sms")}})())},a[3]||(a[3]=[g("svg",{class:"icon dropdown-item-icon","aria-hidden":"true"},[g("use",{"xlink:href":"#icon-logout"})],-1),g("span",null,"注销登录",-1)]))])):I("",!0)])])])])])])])],4)])),_:1}),g("div",{class:f(["router-view-container",{loading:de.value}])},[de.value?(v(),b("div",J,a[5]||(a[5]=[g("div",{class:"loading-spinner"},[g("div",{class:"spinner"}),g("div",{class:"loading-text"},"正在加载中")],-1)]))):I("",!0),ve.value?(v(),m(o,{key:1,class:"admin-box"},{default:p((({Component:e})=>[g("div",Q,[h(w,{mode:"out-in",name:"el-fade-in-linear"},{default:p((()=>[(v(),m(L,{include:j(ae).keepAliveRouters},[(v(),m(z(e)))],1032,["include"]))])),_:2},1024)])])),_:1})):I("",!0)],2)])),_:1})],2)])),_:1})}}});export{V as default};
