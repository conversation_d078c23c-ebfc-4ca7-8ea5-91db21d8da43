/*! 
 Build based on gin-vue-admin 
 Time : 1754836337000 */
System.register(["./index-legacy.aab80689.js"],(function(e,n){"use strict";var t,u,r,i,a,o,l,c,f,s,d;return{setters:[function(e){t=e.$,u=e.h,r=e.a,i=e.b,a=e.j,o=e.w,l=e.W,c=e.k,f=e.a0,s=e.y,d=e.z}],execute:function(){e("default",Object.assign({name:"RouterHolder"},{setup:function(e){var n=t();return function(e,t){var v=u("router-view");return r(),i("div",null,[a(v,null,{default:o((function(e){var t=e.Component;return[a(l,{mode:"out-in",name:"el-fade-in-linear"},{default:o((function(){return[(r(),c(f,{include:s(n).keepAliveRouters},[(r(),c(d(t)))],1032,["include"]))]})),_:2},1024)]})),_:1})])}}}))}}}));
