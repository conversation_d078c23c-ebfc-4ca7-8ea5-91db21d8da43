/*! 
 Build based on gin-vue-admin 
 Time : 1754879545000 */
import{a2 as e}from"./index.ab3e73c8.js";function a(){const a=e("secondary"),r=e("isSecondary"),o=e("uniqKey"),n=e("userName"),u=e("contactType"),l=e("hasContactInfo"),t=e("qrcode"),c=e("CurrentSecret");return{secondary:a,isSecondary:r,uniqKey:o,userName:n,contactType:u,hasContactInfo:l,qrcode:t,CurrentSecret:c,handleSecondaryAuthResponse:e=>{try{return logger.log("处理双因子验证响应:",e),e&&e.isSecondary?(logger.log("触发双因子验证"),r.value=e.isSecondary,a.value=e.secondary,o.value=e.uniqKey,n.value=e.userName,u.value=e.contactType,l.value=e.hasContactInfo,t.value=e.qrcode,c.value=e.CurrentSecret,logger.log("双因子验证状态已设置:",{isSecondary:r.value,secondary:a.value,uniqKey:o.value,userName:n.value,contactType:u.value,hasContactInfo:l.value,qrcode:t.value,CurrentSecret:c.value}),!0):(logger.log("无需双因子验证"),!1)}catch(s){return logger.log("处理双因子验证响应失败:",s),!1}},clearSecondaryAuthState:()=>{try{logger.log("清除双因子验证状态"),r.value=!1,a.value=[],o.value="",n.value="",u.value="",l.value=!1,t.value="",c.value="",logger.log("双因子验证状态已清除")}catch(e){logger.log("清除双因子验证状态失败:",e)}},isInSecondaryAuth:()=>!0===r.value,getSecondaryAuthInfo:()=>({isSecondary:r.value,secondary:a.value,uniqKey:o.value,userName:n.value,contactType:u.value,hasContactInfo:l.value,qrcode:t.value,CurrentSecret:c.value}),setUserName:e=>{n.value=e,logger.log("设置用户名:",e)},validateInjectedData:()=>{const a=["secondary","isSecondary","uniqKey","userName","contactType","hasContactInfo","qrcode","CurrentSecret"].filter((a=>null===e(a,null)));return!(a.length>0)||(logger.log("缺少必要的注入数据:",a),!1)}}}export{a as u};
