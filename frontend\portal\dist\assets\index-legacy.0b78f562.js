/*! 
 Build based on gin-vue-admin 
 Time : 1754879545000 */
System.register(["./index-legacy.39a66d41.js","./index-legacy.a01517a9.js","./menuItem-legacy.390c5673.js","./asyncSubmenu-legacy.fd1c466b.js"],(function(e,n){"use strict";var a,t,o,u,r,l,c,i,d,s,f,v,m,p,h,b,g,x,y,k,T,_=document.createElement("style");return _.textContent='@charset "UTF-8";.aside-container{height:100%;overflow:hidden;display:flex;flex-direction:column}:deep(.base-sub-menu__title:hover),:deep(.base-menu-item:hover){background:transparent}:deep(.base-scrollbar) :deep(.base-scrollbar__view){height:100%}.menu-info .menu-contorl{line-height:52px;font-size:20px;display:table-cell;vertical-align:middle}\n',document.head.appendChild(_),{setters:[function(e){a=e.u,t=e.E,o=e.f,u=e.$,r=e.r,l=e.v,c=e.P,i=e.K,d=e.h,s=e.a,f=e.b,v=e.j,m=e.w,p=e.W,h=e.F,b=e.A,g=e.y,x=e.k,y=e.l,k=e.n},function(e){T=e.default},function(){},function(){}],execute:function(){e("default",Object.assign({name:"Aside"},{setup:function(e){var n=a(),_=t(),j=o(),w=u(),F=r({}),M=function(){switch(j.sideMode){case"#fff":F.value={background:"#fff",activeBackground:"#4D70FF",activeText:"#fff",normalText:"#333",hoverBackground:"rgba(64, 158, 255, 0.08)",hoverText:"#333"};break;case"#273444":F.value={background:"#263444",activeBackground:"#4D70FF",activeText:"#fff",normalText:"#fff",hoverBackground:"rgba(64, 158, 255, 0.08)",hoverText:"#fff"}}};M();var B=r("");l((function(){return n}),(function(){B.value=n.meta.activeName||n.name}),{deep:!0}),l((function(){return j.sideMode}),(function(){M()}));var q=r(!1);B.value=n.meta.activeName||n.name,i.on("collapse",(function(e){console.log("aside 收到 collapse 事件:",e),q.value=e})),c((function(){i.off("collapse")}));var E=function(e,a,t,o){var u,r,l={},c={};(null===(u=w.routeMap[e])||void 0===u?void 0:u.parameters)&&(null===(r=w.routeMap[e])||void 0===r||r.parameters.forEach((function(e){"query"===e.type?l[e.key]=e.value:c[e.key]=e.value}))),e!==n.name&&(e.indexOf("http://")>-1||e.indexOf("https://")>-1?window.open(e):_.push({name:e,query:l,params:c}))};return function(e,n){var a=d("base-menu"),t=d("base-scrollbar");return s(),f("div",{class:"aside-container",style:k({background:g(j).sideMode})},[v(t,{height:"100%"},{default:m((function(){return[v(p,{duration:{enter:800,leave:100},mode:"out-in",name:"el-fade-in-linear"},{default:m((function(){return[v(a,{collapse:q.value,"collapse-transition":!1,"default-active":B.value,"background-color":F.value.background,"active-text-color":F.value.active,mode:"vertical","unique-opened":!0,onSelect:E},{default:m((function(){return[(s(!0),f(h,null,b(g(w).asyncRouters[0].children,(function(e){return s(),f(h,null,[e.hidden?y("",!0):(s(),x(T,{key:e.name,"is-collapse":q.value,"router-info":e,theme:F.value},null,8,["is-collapse","router-info","theme"]))],64)})),256))]})),_:1},8,["collapse","default-active","background-color","active-text-color"])]})),_:1})]})),_:1})],4)}}}))}}}));
