/*! 
 Build based on gin-vue-admin 
 Time : 1754836337000 */
import s from"./index.76790311.js";import{_ as a,h as e,a as c,b as n,d as o,j as p}from"./index.57c3624b.js";const t={class:"access-main"},r={class:"content-wrapper"},d={class:"access-app"};const m=a({name:"BowserAccess",components:{AppPage:s},data:()=>({}),async mounted(){},methods:{}},[["render",function(s,a,m,i,l,f){const u=e("AppPage");return c(),n("div",t,[o("ul",r,[o("li",d,[p(u,{class:"access-app-page"})])])])}],["__scopeId","data-v-550f2b74"]]);export{m as default};
