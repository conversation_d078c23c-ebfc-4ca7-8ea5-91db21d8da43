/*! 
 Build based on gin-vue-admin 
 Time : 1754879545000 */
import{_ as e,V as a,r as l,o,K as s,h as n,a as t,k as i,w as d,j as c,e as u,d as r,n as v,W as m,b as w,i as f,l as p,M as g}from"./index.ab3e73c8.js";import{l as b}from"./logo.b56ac4ae.js";import{g as h}from"./system.9d57df9d.js";const k={style:{background:"'#273444'"}},y={class:"downloadWin"},x={key:1,class:"download-complete"},_={key:1,class:"mobile-notice"},F=e(Object.assign({name:"DownloadWin"},{setup(e){a((e=>({"5c353902":e.activeBackground,"33dfecc2":e.normalText})));const F=l(!1),T=l(!0),j=l(!1),L=l("1"),E=l({});E.value={background:"#273444",activeBackground:"#4D70FF",activeText:"#fff",normalText:"#fff",hoverBackground:"rgba(64, 158, 255, 0.08)",hoverText:"#fff"};const z=l(!1),B=l(0),C=l(!1);let R=0;const S=()=>{const e=document.body.clientWidth;e<768?(j.value=!0,T.value=!1,F.value=!0):e>=768&&e<1024||e>=1024&&e<1200?(j.value=!1,T.value=!1,F.value=!0):(j.value=!1,T.value=!0,F.value=!1)};S();const W=l(!1);o((()=>{s.emit("collapse",F.value),s.emit("mobile",j.value),s.on("showLoading",(()=>{W.value=!0})),s.on("closeLoading",(()=>{W.value=!1})),window.onresize=()=>(S(),s.emit("collapse",F.value),void s.emit("mobile",j.value))}));const M=l("#1f2a36"),O=l(!1),U=()=>{F.value=!F.value,T.value=!F.value,O.value=!F.value,s.emit("collapse",F.value)},D=e=>100===e?"下载完成":`${e}%`,$=async(e,a)=>{try{const l=await q(e);H(l,a)}catch(l){if(R<3&&"网络连接超时"===l.message)return R++,$(e,a);throw new Error(`安装包下载失败，请检查网络连接或联系管理员。错误: ${l.message}`)}},q=e=>new Promise(((a,l)=>{const o=new XMLHttpRequest;o.open("GET",e,!0),o.responseType="blob",o.timeout=3e5;const s=Date.now();o.onprogress=e=>{if(e.lengthComputable){const a=e.loaded/e.total*100;B.value=Math.round(a)}else{const a=(Date.now()-s)/1e3,l=60*(e.loaded/a),o=e.loaded/l*100;B.value=Math.min(99,Math.round(o))}},o.onload=()=>{200===o.status?a(o.response):l(new Error(`HTTP 错误: ${o.status}`))},o.onerror=()=>{l(new Error("网络错误"))},o.ontimeout=()=>{l(new Error("网络连接超时"))},o.send()})),H=(e,a)=>{if(window.navigator.msSaveOrOpenBlob)navigator.msSaveBlob(e,a);else{const l=document.createElement("a"),o=document.querySelector("body");l.href=window.URL.createObjectURL(e),l.download=a,l.style.display="none",o.appendChild(l),l.click(),o.removeChild(l),window.URL.revokeObjectURL(l.href)}};return(e,a)=>{const l=n("base-row"),o=n("base-icon"),s=n("base-menu-item"),S=n("base-menu"),W=n("base-scrollbar"),q=n("base-aside"),H=n("base-link"),P=n("base-progress"),G=n("base-main"),I=n("base-container");return t(),i(I,{class:"layout-cont"},{default:d((()=>[c(I,{class:u([T.value?"openside":"hideside",j.value?"mobile":""])},{default:d((()=>[c(l,{class:u([O.value?"shadowBg":""]),onClick:a[0]||(a[0]=e=>(O.value=!O.value,T.value=!!F.value,void U()))},null,8,["class"]),c(q,{class:"main-cont main-left gva-aside"},{default:d((()=>[r("div",{class:u(["tilte",[T.value?"openlogoimg":"hidelogoimg"]]),style:v({background:M.value})},a[2]||(a[2]=[r("img",{alt:"",class:"logoimg",src:b},null,-1)]),6),r("div",k,[c(W,{height:"calc(100vh - 110px)"},{default:d((()=>[c(m,{duration:{enter:800,leave:100},mode:"out-in",name:"el-fade-in-linear"},{default:d((()=>[c(S,{collapse:F.value,"collapse-transition":!1,"default-active":L.value,"background-color":E.value.background,"active-text-color":E.value.activeText,mode:"vertical","unique-opened":!0},{default:d((()=>[c(s,{index:"1"},{default:d((()=>[c(o,{name:"xiazai",size:"16px"}),a[3]||(a[3]=r("span",null,"客户端下载",-1))])),_:1,__:[3]})])),_:1},8,["collapse","default-active","background-color","active-text-color"])])),_:1})])),_:1})]),r("div",{class:"footer",style:v({background:M.value})},[r("div",{class:"menu-total",onClick:U},[F.value?(t(),i(o,{key:0,color:"#FFFFFF",size:"14px",name:"expand"})):(t(),i(o,{key:1,color:"#FFFFFF",size:"14px",name:"fold"}))])],4)])),_:1}),c(G,{class:"main-cont main-right client"},{default:d((()=>[r("div",y,[j.value?p("",!0):(t(),w("div",{key:0,class:"download-card",onClick:a[1]||(a[1]=e=>(async e=>{if("windows"===e){z.value=!0,B.value=0,C.value=!1,R=0;try{const a=await h({platform:e});if(0!==a.data.code)throw new Error(a.data.msg);{const e=window.location.port,l=new URL(a.data.data.download_url);let o;e?l.toString().includes("asec-deploy")?o=a.data.data.download_url:(l.port=e,o=l.toString()):(l.port="",o=l.toString());const s=e?a.data.data.latest_filename.replace(/@(\d+)/,`@${e}`):a.data.data.latest_filename;await $(o,s),C.value=!0,g({type:"success",message:"下载完成"})}}catch(a){g({type:"error",message:a.message||"下载失败，请联系管理员"})}finally{z.value=!1,setTimeout((()=>{C.value=!1}),3e3)}}})("windows"))},[a[6]||(a[6]=r("svg",{class:"icon window-show download-icon","aria-hidden":"true"},[r("use",{"xlink:href":"#icon-windows"})],-1)),a[7]||(a[7]=r("svg",{class:"icon window-hidden download-icon","aria-hidden":"true"},[r("use",{"xlink:href":"#icon-xiazai"})],-1)),a[8]||(a[8]=r("br",null,null,-1)),c(H,{class:"window-show download-text",underline:!1},{default:d((()=>a[4]||(a[4]=[f(" Windows客户端 ")]))),_:1,__:[4]}),c(H,{class:"window-hidden download-text",underline:!1},{default:d((()=>a[5]||(a[5]=[f(" 点击下载Windows客户端 ")]))),_:1,__:[5]}),z.value?(t(),i(P,{key:0,percentage:B.value,format:D,"stroke-width":10,class:"download-progress"},null,8,["percentage"])):p("",!0),C.value?(t(),w("div",x,"下载完成")):p("",!0)])),j.value?(t(),w("div",_,a[9]||(a[9]=[r("svg",{class:"icon mobile-notice-icon","aria-hidden":"true"},[r("use",{"xlink:href":"#icon-shouji"})],-1),r("h3",{class:"mobile-notice-title"},"移动端访问",-1),r("p",{class:"mobile-notice-text"},[f(" 客户端下载仅适用于桌面设备"),r("br"),f(" 请使用电脑访问此页面进行下载 ")],-1),r("div",{class:"mobile-notice-tips"},[r("p",null,"💡 您可以："),r("ul",null,[r("li",null,"使用电脑浏览器访问"),r("li",null,"发送链接到电脑端"),r("li",null,"扫描二维码在电脑上打开")])],-1)]))):p("",!0)])])),_:1})])),_:1},8,["class"])])),_:1})}}}),[["__scopeId","data-v-fd37253c"]]);export{F as default};
