/*! 
 Build based on gin-vue-admin 
 Time : 1754879545000 */
function e(){import("data:text/javascript,")}
/**
* @vue/shared v3.5.16
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**/
/*! #__NO_SIDE_EFFECTS__ */
function t(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return e=>e in t}!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerpolicy&&(t.referrerPolicy=e.referrerpolicy),"use-credentials"===e.crossorigin?t.credentials="include":"anonymous"===e.crossorigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const n={},r=[],o=()=>{},i=()=>!1,a=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),s=e=>e.startsWith("onUpdate:"),l=Object.assign,c=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},u=Object.prototype.hasOwnProperty,f=(e,t)=>u.call(e,t),p=Array.isArray,d=e=>"[object Map]"===S(e),h=e=>"[object Set]"===S(e),g=e=>"[object Date]"===S(e),m=e=>"function"==typeof e,v=e=>"string"==typeof e,y=e=>"symbol"==typeof e,b=e=>null!==e&&"object"==typeof e,_=e=>(b(e)||m(e))&&m(e.then)&&m(e.catch),w=Object.prototype.toString,S=e=>w.call(e),x=e=>"[object Object]"===S(e),C=e=>v(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,k=t(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),E=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},A=/-(\w)/g,T=E((e=>e.replace(A,((e,t)=>t?t.toUpperCase():"")))),O=/\B([A-Z])/g,I=E((e=>e.replace(O,"-$1").toLowerCase())),j=E((e=>e.charAt(0).toUpperCase()+e.slice(1))),L=E((e=>e?`on${j(e)}`:"")),M=(e,t)=>!Object.is(e,t),P=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},R=(e,t,n,r=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:r,value:n})},z=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let $;const D=()=>$||($="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{});function B(e){if(p(e)){const t={};for(let n=0;n<e.length;n++){const r=e[n],o=v(r)?V(r):B(r);if(o)for(const e in o)t[e]=o[e]}return t}if(v(e)||b(e))return e}const N=/;(?![^(]*\))/g,F=/:([^]+)/,H=/\/\*[^]*?\*\//g;function V(e){const t={};return e.replace(H,"").split(N).forEach((e=>{if(e){const n=e.split(F);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}function U(e){let t="";if(v(e))t=e;else if(p(e))for(let n=0;n<e.length;n++){const r=U(e[n]);r&&(t+=r+" ")}else if(b(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const W=t("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function q(e){return!!e||""===e}function K(e,t){if(e===t)return!0;let n=g(e),r=g(t);if(n||r)return!(!n||!r)&&e.getTime()===t.getTime();if(n=y(e),r=y(t),n||r)return e===t;if(n=p(e),r=p(t),n||r)return!(!n||!r)&&function(e,t){if(e.length!==t.length)return!1;let n=!0;for(let r=0;n&&r<e.length;r++)n=K(e[r],t[r]);return n}(e,t);if(n=b(e),r=b(t),n||r){if(!n||!r)return!1;if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e){const r=e.hasOwnProperty(n),o=t.hasOwnProperty(n);if(r&&!o||!r&&o||!K(e[n],t[n]))return!1}}return String(e)===String(t)}function G(e,t){return e.findIndex((e=>K(e,t)))}const Q=e=>!(!e||!0!==e.__v_isRef),J=e=>v(e)?e:null==e?"":p(e)||b(e)&&(e.toString===w||!m(e.toString))?Q(e)?J(e.value):JSON.stringify(e,X,2):String(e),X=(e,t)=>Q(t)?X(e,t.value):d(t)?{[`Map(${t.size})`]:[...t.entries()].reduce(((e,[t,n],r)=>(e[Z(t,r)+" =>"]=n,e)),{})}:h(t)?{[`Set(${t.size})`]:[...t.values()].map((e=>Z(e)))}:y(t)?Z(t):!b(t)||p(t)||x(t)?t:String(t),Z=(e,t="")=>{var n;return y(e)?`Symbol(${null!=(n=e.description)?n:t})`:e};
/**
* @vue/reactivity v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/
let Y,ee;class te{constructor(e=!1){this.detached=e,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Y,!e&&Y&&(this.index=(Y.scopes||(Y.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){let e,t;if(this._isPaused=!0,this.scopes)for(e=0,t=this.scopes.length;e<t;e++)this.scopes[e].pause();for(e=0,t=this.effects.length;e<t;e++)this.effects[e].pause()}}resume(){if(this._active&&this._isPaused){let e,t;if(this._isPaused=!1,this.scopes)for(e=0,t=this.scopes.length;e<t;e++)this.scopes[e].resume();for(e=0,t=this.effects.length;e<t;e++)this.effects[e].resume()}}run(e){if(this._active){const t=Y;try{return Y=this,e()}finally{Y=t}}}on(){1===++this._on&&(this.prevScope=Y,Y=this)}off(){this._on>0&&0===--this._on&&(Y=this.prevScope,this.prevScope=void 0)}stop(e){if(this._active){let t,n;for(this._active=!1,t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(this.effects.length=0,t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.cleanups.length=0,this.scopes){for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!e){const e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0}}}function ne(e){return new te(e)}function re(){return Y}const oe=new WeakSet;class ie{constructor(e){this.fn=e,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Y&&Y.active&&Y.effects.push(this)}pause(){this.flags|=64}resume(){64&this.flags&&(this.flags&=-65,oe.has(this)&&(oe.delete(this),this.trigger()))}notify(){2&this.flags&&!(32&this.flags)||8&this.flags||ce(this)}run(){if(!(1&this.flags))return this.fn();this.flags|=2,Se(this),pe(this);const e=ee,t=ye;ee=this,ye=!0;try{return this.fn()}finally{de(this),ee=e,ye=t,this.flags&=-3}}stop(){if(1&this.flags){for(let e=this.deps;e;e=e.nextDep)me(e);this.deps=this.depsTail=void 0,Se(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){64&this.flags?oe.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){he(this)&&this.run()}get dirty(){return he(this)}}let ae,se,le=0;function ce(e,t=!1){if(e.flags|=8,t)return e.next=se,void(se=e);e.next=ae,ae=e}function ue(){le++}function fe(){if(--le>0)return;if(se){let e=se;for(se=void 0;e;){const t=e.next;e.next=void 0,e.flags&=-9,e=t}}let e;for(;ae;){let n=ae;for(ae=void 0;n;){const r=n.next;if(n.next=void 0,n.flags&=-9,1&n.flags)try{n.trigger()}catch(t){e||(e=t)}n=r}}if(e)throw e}function pe(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function de(e){let t,n=e.depsTail,r=n;for(;r;){const e=r.prevDep;-1===r.version?(r===n&&(n=e),me(r),ve(r)):t=r,r.dep.activeLink=r.prevActiveLink,r.prevActiveLink=void 0,r=e}e.deps=t,e.depsTail=n}function he(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(ge(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function ge(e){if(4&e.flags&&!(16&e.flags))return;if(e.flags&=-17,e.globalVersion===xe)return;if(e.globalVersion=xe,!e.isSSR&&128&e.flags&&(!e.deps&&!e._dirty||!he(e)))return;e.flags|=2;const t=e.dep,n=ee,r=ye;ee=e,ye=!0;try{pe(e);const n=e.fn(e._value);(0===t.version||M(n,e._value))&&(e.flags|=128,e._value=n,t.version++)}catch(o){throw t.version++,o}finally{ee=n,ye=r,de(e),e.flags&=-3}}function me(e,t=!1){const{dep:n,prevSub:r,nextSub:o}=e;if(r&&(r.nextSub=o,e.prevSub=void 0),o&&(o.prevSub=r,e.nextSub=void 0),n.subs===e&&(n.subs=r,!r&&n.computed)){n.computed.flags&=-5;for(let e=n.computed.deps;e;e=e.nextDep)me(e,!0)}t||--n.sc||!n.map||n.map.delete(n.key)}function ve(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let ye=!0;const be=[];function _e(){be.push(ye),ye=!1}function we(){const e=be.pop();ye=void 0===e||e}function Se(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const e=ee;ee=void 0;try{t()}finally{ee=e}}}let xe=0;class Ce{constructor(e,t){this.sub=e,this.dep=t,this.version=t.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class ke{constructor(e){this.computed=e,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(e){if(!ee||!ye||ee===this.computed)return;let t=this.activeLink;if(void 0===t||t.sub!==ee)t=this.activeLink=new Ce(ee,this),ee.deps?(t.prevDep=ee.depsTail,ee.depsTail.nextDep=t,ee.depsTail=t):ee.deps=ee.depsTail=t,Ee(t);else if(-1===t.version&&(t.version=this.version,t.nextDep)){const e=t.nextDep;e.prevDep=t.prevDep,t.prevDep&&(t.prevDep.nextDep=e),t.prevDep=ee.depsTail,t.nextDep=void 0,ee.depsTail.nextDep=t,ee.depsTail=t,ee.deps===t&&(ee.deps=e)}return t}trigger(e){this.version++,xe++,this.notify(e)}notify(e){ue();try{0;for(let e=this.subs;e;e=e.prevSub)e.sub.notify()&&e.sub.dep.notify()}finally{fe()}}}function Ee(e){if(e.dep.sc++,4&e.sub.flags){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let e=t.deps;e;e=e.nextDep)Ee(e)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const Ae=new WeakMap,Te=Symbol(""),Oe=Symbol(""),Ie=Symbol("");function je(e,t,n){if(ye&&ee){let t=Ae.get(e);t||Ae.set(e,t=new Map);let r=t.get(n);r||(t.set(n,r=new ke),r.map=t,r.key=n),r.track()}}function Le(e,t,n,r,o,i){const a=Ae.get(e);if(!a)return void xe++;const s=e=>{e&&e.trigger()};if(ue(),"clear"===t)a.forEach(s);else{const o=p(e),i=o&&C(n);if(o&&"length"===n){const e=Number(r);a.forEach(((t,n)=>{("length"===n||n===Ie||!y(n)&&n>=e)&&s(t)}))}else switch((void 0!==n||a.has(void 0))&&s(a.get(n)),i&&s(a.get(Ie)),t){case"add":o?i&&s(a.get("length")):(s(a.get(Te)),d(e)&&s(a.get(Oe)));break;case"delete":o||(s(a.get(Te)),d(e)&&s(a.get(Oe)));break;case"set":d(e)&&s(a.get(Te))}}fe()}function Me(e){const t=yt(e);return t===e?t:(je(t,0,Ie),mt(e)?t:t.map(_t))}function Pe(e){return je(e=yt(e),0,Ie),e}const Re={__proto__:null,[Symbol.iterator](){return ze(this,Symbol.iterator,_t)},concat(...e){return Me(this).concat(...e.map((e=>p(e)?Me(e):e)))},entries(){return ze(this,"entries",(e=>(e[1]=_t(e[1]),e)))},every(e,t){return De(this,"every",e,t,void 0,arguments)},filter(e,t){return De(this,"filter",e,t,(e=>e.map(_t)),arguments)},find(e,t){return De(this,"find",e,t,_t,arguments)},findIndex(e,t){return De(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return De(this,"findLast",e,t,_t,arguments)},findLastIndex(e,t){return De(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return De(this,"forEach",e,t,void 0,arguments)},includes(...e){return Ne(this,"includes",e)},indexOf(...e){return Ne(this,"indexOf",e)},join(e){return Me(this).join(e)},lastIndexOf(...e){return Ne(this,"lastIndexOf",e)},map(e,t){return De(this,"map",e,t,void 0,arguments)},pop(){return Fe(this,"pop")},push(...e){return Fe(this,"push",e)},reduce(e,...t){return Be(this,"reduce",e,t)},reduceRight(e,...t){return Be(this,"reduceRight",e,t)},shift(){return Fe(this,"shift")},some(e,t){return De(this,"some",e,t,void 0,arguments)},splice(...e){return Fe(this,"splice",e)},toReversed(){return Me(this).toReversed()},toSorted(e){return Me(this).toSorted(e)},toSpliced(...e){return Me(this).toSpliced(...e)},unshift(...e){return Fe(this,"unshift",e)},values(){return ze(this,"values",_t)}};function ze(e,t,n){const r=Pe(e),o=r[t]();return r===e||mt(e)||(o._next=o.next,o.next=()=>{const e=o._next();return e.value&&(e.value=n(e.value)),e}),o}const $e=Array.prototype;function De(e,t,n,r,o,i){const a=Pe(e),s=a!==e&&!mt(e),l=a[t];if(l!==$e[t]){const t=l.apply(e,i);return s?_t(t):t}let c=n;a!==e&&(s?c=function(t,r){return n.call(this,_t(t),r,e)}:n.length>2&&(c=function(t,r){return n.call(this,t,r,e)}));const u=l.call(a,c,r);return s&&o?o(u):u}function Be(e,t,n,r){const o=Pe(e);let i=n;return o!==e&&(mt(e)?n.length>3&&(i=function(t,r,o){return n.call(this,t,r,o,e)}):i=function(t,r,o){return n.call(this,t,_t(r),o,e)}),o[t](i,...r)}function Ne(e,t,n){const r=yt(e);je(r,0,Ie);const o=r[t](...n);return-1!==o&&!1!==o||!vt(n[0])?o:(n[0]=yt(n[0]),r[t](...n))}function Fe(e,t,n=[]){_e(),ue();const r=yt(e)[t].apply(e,n);return fe(),we(),r}const He=t("__proto__,__v_isRef,__isVue"),Ve=new Set(Object.getOwnPropertyNames(Symbol).filter((e=>"arguments"!==e&&"caller"!==e)).map((e=>Symbol[e])).filter(y));function Ue(e){y(e)||(e=String(e));const t=yt(this);return je(t,0,e),t.hasOwnProperty(e)}class We{constructor(e=!1,t=!1){this._isReadonly=e,this._isShallow=t}get(e,t,n){if("__v_skip"===t)return e.__v_skip;const r=this._isReadonly,o=this._isShallow;if("__v_isReactive"===t)return!r;if("__v_isReadonly"===t)return r;if("__v_isShallow"===t)return o;if("__v_raw"===t)return n===(r?o?lt:st:o?at:it).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(n)?e:void 0;const i=p(e);if(!r){let e;if(i&&(e=Re[t]))return e;if("hasOwnProperty"===t)return Ue}const a=Reflect.get(e,t,St(e)?e:n);return(y(t)?Ve.has(t):He(t))?a:(r||je(e,0,t),o?a:St(a)?i&&C(t)?a:a.value:b(a)?r?pt(a):ut(a):a)}}class qe extends We{constructor(e=!1){super(!1,e)}set(e,t,n,r){let o=e[t];if(!this._isShallow){const t=gt(o);if(mt(n)||gt(n)||(o=yt(o),n=yt(n)),!p(e)&&St(o)&&!St(n))return!t&&(o.value=n,!0)}const i=p(e)&&C(t)?Number(t)<e.length:f(e,t),a=Reflect.set(e,t,n,St(e)?e:r);return e===yt(r)&&(i?M(n,o)&&Le(e,"set",t,n):Le(e,"add",t,n)),a}deleteProperty(e,t){const n=f(e,t);e[t];const r=Reflect.deleteProperty(e,t);return r&&n&&Le(e,"delete",t,void 0),r}has(e,t){const n=Reflect.has(e,t);return y(t)&&Ve.has(t)||je(e,0,t),n}ownKeys(e){return je(e,0,p(e)?"length":Te),Reflect.ownKeys(e)}}class Ke extends We{constructor(e=!1){super(!0,e)}set(e,t){return!0}deleteProperty(e,t){return!0}}const Ge=new qe,Qe=new Ke,Je=new qe(!0),Xe=e=>e,Ze=e=>Reflect.getPrototypeOf(e);function Ye(e){return function(...t){return"delete"!==e&&("clear"===e?void 0:this)}}function et(e,t){const n={get(n){const r=this.__v_raw,o=yt(r),i=yt(n);e||(M(n,i)&&je(o,0,n),je(o,0,i));const{has:a}=Ze(o),s=t?Xe:e?wt:_t;return a.call(o,n)?s(r.get(n)):a.call(o,i)?s(r.get(i)):void(r!==o&&r.get(n))},get size(){const t=this.__v_raw;return!e&&je(yt(t),0,Te),Reflect.get(t,"size",t)},has(t){const n=this.__v_raw,r=yt(n),o=yt(t);return e||(M(t,o)&&je(r,0,t),je(r,0,o)),t===o?n.has(t):n.has(t)||n.has(o)},forEach(n,r){const o=this,i=o.__v_raw,a=yt(i),s=t?Xe:e?wt:_t;return!e&&je(a,0,Te),i.forEach(((e,t)=>n.call(r,s(e),s(t),o)))}};l(n,e?{add:Ye("add"),set:Ye("set"),delete:Ye("delete"),clear:Ye("clear")}:{add(e){t||mt(e)||gt(e)||(e=yt(e));const n=yt(this);return Ze(n).has.call(n,e)||(n.add(e),Le(n,"add",e,e)),this},set(e,n){t||mt(n)||gt(n)||(n=yt(n));const r=yt(this),{has:o,get:i}=Ze(r);let a=o.call(r,e);a||(e=yt(e),a=o.call(r,e));const s=i.call(r,e);return r.set(e,n),a?M(n,s)&&Le(r,"set",e,n):Le(r,"add",e,n),this},delete(e){const t=yt(this),{has:n,get:r}=Ze(t);let o=n.call(t,e);o||(e=yt(e),o=n.call(t,e)),r&&r.call(t,e);const i=t.delete(e);return o&&Le(t,"delete",e,void 0),i},clear(){const e=yt(this),t=0!==e.size,n=e.clear();return t&&Le(e,"clear",void 0,void 0),n}});return["keys","values","entries",Symbol.iterator].forEach((r=>{n[r]=function(e,t,n){return function(...r){const o=this.__v_raw,i=yt(o),a=d(i),s="entries"===e||e===Symbol.iterator&&a,l="keys"===e&&a,c=o[e](...r),u=n?Xe:t?wt:_t;return!t&&je(i,0,l?Oe:Te),{next(){const{value:e,done:t}=c.next();return t?{value:e,done:t}:{value:s?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}(r,e,t)})),n}function tt(e,t){const n=et(e,t);return(t,r,o)=>"__v_isReactive"===r?!e:"__v_isReadonly"===r?e:"__v_raw"===r?t:Reflect.get(f(n,r)&&r in t?n:t,r,o)}const nt={get:tt(!1,!1)},rt={get:tt(!1,!0)},ot={get:tt(!0,!1)},it=new WeakMap,at=new WeakMap,st=new WeakMap,lt=new WeakMap;function ct(e){return e.__v_skip||!Object.isExtensible(e)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}((e=>S(e).slice(8,-1))(e))}function ut(e){return gt(e)?e:dt(e,!1,Ge,nt,it)}function ft(e){return dt(e,!1,Je,rt,at)}function pt(e){return dt(e,!0,Qe,ot,st)}function dt(e,t,n,r,o){if(!b(e))return e;if(e.__v_raw&&(!t||!e.__v_isReactive))return e;const i=ct(e);if(0===i)return e;const a=o.get(e);if(a)return a;const s=new Proxy(e,2===i?r:n);return o.set(e,s),s}function ht(e){return gt(e)?ht(e.__v_raw):!(!e||!e.__v_isReactive)}function gt(e){return!(!e||!e.__v_isReadonly)}function mt(e){return!(!e||!e.__v_isShallow)}function vt(e){return!!e&&!!e.__v_raw}function yt(e){const t=e&&e.__v_raw;return t?yt(t):e}function bt(e){return!f(e,"__v_skip")&&Object.isExtensible(e)&&R(e,"__v_skip",!0),e}const _t=e=>b(e)?ut(e):e,wt=e=>b(e)?pt(e):e;function St(e){return!!e&&!0===e.__v_isRef}function xt(e){return Ct(e,!1)}function Ct(e,t){return St(e)?e:new kt(e,t)}class kt{constructor(e,t){this.dep=new ke,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=t?e:yt(e),this._value=t?e:_t(e),this.__v_isShallow=t}get value(){return this.dep.track(),this._value}set value(e){const t=this._rawValue,n=this.__v_isShallow||mt(e)||gt(e);e=n?e:yt(e),M(e,t)&&(this._rawValue=e,this._value=n?e:_t(e),this.dep.trigger())}}function Et(e){return St(e)?e.value:e}const At={get:(e,t,n)=>"__v_raw"===t?e:Et(Reflect.get(e,t,n)),set:(e,t,n,r)=>{const o=e[t];return St(o)&&!St(n)?(o.value=n,!0):Reflect.set(e,t,n,r)}};function Tt(e){return ht(e)?e:new Proxy(e,At)}class Ot{constructor(e,t,n){this._object=e,this._key=t,this._defaultValue=n,this.__v_isRef=!0,this._value=void 0}get value(){const e=this._object[this._key];return this._value=void 0===e?this._defaultValue:e}set value(e){this._object[this._key]=e}get dep(){return function(e,t){const n=Ae.get(e);return n&&n.get(t)}(yt(this._object),this._key)}}function It(e,t,n){const r=e[t];return St(r)?r:new Ot(e,t,n)}class jt{constructor(e,t,n){this.fn=e,this.setter=t,this._value=void 0,this.dep=new ke(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=xe-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!t,this.isSSR=n}notify(){if(this.flags|=16,!(8&this.flags)&&ee!==this)return ce(this,!0),!0}get value(){const e=this.dep.track();return ge(this),e&&(e.version=this.dep.version),this._value}set value(e){this.setter&&this.setter(e)}}const Lt={},Mt=new WeakMap;let Pt;function Rt(e,t,r=n){const{immediate:i,deep:a,once:s,scheduler:l,augmentJob:u,call:f}=r,d=e=>a?e:mt(e)||!1===a||0===a?zt(e,1):zt(e);let h,g,v,y,b=!1,_=!1;if(St(e)?(g=()=>e.value,b=mt(e)):ht(e)?(g=()=>d(e),b=!0):p(e)?(_=!0,b=e.some((e=>ht(e)||mt(e))),g=()=>e.map((e=>St(e)?e.value:ht(e)?d(e):m(e)?f?f(e,2):e():void 0))):g=m(e)?t?f?()=>f(e,2):e:()=>{if(v){_e();try{v()}finally{we()}}const t=Pt;Pt=h;try{return f?f(e,3,[y]):e(y)}finally{Pt=t}}:o,t&&a){const e=g,t=!0===a?1/0:a;g=()=>zt(e(),t)}const w=re(),S=()=>{h.stop(),w&&w.active&&c(w.effects,h)};if(s&&t){const e=t;t=(...t)=>{e(...t),S()}}let x=_?new Array(e.length).fill(Lt):Lt;const C=e=>{if(1&h.flags&&(h.dirty||e))if(t){const e=h.run();if(a||b||(_?e.some(((e,t)=>M(e,x[t]))):M(e,x))){v&&v();const n=Pt;Pt=h;try{const n=[e,x===Lt?void 0:_&&x[0]===Lt?[]:x,y];x=e,f?f(t,3,n):t(...n)}finally{Pt=n}}}else h.run()};return u&&u(C),h=new ie(g),h.scheduler=l?()=>l(C,!1):C,y=e=>function(e,t=!1,n=Pt){if(n){let t=Mt.get(n);t||Mt.set(n,t=[]),t.push(e)}}(e,!1,h),v=h.onStop=()=>{const e=Mt.get(h);if(e){if(f)f(e,4);else for(const t of e)t();Mt.delete(h)}},t?i?C(!0):x=h.run():l?l(C.bind(null,!0),!0):h.run(),S.pause=h.pause.bind(h),S.resume=h.resume.bind(h),S.stop=S,S}function zt(e,t=1/0,n){if(t<=0||!b(e)||e.__v_skip)return e;if((n=n||new Set).has(e))return e;if(n.add(e),t--,St(e))zt(e.value,t,n);else if(p(e))for(let r=0;r<e.length;r++)zt(e[r],t,n);else if(h(e)||d(e))e.forEach((e=>{zt(e,t,n)}));else if(x(e)){for(const r in e)zt(e[r],t,n);for(const r of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,r)&&zt(e[r],t,n)}return e}
/**
* @vue/runtime-core v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function $t(e,t,n,r){try{return r?e(...r):e()}catch(o){Bt(o,t,n)}}function Dt(e,t,n,r){if(m(e)){const o=$t(e,t,n,r);return o&&_(o)&&o.catch((e=>{Bt(e,t,n)})),o}if(p(e)){const o=[];for(let i=0;i<e.length;i++)o.push(Dt(e[i],t,n,r));return o}}function Bt(e,t,r,o=!0){t&&t.vnode;const{errorHandler:i,throwUnhandledErrorInProduction:a}=t&&t.appContext.config||n;if(t){let n=t.parent;const o=t.proxy,a=`https://vuejs.org/error-reference/#runtime-${r}`;for(;n;){const t=n.ec;if(t)for(let n=0;n<t.length;n++)if(!1===t[n](e,o,a))return;n=n.parent}if(i)return _e(),$t(i,null,10,[e,o,a]),void we()}!function(e,t,n,r=!0,o=!1){if(o)throw e;console.error(e)}(e,0,0,o,a)}const Nt=[];let Ft=-1;const Ht=[];let Vt=null,Ut=0;const Wt=Promise.resolve();let qt=null;function Kt(e){const t=qt||Wt;return e?t.then(this?e.bind(this):e):t}function Gt(e){if(!(1&e.flags)){const t=Yt(e),n=Nt[Nt.length-1];!n||!(2&e.flags)&&t>=Yt(n)?Nt.push(e):Nt.splice(function(e){let t=Ft+1,n=Nt.length;for(;t<n;){const r=t+n>>>1,o=Nt[r],i=Yt(o);i<e||i===e&&2&o.flags?t=r+1:n=r}return t}(t),0,e),e.flags|=1,Qt()}}function Qt(){qt||(qt=Wt.then(en))}function Jt(e){p(e)?Ht.push(...e):Vt&&-1===e.id?Vt.splice(Ut+1,0,e):1&e.flags||(Ht.push(e),e.flags|=1),Qt()}function Xt(e,t,n=Ft+1){for(;n<Nt.length;n++){const t=Nt[n];if(t&&2&t.flags){if(e&&t.id!==e.uid)continue;Nt.splice(n,1),n--,4&t.flags&&(t.flags&=-2),t(),4&t.flags||(t.flags&=-2)}}}function Zt(e){if(Ht.length){const e=[...new Set(Ht)].sort(((e,t)=>Yt(e)-Yt(t)));if(Ht.length=0,Vt)return void Vt.push(...e);for(Vt=e,Ut=0;Ut<Vt.length;Ut++){const e=Vt[Ut];4&e.flags&&(e.flags&=-2),8&e.flags||e(),e.flags&=-2}Vt=null,Ut=0}}const Yt=e=>null==e.id?2&e.flags?-1:1/0:e.id;function en(e){try{for(Ft=0;Ft<Nt.length;Ft++){const e=Nt[Ft];!e||8&e.flags||(4&e.flags&&(e.flags&=-2),$t(e,e.i,e.i?15:14),4&e.flags||(e.flags&=-2))}}finally{for(;Ft<Nt.length;Ft++){const e=Nt[Ft];e&&(e.flags&=-2)}Ft=-1,Nt.length=0,Zt(),qt=null,(Nt.length||Ht.length)&&en()}}let tn=null,nn=null;function rn(e){const t=tn;return tn=e,nn=e&&e.type.__scopeId||null,t}function on(e,t=tn,n){if(!t)return e;if(e._n)return e;const r=(...n)=>{r._d&&jo(-1);const o=rn(t);let i;try{i=e(...n)}finally{rn(o),r._d&&jo(1)}return i};return r._n=!0,r._c=!0,r._d=!0,r}function an(e,t){if(null===tn)return e;const r=ci(tn),o=e.dirs||(e.dirs=[]);for(let i=0;i<t.length;i++){let[e,a,s,l=n]=t[i];e&&(m(e)&&(e={mounted:e,updated:e}),e.deep&&zt(a),o.push({dir:e,instance:r,value:a,oldValue:void 0,arg:s,modifiers:l}))}return e}function sn(e,t,n,r){const o=e.dirs,i=t&&t.dirs;for(let a=0;a<o.length;a++){const s=o[a];i&&(s.oldValue=i[a].value);let l=s.dir[r];l&&(_e(),Dt(l,n,8,[e.el,s,e,t]),we())}}const ln=Symbol("_vte"),cn=e=>e.__isTeleport,un=e=>e&&(e.disabled||""===e.disabled),fn=e=>e&&(e.defer||""===e.defer),pn=e=>"undefined"!=typeof SVGElement&&e instanceof SVGElement,dn=e=>"function"==typeof MathMLElement&&e instanceof MathMLElement,hn=(e,t)=>{const n=e&&e.to;if(v(n)){if(t){return t(n)}return null}return n},gn={name:"Teleport",__isTeleport:!0,process(e,t,n,r,o,i,a,s,l,c){const{mc:u,pc:f,pbc:p,o:{insert:d,querySelector:h,createText:g,createComment:m}}=c,v=un(t.props);let{shapeFlag:y,children:b,dynamicChildren:_}=t;if(null==e){const e=t.el=g(""),c=t.anchor=g("");d(e,n,r),d(c,n,r);const f=(e,t)=>{16&y&&(o&&o.isCE&&(o.ce._teleportTarget=e),u(b,e,t,o,i,a,s,l))},p=()=>{const e=t.target=hn(t.props,h),n=bn(e,t,g,d);e&&("svg"!==a&&pn(e)?a="svg":"mathml"!==a&&dn(e)&&(a="mathml"),v||(f(e,n),yn(t,!1)))};v&&(f(n,c),yn(t,!0)),fn(t.props)?(t.el.__isMounted=!1,eo((()=>{p(),delete t.el.__isMounted}),i)):p()}else{if(fn(t.props)&&!1===e.el.__isMounted)return void eo((()=>{gn.process(e,t,n,r,o,i,a,s,l,c)}),i);t.el=e.el,t.targetStart=e.targetStart;const u=t.anchor=e.anchor,d=t.target=e.target,g=t.targetAnchor=e.targetAnchor,m=un(e.props),y=m?n:d,b=m?u:g;if("svg"===a||pn(d)?a="svg":("mathml"===a||dn(d))&&(a="mathml"),_?(p(e.dynamicChildren,_,y,o,i,a,s),oo(e,t,!0)):l||f(e,t,y,b,o,i,a,s,!1),v)m?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):mn(t,n,u,c,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const e=t.target=hn(t.props,h);e&&mn(t,e,null,c,0)}else m&&mn(t,d,g,c,1);yn(t,v)}},remove(e,t,n,{um:r,o:{remove:o}},i){const{shapeFlag:a,children:s,anchor:l,targetStart:c,targetAnchor:u,target:f,props:p}=e;if(f&&(o(c),o(u)),i&&o(l),16&a){const e=i||!un(p);for(let o=0;o<s.length;o++){const i=s[o];r(i,t,n,e,!!i.dynamicChildren)}}},move:mn,hydrate:function(e,t,n,r,o,i,{o:{nextSibling:a,parentNode:s,querySelector:l,insert:c,createText:u}},f){const p=t.target=hn(t.props,l);if(p){const l=un(t.props),d=p._lpa||p.firstChild;if(16&t.shapeFlag)if(l)t.anchor=f(a(e),t,s(e),n,r,o,i),t.targetStart=d,t.targetAnchor=d&&a(d);else{t.anchor=a(e);let s=d;for(;s;){if(s&&8===s.nodeType)if("teleport start anchor"===s.data)t.targetStart=s;else if("teleport anchor"===s.data){t.targetAnchor=s,p._lpa=t.targetAnchor&&a(t.targetAnchor);break}s=a(s)}t.targetAnchor||bn(p,t,u,c),f(d&&a(d),t,p,n,r,o,i)}yn(t,l)}return t.anchor&&a(t.anchor)}};function mn(e,t,n,{o:{insert:r},m:o},i=2){0===i&&r(e.targetAnchor,t,n);const{el:a,anchor:s,shapeFlag:l,children:c,props:u}=e,f=2===i;if(f&&r(a,t,n),(!f||un(u))&&16&l)for(let p=0;p<c.length;p++)o(c[p],t,n,2);f&&r(s,t,n)}const vn=gn;function yn(e,t){const n=e.ctx;if(n&&n.ut){let r,o;for(t?(r=e.el,o=e.anchor):(r=e.targetStart,o=e.targetAnchor);r&&r!==o;)1===r.nodeType&&r.setAttribute("data-v-owner",n.uid),r=r.nextSibling;n.ut()}}function bn(e,t,n,r){const o=t.targetStart=n(""),i=t.targetAnchor=n("");return o[ln]=i,e&&(r(o,e),r(i,e)),i}const _n=Symbol("_leaveCb"),wn=Symbol("_enterCb");const Sn=[Function,Array],xn={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:Sn,onEnter:Sn,onAfterEnter:Sn,onEnterCancelled:Sn,onBeforeLeave:Sn,onLeave:Sn,onAfterLeave:Sn,onLeaveCancelled:Sn,onBeforeAppear:Sn,onAppear:Sn,onAfterAppear:Sn,onAppearCancelled:Sn},Cn=e=>{const t=e.subTree;return t.component?Cn(t.component):t};function kn(e){let t=e[0];if(e.length>1)for(const n of e)if(n.type!==ko){t=n;break}return t}const En={name:"BaseTransition",props:xn,setup(e,{slots:t}){const n=Zo(),r=function(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Zn((()=>{e.isMounted=!0})),tr((()=>{e.isUnmounting=!0})),e}();return()=>{const o=t.default&&Ln(t.default(),!0);if(!o||!o.length)return;const i=kn(o),a=yt(e),{mode:s}=a;if(r.isLeaving)return On(i);const l=In(i);if(!l)return On(i);let c=Tn(l,a,r,n,(e=>c=e));l.type!==ko&&jn(l,c);let u=n.subTree&&In(n.subTree);if(u&&u.type!==ko&&!zo(l,u)&&Cn(n).type!==ko){let e=Tn(u,a,r,n);if(jn(u,e),"out-in"===s&&l.type!==ko)return r.isLeaving=!0,e.afterLeave=()=>{r.isLeaving=!1,8&n.job.flags||n.update(),delete e.afterLeave,u=void 0},On(i);"in-out"===s&&l.type!==ko?e.delayLeave=(e,t,n)=>{An(r,u)[String(u.key)]=u,e[_n]=()=>{t(),e[_n]=void 0,delete c.delayedLeave,u=void 0},c.delayedLeave=()=>{n(),delete c.delayedLeave,u=void 0}}:u=void 0}else u&&(u=void 0);return i}}};function An(e,t){const{leavingVNodes:n}=e;let r=n.get(t.type);return r||(r=Object.create(null),n.set(t.type,r)),r}function Tn(e,t,n,r,o){const{appear:i,mode:a,persisted:s=!1,onBeforeEnter:l,onEnter:c,onAfterEnter:u,onEnterCancelled:f,onBeforeLeave:d,onLeave:h,onAfterLeave:g,onLeaveCancelled:m,onBeforeAppear:v,onAppear:y,onAfterAppear:b,onAppearCancelled:_}=t,w=String(e.key),S=An(n,e),x=(e,t)=>{e&&Dt(e,r,9,t)},C=(e,t)=>{const n=t[1];x(e,t),p(e)?e.every((e=>e.length<=1))&&n():e.length<=1&&n()},k={mode:a,persisted:s,beforeEnter(t){let r=l;if(!n.isMounted){if(!i)return;r=v||l}t[_n]&&t[_n](!0);const o=S[w];o&&zo(e,o)&&o.el[_n]&&o.el[_n](),x(r,[t])},enter(e){let t=c,r=u,o=f;if(!n.isMounted){if(!i)return;t=y||c,r=b||u,o=_||f}let a=!1;const s=e[wn]=t=>{a||(a=!0,x(t?o:r,[e]),k.delayedLeave&&k.delayedLeave(),e[wn]=void 0)};t?C(t,[e,s]):s()},leave(t,r){const o=String(e.key);if(t[wn]&&t[wn](!0),n.isUnmounting)return r();x(d,[t]);let i=!1;const a=t[_n]=n=>{i||(i=!0,r(),x(n?m:g,[t]),t[_n]=void 0,S[o]===e&&delete S[o])};S[o]=e,h?C(h,[t,a]):a()},clone(e){const i=Tn(e,t,n,r,o);return o&&o(i),i}};return k}function On(e){if(Nn(e))return(e=Fo(e)).children=null,e}function In(e){if(!Nn(e))return cn(e.type)&&e.children?kn(e.children):e;if(e.component)return e.component.subTree;const{shapeFlag:t,children:n}=e;if(n){if(16&t)return n[0];if(32&t&&m(n.default))return n.default()}}function jn(e,t){6&e.shapeFlag&&e.component?(e.transition=t,jn(e.component.subTree,t)):128&e.shapeFlag?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Ln(e,t=!1,n){let r=[],o=0;for(let i=0;i<e.length;i++){let a=e[i];const s=null==n?a.key:String(n)+String(null!=a.key?a.key:i);a.type===xo?(128&a.patchFlag&&o++,r=r.concat(Ln(a.children,t,s))):(t||a.type!==ko)&&r.push(null!=s?Fo(a,{key:s}):a)}if(o>1)for(let i=0;i<r.length;i++)r[i].patchFlag=-2;return r}
/*! #__NO_SIDE_EFFECTS__ */function Mn(e,t){return m(e)?(()=>l({name:e.name},t,{setup:e}))():e}function Pn(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Rn(e,t,r,o,i=!1){if(p(e))return void e.forEach(((e,n)=>Rn(e,t&&(p(t)?t[n]:t),r,o,i)));if($n(o)&&!i)return void(512&o.shapeFlag&&o.type.__asyncResolved&&o.component.subTree.component&&Rn(e,t,r,o.component.subTree));const a=4&o.shapeFlag?ci(o.component):o.el,s=i?null:a,{i:l,r:u}=e,d=t&&t.r,h=l.refs===n?l.refs={}:l.refs,g=l.setupState,y=yt(g),b=g===n?()=>!1:e=>f(y,e);if(null!=d&&d!==u&&(v(d)?(h[d]=null,b(d)&&(g[d]=null)):St(d)&&(d.value=null)),m(u))$t(u,l,12,[s,h]);else{const t=v(u),n=St(u);if(t||n){const o=()=>{if(e.f){const n=t?b(u)?g[u]:h[u]:u.value;i?p(n)&&c(n,a):p(n)?n.includes(a)||n.push(a):t?(h[u]=[a],b(u)&&(g[u]=h[u])):(u.value=[a],e.k&&(h[e.k]=u.value))}else t?(h[u]=s,b(u)&&(g[u]=s)):n&&(u.value=s,e.k&&(h[e.k]=s))};s?(o.id=-1,eo(o,r)):o()}}}const zn=e=>8===e.nodeType;D().requestIdleCallback,D().cancelIdleCallback;const $n=e=>!!e.type.__asyncLoader;
/*! #__NO_SIDE_EFFECTS__ */function Dn(e){m(e)&&(e={loader:e});const{loader:t,loadingComponent:n,errorComponent:r,delay:o=200,hydrate:i,timeout:a,suspensible:s=!0,onError:l}=e;let c,u=null,f=0;const p=()=>{let e;return u||(e=u=t().catch((e=>{if(e=e instanceof Error?e:new Error(String(e)),l)return new Promise(((t,n)=>{l(e,(()=>t((f++,u=null,p()))),(()=>n(e)),f+1)}));throw e})).then((t=>e!==u&&u?u:(t&&(t.__esModule||"Module"===t[Symbol.toStringTag])&&(t=t.default),c=t,t))))};return Mn({name:"AsyncComponentWrapper",__asyncLoader:p,__asyncHydrate(e,t,n){const r=i?()=>{const r=i((()=>{n()}),(t=>function(e,t){if(zn(e)&&"["===e.data){let n=1,r=e.nextSibling;for(;r;){if(1===r.nodeType){if(!1===t(r))break}else if(zn(r))if("]"===r.data){if(0===--n)break}else"["===r.data&&n++;r=r.nextSibling}}else t(e)}(e,t)));r&&(t.bum||(t.bum=[])).push(r),(t.u||(t.u=[])).push((()=>!0))}:n;c?r():p().then((()=>!t.isUnmounted&&r()))},get __asyncResolved(){return c},setup(){const e=Xo;if(Pn(e),c)return()=>Bn(c,e);const t=t=>{u=null,Bt(t,e,13,!r)};if(s&&e.suspense||ii)return p().then((t=>()=>Bn(t,e))).catch((e=>(t(e),()=>r?No(r,{error:e}):null)));const i=xt(!1),l=xt(),f=xt(!!o);return o&&setTimeout((()=>{f.value=!1}),o),null!=a&&setTimeout((()=>{if(!i.value&&!l.value){const e=new Error(`Async component timed out after ${a}ms.`);t(e),l.value=e}}),a),p().then((()=>{i.value=!0,e.parent&&Nn(e.parent.vnode)&&e.parent.update()})).catch((e=>{t(e),l.value=e})),()=>i.value&&c?Bn(c,e):l.value&&r?No(r,{error:l.value}):n&&!f.value?No(n):void 0}})}function Bn(e,t){const{ref:n,props:r,children:o,ce:i}=t.vnode,a=No(e,r,o);return a.ref=n,a.ce=i,delete t.vnode.ce,a}const Nn=e=>e.type.__isKeepAlive,Fn={name:"KeepAlive",__isKeepAlive:!0,props:{include:[String,RegExp,Array],exclude:[String,RegExp,Array],max:[String,Number]},setup(e,{slots:t}){const n=Zo(),r=n.ctx;if(!r.renderer)return()=>{const e=t.default&&t.default();return e&&1===e.length?e[0]:e};const o=new Map,i=new Set;let a=null;const s=n.suspense,{renderer:{p:l,m:c,um:u,o:{createElement:f}}}=r,p=f("div");function d(e){Kn(e),u(e,n,s,!0)}function h(e){o.forEach(((t,n)=>{const r=ui(t.type);r&&!e(r)&&g(n)}))}function g(e){const t=o.get(e);!t||a&&zo(t,a)?a&&Kn(a):d(t),o.delete(e),i.delete(e)}r.activate=(e,t,n,r,o)=>{const i=e.component;c(e,t,n,0,s),l(i.vnode,e,t,n,i,s,r,e.slotScopeIds,o),eo((()=>{i.isDeactivated=!1,i.a&&P(i.a);const t=e.props&&e.props.onVnodeMounted;t&&Go(t,i.parent,e)}),s)},r.deactivate=e=>{const t=e.component;ao(t.m),ao(t.a),c(e,p,null,1,s),eo((()=>{t.da&&P(t.da);const n=e.props&&e.props.onVnodeUnmounted;n&&Go(n,t.parent,e),t.isDeactivated=!0}),s)},co((()=>[e.include,e.exclude]),(([e,t])=>{e&&h((t=>Hn(e,t))),t&&h((e=>!Hn(t,e)))}),{flush:"post",deep:!0});let m=null;const v=()=>{null!=m&&(So(n.subTree.type)?eo((()=>{o.set(m,Gn(n.subTree))}),n.subTree.suspense):o.set(m,Gn(n.subTree)))};return Zn(v),er(v),tr((()=>{o.forEach((e=>{const{subTree:t,suspense:r}=n,o=Gn(t);if(e.type!==o.type||e.key!==o.key)d(e);else{Kn(o);const e=o.component.da;e&&eo(e,r)}}))})),()=>{if(m=null,!t.default)return a=null;const n=t.default(),r=n[0];if(n.length>1)return a=null,n;if(!(Ro(r)&&(4&r.shapeFlag||128&r.shapeFlag)))return a=null,r;let s=Gn(r);if(s.type===ko)return a=null,s;const l=s.type,c=ui($n(s)?s.type.__asyncResolved||{}:l),{include:u,exclude:f,max:p}=e;if(u&&(!c||!Hn(u,c))||f&&c&&Hn(f,c))return s.shapeFlag&=-257,a=s,r;const d=null==s.key?l:s.key,h=o.get(d);return s.el&&(s=Fo(s),128&r.shapeFlag&&(r.ssContent=s)),m=d,h?(s.el=h.el,s.component=h.component,s.transition&&jn(s,s.transition),s.shapeFlag|=512,i.delete(d),i.add(d)):(i.add(d),p&&i.size>parseInt(p,10)&&g(i.values().next().value)),s.shapeFlag|=256,a=s,So(r.type)?r:s}}};function Hn(e,t){return p(e)?e.some((e=>Hn(e,t))):v(e)?e.split(",").includes(t):"[object RegExp]"===S(e)&&(e.lastIndex=0,e.test(t))}function Vn(e,t){Wn(e,"a",t)}function Un(e,t){Wn(e,"da",t)}function Wn(e,t,n=Xo){const r=e.__wdc||(e.__wdc=()=>{let t=n;for(;t;){if(t.isDeactivated)return;t=t.parent}return e()});if(Qn(t,r,n),n){let e=n.parent;for(;e&&e.parent;)Nn(e.parent.vnode)&&qn(r,t,n,e),e=e.parent}}function qn(e,t,n,r){const o=Qn(t,e,r,!0);nr((()=>{c(r[t],o)}),n)}function Kn(e){e.shapeFlag&=-257,e.shapeFlag&=-513}function Gn(e){return 128&e.shapeFlag?e.ssContent:e}function Qn(e,t,n=Xo,r=!1){if(n){const o=n[e]||(n[e]=[]),i=t.__weh||(t.__weh=(...r)=>{_e();const o=ti(n),i=Dt(t,n,e,r);return o(),we(),i});return r?o.unshift(i):o.push(i),i}}const Jn=e=>(t,n=Xo)=>{ii&&"sp"!==e||Qn(e,((...e)=>t(...e)),n)},Xn=Jn("bm"),Zn=Jn("m"),Yn=Jn("bu"),er=Jn("u"),tr=Jn("bum"),nr=Jn("um"),rr=Jn("sp"),or=Jn("rtg"),ir=Jn("rtc");function ar(e,t=Xo){Qn("ec",e,t)}const sr="components";function lr(e,t){return pr(sr,e,!0,t)||e}const cr=Symbol.for("v-ndc");function ur(e){return v(e)?pr(sr,e,!1)||e:e||cr}function fr(e){return pr("directives",e)}function pr(e,t,n=!0,r=!1){const o=tn||Xo;if(o){const n=o.type;if(e===sr){const e=ui(n,!1);if(e&&(e===t||e===T(t)||e===j(T(t))))return n}const i=dr(o[e]||n[e],t)||dr(o.appContext[e],t);return!i&&r?n:i}}function dr(e,t){return e&&(e[t]||e[T(t)]||e[j(T(t))])}function hr(e,t,n,r){let o;const i=n&&n[r],a=p(e);if(a||v(e)){let n=!1,r=!1;a&&ht(e)&&(n=!mt(e),r=gt(e),e=Pe(e)),o=new Array(e.length);for(let a=0,s=e.length;a<s;a++)o[a]=t(n?r?wt(_t(e[a])):_t(e[a]):e[a],a,void 0,i&&i[a])}else if("number"==typeof e){o=new Array(e);for(let n=0;n<e;n++)o[n]=t(n+1,n,void 0,i&&i[n])}else if(b(e))if(e[Symbol.iterator])o=Array.from(e,((e,n)=>t(e,n,void 0,i&&i[n])));else{const n=Object.keys(e);o=new Array(n.length);for(let r=0,a=n.length;r<a;r++){const a=n[r];o[r]=t(e[a],a,r,i&&i[r])}}else o=[];return n&&(n[r]=o),o}function gr(e,t,n={},r,o){if(tn.ce||tn.parent&&$n(tn.parent)&&tn.parent.ce)return"default"!==t&&(n.name=t),Oo(),Po(xo,null,[No("slot",n,r&&r())],64);let i=e[t];i&&i._c&&(i._d=!1),Oo();const a=i&&mr(i(n)),s=n.key||a&&a.key,l=Po(xo,{key:(s&&!y(s)?s:`_${t}`)+(!a&&r?"_fb":"")},a||(r?r():[]),a&&1===e._?64:-2);return!o&&l.scopeId&&(l.slotScopeIds=[l.scopeId+"-s"]),i&&i._c&&(i._d=!0),l}function mr(e){return e.some((e=>!Ro(e)||e.type!==ko&&!(e.type===xo&&!mr(e.children))))?e:null}const vr=e=>e?ri(e)?ci(e):vr(e.parent):null,yr=l(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>vr(e.parent),$root:e=>vr(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Er(e),$forceUpdate:e=>e.f||(e.f=()=>{Gt(e.update)}),$nextTick:e=>e.n||(e.n=Kt.bind(e.proxy)),$watch:e=>fo.bind(e)}),br=(e,t)=>e!==n&&!e.__isScriptSetup&&f(e,t),_r={get({_:e},t){if("__v_skip"===t)return!0;const{ctx:r,setupState:o,data:i,props:a,accessCache:s,type:l,appContext:c}=e;let u;if("$"!==t[0]){const l=s[t];if(void 0!==l)switch(l){case 1:return o[t];case 2:return i[t];case 4:return r[t];case 3:return a[t]}else{if(br(o,t))return s[t]=1,o[t];if(i!==n&&f(i,t))return s[t]=2,i[t];if((u=e.propsOptions[0])&&f(u,t))return s[t]=3,a[t];if(r!==n&&f(r,t))return s[t]=4,r[t];Sr&&(s[t]=0)}}const p=yr[t];let d,h;return p?("$attrs"===t&&je(e.attrs,0,""),p(e)):(d=l.__cssModules)&&(d=d[t])?d:r!==n&&f(r,t)?(s[t]=4,r[t]):(h=c.config.globalProperties,f(h,t)?h[t]:void 0)},set({_:e},t,r){const{data:o,setupState:i,ctx:a}=e;return br(i,t)?(i[t]=r,!0):o!==n&&f(o,t)?(o[t]=r,!0):!f(e.props,t)&&(("$"!==t[0]||!(t.slice(1)in e))&&(a[t]=r,!0))},has({_:{data:e,setupState:t,accessCache:r,ctx:o,appContext:i,propsOptions:a}},s){let l;return!!r[s]||e!==n&&f(e,s)||br(t,s)||(l=a[0])&&f(l,s)||f(o,s)||f(yr,s)||f(i.config.globalProperties,s)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:f(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function wr(e){return p(e)?e.reduce(((e,t)=>(e[t]=null,e)),{}):e}let Sr=!0;function xr(e){const t=Er(e),n=e.proxy,r=e.ctx;Sr=!1,t.beforeCreate&&Cr(t.beforeCreate,e,"bc");const{data:i,computed:a,methods:s,watch:l,provide:c,inject:u,created:f,beforeMount:d,mounted:h,beforeUpdate:g,updated:v,activated:y,deactivated:_,beforeDestroy:w,beforeUnmount:S,destroyed:x,unmounted:C,render:k,renderTracked:E,renderTriggered:A,errorCaptured:T,serverPrefetch:O,expose:I,inheritAttrs:j,components:L,directives:M,filters:P}=t;if(u&&function(e,t){p(e)&&(e=Ir(e));for(const n in e){const r=e[n];let o;o=b(r)?"default"in r?Br(r.from||n,r.default,!0):Br(r.from||n):Br(r),St(o)?Object.defineProperty(t,n,{enumerable:!0,configurable:!0,get:()=>o.value,set:e=>o.value=e}):t[n]=o}}(u,r,null),s)for(const o in s){const e=s[o];m(e)&&(r[o]=e.bind(n))}if(i){const t=i.call(n,n);b(t)&&(e.data=ut(t))}if(Sr=!0,a)for(const p in a){const e=a[p],t=m(e)?e.bind(n,n):m(e.get)?e.get.bind(n,n):o,i=!m(e)&&m(e.set)?e.set.bind(n):o,s=fi({get:t,set:i});Object.defineProperty(r,p,{enumerable:!0,configurable:!0,get:()=>s.value,set:e=>s.value=e})}if(l)for(const o in l)kr(l[o],r,n,o);if(c){const e=m(c)?c.call(n):c;Reflect.ownKeys(e).forEach((t=>{Dr(t,e[t])}))}function R(e,t){p(t)?t.forEach((t=>e(t.bind(n)))):t&&e(t.bind(n))}if(f&&Cr(f,e,"c"),R(Xn,d),R(Zn,h),R(Yn,g),R(er,v),R(Vn,y),R(Un,_),R(ar,T),R(ir,E),R(or,A),R(tr,S),R(nr,C),R(rr,O),p(I))if(I.length){const t=e.exposed||(e.exposed={});I.forEach((e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t})}))}else e.exposed||(e.exposed={});k&&e.render===o&&(e.render=k),null!=j&&(e.inheritAttrs=j),L&&(e.components=L),M&&(e.directives=M),O&&Pn(e)}function Cr(e,t,n){Dt(p(e)?e.map((e=>e.bind(t.proxy))):e.bind(t.proxy),t,n)}function kr(e,t,n,r){let o=r.includes(".")?po(n,r):()=>n[r];if(v(e)){const n=t[e];m(n)&&co(o,n)}else if(m(e))co(o,e.bind(n));else if(b(e))if(p(e))e.forEach((e=>kr(e,t,n,r)));else{const r=m(e.handler)?e.handler.bind(n):t[e.handler];m(r)&&co(o,r,e)}}function Er(e){const t=e.type,{mixins:n,extends:r}=t,{mixins:o,optionsCache:i,config:{optionMergeStrategies:a}}=e.appContext,s=i.get(t);let l;return s?l=s:o.length||n||r?(l={},o.length&&o.forEach((e=>Ar(l,e,a,!0))),Ar(l,t,a)):l=t,b(t)&&i.set(t,l),l}function Ar(e,t,n,r=!1){const{mixins:o,extends:i}=t;i&&Ar(e,i,n,!0),o&&o.forEach((t=>Ar(e,t,n,!0)));for(const a in t)if(r&&"expose"===a);else{const r=Tr[a]||n&&n[a];e[a]=r?r(e[a],t[a]):t[a]}return e}const Tr={data:Or,props:Mr,emits:Mr,methods:Lr,computed:Lr,beforeCreate:jr,created:jr,beforeMount:jr,mounted:jr,beforeUpdate:jr,updated:jr,beforeDestroy:jr,beforeUnmount:jr,destroyed:jr,unmounted:jr,activated:jr,deactivated:jr,errorCaptured:jr,serverPrefetch:jr,components:Lr,directives:Lr,watch:function(e,t){if(!e)return t;if(!t)return e;const n=l(Object.create(null),e);for(const r in t)n[r]=jr(e[r],t[r]);return n},provide:Or,inject:function(e,t){return Lr(Ir(e),Ir(t))}};function Or(e,t){return t?e?function(){return l(m(e)?e.call(this,this):e,m(t)?t.call(this,this):t)}:t:e}function Ir(e){if(p(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function jr(e,t){return e?[...new Set([].concat(e,t))]:t}function Lr(e,t){return e?l(Object.create(null),e,t):t}function Mr(e,t){return e?p(e)&&p(t)?[...new Set([...e,...t])]:l(Object.create(null),wr(e),wr(null!=t?t:{})):t}function Pr(){return{app:null,config:{isNativeTag:i,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Rr=0;function zr(e,t){return function(n,r=null){m(n)||(n=l({},n)),null==r||b(r)||(r=null);const o=Pr(),i=new WeakSet,a=[];let s=!1;const c=o.app={_uid:Rr++,_component:n,_props:r,_container:null,_context:o,_instance:null,version:di,get config(){return o.config},set config(e){},use:(e,...t)=>(i.has(e)||(e&&m(e.install)?(i.add(e),e.install(c,...t)):m(e)&&(i.add(e),e(c,...t))),c),mixin:e=>(o.mixins.includes(e)||o.mixins.push(e),c),component:(e,t)=>t?(o.components[e]=t,c):o.components[e],directive:(e,t)=>t?(o.directives[e]=t,c):o.directives[e],mount(i,a,l){if(!s){const u=c._ceVNode||No(n,r);return u.appContext=o,!0===l?l="svg":!1===l&&(l=void 0),a&&t?t(u,i):e(u,i,l),s=!0,c._container=i,i.__vue_app__=c,ci(u.component)}},onUnmount(e){a.push(e)},unmount(){s&&(Dt(a,c._instance,16),e(null,c._container),delete c._container.__vue_app__)},provide:(e,t)=>(o.provides[e]=t,c),runWithContext(e){const t=$r;$r=c;try{return e()}finally{$r=t}}};return c}}let $r=null;function Dr(e,t){if(Xo){let n=Xo.provides;const r=Xo.parent&&Xo.parent.provides;r===n&&(n=Xo.provides=Object.create(r)),n[e]=t}else;}function Br(e,t,n=!1){const r=Xo||tn;if(r||$r){let o=$r?$r._context.provides:r?null==r.parent||r.ce?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:void 0;if(o&&e in o)return o[e];if(arguments.length>1)return n&&m(t)?t.call(r&&r.proxy):t}}const Nr={},Fr=()=>Object.create(Nr),Hr=e=>Object.getPrototypeOf(e)===Nr;function Vr(e,t,r,o){const[i,a]=e.propsOptions;let s,l=!1;if(t)for(let n in t){if(k(n))continue;const c=t[n];let u;i&&f(i,u=T(n))?a&&a.includes(u)?(s||(s={}))[u]=c:r[u]=c:vo(e.emitsOptions,n)||n in o&&c===o[n]||(o[n]=c,l=!0)}if(a){const t=yt(r),o=s||n;for(let n=0;n<a.length;n++){const s=a[n];r[s]=Ur(i,t,s,o[s],e,!f(o,s))}}return l}function Ur(e,t,n,r,o,i){const a=e[n];if(null!=a){const e=f(a,"default");if(e&&void 0===r){const e=a.default;if(a.type!==Function&&!a.skipFactory&&m(e)){const{propsDefaults:i}=o;if(n in i)r=i[n];else{const a=ti(o);r=i[n]=e.call(null,t),a()}}else r=e;o.ce&&o.ce._setProp(n,r)}a[0]&&(i&&!e?r=!1:!a[1]||""!==r&&r!==I(n)||(r=!0))}return r}const Wr=new WeakMap;function qr(e,t,o=!1){const i=o?Wr:t.propsCache,a=i.get(e);if(a)return a;const s=e.props,c={},u=[];let d=!1;if(!m(e)){const n=e=>{d=!0;const[n,r]=qr(e,t,!0);l(c,n),r&&u.push(...r)};!o&&t.mixins.length&&t.mixins.forEach(n),e.extends&&n(e.extends),e.mixins&&e.mixins.forEach(n)}if(!s&&!d)return b(e)&&i.set(e,r),r;if(p(s))for(let r=0;r<s.length;r++){const e=T(s[r]);Kr(e)&&(c[e]=n)}else if(s)for(const n in s){const e=T(n);if(Kr(e)){const t=s[n],r=c[e]=p(t)||m(t)?{type:t}:l({},t),o=r.type;let i=!1,a=!0;if(p(o))for(let e=0;e<o.length;++e){const t=o[e],n=m(t)&&t.name;if("Boolean"===n){i=!0;break}"String"===n&&(a=!1)}else i=m(o)&&"Boolean"===o.name;r[0]=i,r[1]=a,(i||f(r,"default"))&&u.push(e)}}const h=[c,u];return b(e)&&i.set(e,h),h}function Kr(e){return"$"!==e[0]&&!k(e)}const Gr=e=>"_"===e[0]||"$stable"===e,Qr=e=>p(e)?e.map(Uo):[Uo(e)],Jr=(e,t,n)=>{if(t._n)return t;const r=on(((...e)=>Qr(t(...e))),n);return r._c=!1,r},Xr=(e,t,n)=>{const r=e._ctx;for(const o in e){if(Gr(o))continue;const n=e[o];if(m(n))t[o]=Jr(0,n,r);else if(null!=n){const e=Qr(n);t[o]=()=>e}}},Zr=(e,t)=>{const n=Qr(t);e.slots.default=()=>n},Yr=(e,t,n)=>{for(const r in t)!n&&Gr(r)||(e[r]=t[r])};const eo=function(e,t){t&&t.pendingBranch?p(e)?t.effects.push(...e):t.effects.push(e):Jt(e)};function to(e){return function(e,t){"boolean"!=typeof __VUE_PROD_HYDRATION_MISMATCH_DETAILS__&&(D().__VUE_PROD_HYDRATION_MISMATCH_DETAILS__=!1);D().__VUE__=!0;const{insert:i,remove:a,patchProp:s,createElement:l,createText:c,createComment:u,setText:d,setElementText:h,parentNode:g,nextSibling:m,setScopeId:v=o,insertStaticContent:y}=e,b=(e,t,n,r=null,o=null,i=null,a=void 0,s=null,l=!!t.dynamicChildren)=>{if(e===t)return;e&&!zo(e,t)&&(r=ee(e),Q(e,o,i,!0),e=null),-2===t.patchFlag&&(l=!1,t.dynamicChildren=null);const{type:c,ref:u,shapeFlag:f}=t;switch(c){case Co:w(e,t,n,r);break;case ko:S(e,t,n,r);break;case Eo:null==e&&x(t,n,r,a);break;case xo:B(e,t,n,r,o,i,a,s,l);break;default:1&f?A(e,t,n,r,o,i,a,s,l):6&f?N(e,t,n,r,o,i,a,s,l):(64&f||128&f)&&c.process(e,t,n,r,o,i,a,s,l,oe)}null!=u&&o&&Rn(u,e&&e.ref,i,t||e,!t)},w=(e,t,n,r)=>{if(null==e)i(t.el=c(t.children),n,r);else{const n=t.el=e.el;t.children!==e.children&&d(n,t.children)}},S=(e,t,n,r)=>{null==e?i(t.el=u(t.children||""),n,r):t.el=e.el},x=(e,t,n,r)=>{[e.el,e.anchor]=y(e.children,t,n,r,e.el,e.anchor)},C=({el:e,anchor:t},n,r)=>{let o;for(;e&&e!==t;)o=m(e),i(e,n,r),e=o;i(t,n,r)},E=({el:e,anchor:t})=>{let n;for(;e&&e!==t;)n=m(e),a(e),e=n;a(t)},A=(e,t,n,r,o,i,a,s,l)=>{"svg"===t.type?a="svg":"math"===t.type&&(a="mathml"),null==e?O(t,n,r,o,i,a,s,l):M(e,t,o,i,a,s,l)},O=(e,t,n,r,o,a,c,u)=>{let f,p;const{props:d,shapeFlag:g,transition:m,dirs:v}=e;if(f=e.el=l(e.type,a,d&&d.is,d),8&g?h(f,e.children):16&g&&L(e.children,f,null,r,o,no(e,a),c,u),v&&sn(e,null,r,"created"),j(f,e,e.scopeId,c,r),d){for(const e in d)"value"===e||k(e)||s(f,e,null,d[e],a,r);"value"in d&&s(f,"value",null,d.value,a),(p=d.onVnodeBeforeMount)&&Go(p,r,e)}v&&sn(e,null,r,"beforeMount");const y=function(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}(o,m);y&&m.beforeEnter(f),i(f,t,n),((p=d&&d.onVnodeMounted)||y||v)&&eo((()=>{p&&Go(p,r,e),y&&m.enter(f),v&&sn(e,null,r,"mounted")}),o)},j=(e,t,n,r,o)=>{if(n&&v(e,n),r)for(let i=0;i<r.length;i++)v(e,r[i]);if(o){let n=o.subTree;if(t===n||So(n.type)&&(n.ssContent===t||n.ssFallback===t)){const t=o.vnode;j(e,t,t.scopeId,t.slotScopeIds,o.parent)}}},L=(e,t,n,r,o,i,a,s,l=0)=>{for(let c=l;c<e.length;c++){const l=e[c]=s?Wo(e[c]):Uo(e[c]);b(null,l,t,n,r,o,i,a,s)}},M=(e,t,r,o,i,a,l)=>{const c=t.el=e.el;let{patchFlag:u,dynamicChildren:f,dirs:p}=t;u|=16&e.patchFlag;const d=e.props||n,g=t.props||n;let m;if(r&&ro(r,!1),(m=g.onVnodeBeforeUpdate)&&Go(m,r,t,e),p&&sn(t,e,r,"beforeUpdate"),r&&ro(r,!0),(d.innerHTML&&null==g.innerHTML||d.textContent&&null==g.textContent)&&h(c,""),f?z(e.dynamicChildren,f,c,r,o,no(t,i),a):l||W(e,t,c,null,r,o,no(t,i),a,!1),u>0){if(16&u)$(c,d,g,r,i);else if(2&u&&d.class!==g.class&&s(c,"class",null,g.class,i),4&u&&s(c,"style",d.style,g.style,i),8&u){const e=t.dynamicProps;for(let t=0;t<e.length;t++){const n=e[t],o=d[n],a=g[n];a===o&&"value"!==n||s(c,n,o,a,i,r)}}1&u&&e.children!==t.children&&h(c,t.children)}else l||null!=f||$(c,d,g,r,i);((m=g.onVnodeUpdated)||p)&&eo((()=>{m&&Go(m,r,t,e),p&&sn(t,e,r,"updated")}),o)},z=(e,t,n,r,o,i,a)=>{for(let s=0;s<t.length;s++){const l=e[s],c=t[s],u=l.el&&(l.type===xo||!zo(l,c)||198&l.shapeFlag)?g(l.el):n;b(l,c,u,null,r,o,i,a,!0)}},$=(e,t,r,o,i)=>{if(t!==r){if(t!==n)for(const n in t)k(n)||n in r||s(e,n,t[n],null,i,o);for(const n in r){if(k(n))continue;const a=r[n],l=t[n];a!==l&&"value"!==n&&s(e,n,l,a,i,o)}"value"in r&&s(e,"value",t.value,r.value,i)}},B=(e,t,n,r,o,a,s,l,u)=>{const f=t.el=e?e.el:c(""),p=t.anchor=e?e.anchor:c("");let{patchFlag:d,dynamicChildren:h,slotScopeIds:g}=t;g&&(l=l?l.concat(g):g),null==e?(i(f,n,r),i(p,n,r),L(t.children||[],n,p,o,a,s,l,u)):d>0&&64&d&&h&&e.dynamicChildren?(z(e.dynamicChildren,h,n,o,a,s,l),(null!=t.key||o&&t===o.subTree)&&oo(e,t,!0)):W(e,t,n,p,o,a,s,l,u)},N=(e,t,n,r,o,i,a,s,l)=>{t.slotScopeIds=s,null==e?512&t.shapeFlag?o.ctx.activate(t,n,r,a,l):F(t,n,r,o,i,a,l):H(e,t,l)},F=(e,t,r,o,i,a,s)=>{const l=e.component=function(e,t,r){const o=e.type,i=(t?t.appContext:e.appContext)||Qo,a={uid:Jo++,vnode:e,type:o,parent:t,appContext:i,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new te(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(i.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:qr(o,i),emitsOptions:mo(o,i),emit:null,emitted:null,propsDefaults:n,inheritAttrs:o.inheritAttrs,ctx:n,data:n,props:n,attrs:n,slots:n,refs:n,setupState:n,setupContext:null,suspense:r,suspenseId:r?r.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};a.ctx={_:a},a.root=t?t.root:a,a.emit=go.bind(null,a),e.ce&&e.ce(a);return a}(e,o,i);if(Nn(e)&&(l.ctx.renderer=oe),function(e,t=!1,n=!1){t&&ei(t);const{props:r,children:o}=e.vnode,i=ri(e);(function(e,t,n,r=!1){const o={},i=Fr();e.propsDefaults=Object.create(null),Vr(e,t,o,i);for(const a in e.propsOptions[0])a in o||(o[a]=void 0);n?e.props=r?o:ft(o):e.type.props?e.props=o:e.props=i,e.attrs=i})(e,r,i,t),((e,t,n)=>{const r=e.slots=Fr();if(32&e.vnode.shapeFlag){const e=t._;e?(Yr(r,t,n),n&&R(r,"_",e,!0)):Xr(t,r)}else t&&Zr(e,t)})(e,o,n||t);const a=i?function(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,_r);const{setup:r}=n;if(r){_e();const n=e.setupContext=r.length>1?function(e){const t=t=>{e.exposed=t||{}};return{attrs:new Proxy(e.attrs,li),slots:e.slots,emit:e.emit,expose:t}}(e):null,o=ti(e),i=$t(r,e,0,[e.props,n]),a=_(i);if(we(),o(),!a&&!e.sp||$n(e)||Pn(e),a){if(i.then(ni,ni),t)return i.then((n=>{ai(e,n,t)})).catch((t=>{Bt(t,e,0)}));e.asyncDep=i}else ai(e,i,t)}else si(e,t)}(e,t):void 0;t&&ei(!1)}(l,!1,s),l.asyncDep){if(i&&i.registerDep(l,V,s),!e.el){const e=l.subTree=No(ko);S(null,e,t,r)}}else V(l,e,t,r,i,a,s)},H=(e,t,n)=>{const r=t.component=e.component;if(function(e,t,n){const{props:r,children:o,component:i}=e,{props:a,children:s,patchFlag:l}=t,c=i.emitsOptions;if(t.dirs||t.transition)return!0;if(!(n&&l>=0))return!(!o&&!s||s&&s.$stable)||r!==a&&(r?!a||wo(r,a,c):!!a);if(1024&l)return!0;if(16&l)return r?wo(r,a,c):!!a;if(8&l){const e=t.dynamicProps;for(let t=0;t<e.length;t++){const n=e[t];if(a[n]!==r[n]&&!vo(c,n))return!0}}return!1}(e,t,n)){if(r.asyncDep&&!r.asyncResolved)return void U(r,t,n);r.next=t,r.update()}else t.el=e.el,r.vnode=t},V=(e,t,n,r,o,i,a)=>{const s=()=>{if(e.isMounted){let{next:t,bu:n,u:r,parent:l,vnode:c}=e;{const n=io(e);if(n)return t&&(t.el=c.el,U(e,t,a)),void n.asyncDep.then((()=>{e.isUnmounted||s()}))}let u,f=t;ro(e,!1),t?(t.el=c.el,U(e,t,a)):t=c,n&&P(n),(u=t.props&&t.props.onVnodeBeforeUpdate)&&Go(u,l,t,c),ro(e,!0);const p=yo(e),d=e.subTree;e.subTree=p,b(d,p,g(d.el),ee(d),e,o,i),t.el=p.el,null===f&&function({vnode:e,parent:t},n){for(;t;){const r=t.subTree;if(r.suspense&&r.suspense.activeBranch===e&&(r.el=e.el),r!==e)break;(e=t.vnode).el=n,t=t.parent}}(e,p.el),r&&eo(r,o),(u=t.props&&t.props.onVnodeUpdated)&&eo((()=>Go(u,l,t,c)),o)}else{let a;const{el:s,props:l}=t,{bm:c,m:u,parent:f,root:p,type:d}=e,h=$n(t);if(ro(e,!1),c&&P(c),!h&&(a=l&&l.onVnodeBeforeMount)&&Go(a,f,t),ro(e,!0),s&&se){const t=()=>{e.subTree=yo(e),se(s,e.subTree,e,o,null)};h&&d.__asyncHydrate?d.__asyncHydrate(s,e,t):t()}else{p.ce&&p.ce._injectChildStyle(d);const a=e.subTree=yo(e);b(null,a,n,r,e,o,i),t.el=a.el}if(u&&eo(u,o),!h&&(a=l&&l.onVnodeMounted)){const e=t;eo((()=>Go(a,f,e)),o)}(256&t.shapeFlag||f&&$n(f.vnode)&&256&f.vnode.shapeFlag)&&e.a&&eo(e.a,o),e.isMounted=!0,t=n=r=null}};e.scope.on();const l=e.effect=new ie(s);e.scope.off();const c=e.update=l.run.bind(l),u=e.job=l.runIfDirty.bind(l);u.i=e,u.id=e.uid,l.scheduler=()=>Gt(u),ro(e,!0),c()},U=(e,t,r)=>{t.component=e;const o=e.vnode.props;e.vnode=t,e.next=null,function(e,t,n,r){const{props:o,attrs:i,vnode:{patchFlag:a}}=e,s=yt(o),[l]=e.propsOptions;let c=!1;if(!(r||a>0)||16&a){let r;Vr(e,t,o,i)&&(c=!0);for(const i in s)t&&(f(t,i)||(r=I(i))!==i&&f(t,r))||(l?!n||void 0===n[i]&&void 0===n[r]||(o[i]=Ur(l,s,i,void 0,e,!0)):delete o[i]);if(i!==s)for(const e in i)t&&f(t,e)||(delete i[e],c=!0)}else if(8&a){const n=e.vnode.dynamicProps;for(let r=0;r<n.length;r++){let a=n[r];if(vo(e.emitsOptions,a))continue;const u=t[a];if(l)if(f(i,a))u!==i[a]&&(i[a]=u,c=!0);else{const t=T(a);o[t]=Ur(l,s,t,u,e,!1)}else u!==i[a]&&(i[a]=u,c=!0)}}c&&Le(e.attrs,"set","")}(e,t.props,o,r),((e,t,r)=>{const{vnode:o,slots:i}=e;let a=!0,s=n;if(32&o.shapeFlag){const e=t._;e?r&&1===e?a=!1:Yr(i,t,r):(a=!t.$stable,Xr(t,i)),s=t}else t&&(Zr(e,t),s={default:1});if(a)for(const n in i)Gr(n)||null!=s[n]||delete i[n]})(e,t.children,r),_e(),Xt(e),we()},W=(e,t,n,r,o,i,a,s,l=!1)=>{const c=e&&e.children,u=e?e.shapeFlag:0,f=t.children,{patchFlag:p,shapeFlag:d}=t;if(p>0){if(128&p)return void K(c,f,n,r,o,i,a,s,l);if(256&p)return void q(c,f,n,r,o,i,a,s,l)}8&d?(16&u&&Y(c,o,i),f!==c&&h(n,f)):16&u?16&d?K(c,f,n,r,o,i,a,s,l):Y(c,o,i,!0):(8&u&&h(n,""),16&d&&L(f,n,r,o,i,a,s,l))},q=(e,t,n,o,i,a,s,l,c)=>{t=t||r;const u=(e=e||r).length,f=t.length,p=Math.min(u,f);let d;for(d=0;d<p;d++){const r=t[d]=c?Wo(t[d]):Uo(t[d]);b(e[d],r,n,null,i,a,s,l,c)}u>f?Y(e,i,a,!0,!1,p):L(t,n,o,i,a,s,l,c,p)},K=(e,t,n,o,i,a,s,l,c)=>{let u=0;const f=t.length;let p=e.length-1,d=f-1;for(;u<=p&&u<=d;){const r=e[u],o=t[u]=c?Wo(t[u]):Uo(t[u]);if(!zo(r,o))break;b(r,o,n,null,i,a,s,l,c),u++}for(;u<=p&&u<=d;){const r=e[p],o=t[d]=c?Wo(t[d]):Uo(t[d]);if(!zo(r,o))break;b(r,o,n,null,i,a,s,l,c),p--,d--}if(u>p){if(u<=d){const e=d+1,r=e<f?t[e].el:o;for(;u<=d;)b(null,t[u]=c?Wo(t[u]):Uo(t[u]),n,r,i,a,s,l,c),u++}}else if(u>d)for(;u<=p;)Q(e[u],i,a,!0),u++;else{const h=u,g=u,m=new Map;for(u=g;u<=d;u++){const e=t[u]=c?Wo(t[u]):Uo(t[u]);null!=e.key&&m.set(e.key,u)}let v,y=0;const _=d-g+1;let w=!1,S=0;const x=new Array(_);for(u=0;u<_;u++)x[u]=0;for(u=h;u<=p;u++){const r=e[u];if(y>=_){Q(r,i,a,!0);continue}let o;if(null!=r.key)o=m.get(r.key);else for(v=g;v<=d;v++)if(0===x[v-g]&&zo(r,t[v])){o=v;break}void 0===o?Q(r,i,a,!0):(x[o-g]=u+1,o>=S?S=o:w=!0,b(r,t[o],n,null,i,a,s,l,c),y++)}const C=w?function(e){const t=e.slice(),n=[0];let r,o,i,a,s;const l=e.length;for(r=0;r<l;r++){const l=e[r];if(0!==l){if(o=n[n.length-1],e[o]<l){t[r]=o,n.push(r);continue}for(i=0,a=n.length-1;i<a;)s=i+a>>1,e[n[s]]<l?i=s+1:a=s;l<e[n[i]]&&(i>0&&(t[r]=n[i-1]),n[i]=r)}}i=n.length,a=n[i-1];for(;i-- >0;)n[i]=a,a=t[a];return n}(x):r;for(v=C.length-1,u=_-1;u>=0;u--){const e=g+u,r=t[e],p=e+1<f?t[e+1].el:o;0===x[u]?b(null,r,n,p,i,a,s,l,c):w&&(v<0||u!==C[v]?G(r,n,p,2):v--)}}},G=(e,t,n,r,o=null)=>{const{el:s,type:l,transition:c,children:u,shapeFlag:f}=e;if(6&f)return void G(e.component.subTree,t,n,r);if(128&f)return void e.suspense.move(t,n,r);if(64&f)return void l.move(e,t,n,oe);if(l===xo){i(s,t,n);for(let e=0;e<u.length;e++)G(u[e],t,n,r);return void i(e.anchor,t,n)}if(l===Eo)return void C(e,t,n);if(2!==r&&1&f&&c)if(0===r)c.beforeEnter(s),i(s,t,n),eo((()=>c.enter(s)),o);else{const{leave:r,delayLeave:o,afterLeave:l}=c,u=()=>{e.ctx.isUnmounted?a(s):i(s,t,n)},f=()=>{r(s,(()=>{u(),l&&l()}))};o?o(s,u,f):f()}else i(s,t,n)},Q=(e,t,n,r=!1,o=!1)=>{const{type:i,props:a,ref:s,children:l,dynamicChildren:c,shapeFlag:u,patchFlag:f,dirs:p,cacheIndex:d}=e;if(-2===f&&(o=!1),null!=s&&(_e(),Rn(s,null,n,e,!0),we()),null!=d&&(t.renderCache[d]=void 0),256&u)return void t.ctx.deactivate(e);const h=1&u&&p,g=!$n(e);let m;if(g&&(m=a&&a.onVnodeBeforeUnmount)&&Go(m,t,e),6&u)Z(e.component,n,r);else{if(128&u)return void e.suspense.unmount(n,r);h&&sn(e,null,t,"beforeUnmount"),64&u?e.type.remove(e,t,n,oe,r):c&&!c.hasOnce&&(i!==xo||f>0&&64&f)?Y(c,t,n,!1,!0):(i===xo&&384&f||!o&&16&u)&&Y(l,t,n),r&&J(e)}(g&&(m=a&&a.onVnodeUnmounted)||h)&&eo((()=>{m&&Go(m,t,e),h&&sn(e,null,t,"unmounted")}),n)},J=e=>{const{type:t,el:n,anchor:r,transition:o}=e;if(t===xo)return void X(n,r);if(t===Eo)return void E(e);const i=()=>{a(n),o&&!o.persisted&&o.afterLeave&&o.afterLeave()};if(1&e.shapeFlag&&o&&!o.persisted){const{leave:t,delayLeave:r}=o,a=()=>t(n,i);r?r(e.el,i,a):a()}else i()},X=(e,t)=>{let n;for(;e!==t;)n=m(e),a(e),e=n;a(t)},Z=(e,t,n)=>{const{bum:r,scope:o,job:i,subTree:a,um:s,m:l,a:c,parent:u,slots:{__:f}}=e;ao(l),ao(c),r&&P(r),u&&p(f)&&f.forEach((e=>{u.renderCache[e]=void 0})),o.stop(),i&&(i.flags|=8,Q(a,e,t,n)),s&&eo(s,t),eo((()=>{e.isUnmounted=!0}),t),t&&t.pendingBranch&&!t.isUnmounted&&e.asyncDep&&!e.asyncResolved&&e.suspenseId===t.pendingId&&(t.deps--,0===t.deps&&t.resolve())},Y=(e,t,n,r=!1,o=!1,i=0)=>{for(let a=i;a<e.length;a++)Q(e[a],t,n,r,o)},ee=e=>{if(6&e.shapeFlag)return ee(e.component.subTree);if(128&e.shapeFlag)return e.suspense.next();const t=m(e.anchor||e.el),n=t&&t[ln];return n?m(n):t};let ne=!1;const re=(e,t,n)=>{null==e?t._vnode&&Q(t._vnode,null,null,!0):b(t._vnode||null,e,t,null,null,null,n),t._vnode=e,ne||(ne=!0,Xt(),Zt(),ne=!1)},oe={p:b,um:Q,m:G,r:J,mt:F,mc:L,pc:W,pbc:z,n:ee,o:e};let ae,se;t&&([ae,se]=t(oe));return{render:re,hydrate:ae,createApp:zr(re,ae)}}(e)}function no({type:e,props:t},n){return"svg"===n&&"foreignObject"===e||"mathml"===n&&"annotation-xml"===e&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function ro({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function oo(e,t,n=!1){const r=e.children,o=t.children;if(p(r)&&p(o))for(let i=0;i<r.length;i++){const e=r[i];let t=o[i];1&t.shapeFlag&&!t.dynamicChildren&&((t.patchFlag<=0||32===t.patchFlag)&&(t=o[i]=Wo(o[i]),t.el=e.el),n||-2===t.patchFlag||oo(e,t)),t.type===Co&&(t.el=e.el),t.type!==ko||t.el||(t.el=e.el)}}function io(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:io(t)}function ao(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const so=Symbol.for("v-scx"),lo=()=>Br(so);function co(e,t,n){return uo(e,t,n)}function uo(e,t,r=n){const{immediate:i,deep:a,flush:s,once:c}=r,u=l({},r),f=t&&i||!t&&"post"!==s;let p;if(ii)if("sync"===s){const e=lo();p=e.__watcherHandles||(e.__watcherHandles=[])}else if(!f){const e=()=>{};return e.stop=o,e.resume=o,e.pause=o,e}const d=Xo;u.call=(e,t,n)=>Dt(e,d,t,n);let h=!1;"post"===s?u.scheduler=e=>{eo(e,d&&d.suspense)}:"sync"!==s&&(h=!0,u.scheduler=(e,t)=>{t?e():Gt(e)}),u.augmentJob=e=>{t&&(e.flags|=4),h&&(e.flags|=2,d&&(e.id=d.uid,e.i=d))};const g=Rt(e,t,u);return ii&&(p?p.push(g):f&&g()),g}function fo(e,t,n){const r=this.proxy,o=v(e)?e.includes(".")?po(r,e):()=>r[e]:e.bind(r,r);let i;m(t)?i=t:(i=t.handler,n=t);const a=ti(this),s=uo(o,i.bind(r),n);return a(),s}function po(e,t){const n=t.split(".");return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}const ho=(e,t)=>"modelValue"===t||"model-value"===t?e.modelModifiers:e[`${t}Modifiers`]||e[`${T(t)}Modifiers`]||e[`${I(t)}Modifiers`];function go(e,t,...r){if(e.isUnmounted)return;const o=e.vnode.props||n;let i=r;const a=t.startsWith("update:"),s=a&&ho(o,t.slice(7));let l;s&&(s.trim&&(i=r.map((e=>v(e)?e.trim():e))),s.number&&(i=r.map(z)));let c=o[l=L(t)]||o[l=L(T(t))];!c&&a&&(c=o[l=L(I(t))]),c&&Dt(c,e,6,i);const u=o[l+"Once"];if(u){if(e.emitted){if(e.emitted[l])return}else e.emitted={};e.emitted[l]=!0,Dt(u,e,6,i)}}function mo(e,t,n=!1){const r=t.emitsCache,o=r.get(e);if(void 0!==o)return o;const i=e.emits;let a={},s=!1;if(!m(e)){const r=e=>{const n=mo(e,t,!0);n&&(s=!0,l(a,n))};!n&&t.mixins.length&&t.mixins.forEach(r),e.extends&&r(e.extends),e.mixins&&e.mixins.forEach(r)}return i||s?(p(i)?i.forEach((e=>a[e]=null)):l(a,i),b(e)&&r.set(e,a),a):(b(e)&&r.set(e,null),null)}function vo(e,t){return!(!e||!a(t))&&(t=t.slice(2).replace(/Once$/,""),f(e,t[0].toLowerCase()+t.slice(1))||f(e,I(t))||f(e,t))}function yo(e){const{type:t,vnode:n,proxy:r,withProxy:o,propsOptions:[i],slots:a,attrs:l,emit:c,render:u,renderCache:f,props:p,data:d,setupState:h,ctx:g,inheritAttrs:m}=e,v=rn(e);let y,b;try{if(4&n.shapeFlag){const e=o||r,t=e;y=Uo(u.call(t,e,f,p,h,d,g)),b=l}else{const e=t;0,y=Uo(e.length>1?e(p,{attrs:l,slots:a,emit:c}):e(p,null)),b=t.props?l:bo(l)}}catch(w){Ao.length=0,Bt(w,e,1),y=No(ko)}let _=y;if(b&&!1!==m){const e=Object.keys(b),{shapeFlag:t}=_;e.length&&7&t&&(i&&e.some(s)&&(b=_o(b,i)),_=Fo(_,b,!1,!0))}return n.dirs&&(_=Fo(_,null,!1,!0),_.dirs=_.dirs?_.dirs.concat(n.dirs):n.dirs),n.transition&&jn(_,n.transition),y=_,rn(v),y}const bo=e=>{let t;for(const n in e)("class"===n||"style"===n||a(n))&&((t||(t={}))[n]=e[n]);return t},_o=(e,t)=>{const n={};for(const r in e)s(r)&&r.slice(9)in t||(n[r]=e[r]);return n};function wo(e,t,n){const r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let o=0;o<r.length;o++){const i=r[o];if(t[i]!==e[i]&&!vo(n,i))return!0}return!1}const So=e=>e.__isSuspense;const xo=Symbol.for("v-fgt"),Co=Symbol.for("v-txt"),ko=Symbol.for("v-cmt"),Eo=Symbol.for("v-stc"),Ao=[];let To=null;function Oo(e=!1){Ao.push(To=e?null:[])}let Io=1;function jo(e,t=!1){Io+=e,e<0&&To&&t&&(To.hasOnce=!0)}function Lo(e){return e.dynamicChildren=Io>0?To||r:null,Ao.pop(),To=Ao[Ao.length-1]||null,Io>0&&To&&To.push(e),e}function Mo(e,t,n,r,o,i){return Lo(Bo(e,t,n,r,o,i,!0))}function Po(e,t,n,r,o){return Lo(No(e,t,n,r,o,!0))}function Ro(e){return!!e&&!0===e.__v_isVNode}function zo(e,t){return e.type===t.type&&e.key===t.key}const $o=({key:e})=>null!=e?e:null,Do=({ref:e,ref_key:t,ref_for:n})=>("number"==typeof e&&(e=""+e),null!=e?v(e)||St(e)||m(e)?{i:tn,r:e,k:t,f:!!n}:e:null);function Bo(e,t=null,n=null,r=0,o=null,i=(e===xo?0:1),a=!1,s=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&$o(t),ref:t&&Do(t),scopeId:nn,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:r,dynamicProps:o,dynamicChildren:null,appContext:null,ctx:tn};return s?(qo(l,n),128&i&&e.normalize(l)):n&&(l.shapeFlag|=v(n)?8:16),Io>0&&!a&&To&&(l.patchFlag>0||6&i)&&32!==l.patchFlag&&To.push(l),l}const No=function(e,t=null,n=null,r=0,o=null,i=!1){e&&e!==cr||(e=ko);if(Ro(e)){const r=Fo(e,t,!0);return n&&qo(r,n),Io>0&&!i&&To&&(6&r.shapeFlag?To[To.indexOf(e)]=r:To.push(r)),r.patchFlag=-2,r}a=e,m(a)&&"__vccOpts"in a&&(e=e.__vccOpts);var a;if(t){t=function(e){return e?vt(e)||Hr(e)?l({},e):e:null}(t);let{class:e,style:n}=t;e&&!v(e)&&(t.class=U(e)),b(n)&&(vt(n)&&!p(n)&&(n=l({},n)),t.style=B(n))}const s=v(e)?1:So(e)?128:cn(e)?64:b(e)?4:m(e)?2:0;return Bo(e,t,n,r,o,s,i,!0)};function Fo(e,t,n=!1,r=!1){const{props:o,ref:i,patchFlag:a,children:s,transition:l}=e,c=t?Ko(o||{},t):o,u={__v_isVNode:!0,__v_skip:!0,type:e.type,props:c,key:c&&$o(c),ref:t&&t.ref?n&&i?p(i)?i.concat(Do(t)):[i,Do(t)]:Do(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:s,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==xo?-1===a?16:16|a:a,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:l,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Fo(e.ssContent),ssFallback:e.ssFallback&&Fo(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return l&&r&&jn(u,l.clone(u)),u}function Ho(e=" ",t=0){return No(Co,null,e,t)}function Vo(e="",t=!1){return t?(Oo(),Po(ko,null,e)):No(ko,null,e)}function Uo(e){return null==e||"boolean"==typeof e?No(ko):p(e)?No(xo,null,e.slice()):Ro(e)?Wo(e):No(Co,null,String(e))}function Wo(e){return null===e.el&&-1!==e.patchFlag||e.memo?e:Fo(e)}function qo(e,t){let n=0;const{shapeFlag:r}=e;if(null==t)t=null;else if(p(t))n=16;else if("object"==typeof t){if(65&r){const n=t.default;return void(n&&(n._c&&(n._d=!1),qo(e,n()),n._c&&(n._d=!0)))}{n=32;const r=t._;r||Hr(t)?3===r&&tn&&(1===tn.slots._?t._=1:(t._=2,e.patchFlag|=1024)):t._ctx=tn}}else m(t)?(t={default:t,_ctx:tn},n=32):(t=String(t),64&r?(n=16,t=[Ho(t)]):n=8);e.children=t,e.shapeFlag|=n}function Ko(...e){const t={};for(let n=0;n<e.length;n++){const r=e[n];for(const e in r)if("class"===e)t.class!==r.class&&(t.class=U([t.class,r.class]));else if("style"===e)t.style=B([t.style,r.style]);else if(a(e)){const n=t[e],o=r[e];!o||n===o||p(n)&&n.includes(o)||(t[e]=n?[].concat(n,o):o)}else""!==e&&(t[e]=r[e])}return t}function Go(e,t,n,r=null){Dt(e,t,7,[n,r])}const Qo=Pr();let Jo=0;let Xo=null;const Zo=()=>Xo||tn;let Yo,ei;{const e=D(),t=(t,n)=>{let r;return(r=e[t])||(r=e[t]=[]),r.push(n),e=>{r.length>1?r.forEach((t=>t(e))):r[0](e)}};Yo=t("__VUE_INSTANCE_SETTERS__",(e=>Xo=e)),ei=t("__VUE_SSR_SETTERS__",(e=>ii=e))}const ti=e=>{const t=Xo;return Yo(e),e.scope.on(),()=>{e.scope.off(),Yo(t)}},ni=()=>{Xo&&Xo.scope.off(),Yo(null)};function ri(e){return 4&e.vnode.shapeFlag}let oi,ii=!1;function ai(e,t,n){m(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:b(t)&&(e.setupState=Tt(t)),si(e,n)}function si(e,t,n){const r=e.type;if(!e.render){if(!t&&oi&&!r.render){const t=r.template||Er(e).template;if(t){const{isCustomElement:n,compilerOptions:o}=e.appContext.config,{delimiters:i,compilerOptions:a}=r,s=l(l({isCustomElement:n,delimiters:i},o),a);r.render=oi(t,s)}}e.render=r.render||o}{const t=ti(e);_e();try{xr(e)}finally{we(),t()}}}const li={get:(e,t)=>(je(e,0,""),e[t])};function ci(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Tt(bt(e.exposed)),{get:(t,n)=>n in t?t[n]:n in yr?yr[n](e):void 0,has:(e,t)=>t in e||t in yr})):e.proxy}function ui(e,t=!0){return m(e)?e.displayName||e.name:e.name||t&&e.__name}const fi=(e,t)=>{const n=function(e,t,n=!1){let r,o;return m(e)?r=e:(r=e.get,o=e.set),new jt(r,o,n)}(e,0,ii);return n};function pi(e,t,n){const r=arguments.length;return 2===r?b(t)&&!p(t)?Ro(t)?No(e,null,[t]):No(e,t):No(e,null,t):(r>3?n=Array.prototype.slice.call(arguments,2):3===r&&Ro(n)&&(n=[n]),No(e,t,n))}const di="3.5.16";
/**
* @vue/runtime-dom v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let hi;const gi="undefined"!=typeof window&&window.trustedTypes;if(gi)try{hi=gi.createPolicy("vue",{createHTML:e=>e})}catch($v){}const mi=hi?e=>hi.createHTML(e):e=>e,vi="undefined"!=typeof document?document:null,yi=vi&&vi.createElement("template"),bi={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,r)=>{const o="svg"===t?vi.createElementNS("http://www.w3.org/2000/svg",e):"mathml"===t?vi.createElementNS("http://www.w3.org/1998/Math/MathML",e):n?vi.createElement(e,{is:n}):vi.createElement(e);return"select"===e&&r&&null!=r.multiple&&o.setAttribute("multiple",r.multiple),o},createText:e=>vi.createTextNode(e),createComment:e=>vi.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>vi.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,r,o,i){const a=n?n.previousSibling:t.lastChild;if(o&&(o===i||o.nextSibling))for(;t.insertBefore(o.cloneNode(!0),n),o!==i&&(o=o.nextSibling););else{yi.innerHTML=mi("svg"===r?`<svg>${e}</svg>`:"mathml"===r?`<math>${e}</math>`:e);const o=yi.content;if("svg"===r||"mathml"===r){const e=o.firstChild;for(;e.firstChild;)o.appendChild(e.firstChild);o.removeChild(e)}t.insertBefore(o,n)}return[a?a.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},_i="transition",wi="animation",Si=Symbol("_vtc"),xi={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},Ci=l({},xn,xi),ki=(e=>(e.displayName="Transition",e.props=Ci,e))(((e,{slots:t})=>pi(En,function(e){const t={};for(const l in e)l in xi||(t[l]=e[l]);if(!1===e.css)return t;const{name:n="v",type:r,duration:o,enterFromClass:i=`${n}-enter-from`,enterActiveClass:a=`${n}-enter-active`,enterToClass:s=`${n}-enter-to`,appearFromClass:c=i,appearActiveClass:u=a,appearToClass:f=s,leaveFromClass:p=`${n}-leave-from`,leaveActiveClass:d=`${n}-leave-active`,leaveToClass:h=`${n}-leave-to`}=e,g=function(e){if(null==e)return null;if(b(e))return[Ti(e.enter),Ti(e.leave)];{const t=Ti(e);return[t,t]}}(o),m=g&&g[0],v=g&&g[1],{onBeforeEnter:y,onEnter:_,onEnterCancelled:w,onLeave:S,onLeaveCancelled:x,onBeforeAppear:C=y,onAppear:k=_,onAppearCancelled:E=w}=t,A=(e,t,n,r)=>{e._enterCancelled=r,Ii(e,t?f:s),Ii(e,t?u:a),n&&n()},T=(e,t)=>{e._isLeaving=!1,Ii(e,p),Ii(e,h),Ii(e,d),t&&t()},O=e=>(t,n)=>{const o=e?k:_,a=()=>A(t,e,n);Ei(o,[t,a]),ji((()=>{Ii(t,e?c:i),Oi(t,e?f:s),Ai(o)||Mi(t,r,m,a)}))};return l(t,{onBeforeEnter(e){Ei(y,[e]),Oi(e,i),Oi(e,a)},onBeforeAppear(e){Ei(C,[e]),Oi(e,c),Oi(e,u)},onEnter:O(!1),onAppear:O(!0),onLeave(e,t){e._isLeaving=!0;const n=()=>T(e,t);Oi(e,p),e._enterCancelled?(Oi(e,d),zi()):(zi(),Oi(e,d)),ji((()=>{e._isLeaving&&(Ii(e,p),Oi(e,h),Ai(S)||Mi(e,r,v,n))})),Ei(S,[e,n])},onEnterCancelled(e){A(e,!1,void 0,!0),Ei(w,[e])},onAppearCancelled(e){A(e,!0,void 0,!0),Ei(E,[e])},onLeaveCancelled(e){T(e),Ei(x,[e])}})}(e),t))),Ei=(e,t=[])=>{p(e)?e.forEach((e=>e(...t))):e&&e(...t)},Ai=e=>!!e&&(p(e)?e.some((e=>e.length>1)):e.length>1);function Ti(e){const t=(e=>{const t=v(e)?Number(e):NaN;return isNaN(t)?e:t})(e);return t}function Oi(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.add(t))),(e[Si]||(e[Si]=new Set)).add(t)}function Ii(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.remove(t)));const n=e[Si];n&&(n.delete(t),n.size||(e[Si]=void 0))}function ji(e){requestAnimationFrame((()=>{requestAnimationFrame(e)}))}let Li=0;function Mi(e,t,n,r){const o=e._endId=++Li,i=()=>{o===e._endId&&r()};if(null!=n)return setTimeout(i,n);const{type:a,timeout:s,propCount:l}=function(e,t){const n=window.getComputedStyle(e),r=e=>(n[e]||"").split(", "),o=r(`${_i}Delay`),i=r(`${_i}Duration`),a=Pi(o,i),s=r(`${wi}Delay`),l=r(`${wi}Duration`),c=Pi(s,l);let u=null,f=0,p=0;t===_i?a>0&&(u=_i,f=a,p=i.length):t===wi?c>0&&(u=wi,f=c,p=l.length):(f=Math.max(a,c),u=f>0?a>c?_i:wi:null,p=u?u===_i?i.length:l.length:0);const d=u===_i&&/\b(transform|all)(,|$)/.test(r(`${_i}Property`).toString());return{type:u,timeout:f,propCount:p,hasTransform:d}}(e,t);if(!a)return r();const c=a+"end";let u=0;const f=()=>{e.removeEventListener(c,p),i()},p=t=>{t.target===e&&++u>=l&&f()};setTimeout((()=>{u<l&&f()}),s+1),e.addEventListener(c,p)}function Pi(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map(((t,n)=>Ri(t)+Ri(e[n]))))}function Ri(e){return"auto"===e?0:1e3*Number(e.slice(0,-1).replace(",","."))}function zi(){return document.body.offsetHeight}const $i=Symbol("_vod"),Di=Symbol("_vsh"),Bi={beforeMount(e,{value:t},{transition:n}){e[$i]="none"===e.style.display?"":e.style.display,n&&t?n.beforeEnter(e):Ni(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:r}){!t!=!n&&(r?t?(r.beforeEnter(e),Ni(e,!0),r.enter(e)):r.leave(e,(()=>{Ni(e,!1)})):Ni(e,t))},beforeUnmount(e,{value:t}){Ni(e,t)}};function Ni(e,t){e.style.display=t?e[$i]:"none",e[Di]=!t}const Fi=Symbol("");function Hi(e){const t=Zo();if(!t)return;const n=t.ut=(n=e(t.proxy))=>{Array.from(document.querySelectorAll(`[data-v-owner="${t.uid}"]`)).forEach((e=>Ui(e,n)))},r=()=>{const r=e(t.proxy);t.ce?Ui(t.ce,r):Vi(t.subTree,r),n(r)};Yn((()=>{Jt(r)})),Zn((()=>{co(r,o,{flush:"post"});const e=new MutationObserver(r);e.observe(t.subTree.el.parentNode,{childList:!0}),nr((()=>e.disconnect()))}))}function Vi(e,t){if(128&e.shapeFlag){const n=e.suspense;e=n.activeBranch,n.pendingBranch&&!n.isHydrating&&n.effects.push((()=>{Vi(n.activeBranch,t)}))}for(;e.component;)e=e.component.subTree;if(1&e.shapeFlag&&e.el)Ui(e.el,t);else if(e.type===xo)e.children.forEach((e=>Vi(e,t)));else if(e.type===Eo){let{el:n,anchor:r}=e;for(;n&&(Ui(n,t),n!==r);)n=n.nextSibling}}function Ui(e,t){if(1===e.nodeType){const n=e.style;let r="";for(const e in t)n.setProperty(`--${e}`,t[e]),r+=`--${e}: ${t[e]};`;n[Fi]=r}}const Wi=/(^|;)\s*display\s*:/;const qi=/\s*!important$/;function Ki(e,t,n){if(p(n))n.forEach((n=>Ki(e,t,n)));else if(null==n&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const r=function(e,t){const n=Qi[t];if(n)return n;let r=T(t);if("filter"!==r&&r in e)return Qi[t]=r;r=j(r);for(let o=0;o<Gi.length;o++){const n=Gi[o]+r;if(n in e)return Qi[t]=n}return t}(e,t);qi.test(n)?e.setProperty(I(r),n.replace(qi,""),"important"):e[r]=n}}const Gi=["Webkit","Moz","ms"],Qi={};const Ji="http://www.w3.org/1999/xlink";function Xi(e,t,n,r,o,i=W(t)){r&&t.startsWith("xlink:")?null==n?e.removeAttributeNS(Ji,t.slice(6,t.length)):e.setAttributeNS(Ji,t,n):null==n||i&&!q(n)?e.removeAttribute(t):e.setAttribute(t,i?"":y(n)?String(n):n)}function Zi(e,t,n,r,o){if("innerHTML"===t||"textContent"===t)return void(null!=n&&(e[t]="innerHTML"===t?mi(n):n));const i=e.tagName;if("value"===t&&"PROGRESS"!==i&&!i.includes("-")){const r="OPTION"===i?e.getAttribute("value")||"":e.value,o=null==n?"checkbox"===e.type?"on":"":String(n);return r===o&&"_value"in e||(e.value=o),null==n&&e.removeAttribute(t),void(e._value=n)}let a=!1;if(""===n||null==n){const r=typeof e[t];"boolean"===r?n=q(n):null==n&&"string"===r?(n="",a=!0):"number"===r&&(n=0,a=!0)}try{e[t]=n}catch($v){}a&&e.removeAttribute(o||t)}function Yi(e,t,n,r){e.addEventListener(t,n,r)}const ea=Symbol("_vei");function ta(e,t,n,r,o=null){const i=e[ea]||(e[ea]={}),a=i[t];if(r&&a)a.value=r;else{const[n,s]=function(e){let t;if(na.test(e)){let n;for(t={};n=e.match(na);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}const n=":"===e[2]?e.slice(3):I(e.slice(2));return[n,t]}(t);if(r){const a=i[t]=function(e,t){const n=e=>{if(e._vts){if(e._vts<=n.attached)return}else e._vts=Date.now();Dt(function(e,t){if(p(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map((e=>t=>!t._stopped&&e&&e(t)))}return t}(e,n.value),t,5,[e])};return n.value=e,n.attached=ia(),n}(r,o);Yi(e,n,a,s)}else a&&(!function(e,t,n,r){e.removeEventListener(t,n,r)}(e,n,a,s),i[t]=void 0)}}const na=/(?:Once|Passive|Capture)$/;let ra=0;const oa=Promise.resolve(),ia=()=>ra||(oa.then((()=>ra=0)),ra=Date.now());const aa=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123;const sa=e=>{const t=e.props["onUpdate:modelValue"]||!1;return p(t)?e=>P(t,e):t},la=Symbol("_assign"),ca={deep:!0,created(e,t,n){e[la]=sa(n),Yi(e,"change",(()=>{const t=e._modelValue,n=pa(e),r=e.checked,o=e[la];if(p(t)){const e=G(t,n),i=-1!==e;if(r&&!i)o(t.concat(n));else if(!r&&i){const n=[...t];n.splice(e,1),o(n)}}else if(h(t)){const e=new Set(t);r?e.add(n):e.delete(n),o(e)}else o(da(e,r))}))},mounted:ua,beforeUpdate(e,t,n){e[la]=sa(n),ua(e,t,n)}};function ua(e,{value:t,oldValue:n},r){let o;if(e._modelValue=t,p(t))o=G(t,r.props.value)>-1;else if(h(t))o=t.has(r.props.value);else{if(t===n)return;o=K(t,da(e,!0))}e.checked!==o&&(e.checked=o)}const fa={created(e,{value:t},n){e.checked=K(t,n.props.value),e[la]=sa(n),Yi(e,"change",(()=>{e[la](pa(e))}))},beforeUpdate(e,{value:t,oldValue:n},r){e[la]=sa(r),t!==n&&(e.checked=K(t,r.props.value))}};function pa(e){return"_value"in e?e._value:e.value}function da(e,t){const n=t?"_trueValue":"_falseValue";return n in e?e[n]:t}const ha=["ctrl","shift","alt","meta"],ga={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&0!==e.button,middle:e=>"button"in e&&1!==e.button,right:e=>"button"in e&&2!==e.button,exact:(e,t)=>ha.some((n=>e[`${n}Key`]&&!t.includes(n)))},ma=(e,t)=>{const n=e._withMods||(e._withMods={}),r=t.join(".");return n[r]||(n[r]=(n,...r)=>{for(let e=0;e<t.length;e++){const r=ga[t[e]];if(r&&r(n,t))return}return e(n,...r)})},va={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},ya=(e,t)=>{const n=e._withKeys||(e._withKeys={}),r=t.join(".");return n[r]||(n[r]=n=>{if(!("key"in n))return;const r=I(n.key);return t.some((e=>e===r||va[e]===r))?e(n):void 0})},ba=l({patchProp:(e,t,n,r,o,i)=>{const l="svg"===o;"class"===t?function(e,t,n){const r=e[Si];r&&(t=(t?[t,...r]:[...r]).join(" ")),null==t?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}(e,r,l):"style"===t?function(e,t,n){const r=e.style,o=v(n);let i=!1;if(n&&!o){if(t)if(v(t))for(const e of t.split(";")){const t=e.slice(0,e.indexOf(":")).trim();null==n[t]&&Ki(r,t,"")}else for(const e in t)null==n[e]&&Ki(r,e,"");for(const e in n)"display"===e&&(i=!0),Ki(r,e,n[e])}else if(o){if(t!==n){const e=r[Fi];e&&(n+=";"+e),r.cssText=n,i=Wi.test(n)}}else t&&e.removeAttribute("style");$i in e&&(e[$i]=i?r.display:"",e[Di]&&(r.display="none"))}(e,n,r):a(t)?s(t)||ta(e,t,0,r,i):("."===t[0]?(t=t.slice(1),1):"^"===t[0]?(t=t.slice(1),0):function(e,t,n,r){if(r)return"innerHTML"===t||"textContent"===t||!!(t in e&&aa(t)&&m(n));if("spellcheck"===t||"draggable"===t||"translate"===t||"autocorrect"===t)return!1;if("form"===t)return!1;if("list"===t&&"INPUT"===e.tagName)return!1;if("type"===t&&"TEXTAREA"===e.tagName)return!1;if("width"===t||"height"===t){const t=e.tagName;if("IMG"===t||"VIDEO"===t||"CANVAS"===t||"SOURCE"===t)return!1}if(aa(t)&&v(n))return!1;return t in e}(e,t,r,l))?(Zi(e,t,r),e.tagName.includes("-")||"value"!==t&&"checked"!==t&&"selected"!==t||Xi(e,t,r,l,0,"value"!==t)):!e._isVueCE||!/[A-Z]/.test(t)&&v(r)?("true-value"===t?e._trueValue=r:"false-value"===t&&(e._falseValue=r),Xi(e,t,r,l)):Zi(e,T(t),r,0,t)}},bi);let _a;const wa=(...e)=>{const t=(_a||(_a=to(ba))).createApp(...e),{mount:n}=t;return t.mount=e=>{const r=function(e){if(v(e)){return document.querySelector(e)}return e}(e);if(!r)return;const o=t._component;m(o)||o.render||o.template||(o.template=r.innerHTML),1===r.nodeType&&(r.textContent="");const i=n(r,!1,function(e){if(e instanceof SVGElement)return"svg";if("function"==typeof MathMLElement&&e instanceof MathMLElement)return"mathml"}(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),i},t};const Sa=(e,t)=>{const n=e.__vccOpts||e;for(const[r,o]of t)n[r]=o;return n},xa=["disabled","type"],Ca={key:0,class:"loading"},ka=Sa({__name:"Button",props:{type:{type:String,default:"default",validator:e=>["default","primary","success","warning","danger"].includes(e)},size:{type:String,default:"default",validator:e=>["small","default","large"].includes(e)},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},nativeType:{type:String,default:"button",validator:e=>["button","submit","reset"].includes(e)}},emits:["click"],setup(e,{emit:t}){const n=e,r=t,o=fi((()=>{const e=["btn"];return"default"!==n.type?e.push(`btn-${n.type}`):e.push("btn-default"),"default"!==n.size&&e.push(`btn-${n.size}`),n.loading&&e.push("btn-loading"),e.join(" ")})),i=e=>{n.disabled||n.loading||r("click",e)};return(t,n)=>(Oo(),Mo("button",{class:U(o.value),disabled:e.disabled,type:e.nativeType,onClick:i},[e.loading?(Oo(),Mo("span",Ca)):Vo("",!0),gr(t.$slots,"default",{},void 0,!0)],10,xa))}},[["__scopeId","data-v-c70c1d15"]]),Ea={class:"input-wrapper"},Aa=["type","value","placeholder","disabled","readonly","maxlength","autocomplete"],Ta=Sa({__name:"Input",props:{modelValue:{type:[String,Number],default:""},type:{type:String,default:"text"},placeholder:{type:String,default:""},autocomplete:{type:String,default:""},disabled:{type:Boolean,default:!1},readonly:{type:Boolean,default:!1},maxlength:{type:[String,Number],default:void 0},size:{type:String,default:"default",validator:e=>["small","default","large"].includes(e)}},emits:["update:modelValue","input","change","focus","blur"],setup(e,{expose:t,emit:n}){const r=e,o=n,i=xt(null),a=xt(!1),s=fi((()=>{const e=["base-input"];return"default"!==r.size&&e.push(`base-input--${r.size}`),a.value&&e.push("base-input--focused"),r.disabled&&e.push("base-input--disabled"),e.join(" ")})),l=e=>{const t=e.target.value;o("update:modelValue",t),o("input",t,e)},c=e=>{o("change",e.target.value,e)},u=e=>{a.value=!0,o("focus",e)},f=e=>{a.value=!1,o("blur",e)};return t({focus:()=>{var e;return null==(e=i.value)?void 0:e.focus()},blur:()=>{var e;return null==(e=i.value)?void 0:e.blur()}}),(t,n)=>(Oo(),Mo("div",Ea,[Bo("input",{ref_key:"inputRef",ref:i,class:U(s.value),type:e.type,value:e.modelValue,placeholder:e.placeholder,disabled:e.disabled,readonly:e.readonly,maxlength:e.maxlength,autocomplete:e.autocomplete,onInput:l,onChange:c,onFocus:u,onBlur:f},null,42,Aa)]))}},[["__scopeId","data-v-0bdb55ce"]]),Oa=Sa({__name:"Form",props:{model:{type:Object,default:()=>({})},rules:{type:Object,default:()=>({})},labelPosition:{type:String,default:"right",validator:e=>["left","right","top"].includes(e)},labelWidth:{type:String,default:"100px"},inline:{type:Boolean,default:!1}},emits:["submit","validate"],setup(e,{expose:t,emit:n}){const r=e,o=n,i=xt([]),a=fi((()=>{const e=["base-form"];return r.inline&&e.push("base-form--inline"),e.push(`base-form--label-${r.labelPosition}`),e.join(" ")})),s=e=>{o("submit",e)};return t({validate:async e=>{try{let t=!0;const n=[];if(0===i.value.length)return e&&e(!0),!0;const r=i.value.map((e=>new Promise((r=>{e.validate("",(e=>{e&&(t=!1,n.push(e)),r(e)}))}))));if(await Promise.all(r),e&&e(t,n),t)return!0;throw n}catch(t){throw e&&e(!1,t),t}},validateField:async(e,t)=>{const n=Array.isArray(e)?e:[e],r=i.value.filter((e=>n.includes(e.prop)));if(0===r.length)return t&&t(),!0;try{let e=!0;const n=r.map((t=>new Promise((n=>{t.validate("",(t=>{t&&(e=!1),n(t)}))}))));return await Promise.all(n),t&&t(e),e}catch(o){return t&&t(!1),!1}},resetFields:()=>{i.value.forEach((e=>{e.resetField()}))},clearValidate:e=>{if(e){const t=Array.isArray(e)?e:[e];i.value.forEach((e=>{t.includes(e.prop)&&e.clearValidate()}))}else i.value.forEach((e=>{e.clearValidate()}))}}),Dr("baseForm",{model:r.model,rules:r.rules,labelPosition:r.labelPosition,labelWidth:r.labelWidth,addFormItem:e=>{i.value.push(e)},removeFormItem:e=>{const t=i.value.indexOf(e);t>-1&&i.value.splice(t,1)}}),(e,t)=>(Oo(),Mo("form",{class:U(a.value),onSubmit:ma(s,["prevent"])},[gr(e.$slots,"default",{},void 0,!0)],34))}},[["__scopeId","data-v-2562b957"]]),Ia={class:"base-form-item__content"},ja={key:0,class:"base-form-item__error"},La=Sa({__name:"FormItem",props:{label:{type:String,default:""},prop:{type:String,default:""},rules:{type:[Object,Array],default:()=>[]},required:{type:Boolean,default:!1},labelWidth:{type:String,default:""}},setup(e,{expose:t}){const n=e,r=Br("baseForm",{}),o=xt(""),i=xt(null),a=fi((()=>{const e=["base-form-item"];return o.value&&e.push("base-form-item--error"),(n.required||c.value)&&e.push("base-form-item--required"),e.join(" ")})),s=fi((()=>{const e=["base-form-item__label"];return(n.required||c.value)&&e.push("base-form-item__label--required"),e.join(" ")})),l=fi((()=>{const e=n.labelWidth||r.labelWidth;return e&&"top"!==r.labelPosition?{width:e,minWidth:e}:{}})),c=fi((()=>u().some((e=>e.required)))),u=()=>{var e;const t=(null==(e=r.rules)?void 0:e[n.prop])||[],o=n.rules||[];return[].concat(t,o)},f=async(e,t)=>{if(!n.prop||!r.model)return t&&t(),!0;const i=r.model[n.prop],a=u();if(0===a.length)return t&&t(),!0;for(const r of a){if(e&&r.trigger){if(!(Array.isArray(r.trigger)?r.trigger:[r.trigger]).includes(e))continue}if(r.required&&(null==i||""===i)){const e=r.message||`${n.label}是必填项`;return o.value=e,t&&t(e),!1}if(null!=i&&""!==i){if(r.min&&String(i).length<r.min){const e=r.message||`${n.label}长度不能少于${r.min}个字符`;return o.value=e,t&&t(e),!1}if(r.max&&String(i).length>r.max){const e=r.message||`${n.label}长度不能超过${r.max}个字符`;return o.value=e,t&&t(e),!1}if(r.pattern&&!r.pattern.test(String(i))){const e=r.message||`${n.label}格式不正确`;return o.value=e,t&&t(e),!1}if(r.validator&&"function"==typeof r.validator)try{const e=await new Promise((e=>{const t=r.validator(r,i,(t=>{if(t){const n=t.message||t;e({success:!1,message:n})}else e({success:!0})}));!1===t?e({success:!1,message:r.message||`${n.label}验证失败`}):!0===t&&e({success:!0})}));if(!e.success)return o.value=e.message,t&&t(e.message),!1}catch(s){const e=r.message||s.message||`${n.label}验证失败`;return o.value=e,t&&t(e),!1}}}return o.value="",t&&t(),!0},p=()=>{n.prop&&r.model&&void 0!==i.value&&(r.model[n.prop]=i.value),o.value=""},d=()=>{o.value=""};return n.prop&&r.model&&co((()=>r.model[n.prop]),(()=>{o.value&&f("change")})),Zn((()=>{n.prop&&r.model&&(i.value=r.model[n.prop]),r.addFormItem&&r.addFormItem({prop:n.prop,validate:f,resetField:p,clearValidate:d})})),nr((()=>{r.removeFormItem&&r.removeFormItem({prop:n.prop,validate:f,resetField:p,clearValidate:d})})),t({validate:f,resetField:p,clearValidate:d,prop:n.prop}),(t,n)=>(Oo(),Mo("div",{class:U(a.value)},[e.label?(Oo(),Mo("label",{key:0,class:U(s.value),style:B(l.value)},J(e.label),7)):Vo("",!0),Bo("div",Ia,[gr(t.$slots,"default",{},void 0,!0),o.value?(Oo(),Mo("div",ja,J(o.value),1)):Vo("",!0)])],2))}},[["__scopeId","data-v-36c5a368"]]),Ma={class:"base-container"},Pa=Sa({__name:"Container",setup:e=>(e,t)=>(Oo(),Mo("div",Ma,[gr(e.$slots,"default",{},void 0,!0)]))},[["__scopeId","data-v-311eab9d"]]),Ra=Sa({__name:"Aside",props:{width:{type:String,default:"180px"},collapsed:{type:Boolean,default:!1},collapsedWidth:{type:String,default:"54px"}},setup(e){const t=e,n=fi((()=>{const e=["aside"];return t.collapsed&&e.push("collapsed"),e.join(" ")})),r=fi((()=>({width:t.collapsed?t.collapsedWidth:t.width})));return(e,t)=>(Oo(),Mo("div",{class:U(n.value),style:B(r.value)},[gr(e.$slots,"default",{},void 0,!0)],6))}},[["__scopeId","data-v-26200e13"]]),za={class:"main"},$a=Sa({__name:"Main",setup:e=>(e,t)=>(Oo(),Mo("main",za,[gr(e.$slots,"default",{},void 0,!0)]))},[["__scopeId","data-v-0a2053bd"]]),Da=Sa({__name:"Row",props:{gutter:{type:Number,default:0},justify:{type:String,default:"start",validator:e=>["start","end","center","space-around","space-between"].includes(e)},align:{type:String,default:"top",validator:e=>["top","middle","bottom"].includes(e)}},setup(e){const t=e,n=fi((()=>{const e=["row"];return"start"!==t.justify&&e.push(`row-justify-${t.justify}`),"top"!==t.align&&e.push(`row-align-${t.align}`),e.join(" ")})),r=fi((()=>{const e={};return t.gutter>0&&(e.marginLeft=`-${t.gutter/2}px`,e.marginRight=`-${t.gutter/2}px`),e}));return provide("row",{gutter:t.gutter}),(e,t)=>(Oo(),Mo("div",{class:U(n.value),style:B(r.value)},[gr(e.$slots,"default",{},void 0,!0)],6))}},[["__scopeId","data-v-80df48c9"]]),Ba=Sa({__name:"Col",props:{span:{type:Number,default:24},offset:{type:Number,default:0},push:{type:Number,default:0},pull:{type:Number,default:0},xs:{type:[Number,Object],default:void 0},sm:{type:[Number,Object],default:void 0},md:{type:[Number,Object],default:void 0},lg:{type:[Number,Object],default:void 0},xl:{type:[Number,Object],default:void 0}},setup(e){const t=e,n=Br("row",{gutter:0}),r=fi((()=>{const e=["col"];24!==t.span&&e.push(`col-${t.span}`),t.offset>0&&e.push(`col-offset-${t.offset}`),t.push>0&&e.push(`col-push-${t.push}`),t.pull>0&&e.push(`col-pull-${t.pull}`);return["xs","sm","md","lg","xl"].forEach((n=>{const r=t[n];void 0!==r&&("number"==typeof r?e.push(`col-${n}-${r}`):"object"==typeof r&&(void 0!==r.span&&e.push(`col-${n}-${r.span}`),void 0!==r.offset&&e.push(`col-${n}-offset-${r.offset}`),void 0!==r.push&&e.push(`col-${n}-push-${r.push}`),void 0!==r.pull&&e.push(`col-${n}-pull-${r.pull}`)))})),e.join(" ")})),o=fi((()=>{const e={};return n.gutter>0&&(e.paddingLeft=n.gutter/2+"px",e.paddingRight=n.gutter/2+"px"),e}));return(e,t)=>(Oo(),Mo("div",{class:U(r.value),style:B(o.value)},[gr(e.$slots,"default",{},void 0,!0)],6))}},[["__scopeId","data-v-40a80e46"]]),Na=Sa({__name:"Divider",props:{direction:{type:String,default:"horizontal",validator:e=>["horizontal","vertical"].includes(e)},contentPosition:{type:String,default:"center",validator:e=>["left","center","right"].includes(e)}},setup(e){const t=e,n=fi((()=>{const e=["divider"];return"vertical"===t.direction?e.push("divider-vertical"):e.push("divider-horizontal"),e.join(" ")})),r=fi((()=>{const e=["divider-content"];return"horizontal"===t.direction&&e.push(`divider-content-${t.contentPosition}`),e.join(" ")}));return(e,t)=>(Oo(),Mo("div",{class:U(n.value)},[e.$slots.default?(Oo(),Mo("span",{key:0,class:U(r.value)},[gr(e.$slots,"default",{},void 0,!0)],2)):Vo("",!0)],2))}},[["__scopeId","data-v-66ec6f49"]]),Fa=["src","alt"],Ha=["xlink:href"],Va=Sa({__name:"Avatar",props:{size:{type:[Number,String],default:40,validator:e=>"string"==typeof e?["small","default","large"].includes(e):"number"==typeof e&&e>0},shape:{type:String,default:"circle",validator:e=>["circle","square"].includes(e)},src:{type:String,default:""},alt:{type:String,default:""},icon:{type:String,default:""},text:{type:String,default:""}},emits:["error"],setup(e,{emit:t}){const n=e,r=t,o=xt(!1),i=fi((()=>{const e=["avatar"];return"string"==typeof n.size&&e.push(`avatar-${n.size}`),"square"===n.shape&&e.push("avatar-square"),e.join(" ")})),a=fi((()=>{const e={};return"number"==typeof n.size&&(e.width=`${n.size}px`,e.height=`${n.size}px`,e.lineHeight=`${n.size}px`,e.fontSize=`${Math.floor(.35*n.size)}px`),e})),s=e=>{o.value=!0,r("error",e)};return(t,n)=>(Oo(),Mo("div",{class:U(i.value),style:B(a.value),draggable:"false",onDragstart:n[9]||(n[9]=ma((()=>{}),["prevent"])),onDrag:n[10]||(n[10]=ma((()=>{}),["prevent"])),onDragend:n[11]||(n[11]=ma((()=>{}),["prevent"]))},[e.src?(Oo(),Mo("img",{key:0,src:e.src,alt:e.alt,draggable:"false",onError:s,onDragstart:n[0]||(n[0]=ma((()=>{}),["prevent"])),onDrag:n[1]||(n[1]=ma((()=>{}),["prevent"])),onDragend:n[2]||(n[2]=ma((()=>{}),["prevent"]))},null,40,Fa)):e.icon?(Oo(),Mo("svg",{key:1,class:"avatar-icon","aria-hidden":"true",draggable:"false",onDragstart:n[3]||(n[3]=ma((()=>{}),["prevent"])),onDrag:n[4]||(n[4]=ma((()=>{}),["prevent"])),onDragend:n[5]||(n[5]=ma((()=>{}),["prevent"]))},[Bo("use",{"xlink:href":`#${e.icon}`},null,8,Ha)],32)):(Oo(),Mo("span",{key:2,class:"avatar-text",draggable:"false",onDragstart:n[6]||(n[6]=ma((()=>{}),["prevent"])),onDrag:n[7]||(n[7]=ma((()=>{}),["prevent"])),onDragend:n[8]||(n[8]=ma((()=>{}),["prevent"]))},[gr(t.$slots,"default",{},(()=>[Ho(J(e.text),1)]),!0)],32))],38))}},[["__scopeId","data-v-959ab2de"]]),Ua=["onClick"],Wa=Sa({__name:"Carousel",props:{height:{type:String,default:"300px"},autoplay:{type:Boolean,default:!0},interval:{type:Number,default:3e3},indicatorPosition:{type:String,default:"bottom",validator:e=>["bottom","top","none"].includes(e)},arrow:{type:String,default:"hover",validator:e=>["always","hover","never"].includes(e)}},emits:["change"],setup(e,{expose:t,emit:n}){const r=e,o=n,i=xt(0),a=xt(0);let s=null;const l=fi((()=>({transform:`translateX(-${100*i.value}%)`}))),c=fi((()=>{const e=["carousel-indicators"];return e.push(`carousel-indicators-${r.indicatorPosition}`),e.join(" ")})),u=e=>{e!==i.value&&(i.value=e,o("change",e))},f=()=>{const e=(i.value+1)%a.value;u(e)},p=()=>{const e=(i.value-1+a.value)%a.value;u(e)};return Dr("carousel",{addItem:()=>{a.value++},removeItem:()=>{a.value--}}),Zn((()=>{r.autoplay&&a.value>1&&(s=setInterval(f,r.interval))})),nr((()=>{s&&(clearInterval(s),s=null)})),t({next:f,prev:p,setCurrentIndex:u}),(t,n)=>(Oo(),Mo("div",{class:"carousel",style:B({height:e.height})},[Bo("div",{class:"carousel-container",style:B(l.value)},[gr(t.$slots,"default",{},void 0,!0)],4),"none"!==e.indicatorPosition?(Oo(),Mo("div",{key:0,class:U(c.value)},[(Oo(!0),Mo(xo,null,hr(a.value,((e,t)=>(Oo(),Mo("button",{key:t,class:U(["carousel-indicator",{active:t===i.value}]),onClick:e=>u(t)},null,10,Ua)))),128))],2)):Vo("",!0),"never"!==e.arrow?(Oo(),Mo("button",{key:1,class:"carousel-arrow carousel-arrow-left",onClick:p}," ‹ ")):Vo("",!0),"never"!==e.arrow?(Oo(),Mo("button",{key:2,class:"carousel-arrow carousel-arrow-right",onClick:f}," › ")):Vo("",!0)],4))}},[["__scopeId","data-v-09fcb238"]]),qa={class:"carousel-item"},Ka=Sa({__name:"CarouselItem",setup(e){const t=Br("carousel",null);return Zn((()=>{null==t||t.addItem()})),nr((()=>{null==t||t.removeItem()})),(e,t)=>(Oo(),Mo("div",qa,[gr(e.$slots,"default",{},void 0,!0)]))}},[["__scopeId","data-v-a7a237f0"]]),Ga={key:0,class:"base-card__header"};const Qa=Sa({name:"BaseCard",props:{shadow:{type:String,default:"always",validator:e=>["always","hover","never"].includes(e)},bodyStyle:{type:Object,default:()=>({})}}},[["render",function(e,t,n,r,o,i){return Oo(),Mo("div",{class:U(["base-card",{"base-card--shadow":n.shadow}])},[e.$slots.header?(Oo(),Mo("div",Ga,[gr(e.$slots,"header",{},void 0,!0)])):Vo("",!0),Bo("div",{class:"base-card__body",style:B(n.bodyStyle)},[gr(e.$slots,"default",{},void 0,!0)],4)],2)}],["__scopeId","data-v-f4e6d4c0"]]),Ja={class:"base-timeline"};const Xa=Sa({name:"BaseTimeline"},[["render",function(e,t,n,r,o,i){return Oo(),Mo("div",Ja,[gr(e.$slots,"default",{},void 0,!0)])}],["__scopeId","data-v-d29adfb9"]]),Za={name:"BaseTimelineItem",props:{timestamp:{type:String,default:""},hideTimestamp:{type:Boolean,default:!1},placement:{type:String,default:"bottom",validator:e=>["top","bottom"].includes(e)},type:{type:String,default:"",validator:e=>["primary","success","warning","danger","info",""].includes(e)},color:{type:String,default:""},size:{type:String,default:"normal",validator:e=>["normal","large"].includes(e)},icon:{type:String,default:""}},computed:{nodeClass(){const e=[`base-timeline-item__node--${this.size}`];return this.type&&e.push(`base-timeline-item__node--${this.type}`),e},nodeStyle(){const e={};return this.color&&(e.backgroundColor=this.color,e.borderColor=this.color),e},timestampClass(){return[`base-timeline-item__timestamp--${this.placement}`]}}},Ya={class:"base-timeline-item"},es={class:"base-timeline-item__wrapper"},ts={class:"base-timeline-item__content"};const ns=Sa(Za,[["render",function(e,t,n,r,o,i){return Oo(),Mo("div",Ya,[t[1]||(t[1]=Bo("div",{class:"base-timeline-item__tail"},null,-1)),Bo("div",{class:U(["base-timeline-item__node",i.nodeClass]),style:B(i.nodeStyle)},[gr(e.$slots,"dot",{},(()=>[t[0]||(t[0]=Bo("div",{class:"base-timeline-item__node-normal"},null,-1))]),!0)],6),Bo("div",es,[n.timestamp?(Oo(),Mo("div",{key:0,class:U(["base-timeline-item__timestamp",i.timestampClass])},J(n.timestamp),3)):Vo("",!0),Bo("div",ts,[gr(e.$slots,"default",{},void 0,!0)])])])}],["__scopeId","data-v-fe1a9ae8"]]),rs={name:"BaseSelect",provide(){return{select:this,registerOption:this.registerOption,unregisterOption:this.unregisterOption}},props:{modelValue:{type:[String,Number,Boolean],default:""},placeholder:{type:String,default:"请选择"},disabled:{type:Boolean,default:!1},size:{type:String,default:"default",validator:e=>["large","default","small"].includes(e)}},emits:["update:modelValue","change"],data:()=>({visible:!1,selectedLabel:"",options:[]}),watch:{modelValue:{handler(){this.updateSelectedLabel()},immediate:!0}},mounted(){this.updateSelectedLabel()},methods:{toggleDropdown(){this.disabled||(this.visible=!this.visible)},handleMouseLeave(){this.visible&&(this.visible=!1)},handleOptionClick(e,t){this.$emit("update:modelValue",e),this.$emit("change",e),this.selectedLabel=t,this.visible=!1},updateSelectedLabel(){const e=this.options.find((e=>e.value===this.modelValue));this.selectedLabel=e?e.label:""},registerOption(e){this.options.push(e),e.value===this.modelValue&&(this.selectedLabel=e.label)},unregisterOption(e){const t=this.options.findIndex((t=>t.value===e.value));t>-1&&this.options.splice(t,1)}}},os={key:0,class:"base-select__selected"},is={key:1,class:"base-select__placeholder"},as={class:"base-select__options"};const ss=Sa(rs,[["render",function(e,t,n,r,o,i){const a=lr("base-icon");return Oo(),Mo("div",{class:U(["base-select",{"is-disabled":n.disabled}])},[Bo("div",{class:U(["base-select__input",{"is-focus":o.visible}]),onClick:t[0]||(t[0]=(...e)=>i.toggleDropdown&&i.toggleDropdown(...e))},[o.selectedLabel?(Oo(),Mo("span",os,J(o.selectedLabel),1)):(Oo(),Mo("span",is,J(n.placeholder),1)),o.visible?(Oo(),Po(a,{key:2,name:"shouqi",size:"12px"})):(Oo(),Po(a,{key:3,name:"zhankai",size:"12px"}))],2),an(Bo("div",{class:"base-select__dropdown",onMouseleave:t[1]||(t[1]=(...e)=>i.handleMouseLeave&&i.handleMouseLeave(...e))},[Bo("div",as,[gr(e.$slots,"default",{},void 0,!0)])],544),[[Bi,o.visible]])],2)}],["__scopeId","data-v-d57a604f"]]);const ls=Sa({name:"BaseOption",inject:["select","registerOption","unregisterOption"],props:{value:{type:[String,Number,Boolean],required:!0},label:{type:[String,Number],default:""},disabled:{type:Boolean,default:!1}},computed:{isSelected(){return this.select.modelValue===this.value},displayLabel(){var e,t,n,r;return this.label||(null==(r=null==(n=null==(t=(e=this.$slots).default)?void 0:t.call(e))?void 0:n[0])?void 0:r.children)||""}},mounted(){this.registerOption({value:this.value,label:this.displayLabel})},beforeUnmount(){this.unregisterOption({value:this.value,label:this.displayLabel})},methods:{handleClick(){this.disabled||this.select.handleOptionClick(this.value,this.displayLabel)}}},[["render",function(e,t,n,r,o,i){return Oo(),Mo("div",{class:U(["base-option",{"is-selected":i.isSelected,"is-disabled":n.disabled}]),onClick:t[0]||(t[0]=(...e)=>i.handleClick&&i.handleClick(...e))},[gr(e.$slots,"default",{},(()=>[Ho(J(n.label),1)]),!0)],2)}],["__scopeId","data-v-4e1b9367"]]),cs={name:"BaseCheckbox",props:{modelValue:{type:[Boolean,String,Number,Array],default:!1},label:{type:[String,Number,Boolean],default:""},disabled:{type:Boolean,default:!1},size:{type:String,default:"default",validator:e=>["large","default","small"].includes(e)}},emits:["update:modelValue","change"],computed:{model:{get(){return this.modelValue},set(e){this.$emit("update:modelValue",e)}},isChecked(){return Array.isArray(this.modelValue)?this.modelValue.includes(this.label):!0===this.modelValue}},methods:{handleChange(e){this.$emit("change",e.target.checked)}}},us={class:"base-checkbox__input"},fs=["disabled","value"],ps={key:0,class:"base-checkbox__label"};const ds=Sa(cs,[["render",function(e,t,n,r,o,i){return Oo(),Mo("label",{class:U(["base-checkbox",{"is-disabled":n.disabled,"is-checked":i.isChecked}])},[Bo("span",us,[t[2]||(t[2]=Bo("span",{class:"base-checkbox__inner"},null,-1)),an(Bo("input",{"onUpdate:modelValue":t[0]||(t[0]=e=>i.model=e),type:"checkbox",class:"base-checkbox__original",disabled:n.disabled,value:n.label,onChange:t[1]||(t[1]=(...e)=>i.handleChange&&i.handleChange(...e))},null,40,fs),[[ca,i.model]])]),e.$slots.default||n.label?(Oo(),Mo("span",ps,[gr(e.$slots,"default",{},(()=>[Ho(J(n.label),1)]),!0)])):Vo("",!0)],2)}],["__scopeId","data-v-cec5e1b2"]]),hs={name:"BaseRadio",props:{modelValue:{type:[String,Number,Boolean],default:""},label:{type:[String,Number,Boolean],default:""},disabled:{type:Boolean,default:!1},size:{type:String,default:"default",validator:e=>["large","default","small"].includes(e)}},emits:["update:modelValue","change"],computed:{model:{get(){return this.modelValue},set(e){this.$emit("update:modelValue",e)}},isChecked(){return this.modelValue===this.label}},methods:{handleChange(e){this.$emit("change",e.target.value)}}},gs={class:"base-radio__input"},ms=["disabled","value"],vs={key:0,class:"base-radio__label"};const ys=Sa(hs,[["render",function(e,t,n,r,o,i){return Oo(),Mo("label",{class:U(["base-radio",{"is-disabled":n.disabled,"is-checked":i.isChecked}])},[Bo("span",gs,[t[2]||(t[2]=Bo("span",{class:"base-radio__inner"},null,-1)),an(Bo("input",{"onUpdate:modelValue":t[0]||(t[0]=e=>i.model=e),type:"radio",class:"base-radio__original",disabled:n.disabled,value:n.label,onChange:t[1]||(t[1]=(...e)=>i.handleChange&&i.handleChange(...e))},null,40,ms),[[fa,i.model]])]),e.$slots.default||n.label?(Oo(),Mo("span",vs,[gr(e.$slots,"default",{},(()=>[Ho(J(n.label),1)]),!0)])):Vo("",!0)],2)}],["__scopeId","data-v-d9779c99"]]),bs={name:"BaseRadioGroup",provide(){return{radioGroup:this}},props:{modelValue:{type:[String,Number,Boolean],default:""},disabled:{type:Boolean,default:!1},size:{type:String,default:"default",validator:e=>["large","default","small"].includes(e)},textColor:{type:String,default:""},fill:{type:String,default:""}},emits:["update:modelValue","change"],watch:{modelValue(e){this.$emit("change",e)}}},_s={class:"base-radio-group",role:"radiogroup"};const ws=Sa(bs,[["render",function(e,t,n,r,o,i){return Oo(),Mo("div",_s,[gr(e.$slots,"default",{},void 0,!0)])}],["__scopeId","data-v-a0bc7173"]]),Ss=["d"];const xs=Sa({name:"BaseIcon",props:{name:{type:String,default:""},size:{type:[String,Number],default:"16px"},color:{type:String,default:"currentColor"}},computed:{iconClass(){return{[`base-icon--${this.name}`]:this.name}},iconStyle(){return{fontSize:"number"==typeof this.size?`${this.size}px`:this.size,color:this.color}},iconPath(){return{search:"M460.861506 0a460.852728 460.852728 0 0 1 355.030535 754.713943l194.616368 194.616368a43.19124 43.19124 0 0 1-56.272852 65.261891l-4.896469-4.092571-194.616368-194.616367A460.852728 460.852728 0 1 1 460.861506 0z m0 86.382481a374.470247 374.470247 0 1 0 259.293606 644.579934 40.194894 40.194894 0 0 1 4.896469-5.84653V724.969722a43.337404 43.337404 0 0 1 5.84653-4.969551 374.470247 374.470247 0 0 0-270.109687-633.61769z",warning:"M955.7 856l-416-720c-6.2-10.7-16.9-16-27.7-16s-21.6 5.3-27.7 16l-416 720C56 877.4 71.4 904 96 904h832c24.6 0 40-26.6 27.7-48zM480 416c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v184c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V416zm32 352a48.01 48.01 0 0 1 0-96 48.01 48.01 0 0 1 0 96z",info:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm32 664c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V456c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272zm-32-344a48.01 48.01 0 0 1 0-96 48.01 48.01 0 0 1 0 96z",document:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0 0 42 42h216v494z",loading:"M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 0 0-94.3-139.9 437.71 437.71 0 0 0-139.9-94.3C637 83.6 579.4 72 520 72s-117 11.6-171.3 34.6a440.45 440.45 0 0 0-139.9 94.3 437.71 437.71 0 0 0-94.3 139.9C91.6 395 80 452.6 80 512s11.6 117 34.6 171.3a440.45 440.45 0 0 0 94.3 139.9 437.71 437.71 0 0 0 139.9 94.3C475 940.4 532.6 952 592 952c19.9 0 36 16.1 36 36s-16.1 36-36 36c-59.4 0-117-11.6-171.3-34.6a512.69 512.69 0 0 1-139.9-94.3c-40.8-35.4-73.4-76.3-94.3-139.9C163.6 709 152 651.4 152 592s11.6-117 34.6-171.3a512.69 512.69 0 0 1 94.3-139.9c35.4-40.8 76.3-73.4 139.9-94.3C467 163.6 524.6 152 584 152c19.9 0 36 16.1 36 36s-16.1 36-36 36z",zhankai:"M192 384l320 384 320-384z",shouqi:"M512 320L192 704h639.936z",shoucang:"M802.2336 960a43.072 43.072 0 0 1-19.84-4.864L512.0576 815.488 241.9776 955.2a43.456 43.456 0 0 1-44.992-3.264 41.792 41.792 0 0 1-17.024-41.088l51.584-295.808L12.8576 405.504a41.6 41.6 0 0 1-10.752-43.136 42.56 42.56 0 0 1 34.56-28.608l302.016-43.072L473.6576 21.504c14.4-28.672 62.272-28.672 76.672 0l135.04 269.184 302.08 43.072c16.064 2.368 29.44 13.44 34.432 28.672a41.6 41.6 0 0 1-10.752 43.136l-218.432 209.408 51.648 295.808a41.6 41.6 0 0 1-9.472 34.176 43.072 43.072 0 0 1-32.64 14.912l-0.064 0.128zM512.0576 725.952c6.848 0 13.632 1.6 19.84 4.864l213.44 110.272-40.768-233.6a41.6 41.6 0 0 1 12.288-37.12L889.6576 404.736l-238.592-34.112a42.624 42.624 0 0 1-32.192-23.04l-106.688-212.48-106.688 212.48a42.624 42.624 0 0 1-32.192 23.04l-238.592 34.112L307.3216 570.24a41.6 41.6 0 0 1 12.288 37.12l-40.768 233.664 213.44-110.272a43.136 43.136 0 0 1 19.84-4.864v0.064z",yishoucang:"M802.2336 960a43.072 43.072 0 0 1-19.84-4.864L512.0576 815.488 241.9776 955.2a43.456 43.456 0 0 1-44.992-3.264 41.792 41.792 0 0 1-17.024-41.088l51.584-295.808L12.8576 405.504a41.6 41.6 0 0 1-10.752-43.136 42.56 42.56 0 0 1 34.56-28.608l302.016-43.072L473.6576 21.504c14.4-28.672 62.272-28.672 76.672 0l135.04 269.184 302.08 43.072c16.064 2.368 29.44 13.44 34.432 28.672a41.6 41.6 0 0 1-10.752 43.136l-218.432 209.408 51.648 295.808a41.6 41.6 0 0 1-9.472 34.176 43.072 43.072 0 0 1-32.64 14.912l-0.064 0.128z",bianji:"M862.709333 116.042667a32 32 0 1 1 45.248 45.248L455.445333 613.813333a32 32 0 1 1-45.258666-45.258666L862.709333 116.053333zM853.333333 448a32 32 0 0 1 64 0v352c0 64.8-52.533333 117.333333-117.333333 117.333333H224c-64.8 0-117.333333-52.533333-117.333333-117.333333V224c0-64.8 52.533333-117.333333 117.333333-117.333333h341.333333a32 32 0 0 1 0 64H224a53.333333 53.333333 0 0 0-53.333333 53.333333v576a53.333333 53.333333 0 0 0 53.333333 53.333333h576a53.333333 53.333333 0 0 0 53.333333-53.333333V448z",jieru:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm192 472c0 4.4-3.6 8-8 8H544v152c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V544H328c-4.4 0-8-3.6-8-8v-48c0-4.4 3.6-8 8-8h152V328c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v152h152c4.4 0 8 3.6 8 8v48z",shezhi:"M924.8 625.7l-65.5-56c3.1-19 4.7-38.4 4.7-57.8s-1.6-38.8-4.7-57.8l65.5-56a32.03 32.03 0 0 0 9.3-35.2l-.9-2.6a443.74 443.74 0 0 0-79.7-137.9l-1.8-2.1a32.12 32.12 0 0 0-35.1-9.5l-81.3 28.9c-30-24.6-63.5-44-99.7-57.6l-15.7-85a32.05 32.05 0 0 0-25.8-25.7l-2.7-.5c-52.1-9.4-106.9-9.4-159 0l-2.7.5a32.05 32.05 0 0 0-25.8 25.7l-15.8 85.4a351.86 351.86 0 0 0-99 57.4l-81.9-29.1a32 32 0 0 0-35.1 9.5l-1.8 2.1a446.02 446.02 0 0 0-79.7 137.9l-.9 2.6c-4.5 12.5-.8 26.5 9.3 35.2l66.3 56.6c-3.1 18.8-4.6 38-4.6 57.1 0 19.2 1.5 38.4 4.6 57.1L99 625.5a32.03 32.03 0 0 0-9.3 35.2l.9 2.6c18.1 50.4 44.9 96.9 79.7 137.9l1.8 2.1a32.12 32.12 0 0 0 35.1 9.5l81.9-29.1c29.8 24.5 63.1 43.9 99 57.4l15.8 85.4a32.05 32.05 0 0 0 25.8 25.7l2.7.5a449.4 449.4 0 0 0 159 0l2.7-.5a32.05 32.05 0 0 0 25.8-25.7l15.7-85a350 350 0 0 0 99.7-57.6l81.3 28.9a32 32 0 0 0 35.1-9.5l1.8-2.1c34.8-41.1 61.6-87.5 79.7-137.9l.9-2.6c4.5-12.3.8-26.3-9.3-35zM512 701c-104.9 0-190-85.1-190-190s85.1-190 190-190 190 85.1 190 190-85.1 190-190 190z",windows:"M120.83 208.13l344.49-47.04v331.65H120.83V208.13zm0 607.78l344.49 47.04V531.3H120.83v284.61zm384.34 50.02L903.17 896V531.3H505.17v334.63zM505.17 128L903.17 96v435.3H505.17V128z",mac:"M747.4 535.7c-.4-68.2 30.5-119.6 92.9-157.5-34.9-50-89.5-76.7-164.1-81.2-67.4-4.7-131.9 39.7-166.2 39.7-35.3 0-89.7-38.1-147.9-37-76.1 1.1-146.4 44.7-185.3 113.7-79.3 138.8-20.5 344.2 56.3 456.7 37.4 55.2 82.1 117.2 141.2 114.9 57.2-2.3 78.9-36.9 148.1-36.9 68.2 0 88.9 36.9 149.3 35.8 61.9-1.1 99.9-56.8 136.3-112.1 42.2-64.3 59.1-126.6 59.9-129.8-1.3-.6-114.6-44.3-115.3-175.5z",ios:"M747.4 535.7c-.4-68.2 30.5-119.6 92.9-157.5-34.9-50-89.5-76.7-164.1-81.2-67.4-4.7-131.9 39.7-166.2 39.7-35.3 0-89.7-38.1-147.9-37-76.1 1.1-146.4 44.7-185.3 113.7-79.3 138.8-20.5 344.2 56.3 456.7 37.4 55.2 82.1 117.2 141.2 114.9 57.2-2.3 78.9-36.9 148.1-36.9 68.2 0 88.9 36.9 149.3 35.8 61.9-1.1 99.9-56.8 136.3-112.1 42.2-64.3 59.1-126.6 59.9-129.8-1.3-.6-114.6-44.3-115.3-175.5z",android:"M765.7 486.8L620.7 182.2c-3.4-7.2-11.1-11.7-19.5-11.7s-16.1 4.5-19.5 11.7L436.6 486.8a32.05 32.05 0 0 0 28.6 46.4c7.9 0 15.3-2.9 21.1-8.2l23.9-23.9h185.6l23.9 23.9c5.8 5.3 13.2 8.2 21.1 8.2 17.7 0 32-14.3 32-32 0-7.6-2.6-14.8-7.4-20.4zM334.1 548.9L149.9 757.1a32.06 32.06 0 0 0 0 45.3l208.2 208.2c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L195.2 757.1 403.4 548.9c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0z",xiazai:"M505.7 661a8 8 0 0 0 12.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z",expand:"M342 88H120c-17.7 0-32 14.3-32 32v222c0 8.8 7.2 16 16 16h48c8.8 0 16-7.2 16-16V168h174c8.8 0 16-7.2 16-16v-48c0-8.8-7.2-16-16-16zm578 0H698c-8.8 0-16 7.2-16 16v48c0 8.8 7.2 16 16 16h174v174c0 8.8 7.2 16 16 16h48c8.8 0 16-7.2 16-16V120c0-17.7-14.3-32-32-32zM342 856h174c8.8 0 16-7.2 16-16v-48c0-8.8-7.2-16-16-16H342V602c0-8.8-7.2-16-16-16h-48c-8.8 0-16 7.2-16 16v222c0 17.7 14.3 32 32 32h174c8.8 0 16-7.2 16-16v-48c0-8.8-7.2-16-16-16zm578 0c17.7 0 32-14.3 32-32V602c0-8.8-7.2-16-16-16h-48c-8.8 0-16 7.2-16 16v174H698c-8.8 0-16 7.2-16 16v48c0 8.8 7.2 16 16 16h222z",fullscreen:"M177.536 385.728c25.216 0 46.08-18.88 49.088-43.264l0.384-6.272v-237.184h237.184c22.976 0 42.24-15.616 47.872-36.8l1.28-6.464 0.384-6.208c0-25.28-18.88-46.08-43.264-49.152L464.192 0h-286.72a49.536 49.536 0 0 0-49.088 43.328L128 49.536v286.72c0 27.328 22.144 49.472 49.536 49.472zM846.464 768c25.28 0 46.08-18.88 49.152-43.328l0.384-6.208v-286.72a49.536 49.536 0 0 0-98.624-6.208l-0.384 6.272V669.056l-237.184-0.064a49.536 49.536 0 0 0-47.872 36.8l-1.28 6.464-0.384 6.208c0 25.28 18.88 46.08 43.264 49.152L559.808 768h286.72z",fullscreen_exit:"M400.192-64a49.536 49.536 0 0 0-49.088 43.328l-0.384 6.208V222.72H113.6a49.536 49.536 0 0 0-47.872 36.8l-1.28 6.464L64 272.192c0 25.28 18.88 46.08 43.328 49.152l6.208 0.384h286.72c25.216 0 46.08-18.88 49.088-43.264l0.384-6.272v-286.72a49.536 49.536 0 0 0-49.536-49.472zM623.808 446.272a49.536 49.536 0 0 0-49.152 43.264l-0.384 6.272v286.72a49.536 49.536 0 0 0 98.624 6.144l0.384-6.208V545.28l237.184 0.064c22.976 0 42.24-15.616 47.872-36.8l1.28-6.464 0.384-6.208c0-25.28-18.88-46.08-43.328-49.152l-6.208-0.384h-286.72z",minus:"M909.824 345.6H114.176A50.752 50.752 0 0 0 64 396.8c0 28.288 22.464 51.2 50.176 51.2h795.648c27.712 0 50.176-22.912 50.176-51.2 0-28.288-22.464-51.2-50.176-51.2z",close:"M581.824 383.936l299.712 299.648a49.472 49.472 0 0 1-69.888 69.888L511.936 453.824 212.48 753.472a49.472 49.472 0 0 1-69.888-69.888L441.984 384l-299.52-299.648a49.472 49.472 0 1 1 69.952-69.952L512 313.984l299.52-299.52a49.152 49.152 0 0 1 69.888 0 49.472 49.472 0 0 1 0 69.952l-299.52 299.52z",check:"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z",fold:"M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM142.4 642.1L298.7 519a8.84 8.84 0 0 0 0-13.9L142.4 381.9c-5.8-4.6-14.4-.5-14.4 6.9v246.3c0 7.4 8.5 11.5 14.4 6.9z",shield:"M512 64L264.8 125.3l-39.7 221.1c-14.9 83.1 21.2 166.6 96.3 221.8l126.6 93.3 126.6-93.3c75.1-55.2 111.2-138.7 96.3-221.8L630.2 125.3 512 64zm0 64l200.2 49.1 32.2 179.4c12.1 67.5-17.2 135.2-78.1 179.9L512 631.3 357.7 536.4c-60.9-44.7-90.2-112.4-78.1-179.9l32.2-179.4L512 128z",logout:"M829.44 788.48C952.32 686.08 1024 542.72 1024 384c0-281.6-230.4-512-512-512s-512 230.4-512 512c0 158.72 71.68 302.08 194.56 399.36 20.48 15.36 56.32 15.36 71.68-10.24 15.36-20.48 10.24-51.2-10.24-66.56C158.72 624.64 102.4 506.88 102.4 384c0-225.28 184.32-409.6 409.6-409.6 225.28 0 409.6 184.32 409.6 409.6 0 128-56.32 240.64-153.6 322.56-20.48 15.36-25.6 51.2-10.24 71.68 15.36 20.48 51.2 25.6 71.68 10.24zM512 896c30.72 0 51.2-23.917714 51.2-59.757714v-358.4c0-35.84-20.48-59.684571-51.2-59.684572-30.72 0-51.2 23.844571-51.2 59.684572v358.4C460.8 872.082286 481.28 896 512 896z",plus:"M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z M176 474h672q8 0 8 8v60q0 8-8 8H176q-8 0-8-8v-60q0-8 8-8z",link:"M574 665.4a8.03 8.03 0 0 0-11.3 0L446.5 781.6c-53.8 53.8-144.6 59.5-204 0-59.5-59.5-53.8-150.2 0-204l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3l-39.8-39.8a8.03 8.03 0 0 0-11.3 0L191.4 526.5c-84.6 84.6-84.6 221.5 0 306s221.5 84.6 306 0l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3L574 665.4zm258.6-474c-84.6-84.6-221.5-84.6-306 0L410.3 307.6a8.03 8.03 0 0 0 0 11.3l39.7 39.7c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c53.8-53.8 144.6-59.5 204 0 59.5 59.5 53.8 150.2 0 204L665.3 562.6a8.03 8.03 0 0 0 0 11.3l39.8 39.8c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c84.5-84.6 84.5-221.5 0-306.1zM610.1 372.3a8.03 8.03 0 0 0-11.3 0L372.3 598.7a8.03 8.03 0 0 0 0 11.3l39.6 39.6c3.1 3.1 8.2 3.1 11.3 0l226.4-226.4c3.1-3.1 3.1-8.2 0-11.3l-39.5-39.6z"}[this.name]||""}}},[["render",function(e,t,n,r,o,i){return Oo(),Mo("i",{class:U(["base-icon",i.iconClass]),style:B(i.iconStyle),draggable:"false",onDragstart:t[3]||(t[3]=ma((()=>{}),["prevent"])),onDrag:t[4]||(t[4]=ma((()=>{}),["prevent"])),onDragend:t[5]||(t[5]=ma((()=>{}),["prevent"]))},[n.name?(Oo(),Mo("svg",{key:0,viewBox:"0 0 1024 1024",width:"1em",height:"1em",fill:"currentColor",draggable:"false",onDragstart:t[0]||(t[0]=ma((()=>{}),["prevent"])),onDrag:t[1]||(t[1]=ma((()=>{}),["prevent"])),onDragend:t[2]||(t[2]=ma((()=>{}),["prevent"]))},[Bo("path",{d:i.iconPath},null,8,Ss)],32)):gr(e.$slots,"default",{key:1},void 0,!0)],38)}],["__scopeId","data-v-715f7929"]]),Cs=["xlink:href","href"];const ks=Sa({name:"SvgIcon",props:{iconClass:{type:String,required:!0},className:{type:String,default:""},size:{type:[String,Number],default:"16px"},color:{type:String,default:"currentColor"}},computed:{iconName(){return`#icon-${this.iconClass}`},svgClass(){return this.className?"svg-icon "+this.className:"svg-icon"},svgStyle(){return{fontSize:"number"==typeof this.size?`${this.size}px`:this.size,color:this.color,width:"1em",height:"1em"}}}},[["render",function(e,t,n,r,o,i){return Oo(),Mo("svg",Ko({class:i.svgClass,style:i.svgStyle,"aria-hidden":"true"},function(e,t){const n={};for(const r in e)n[t&&/[A-Z]/.test(r)?`on:${r}`:L(r)]=e[r];return n}(e.$listeners,!0)),[Bo("use",{"xlink:href":i.iconName,href:i.iconName},null,8,Cs)],16)}],["__scopeId","data-v-34362d91"]]);const Es=Sa({name:"BaseMenu",provide(){return{menu:this,activeIndex:()=>this.activeIndex,setActiveIndex:this.setActiveIndex,collapse:()=>this.collapse,mode:()=>this.mode}},props:{mode:{type:String,default:"vertical",validator:e=>["vertical","horizontal"].includes(e)},collapse:{type:Boolean,default:!1},backgroundColor:{type:String,default:"#273444"},textColor:{type:String,default:"#ffffff"},activeTextColor:{type:String,default:"#536ce6"},defaultActive:{type:String,default:""},uniqueOpened:{type:Boolean,default:!1},collapseTransition:{type:Boolean,default:!0}},emits:["select"],data(){return{activeIndex:this.defaultActive}},computed:{menuStyle(){return{backgroundColor:this.backgroundColor,color:this.textColor,"--menu-active-color":this.activeTextColor,"--menu-text-color":this.textColor,"--menu-bg-color":this.backgroundColor}}},methods:{setActiveIndex(e){this.activeIndex=e,this.$emit("select",e)}}},[["render",function(e,t,n,r,o,i){return Oo(),Mo("nav",{class:U(["base-menu",{"base-menu--collapse":n.collapse},{"base-menu--vertical":"vertical"===n.mode},{"base-menu--horizontal":"horizontal"===n.mode}]),style:B(i.menuStyle)},[gr(e.$slots,"default",{},void 0,!0)],6)}],["__scopeId","data-v-514d5b85"]]),As={name:"BaseMenuItem",inject:{menu:{default:null},activeIndex:{default:()=>""},setActiveIndex:{default:()=>{}},collapse:{default:()=>!1}},props:{index:{type:String,required:!0},disabled:{type:Boolean,default:!1}},computed:{isActive(){return this.activeIndex()===this.index},isCollapse(){return this.collapse()}},methods:{handleClick(){this.disabled||this.setActiveIndex(this.index)}}},Ts={class:"base-menu-item__content"};const Os=Sa(As,[["render",function(e,t,n,r,o,i){return Oo(),Mo("li",{class:U(["base-menu-item",{"base-menu-item--active":i.isActive},{"base-menu-item--disabled":n.disabled},{"base-menu-item--collapse":i.isCollapse}]),onClick:t[0]||(t[0]=(...e)=>i.handleClick&&i.handleClick(...e))},[Bo("div",Ts,[gr(e.$slots,"default",{},void 0,!0)])],2)}],["__scopeId","data-v-38f6cc46"]]);const Is=Sa({name:"BaseScrollbar",props:{height:{type:String,default:""},maxHeight:{type:String,default:""},always:{type:Boolean,default:!1},tag:{type:String,default:"div"}},data:()=>({scrollTop:0,scrollLeft:0,scrollHeight:0,scrollWidth:0,clientHeight:0,clientWidth:0,isDragging:!1,dragDirection:"",startY:0,startX:0,startScrollTop:0,startScrollLeft:0}),computed:{containerStyle(){const e={};return this.height&&(e.height=this.height),this.maxHeight&&(e.maxHeight=this.maxHeight),e},contentStyle:()=>({}),showVerticalBar(){return this.always||this.scrollHeight>this.clientHeight},showHorizontalBar(){return this.always||this.scrollWidth>this.clientWidth},verticalThumbStyle(){const e=Math.max(this.clientHeight/this.scrollHeight*100,10);return{height:`${e}%`,transform:`translateY(${this.scrollTop/(this.scrollHeight-this.clientHeight)*(100-e)}%)`}},horizontalThumbStyle(){const e=Math.max(this.clientWidth/this.scrollWidth*100,10);return{width:`${e}%`,transform:`translateX(${this.scrollLeft/(this.scrollWidth-this.clientWidth)*(100-e)}%)`}}},mounted(){this.updateScrollInfo(),this.addEventListeners()},beforeUnmount(){this.removeEventListeners()},methods:{updateScrollInfo(){const e=this.$refs.scrollContainer;e&&(this.scrollTop=e.scrollTop,this.scrollLeft=e.scrollLeft,this.scrollHeight=e.scrollHeight,this.scrollWidth=e.scrollWidth,this.clientHeight=e.clientHeight,this.clientWidth=e.clientWidth)},handleScroll(){this.updateScrollInfo()},handleBarMouseDown(e,t){t.preventDefault(),this.isDragging=!0,this.dragDirection=e,"vertical"===e?(this.startY=t.clientY,this.startScrollTop=this.scrollTop):(this.startX=t.clientX,this.startScrollLeft=this.scrollLeft),document.addEventListener("mousemove",this.handleMouseMove),document.addEventListener("mouseup",this.handleMouseUp)},handleMouseMove(e){if(!this.isDragging)return;const t=this.$refs.scrollContainer;if(t)if("vertical"===this.dragDirection){const n=(e.clientY-this.startY)/this.clientHeight,r=this.startScrollTop+n*this.scrollHeight;t.scrollTop=Math.max(0,Math.min(r,this.scrollHeight-this.clientHeight))}else{const n=(e.clientX-this.startX)/this.clientWidth,r=this.startScrollLeft+n*this.scrollWidth;t.scrollLeft=Math.max(0,Math.min(r,this.scrollWidth-this.clientWidth))}},handleMouseUp(){this.isDragging=!1,this.dragDirection="",document.removeEventListener("mousemove",this.handleMouseMove),document.removeEventListener("mouseup",this.handleMouseUp)},addEventListeners(){window.addEventListener("resize",this.updateScrollInfo)},removeEventListeners(){window.removeEventListener("resize",this.updateScrollInfo)},scrollTo(e){const t=this.$refs.scrollContainer;t&&("number"==typeof e?t.scrollTop=e:t.scrollTo(e))}}},[["render",function(e,t,n,r,o,i){return Oo(),Mo("div",{ref:"scrollContainer",class:U(["base-scrollbar",{"base-scrollbar--always":n.always}]),style:B(i.containerStyle),onScroll:t[2]||(t[2]=(...e)=>i.handleScroll&&i.handleScroll(...e))},[Bo("div",{ref:"scrollContent",class:"base-scrollbar__content",style:B(i.contentStyle)},[gr(e.$slots,"default",{},void 0,!0)],4),an(Bo("div",{class:"base-scrollbar__bar base-scrollbar__bar--vertical",onMousedown:t[0]||(t[0]=e=>i.handleBarMouseDown("vertical",e))},[Bo("div",{class:"base-scrollbar__thumb",style:B(i.verticalThumbStyle)},null,4)],544),[[Bi,i.showVerticalBar]]),an(Bo("div",{class:"base-scrollbar__bar base-scrollbar__bar--horizontal",onMousedown:t[1]||(t[1]=e=>i.handleBarMouseDown("horizontal",e))},[Bo("div",{class:"base-scrollbar__thumb",style:B(i.horizontalThumbStyle)},null,4)],544),[[Bi,i.showHorizontalBar]])],38)}],["__scopeId","data-v-d5cc918b"]]),js={name:"BaseTooltip",props:{content:{type:String,default:""},placement:{type:String,default:"top",validator:e=>["top","top-start","top-end","bottom","bottom-start","bottom-end","left","left-start","left-end","right","right-start","right-end"].includes(e)},effect:{type:String,default:"dark",validator:e=>["dark","light"].includes(e)},trigger:{type:String,default:"hover",validator:e=>["hover","click","focus","manual"].includes(e)},disabled:{type:Boolean,default:!1},offset:{type:Number,default:8},showAfter:{type:Number,default:0},hideAfter:{type:Number,default:200}},data:()=>({visible:!1,tooltipStyle:{},showTimer:null,hideTimer:null}),beforeUnmount(){this.clearTimers()},methods:{show(){this.disabled||(this.clearTimers(),this.showAfter>0?this.showTimer=setTimeout((()=>{this.visible=!0,this.$nextTick(this.updatePosition)}),this.showAfter):(this.visible=!0,this.$nextTick(this.updatePosition)))},hide(){this.clearTimers(),this.hideAfter>0?this.hideTimer=setTimeout((()=>{this.visible=!1}),this.hideAfter):this.visible=!1},handleMouseEnter(){"hover"===this.trigger&&this.show()},handleMouseLeave(){"hover"===this.trigger&&this.hide()},handleClick(){"click"===this.trigger&&(this.visible?this.hide():this.show())},clearTimers(){this.showTimer&&(clearTimeout(this.showTimer),this.showTimer=null),this.hideTimer&&(clearTimeout(this.hideTimer),this.hideTimer=null)},updatePosition(){const e=this.$refs.trigger,t=this.$refs.tooltip;if(!e||!t)return;const n=e.getBoundingClientRect(),r=t.getBoundingClientRect(),{placement:o,offset:i}=this;let a=0,s=0;switch(o.split("-")[0]){case"top":a=n.top-r.height-i,s=n.left+(n.width-r.width)/2;break;case"bottom":a=n.bottom+i,s=n.left+(n.width-r.width)/2;break;case"left":a=n.top+(n.height-r.height)/2,s=n.left-r.width-i;break;case"right":a=n.top+(n.height-r.height)/2,s=n.right+i}o.includes("-start")?["top","bottom"].includes(o.split("-")[0])?s=n.left:a=n.top:o.includes("-end")&&(["top","bottom"].includes(o.split("-")[0])?s=n.right-r.width:a=n.bottom-r.height);const l=window.innerWidth,c=window.innerHeight;s<0&&(s=8),s+r.width>l&&(s=l-r.width-8),a<0&&(a=8),a+r.height>c&&(a=c-r.height-8),this.tooltipStyle={position:"fixed",top:`${a}px`,left:`${s}px`,zIndex:9999}}}},Ls={class:"base-tooltip__content"};const Ms=Sa(js,[["render",function(e,t,n,r,o,i){return Oo(),Mo("div",{ref:"trigger",class:"base-tooltip",onMouseenter:t[0]||(t[0]=(...e)=>i.handleMouseEnter&&i.handleMouseEnter(...e)),onMouseleave:t[1]||(t[1]=(...e)=>i.handleMouseLeave&&i.handleMouseLeave(...e)),onClick:t[2]||(t[2]=(...e)=>i.handleClick&&i.handleClick(...e))},[gr(e.$slots,"default",{},void 0,!0),(Oo(),Po(vn,{to:"body"},[No(ki,{name:"tooltip-fade"},{default:on((()=>[an(Bo("div",{ref:"tooltip",class:U(["base-tooltip__popper",`base-tooltip__popper--${n.placement}`,`base-tooltip__popper--${n.effect}`]),style:B(o.tooltipStyle)},[Bo("div",Ls,[gr(e.$slots,"content",{},(()=>[Ho(J(n.content),1)]),!0)]),t[3]||(t[3]=Bo("div",{class:"base-tooltip__arrow"},null,-1))],6),[[Bi,o.visible]])])),_:3})]))],544)}],["__scopeId","data-v-7dbf5dce"]]),Ps={name:"BaseLink",props:{type:{type:String,default:"default",validator:e=>["default","primary","success","warning","danger","info"].includes(e)},underline:{type:Boolean,default:!0},disabled:{type:Boolean,default:!1},href:{type:String,default:""},target:{type:String,default:"_self",validator:e=>["_blank","_self","_parent","_top"].includes(e)},rel:{type:String,default:""},icon:{type:String,default:""},suffixIcon:{type:String,default:""}},emits:["click"],computed:{tag(){return this.href?"a":"span"}},methods:{handleClick(e){this.disabled?e.preventDefault():this.$emit("click",e)}}},Rs={class:"base-link__content"};const zs=Sa(Ps,[["render",function(e,t,n,r,o,i){const a=lr("base-icon");return Oo(),Po(ur(i.tag),{class:U(["base-link",`base-link--${n.type}`,{"base-link--disabled":n.disabled,"base-link--underline":n.underline&&!n.disabled}]),href:n.href,target:n.target,rel:n.rel,onClick:i.handleClick},{default:on((()=>[n.icon?(Oo(),Po(a,{key:0,name:n.icon,class:"base-link__icon"},null,8,["name"])):Vo("",!0),Bo("span",Rs,[gr(e.$slots,"default",{},void 0,!0)]),n.suffixIcon?(Oo(),Po(a,{key:1,name:n.suffixIcon,class:"base-link__suffix-icon"},null,8,["name"])):Vo("",!0)])),_:3},8,["class","href","target","rel","onClick"])}],["__scopeId","data-v-bf2b2de6"]]),$s={name:"BaseHeader",props:{height:{type:String,default:"60px"},backgroundColor:{type:String,default:"#ffffff"},textColor:{type:String,default:"#333333"},shadow:{type:Boolean,default:!0},border:{type:Boolean,default:!1},padding:{type:String,default:"0 20px"}},computed:{headerStyle(){return{height:this.height,backgroundColor:this.backgroundColor,color:this.textColor,padding:this.padding}}}},Ds={class:"base-header__content"};const Bs=Sa($s,[["render",function(e,t,n,r,o,i){return Oo(),Mo("header",{class:U(["base-header",{"base-header--shadow":n.shadow},{"base-header--border":n.border}]),style:B(i.headerStyle)},[Bo("div",Ds,[gr(e.$slots,"default",{},void 0,!0)])],6)}],["__scopeId","data-v-00524eae"]]),Ns={name:"BaseProgress",props:{percentage:{type:Number,default:0,validator:e=>e>=0&&e<=100},type:{type:String,default:"line",validator:e=>["line","circle","dashboard"].includes(e)},strokeWidth:{type:Number,default:6},textInside:{type:Boolean,default:!1},status:{type:String,default:"",validator:e=>["","success","exception","warning"].includes(e)},color:{type:[String,Array,Function],default:""},width:{type:Number,default:126},showText:{type:Boolean,default:!0},strokeLinecap:{type:String,default:"round",validator:e=>["butt","round","square"].includes(e)},format:{type:Function,default:null}},computed:{progressStyle(){return{height:"line"===this.type?`${this.strokeWidth}px`:"auto",width:"line"!==this.type?`${this.width}px`:"100%"}},barStyle(){const e={width:`${this.percentage}%`,borderRadius:"round"===this.strokeLinecap?this.strokeWidth/2+"px":"0"};return this.color?"string"==typeof this.color?e.backgroundColor=this.color:Array.isArray(this.color)?e.background=`linear-gradient(to right, ${this.color.join(", ")})`:"function"==typeof this.color&&(e.backgroundColor=this.color(this.percentage)):e.backgroundColor=this.statusColor,e},statusColor(){const e={success:"#67c23a",exception:"#f56c6c",warning:"#e6a23c"};return this.status&&e[this.status]?e[this.status]:100===this.percentage?e.success:"#536ce6"},displayText(){return this.format&&"function"==typeof this.format?this.format(this.percentage):"success"===this.status?"✓":"exception"===this.status?"✕":`${this.percentage}%`}}},Fs={class:"base-progress__outer"},Hs={class:"base-progress__inner"},Vs={key:0,class:"base-progress__text-inside"},Us={key:0,class:"base-progress__text"};const Ws=Sa(Ns,[["render",function(e,t,n,r,o,i){return Oo(),Mo("div",{class:U(["base-progress",`base-progress--${n.type}`,{"base-progress--text-inside":n.textInside}]),style:B(i.progressStyle)},[Bo("div",Fs,[Bo("div",Hs,[Bo("div",{class:"base-progress__bar",style:B(i.barStyle)},[n.textInside?(Oo(),Mo("div",Vs,J(i.displayText),1)):Vo("",!0)],4)])]),!n.textInside&&n.showText?(Oo(),Mo("div",Us,J(i.displayText),1)):Vo("",!0)],6)}],["__scopeId","data-v-e0d65af4"]]),qs={name:"BaseSubMenu",inject:{menu:{default:null},activeIndex:{default:()=>""},collapse:{default:()=>!1},uniqueOpened:{default:()=>!1}},props:{index:{type:String,required:!0},title:{type:String,default:""},disabled:{type:Boolean,default:!1},popperClass:{type:String,default:""},showTimeout:{type:Number,default:300},hideTimeout:{type:Number,default:300}},data:()=>({isOpened:!1,timeout:null}),computed:{isActive(){const e=this.activeIndex();return!!e&&e.startsWith(this.index)},isCollapse(){return this.collapse()}},watch:{isActive:{handler(e){e&&!this.isOpened&&this.open()},immediate:!0}},methods:{handleTitleClick(){this.disabled||this.isCollapse||this.toggle()},open(){this.disabled||(this.uniqueOpened()&&this.menu&&this.menu.$children.forEach((e=>{e!==this&&e.close&&e.close()})),this.isOpened=!0,this.$emit("open",this.index))},close(){this.isOpened=!1,this.$emit("close",this.index)},toggle(){this.isOpened?this.close():this.open()}}},Ks={class:"base-sub-menu__list"};const Gs=Sa(qs,[["render",function(e,t,n,r,o,i){return Oo(),Mo("li",{class:U(["base-sub-menu",{"base-sub-menu--active":i.isActive},{"base-sub-menu--opened":o.isOpened},{"base-sub-menu--disabled":n.disabled}])},[Bo("div",{class:"base-sub-menu__title",onClick:t[0]||(t[0]=(...e)=>i.handleTitleClick&&i.handleTitleClick(...e))},[gr(e.$slots,"title",{},(()=>[Bo("span",null,J(n.title),1)]),!0),(Oo(),Mo("svg",{class:U(["base-sub-menu__icon",{"base-sub-menu__icon--opened":o.isOpened}]),viewBox:"0 0 1024 1024",width:"1em",height:"1em"},t[1]||(t[1]=[Bo("path",{d:"M765.7 486.8L314.9 134.7A7.97 7.97 0 0 0 302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.8 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 0 0 0-50.4z"},null,-1)]),2))]),No(ki,{name:"sub-menu-collapse"},{default:on((()=>[an(Bo("ul",Ks,[gr(e.$slots,"default",{},void 0,!0)],512),[[Bi,o.isOpened]])])),_:3})],2)}],["__scopeId","data-v-1b8f2aaa"]]),Qs={name:"BaseTabs",provide(){return{tabs:this,addPane:this.addPane,removePane:this.removePane}},props:{modelValue:{type:[String,Number],default:""},type:{type:String,default:"line",validator:e=>["line","card","border-card"].includes(e)},tabPosition:{type:String,default:"top",validator:e=>["top","right","bottom","left"].includes(e)},stretch:{type:Boolean,default:!1},beforeLeave:{type:Function,default:null},activeName:{type:[String,Number],default:""}},emits:["update:modelValue","tab-click","tab-remove","tab-add"],data(){return{panes:[],activeTab:this.modelValue||this.activeName,activeBarStyle:{}}},computed:{scrollable(){return this.panes.length>6}},watch:{modelValue(e){this.activeTab=e,this.updateActiveBar()},activeName(e){this.activeTab=e,this.updateActiveBar()},activeTab(){this.$nextTick((()=>{this.updateActiveBar()}))}},mounted(){this.updateActiveBar()},methods:{addPane(e){this.panes.push(e),this.activeTab||1!==this.panes.length||(this.activeTab=e.name)},removePane(e){const t=this.panes.indexOf(e);t>-1&&this.panes.splice(t,1)},handleTabClick(e,t){if(!e.disabled){if(this.beforeLeave){if(!1===this.beforeLeave(e.name,this.activeTab))return}this.activeTab=e.name,this.$emit("update:modelValue",e.name),this.$emit("tab-click",e,t)}},handleTabRemove(e,t){this.$emit("tab-remove",e.name,t)},handleTabKeydown(e){const{code:t}=e,n=this.$refs.nav.querySelectorAll(".base-tabs__item:not(.base-tabs__item--disabled)"),r=Array.from(n).indexOf(e.target);let o;"ArrowLeft"===t||"ArrowUp"===t?o=0===r?n.length-1:r-1:"ArrowRight"!==t&&"ArrowDown"!==t||(o=r===n.length-1?0:r+1),void 0!==o&&(n[o].focus(),n[o].click(),e.preventDefault())},updateActiveBar(){this.$nextTick((()=>{var e;const t=null==(e=this.$refs.nav)?void 0:e.querySelector(".base-tabs__item--active");if(!t)return;const{offsetLeft:n,offsetWidth:r}=t;this.activeBarStyle={transform:`translateX(${n}px)`,width:`${r}px`}}))}}},Js={class:"base-tabs__header"},Xs={class:"base-tabs__nav-scroll"},Zs={ref:"nav",class:"base-tabs__nav",role:"tablist"},Ys=["id","aria-controls","aria-selected","tabindex","onClick"],el={class:"base-tabs__item-label"},tl=["onClick"],nl={class:"base-tabs__content"};const rl=Sa(Qs,[["render",function(e,t,n,r,o,i){return Oo(),Mo("div",{class:U(["base-tabs",`base-tabs--${n.type}`,`base-tabs--${n.tabPosition}`])},[Bo("div",Js,[Bo("div",{class:U(["base-tabs__nav-wrap",{"base-tabs__nav-wrap--scrollable":i.scrollable}])},[Bo("div",Xs,[Bo("div",Zs,[(Oo(!0),Mo(xo,null,hr(o.panes,((e,n)=>(Oo(),Mo("div",{id:`tab-${e.name||n}`,key:e.name||n,class:U(["base-tabs__item",{"base-tabs__item--active":e.name===o.activeTab},{"base-tabs__item--disabled":e.disabled},{"base-tabs__item--closable":e.closable}]),"aria-controls":`pane-${e.name||n}`,role:"tab","aria-selected":e.name===o.activeTab,tabindex:e.name===o.activeTab?0:-1,onClick:t=>i.handleTabClick(e,n),onKeydown:t[0]||(t[0]=(...e)=>i.handleTabKeydown&&i.handleTabKeydown(...e))},[Bo("span",el,J(e.label),1),e.closable?(Oo(),Mo("span",{key:0,class:"base-tabs__item-close",onClick:ma((t=>i.handleTabRemove(e,n)),["stop"])}," × ",8,tl)):Vo("",!0)],42,Ys)))),128)),Bo("div",{ref:"activeBar",class:"base-tabs__active-bar",style:B(o.activeBarStyle)},null,4)],512)])],2)]),Bo("div",nl,[gr(e.$slots,"default",{},void 0,!0)])],2)}],["__scopeId","data-v-a72ac9ec"]]),ol={name:"BaseTabPane",inject:{tabs:{default:null},addPane:{default:()=>{}},removePane:{default:()=>{}}},props:{label:{type:String,default:""},name:{type:[String,Number],required:!0},disabled:{type:Boolean,default:!1},closable:{type:Boolean,default:!1},lazy:{type:Boolean,default:!1}},data:()=>({loaded:!1}),computed:{isActive(){var e;return(null==(e=this.tabs)?void 0:e.activeTab)===this.name},shouldRender(){return this.isActive||this.loaded||!this.lazy}},watch:{isActive(e){e&&!this.loaded&&(this.loaded=!0)}},created(){this.tabs&&this.addPane(this)},beforeUnmount(){this.tabs&&this.removePane(this)}},il=["id","aria-labelledby","aria-hidden"];const al=Sa(ol,[["render",function(e,t,n,r,o,i){return an((Oo(),Mo("div",{id:`pane-${n.name}`,class:"base-tab-pane",role:"tabpanel","aria-labelledby":`tab-${n.name}`,"aria-hidden":!i.isActive},[gr(e.$slots,"default",{},void 0,!0)],8,il)),[[Bi,i.isActive]])}],["__scopeId","data-v-64dcaa0a"]]),sl={name:"BaseDrawer",props:{modelValue:{type:Boolean,default:!1},title:{type:String,default:""},size:{type:[String,Number],default:"30%"},direction:{type:String,default:"rtl",validator:e=>["ltr","rtl","ttb","btt"].includes(e)},modal:{type:Boolean,default:!0},modalClass:{type:String,default:""},showClose:{type:Boolean,default:!0},closeOnClickModal:{type:Boolean,default:!0},closeOnPressEscape:{type:Boolean,default:!0},openDelay:{type:Number,default:0},closeDelay:{type:Number,default:0},customClass:{type:String,default:""},destroyOnClose:{type:Boolean,default:!1},beforeClose:{type:Function,default:null},lockScroll:{type:Boolean,default:!0},withHeader:{type:Boolean,default:!0}},emits:["update:modelValue","open","opened","close","closed"],data(){return{visible:this.modelValue,opening:!1,closing:!1}},computed:{showHeader(){return this.withHeader&&(this.title||this.$slots.title)},drawerStyle(){const e={};return"ltr"===this.direction||"rtl"===this.direction?e.width="number"==typeof this.size?`${this.size}px`:this.size:e.height="number"==typeof this.size?`${this.size}px`:this.size,e}},watch:{modelValue(e){e?this.open():this.close()},visible(e){e?this.handleLockScroll():this.handleUnlockScroll()}},mounted(){this.modelValue&&this.open()},created(){this.closeOnPressEscape&&document.addEventListener("keydown",this.handleEscapeKeydown)},beforeUnmount(){this.closeOnPressEscape&&document.removeEventListener("keydown",this.handleEscapeKeydown)},methods:{open(){this.opening||(this.opening=!0,this.visible=!0,this.$emit("update:modelValue",!0),this.$emit("open"),this.$nextTick((()=>{this.opening=!1,this.$emit("opened")})))},close(){if(this.closing)return;const e=()=>{this.closing=!0,this.visible=!1,this.$emit("update:modelValue",!1),this.$emit("close"),this.$nextTick((()=>{this.closing=!1,this.$emit("closed")}))};this.beforeClose?this.beforeClose(e):e()},handleClose(){this.close()},handleWrapperClick(){this.closeOnClickModal&&this.close()},handleLockScroll(){this.lockScroll&&(document.body.style.overflow="hidden")},handleUnlockScroll(){this.lockScroll&&(document.body.style.overflow="")},handleEscapeKeydown(e){this.closeOnPressEscape&&"Escape"===e.code&&this.visible&&this.close()}}},ll={key:0,class:"base-drawer__header"},cl={class:"base-drawer__title"},ul={class:"base-drawer__body"},fl={key:1,class:"base-drawer__footer"};const pl=Sa(sl,[["render",function(e,t,n,r,o,i){return Oo(),Po(vn,{to:"body"},[No(ki,{name:"drawer-fade"},{default:on((()=>[an(Bo("div",{class:"base-drawer__wrapper",onClick:t[2]||(t[2]=(...e)=>i.handleWrapperClick&&i.handleWrapperClick(...e))},[Bo("div",{class:U(["base-drawer",`base-drawer--${n.direction}`,{"base-drawer--with-header":i.showHeader}]),style:B(i.drawerStyle),onClick:t[1]||(t[1]=ma((()=>{}),["stop"]))},[i.showHeader?(Oo(),Mo("header",ll,[gr(e.$slots,"title",{},(()=>[Bo("span",cl,J(n.title),1)]),!0),n.showClose?(Oo(),Mo("button",{key:0,class:"base-drawer__close-btn",type:"button",onClick:t[0]||(t[0]=(...e)=>i.handleClose&&i.handleClose(...e))},t[3]||(t[3]=[Bo("svg",{class:"base-drawer__close-icon",viewBox:"0 0 1024 1024"},[Bo("path",{d:"M563.8 512l262.5-312.9c4.4-5.2.7-13.1-6.1-13.1h-79.8c-4.7 0-9.2 2.1-12.3 5.7L511.6 449.8 295.1 191.7c-3.1-3.6-7.6-5.7-12.3-5.7H203c-6.8 0-10.5 7.9-6.1 13.1L459.4 512 196.9 824.9A7.95 7.95 0 0 0 203 838h79.8c4.7 0 9.2-2.1 12.3-5.7l216.5-258.1 216.5 258.1c3.1 3.6 7.6 5.7 12.3 5.7h79.8c6.8 0 10.5-7.9 6.1-13.1L563.8 512z"})],-1)]))):Vo("",!0)])):Vo("",!0),Bo("main",ul,[gr(e.$slots,"default",{},void 0,!0)]),e.$slots.footer?(Oo(),Mo("footer",fl,[gr(e.$slots,"footer",{},void 0,!0)])):Vo("",!0)],6)],512),[[Bi,o.visible]])])),_:3})])}],["__scopeId","data-v-1b0b95c9"]]);const dl=new class{constructor(){this.container=null}service(e={}){logger.log("Loading服务启动:",e),this.container&&(logger.log("关闭现有Loading实例"),this.close()),this.container=document.createElement("div"),this.container.style.cssText="\n      position: fixed;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background-color: rgba(0, 0, 0, 0.1);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      z-index: 9999;\n    ";const t=document.createElement("div");t.style.cssText="\n      border-radius: 12px;\n      padding: 24px 32px;\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      text-align: center;\n      min-width: 180px;\n      max-width: 280px;\n    ";const n=document.createElement("div");n.className="loading";const r=document.createElement("div");return r.className="loading-text",r.textContent=e.text||"",r.style.cssText="\n      margin-top: 12px;\n      color: #606266;\n      font-size: 14px;\n    ",t.appendChild(n),e.text&&t.appendChild(r),this.container.appendChild(t),document.body.appendChild(this.container),{close:()=>this.close(),updateText:e=>{r&&(r.textContent=e)}}}close(){this.container&&this.container.parentNode&&(this.container.parentNode.removeChild(this.container),this.container=null)}},hl={service:e=>dl.service(e)},gl={name:"BaseMessage",props:{message:{type:String,default:""},type:{type:String,default:"info",validator:e=>["success","warning","info","error"].includes(e)},showClose:{type:Boolean,default:!1},duration:{type:Number,default:3e3}},data:()=>({visible:!0}),mounted(){this.duration>0&&setTimeout((()=>{this.close()}),this.duration)},methods:{close(){this.visible=!1,setTimeout((()=>{this.$el.remove()}),300)},getIcon(){const e={success:"success",warning:"warning",error:"error",info:"warning"};return e[this.type]||e.info}},render(){return this.visible?pi("div",{class:["base-message",`base-message--${this.type}`,{"base-message--closable":this.showClose}],style:{position:"fixed",top:"42px",left:"50%",transform:"translateX(-50%)",zIndex:9999,padding:"9px 18px 9px 14px",borderRadius:"20px",fontSize:"14px",fontWeight:"500",boxShadow:"0 6px 16px rgba(0, 0, 0, 0.12), 0 3px 6px rgba(0, 0, 0, 0.08)",transition:"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",background:"#ffffff",color:"#fff",display:"flex",alignItems:"center",minWidth:"160px",maxWidth:"420px",backdropFilter:"blur(8px)",border:"1px solid rgba(255, 255, 255, 0.2)"}},[pi("span",{style:{marginRight:"8px",display:"flex",alignItems:"center",justifyContent:"center",width:"20px",height:"20px"}},[pi("svg",{class:"icon","aria-hidden":"true",style:{width:"16px",height:"16px",fill:"currentColor"}},[pi("use",{"xlink:href":`#icon-${this.getIcon()}`})])]),pi("span",{style:{flex:1,color:"#3c404d",lineHeight:"1.4"}},this.message),this.showClose&&pi("span",{style:{marginLeft:"12px",cursor:"pointer",fontSize:"18px",opacity:"0.8",transition:"opacity 0.2s",display:"flex",alignItems:"center",justifyContent:"center",width:"20px",height:"20px",borderRadius:"50%"},onClick:this.close,onMouseenter:e=>{e.target.style.opacity="1",e.target.style.backgroundColor="rgba(0, 0, 0, 0.2)"},onMouseleave:e=>{e.target.style.opacity="0.8",e.target.style.backgroundColor="rgba(0, 0, 0, 0.4)"}},"×")]):null}},ml=e=>{"string"==typeof e&&(e={message:e});const t=document.createElement("div");document.body.appendChild(t);const n=wa(gl,e);return n.mount(t),{close:()=>{n.unmount(),document.body.removeChild(t)}}},vl={name:"BaseMessageBox",props:{title:{type:String,default:"提示"},message:{type:String,default:""},type:{type:String,default:"warning",validator:e=>["success","warning","info","error"].includes(e)},confirmButtonText:{type:String,default:"确定"},cancelButtonText:{type:String,default:"取消"},showCancelButton:{type:Boolean,default:!0},showConfirmButton:{type:Boolean,default:!0}},data:()=>({visible:!0}),methods:{handleConfirm(){this.$emit("confirm"),this.close()},handleCancel(){this.$emit("cancel"),this.close()},close(){this.visible=!1,setTimeout((()=>{this.$emit("close")}),300)},getIcon(){const e={success:"success",warning:"warning",error:"error",info:"warning"};return e[this.type]||e.warning},getIconColor(){const e={success:"#52c41a",warning:"#faad14",error:"#ff4d4f",info:"#1890ff"};return e[this.type]||e.warning}},render(){return this.visible?pi("div",{class:"message-box-overlay",style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",zIndex:1e4,display:"flex",alignItems:"center",justifyContent:"center",opacity:this.visible?1:0,transition:"opacity 0.3s ease"},onClick:e=>{e.target===e.currentTarget&&this.handleCancel()}},[pi("div",{class:"message-box",style:{background:"#ffffff",borderRadius:"4px",boxShadow:"0px 2px 8px 0px rgba(16,36,66,0.20)",minWidth:"380px",maxWidth:"480px",transform:this.visible?"scale(1)":"scale(0.9)",transition:"transform 0.3s ease",overflow:"hidden"}},[pi("div",{style:{padding:"12px 24px 12px",borderBottom:"1px solid #f0f0f0"}},[pi("div",{style:{display:"flex",alignItems:"center",fontSize:"16px",fontWeight:"500",color:"#262626"}},[pi("span",{style:{marginRight:"12px",display:"flex",alignItems:"center",justifyContent:"center",width:"24px",height:"24px"}},[pi("svg",{class:"icon","aria-hidden":"true",style:{width:"20px",height:"20px",fill:this.getIconColor()}},[pi("use",{"xlink:href":`#icon-${this.getIcon()}`})])]),pi("span",this.title)])]),pi("div",{style:{padding:"20px 24px",fontSize:"14px",lineHeight:"1.6",color:"#595959"}},this.message),pi("div",{style:{padding:"0px",display:"flex",justifyContent:"space-between",borderTop:"1px solid #f0f0f0"}},[this.showCancelButton&&pi("button",{style:{padding:"6px 16px",background:"none",color:"#686e84",fontSize:"14px",cursor:"pointer",transition:"all 0.2s",minWidth:"50%",height:"40px",borderTop:"none",borderBottom:"none",borderLeft:"none",borderRight:"1px solid #f0f0f0"},onClick:this.handleCancel,onMouseenter:e=>{e.target.style.backgroundColor="#536ce6",e.target.style.borderColor="#536ce6",e.target.style.color="#ffffff"},onMouseleave:e=>{e.target.style.backgroundColor="#ffffff",e.target.style.borderColor="#ffffff",e.target.style.color="#686e84"}},this.cancelButtonText),this.showConfirmButton&&pi("button",{style:{padding:"6px 16px",border:"none",background:"none",color:"#536ce6",fontSize:"14px",cursor:"pointer",transition:"all 0.2s",minWidth:"50%",height:"40px"},onClick:this.handleConfirm,onMouseenter:e=>{e.target.style.backgroundColor="#536ce6",e.target.style.borderColor="#536ce6",e.target.style.color="#ffffff"},onMouseleave:e=>{e.target.style.backgroundColor="#ffffff",e.target.style.borderColor="#ffffff",e.target.style.color="#536ce6"}},this.confirmButtonText)])])]):null}},yl=e=>new Promise(((t,n)=>{const r=document.createElement("div");document.body.appendChild(r);const o=wa(vl,{...e,onConfirm:()=>{o.unmount(),document.body.removeChild(r),t("confirm")},onCancel:()=>{o.unmount(),document.body.removeChild(r),n("cancel")},onClose:()=>{o.unmount(),document.body.removeChild(r)}});o.mount(r)}));function bl(e){return e.preventDefault(),e.stopPropagation(),!1}function _l(e){if(!e)return;e.setAttribute("draggable","false");["dragstart","drag","dragenter","dragover","dragleave","drop","dragend"].forEach((t=>{e.addEventListener(t,bl,!0)}))}yl.confirm=(e,t="确认",n={})=>yl({title:t,message:e,type:"warning",showCancelButton:!0,...n}),yl.alert=(e,t="提示",n={})=>yl({title:t,message:e,type:"info",showCancelButton:!1,...n}),ml.success=e=>ml({message:e,type:"success"}),ml.warning=e=>ml({message:e,type:"warning"}),ml.error=e=>ml({message:e,type:"error"}),ml.info=e=>ml({message:e,type:"info"});const wl={mounted(e){_l(e)},unmounted(e){!function(e){if(!e)return;["dragstart","drag","dragenter","dragover","dragleave","drop","dragend"].forEach((t=>{e.removeEventListener(t,bl,!0)}))}(e)}},Sl={"base-button":ka,"base-input":Ta,"base-form":Oa,"base-form-item":La,"base-container":Pa,"base-aside":Ra,"base-main":$a,"base-row":Da,"base-col":Ba,"base-divider":Na,"base-avatar":Va,"base-carousel":Wa,"base-carousel-item":Ka,"base-card":Qa,"base-timeline":Xa,"base-timeline-item":ns,"base-select":ss,"base-option":ls,"base-checkbox":ds,"base-radio":ys,"base-radio-group":ws,"base-icon":xs,"svg-icon":ks,"base-menu":Es,"base-menu-item":Os,"base-scrollbar":Is,"base-tooltip":Ms,"base-link":zs,"base-header":Bs,"base-progress":Ws,"base-sub-menu":Gs,"base-tabs":rl,"base-tab-pane":al,"base-drawer":pl},xl={install(e){Object.keys(Sl).forEach((t=>{e.component(t,Sl[t])})),e.directive("prevent-drag",wl),e.config.globalProperties.$loading=hl,e.config.globalProperties.$message=ml,e.config.globalProperties.$messageBox=yl},Loading:hl,Message:ml,MessageBox:yl},Cl={appName:"ASec安全平台",appLogo:"/src/assets/logo.png",introduction:"ASec",showViteLogo:!1},kl={install:e=>{(e=>{e.config.globalProperties.$GIN_VUE_ADMIN=Cl})(e)}},El={},Al=function(e,t,n){if(!t||0===t.length)return e();const r=document.getElementsByTagName("link");return Promise.all(t.map((e=>{if(e=function(e,t){return new URL(e,t).href}(e,n),e in El)return;El[e]=!0;const t=e.endsWith(".css"),o=t?'[rel="stylesheet"]':"";if(!!n)for(let n=r.length-1;n>=0;n--){const o=r[n];if(o.href===e&&(!t||"stylesheet"===o.rel))return}else if(document.querySelector(`link[href="${e}"]${o}`))return;const i=document.createElement("link");return i.rel=t?"stylesheet":"modulepreload",t||(i.as="script",i.crossOrigin=""),i.href=e,document.head.appendChild(i),t?new Promise(((t,n)=>{i.addEventListener("load",t),i.addEventListener("error",(()=>n(new Error(`Unable to preload CSS for ${e}`))))})):void 0}))).then((()=>e()))},Tl="undefined"!=typeof document;function Ol(e){return"object"==typeof e||"displayName"in e||"props"in e||"__vccOpts"in e}const Il=Object.assign;function jl(e,t){const n={};for(const r in t){const o=t[r];n[r]=Ml(o)?o.map(e):e(o)}return n}const Ll=()=>{},Ml=Array.isArray,Pl=/#/g,Rl=/&/g,zl=/\//g,$l=/=/g,Dl=/\?/g,Bl=/\+/g,Nl=/%5B/g,Fl=/%5D/g,Hl=/%5E/g,Vl=/%60/g,Ul=/%7B/g,Wl=/%7C/g,ql=/%7D/g,Kl=/%20/g;function Gl(e){return encodeURI(""+e).replace(Wl,"|").replace(Nl,"[").replace(Fl,"]")}function Ql(e){return Gl(e).replace(Bl,"%2B").replace(Kl,"+").replace(Pl,"%23").replace(Rl,"%26").replace(Vl,"`").replace(Ul,"{").replace(ql,"}").replace(Hl,"^")}function Jl(e){return null==e?"":function(e){return Gl(e).replace(Pl,"%23").replace(Dl,"%3F")}(e).replace(zl,"%2F")}function Xl(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}const Zl=/\/$/;function Yl(e,t,n="/"){let r,o={},i="",a="";const s=t.indexOf("#");let l=t.indexOf("?");return s<l&&s>=0&&(l=-1),l>-1&&(r=t.slice(0,l),i=t.slice(l+1,s>-1?s:t.length),o=e(i)),s>-1&&(r=r||t.slice(0,s),a=t.slice(s,t.length)),r=function(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/"),o=r[r.length-1];".."!==o&&"."!==o||r.push("");let i,a,s=n.length-1;for(i=0;i<r.length;i++)if(a=r[i],"."!==a){if(".."!==a)break;s>1&&s--}return n.slice(0,s).join("/")+"/"+r.slice(i).join("/")}(null!=r?r:t,n),{fullPath:r+(i&&"?")+i+a,path:r,query:o,hash:Xl(a)}}function ec(e,t){return t&&e.toLowerCase().startsWith(t.toLowerCase())?e.slice(t.length)||"/":e}function tc(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function nc(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!rc(e[n],t[n]))return!1;return!0}function rc(e,t){return Ml(e)?oc(e,t):Ml(t)?oc(t,e):e===t}function oc(e,t){return Ml(t)?e.length===t.length&&e.every(((e,n)=>e===t[n])):1===e.length&&e[0]===t}const ic={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var ac,sc,lc,cc;function uc(e){if(!e)if(Tl){const t=document.querySelector("base");e=(e=t&&t.getAttribute("href")||"/").replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return"/"!==e[0]&&"#"!==e[0]&&(e="/"+e),e.replace(Zl,"")}(sc=ac||(ac={})).pop="pop",sc.push="push",(cc=lc||(lc={})).back="back",cc.forward="forward",cc.unknown="";const fc=/^[^#]+#/;function pc(e,t){return e.replace(fc,"#")+t}const dc=()=>({left:window.scrollX,top:window.scrollY});function hc(e){let t;if("el"in e){const n=e.el,r="string"==typeof n&&n.startsWith("#"),o="string"==typeof n?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!o)return;t=function(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}(o,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(null!=t.left?t.left:window.scrollX,null!=t.top?t.top:window.scrollY)}function gc(e,t){return(history.state?history.state.position-t:-1)+e}const mc=new Map;function vc(e,t){const{pathname:n,search:r,hash:o}=t,i=e.indexOf("#");if(i>-1){let t=o.includes(e.slice(i))?e.slice(i).length:1,n=o.slice(t);return"/"!==n[0]&&(n="/"+n),ec(n,"")}return ec(n,e)+r+o}function yc(e,t,n,r=!1,o=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:o?dc():null}}function bc(e){const{history:t,location:n}=window,r={value:vc(e,n)},o={value:t.state};function i(r,i,a){const s=e.indexOf("#"),l=s>-1?(n.host&&document.querySelector("base")?e:e.slice(s))+r:location.protocol+"//"+location.host+e+r;try{t[a?"replaceState":"pushState"](i,"",l),o.value=i}catch(c){console.error(c),n[a?"replace":"assign"](l)}}return o.value||i(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0),{location:r,state:o,push:function(e,n){const a=Il({},o.value,t.state,{forward:e,scroll:dc()});i(a.current,a,!0),i(e,Il({},yc(r.value,e,null),{position:a.position+1},n),!1),r.value=e},replace:function(e,n){i(e,Il({},t.state,yc(o.value.back,e,o.value.forward,!0),n,{position:o.value.position}),!0),r.value=e}}}function _c(e){const t=bc(e=uc(e)),n=function(e,t,n,r){let o=[],i=[],a=null;const s=({state:i})=>{const s=vc(e,location),l=n.value,c=t.value;let u=0;if(i){if(n.value=s,t.value=i,a&&a===l)return void(a=null);u=c?i.position-c.position:0}else r(s);o.forEach((e=>{e(n.value,l,{delta:u,type:ac.pop,direction:u?u>0?lc.forward:lc.back:lc.unknown})}))};function l(){const{history:e}=window;e.state&&e.replaceState(Il({},e.state,{scroll:dc()}),"")}return window.addEventListener("popstate",s),window.addEventListener("beforeunload",l,{passive:!0}),{pauseListeners:function(){a=n.value},listen:function(e){o.push(e);const t=()=>{const t=o.indexOf(e);t>-1&&o.splice(t,1)};return i.push(t),t},destroy:function(){for(const e of i)e();i=[],window.removeEventListener("popstate",s),window.removeEventListener("beforeunload",l)}}}(e,t.state,t.location,t.replace);const r=Il({location:"",base:e,go:function(e,t=!0){t||n.pauseListeners(),history.go(e)},createHref:pc.bind(null,e)},t,n);return Object.defineProperty(r,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(r,"state",{enumerable:!0,get:()=>t.state.value}),r}function wc(e){return"string"==typeof e||"symbol"==typeof e}const Sc=Symbol("");var xc,Cc;function kc(e,t){return Il(new Error,{type:e,[Sc]:!0},t)}function Ec(e,t){return e instanceof Error&&Sc in e&&(null==t||!!(e.type&t))}(Cc=xc||(xc={}))[Cc.aborted=4]="aborted",Cc[Cc.cancelled=8]="cancelled",Cc[Cc.duplicated=16]="duplicated";const Ac="[^/]+?",Tc={sensitive:!1,strict:!1,start:!0,end:!0},Oc=/[.+*?^${}()[\]/\\]/g;function Ic(e,t){let n=0;for(;n<e.length&&n<t.length;){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?1===e.length&&80===e[0]?-1:1:e.length>t.length?1===t.length&&80===t[0]?1:-1:0}function jc(e,t){let n=0;const r=e.score,o=t.score;for(;n<r.length&&n<o.length;){const e=Ic(r[n],o[n]);if(e)return e;n++}if(1===Math.abs(o.length-r.length)){if(Lc(r))return 1;if(Lc(o))return-1}return o.length-r.length}function Lc(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Mc={type:0,value:""},Pc=/[a-zA-Z0-9_]/;function Rc(e,t,n){const r=function(e,t){const n=Il({},Tc,t),r=[];let o=n.start?"^":"";const i=[];for(const l of e){const e=l.length?[]:[90];n.strict&&!l.length&&(o+="/");for(let t=0;t<l.length;t++){const r=l[t];let a=40+(n.sensitive?.25:0);if(0===r.type)t||(o+="/"),o+=r.value.replace(Oc,"\\$&"),a+=40;else if(1===r.type){const{value:e,repeatable:n,optional:c,regexp:u}=r;i.push({name:e,repeatable:n,optional:c});const f=u||Ac;if(f!==Ac){a+=10;try{new RegExp(`(${f})`)}catch(s){throw new Error(`Invalid custom RegExp for param "${e}" (${f}): `+s.message)}}let p=n?`((?:${f})(?:/(?:${f}))*)`:`(${f})`;t||(p=c&&l.length<2?`(?:/${p})`:"/"+p),c&&(p+="?"),o+=p,a+=20,c&&(a+=-8),n&&(a+=-20),".*"===f&&(a+=-50)}e.push(a)}r.push(e)}if(n.strict&&n.end){const e=r.length-1;r[e][r[e].length-1]+=.7000000000000001}n.strict||(o+="/?"),n.end?o+="$":n.strict&&!o.endsWith("/")&&(o+="(?:/|$)");const a=new RegExp(o,n.sensitive?"":"i");return{re:a,score:r,keys:i,parse:function(e){const t=e.match(a),n={};if(!t)return null;for(let r=1;r<t.length;r++){const e=t[r]||"",o=i[r-1];n[o.name]=e&&o.repeatable?e.split("/"):e}return n},stringify:function(t){let n="",r=!1;for(const o of e){r&&n.endsWith("/")||(n+="/"),r=!1;for(const e of o)if(0===e.type)n+=e.value;else if(1===e.type){const{value:i,repeatable:a,optional:s}=e,l=i in t?t[i]:"";if(Ml(l)&&!a)throw new Error(`Provided param "${i}" is an array but it is not repeatable (* or + modifiers)`);const c=Ml(l)?l.join("/"):l;if(!c){if(!s)throw new Error(`Missing required param "${i}"`);o.length<2&&(n.endsWith("/")?n=n.slice(0,-1):r=!0)}n+=c}}return n||"/"}}}(function(e){if(!e)return[[]];if("/"===e)return[[Mc]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(e){throw new Error(`ERR (${n})/"${c}": ${e}`)}let n=0,r=n;const o=[];let i;function a(){i&&o.push(i),i=[]}let s,l=0,c="",u="";function f(){c&&(0===n?i.push({type:0,value:c}):1===n||2===n||3===n?(i.length>1&&("*"===s||"+"===s)&&t(`A repeatable param (${c}) must be alone in its segment. eg: '/:ids+.`),i.push({type:1,value:c,regexp:u,repeatable:"*"===s||"+"===s,optional:"*"===s||"?"===s})):t("Invalid state to consume buffer"),c="")}function p(){c+=s}for(;l<e.length;)if(s=e[l++],"\\"!==s||2===n)switch(n){case 0:"/"===s?(c&&f(),a()):":"===s?(f(),n=1):p();break;case 4:p(),n=r;break;case 1:"("===s?n=2:Pc.test(s)?p():(f(),n=0,"*"!==s&&"?"!==s&&"+"!==s&&l--);break;case 2:")"===s?"\\"==u[u.length-1]?u=u.slice(0,-1)+s:n=3:u+=s;break;case 3:f(),n=0,"*"!==s&&"?"!==s&&"+"!==s&&l--,u="";break;default:t("Unknown state")}else r=n,n=4;return 2===n&&t(`Unfinished custom RegExp for param "${c}"`),f(),a(),o}(e.path),n),o=Il(r,{record:e,parent:t,children:[],alias:[]});return t&&!o.record.aliasOf==!t.record.aliasOf&&t.children.push(o),o}function zc(e,t){const n=[],r=new Map;function o(e,n,r){const s=!r,l=Dc(e);l.aliasOf=r&&r.record;const c=Hc(t,e),u=[l];if("alias"in e){const t="string"==typeof e.alias?[e.alias]:e.alias;for(const e of t)u.push(Dc(Il({},l,{components:r?r.record.components:l.components,path:e,aliasOf:r?r.record:l})))}let f,p;for(const t of u){const{path:u}=t;if(n&&"/"!==u[0]){const e=n.record.path,r="/"===e[e.length-1]?"":"/";t.path=n.record.path+(u&&r+u)}if(f=Rc(t,n,c),r?r.alias.push(f):(p=p||f,p!==f&&p.alias.push(f),s&&e.name&&!Nc(f)&&i(e.name)),Vc(f)&&a(f),l.children){const e=l.children;for(let t=0;t<e.length;t++)o(e[t],f,r&&r.children[t])}r=r||f}return p?()=>{i(p)}:Ll}function i(e){if(wc(e)){const t=r.get(e);t&&(r.delete(e),n.splice(n.indexOf(t),1),t.children.forEach(i),t.alias.forEach(i))}else{const t=n.indexOf(e);t>-1&&(n.splice(t,1),e.record.name&&r.delete(e.record.name),e.children.forEach(i),e.alias.forEach(i))}}function a(e){const t=function(e,t){let n=0,r=t.length;for(;n!==r;){const o=n+r>>1;jc(e,t[o])<0?r=o:n=o+1}const o=function(e){let t=e;for(;t=t.parent;)if(Vc(t)&&0===jc(e,t))return t;return}(e);o&&(r=t.lastIndexOf(o,r-1));return r}(e,n);n.splice(t,0,e),e.record.name&&!Nc(e)&&r.set(e.record.name,e)}return t=Hc({strict:!1,end:!0,sensitive:!1},t),e.forEach((e=>o(e))),{addRoute:o,resolve:function(e,t){let o,i,a,s={};if("name"in e&&e.name){if(o=r.get(e.name),!o)throw kc(1,{location:e});a=o.record.name,s=Il($c(t.params,o.keys.filter((e=>!e.optional)).concat(o.parent?o.parent.keys.filter((e=>e.optional)):[]).map((e=>e.name))),e.params&&$c(e.params,o.keys.map((e=>e.name)))),i=o.stringify(s)}else if(null!=e.path)i=e.path,o=n.find((e=>e.re.test(i))),o&&(s=o.parse(i),a=o.record.name);else{if(o=t.name?r.get(t.name):n.find((e=>e.re.test(t.path))),!o)throw kc(1,{location:e,currentLocation:t});a=o.record.name,s=Il({},t.params,e.params),i=o.stringify(s)}const l=[];let c=o;for(;c;)l.unshift(c.record),c=c.parent;return{name:a,path:i,params:s,matched:l,meta:Fc(l)}},removeRoute:i,clearRoutes:function(){n.length=0,r.clear()},getRoutes:function(){return n},getRecordMatcher:function(e){return r.get(e)}}}function $c(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function Dc(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:Bc(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function Bc(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]="object"==typeof n?n[r]:n;return t}function Nc(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Fc(e){return e.reduce(((e,t)=>Il(e,t.meta)),{})}function Hc(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}function Vc({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Uc(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let r=0;r<n.length;++r){const e=n[r].replace(Bl," "),o=e.indexOf("="),i=Xl(o<0?e:e.slice(0,o)),a=o<0?null:Xl(e.slice(o+1));if(i in t){let e=t[i];Ml(e)||(e=t[i]=[e]),e.push(a)}else t[i]=a}return t}function Wc(e){let t="";for(let n in e){const r=e[n];if(n=Ql(n).replace($l,"%3D"),null==r){void 0!==r&&(t+=(t.length?"&":"")+n);continue}(Ml(r)?r.map((e=>e&&Ql(e))):[r&&Ql(r)]).forEach((e=>{void 0!==e&&(t+=(t.length?"&":"")+n,null!=e&&(t+="="+e))}))}return t}function qc(e){const t={};for(const n in e){const r=e[n];void 0!==r&&(t[n]=Ml(r)?r.map((e=>null==e?null:""+e)):null==r?r:""+r)}return t}const Kc=Symbol(""),Gc=Symbol(""),Qc=Symbol(""),Jc=Symbol(""),Xc=Symbol("");function Zc(){let e=[];return{add:function(t){return e.push(t),()=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)}},list:()=>e.slice(),reset:function(){e=[]}}}function Yc(e,t,n,r,o,i=e=>e()){const a=r&&(r.enterCallbacks[o]=r.enterCallbacks[o]||[]);return()=>new Promise(((s,l)=>{const c=e=>{var i;!1===e?l(kc(4,{from:n,to:t})):e instanceof Error?l(e):"string"==typeof(i=e)||i&&"object"==typeof i?l(kc(2,{from:t,to:e})):(a&&r.enterCallbacks[o]===a&&"function"==typeof e&&a.push(e),s())},u=i((()=>e.call(r&&r.instances[o],t,n,c)));let f=Promise.resolve(u);e.length<3&&(f=f.then(c)),f.catch((e=>l(e)))}))}function eu(e,t,n,r,o=e=>e()){const i=[];for(const a of e)for(const e in a.components){let s=a.components[e];if("beforeRouteEnter"===t||a.instances[e])if(Ol(s)){const l=(s.__vccOpts||s)[t];l&&i.push(Yc(l,n,r,a,e,o))}else{let l=s();i.push((()=>l.then((i=>{if(!i)throw new Error(`Couldn't resolve component "${e}" at "${a.path}"`);const s=(l=i).__esModule||"Module"===l[Symbol.toStringTag]||l.default&&Ol(l.default)?i.default:i;var l;a.mods[e]=i,a.components[e]=s;const c=(s.__vccOpts||s)[t];return c&&Yc(c,n,r,a,e,o)()}))))}}return i}function tu(e){const t=Br(Qc),n=Br(Jc),r=fi((()=>{const n=Et(e.to);return t.resolve(n)})),o=fi((()=>{const{matched:e}=r.value,{length:t}=e,o=e[t-1],i=n.matched;if(!o||!i.length)return-1;const a=i.findIndex(tc.bind(null,o));if(a>-1)return a;const s=ru(e[t-2]);return t>1&&ru(o)===s&&i[i.length-1].path!==s?i.findIndex(tc.bind(null,e[t-2])):a})),i=fi((()=>o.value>-1&&function(e,t){for(const n in t){const r=t[n],o=e[n];if("string"==typeof r){if(r!==o)return!1}else if(!Ml(o)||o.length!==r.length||r.some(((e,t)=>e!==o[t])))return!1}return!0}(n.params,r.value.params))),a=fi((()=>o.value>-1&&o.value===n.matched.length-1&&nc(n.params,r.value.params)));return{route:r,href:fi((()=>r.value.href)),isActive:i,isExactActive:a,navigate:function(n={}){if(function(e){if(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)return;if(e.defaultPrevented)return;if(void 0!==e.button&&0!==e.button)return;if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}e.preventDefault&&e.preventDefault();return!0}(n)){const n=t[Et(e.replace)?"replace":"push"](Et(e.to)).catch(Ll);return e.viewTransition&&"undefined"!=typeof document&&"startViewTransition"in document&&document.startViewTransition((()=>n)),n}return Promise.resolve()}}}const nu=Mn({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:tu,setup(e,{slots:t}){const n=ut(tu(e)),{options:r}=Br(Qc),o=fi((()=>({[ou(e.activeClass,r.linkActiveClass,"router-link-active")]:n.isActive,[ou(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive})));return()=>{const r=t.default&&(1===(i=t.default(n)).length?i[0]:i);var i;return e.custom?r:pi("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:o.value},r)}}});function ru(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const ou=(e,t,n)=>null!=e?e:null!=t?t:n;function iu(e,t){if(!e)return null;const n=e(t);return 1===n.length?n[0]:n}const au=Mn({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const r=Br(Xc),o=fi((()=>e.route||r.value)),i=Br(Gc,0),a=fi((()=>{let e=Et(i);const{matched:t}=o.value;let n;for(;(n=t[e])&&!n.components;)e++;return e})),s=fi((()=>o.value.matched[a.value]));Dr(Gc,fi((()=>a.value+1))),Dr(Kc,s),Dr(Xc,o);const l=xt();return co((()=>[l.value,s.value,e.name]),(([e,t,n],[r,o,i])=>{t&&(t.instances[n]=e,o&&o!==t&&e&&e===r&&(t.leaveGuards.size||(t.leaveGuards=o.leaveGuards),t.updateGuards.size||(t.updateGuards=o.updateGuards))),!e||!t||o&&tc(t,o)&&r||(t.enterCallbacks[n]||[]).forEach((t=>t(e)))}),{flush:"post"}),()=>{const r=o.value,i=e.name,a=s.value,c=a&&a.components[i];if(!c)return iu(n.default,{Component:c,route:r});const u=a.props[i],f=u?!0===u?r.params:"function"==typeof u?u(r):u:null,p=pi(c,Il({},f,t,{onVnodeUnmounted:e=>{e.component.isUnmounted&&(a.instances[i]=null)},ref:l}));return iu(n.default,{Component:p,route:r})||p}}});function su(){return Br(Qc)}function lu(e){return Br(Jc)}var cu="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function uu(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function fu(e){var t=e.default;if("function"==typeof t){var n=function(){return t.apply(this,arguments)};n.prototype=t.prototype}else n={};return Object.defineProperty(n,"__esModule",{value:!0}),Object.keys(e).forEach((function(t){var r=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(n,t,r.get?r:{enumerable:!0,get:function(){return e[t]}})})),n}var pu,du,hu={exports:{}};
/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
 */pu=hu,du=hu.exports,function(){var e,t="Expected a function",n="__lodash_hash_undefined__",r="__lodash_placeholder__",o=16,i=32,a=64,s=128,l=256,c=1/0,u=9007199254740991,f=NaN,p=**********,d=[["ary",s],["bind",1],["bindKey",2],["curry",8],["curryRight",o],["flip",512],["partial",i],["partialRight",a],["rearg",l]],h="[object Arguments]",g="[object Array]",m="[object Boolean]",v="[object Date]",y="[object Error]",b="[object Function]",_="[object GeneratorFunction]",w="[object Map]",S="[object Number]",x="[object Object]",C="[object Promise]",k="[object RegExp]",E="[object Set]",A="[object String]",T="[object Symbol]",O="[object WeakMap]",I="[object ArrayBuffer]",j="[object DataView]",L="[object Float32Array]",M="[object Float64Array]",P="[object Int8Array]",R="[object Int16Array]",z="[object Int32Array]",$="[object Uint8Array]",D="[object Uint8ClampedArray]",B="[object Uint16Array]",N="[object Uint32Array]",F=/\b__p \+= '';/g,H=/\b(__p \+=) '' \+/g,V=/(__e\(.*?\)|\b__t\)) \+\n'';/g,U=/&(?:amp|lt|gt|quot|#39);/g,W=/[&<>"']/g,q=RegExp(U.source),K=RegExp(W.source),G=/<%-([\s\S]+?)%>/g,Q=/<%([\s\S]+?)%>/g,J=/<%=([\s\S]+?)%>/g,X=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Z=/^\w*$/,Y=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,ee=/[\\^$.*+?()[\]{}|]/g,te=RegExp(ee.source),ne=/^\s+/,re=/\s/,oe=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,ie=/\{\n\/\* \[wrapped with (.+)\] \*/,ae=/,? & /,se=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,le=/[()=,{}\[\]\/\s]/,ce=/\\(\\)?/g,ue=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,fe=/\w*$/,pe=/^[-+]0x[0-9a-f]+$/i,de=/^0b[01]+$/i,he=/^\[object .+?Constructor\]$/,ge=/^0o[0-7]+$/i,me=/^(?:0|[1-9]\d*)$/,ve=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,ye=/($^)/,be=/['\n\r\u2028\u2029\\]/g,_e="\\ud800-\\udfff",we="\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff",Se="\\u2700-\\u27bf",xe="a-z\\xdf-\\xf6\\xf8-\\xff",Ce="A-Z\\xc0-\\xd6\\xd8-\\xde",ke="\\ufe0e\\ufe0f",Ee="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",Ae="['’]",Te="["+_e+"]",Oe="["+Ee+"]",Ie="["+we+"]",je="\\d+",Le="["+Se+"]",Me="["+xe+"]",Pe="[^"+_e+Ee+je+Se+xe+Ce+"]",Re="\\ud83c[\\udffb-\\udfff]",ze="[^"+_e+"]",$e="(?:\\ud83c[\\udde6-\\uddff]){2}",De="[\\ud800-\\udbff][\\udc00-\\udfff]",Be="["+Ce+"]",Ne="\\u200d",Fe="(?:"+Me+"|"+Pe+")",He="(?:"+Be+"|"+Pe+")",Ve="(?:['’](?:d|ll|m|re|s|t|ve))?",Ue="(?:['’](?:D|LL|M|RE|S|T|VE))?",We="(?:"+Ie+"|"+Re+")?",qe="["+ke+"]?",Ke=qe+We+"(?:"+Ne+"(?:"+[ze,$e,De].join("|")+")"+qe+We+")*",Ge="(?:"+[Le,$e,De].join("|")+")"+Ke,Qe="(?:"+[ze+Ie+"?",Ie,$e,De,Te].join("|")+")",Je=RegExp(Ae,"g"),Xe=RegExp(Ie,"g"),Ze=RegExp(Re+"(?="+Re+")|"+Qe+Ke,"g"),Ye=RegExp([Be+"?"+Me+"+"+Ve+"(?="+[Oe,Be,"$"].join("|")+")",He+"+"+Ue+"(?="+[Oe,Be+Fe,"$"].join("|")+")",Be+"?"+Fe+"+"+Ve,Be+"+"+Ue,"\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])","\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",je,Ge].join("|"),"g"),et=RegExp("["+Ne+_e+we+ke+"]"),tt=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,nt=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],rt=-1,ot={};ot[L]=ot[M]=ot[P]=ot[R]=ot[z]=ot[$]=ot[D]=ot[B]=ot[N]=!0,ot[h]=ot[g]=ot[I]=ot[m]=ot[j]=ot[v]=ot[y]=ot[b]=ot[w]=ot[S]=ot[x]=ot[k]=ot[E]=ot[A]=ot[O]=!1;var it={};it[h]=it[g]=it[I]=it[j]=it[m]=it[v]=it[L]=it[M]=it[P]=it[R]=it[z]=it[w]=it[S]=it[x]=it[k]=it[E]=it[A]=it[T]=it[$]=it[D]=it[B]=it[N]=!0,it[y]=it[b]=it[O]=!1;var at={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},st=parseFloat,lt=parseInt,ct="object"==typeof cu&&cu&&cu.Object===Object&&cu,ut="object"==typeof self&&self&&self.Object===Object&&self,ft=ct||ut||Function("return this")(),pt=du&&!du.nodeType&&du,dt=pt&&pu&&!pu.nodeType&&pu,ht=dt&&dt.exports===pt,gt=ht&&ct.process,mt=function(){try{var e=dt&&dt.require&&dt.require("util").types;return e||gt&&gt.binding&&gt.binding("util")}catch($v){}}(),vt=mt&&mt.isArrayBuffer,yt=mt&&mt.isDate,bt=mt&&mt.isMap,_t=mt&&mt.isRegExp,wt=mt&&mt.isSet,St=mt&&mt.isTypedArray;function xt(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)}function Ct(e,t,n,r){for(var o=-1,i=null==e?0:e.length;++o<i;){var a=e[o];t(r,a,n(a),e)}return r}function kt(e,t){for(var n=-1,r=null==e?0:e.length;++n<r&&!1!==t(e[n],n,e););return e}function Et(e,t){for(var n=null==e?0:e.length;n--&&!1!==t(e[n],n,e););return e}function At(e,t){for(var n=-1,r=null==e?0:e.length;++n<r;)if(!t(e[n],n,e))return!1;return!0}function Tt(e,t){for(var n=-1,r=null==e?0:e.length,o=0,i=[];++n<r;){var a=e[n];t(a,n,e)&&(i[o++]=a)}return i}function Ot(e,t){return!(null==e||!e.length)&&Bt(e,t,0)>-1}function It(e,t,n){for(var r=-1,o=null==e?0:e.length;++r<o;)if(n(t,e[r]))return!0;return!1}function jt(e,t){for(var n=-1,r=null==e?0:e.length,o=Array(r);++n<r;)o[n]=t(e[n],n,e);return o}function Lt(e,t){for(var n=-1,r=t.length,o=e.length;++n<r;)e[o+n]=t[n];return e}function Mt(e,t,n,r){var o=-1,i=null==e?0:e.length;for(r&&i&&(n=e[++o]);++o<i;)n=t(n,e[o],o,e);return n}function Pt(e,t,n,r){var o=null==e?0:e.length;for(r&&o&&(n=e[--o]);o--;)n=t(n,e[o],o,e);return n}function Rt(e,t){for(var n=-1,r=null==e?0:e.length;++n<r;)if(t(e[n],n,e))return!0;return!1}var zt=Vt("length");function $t(e,t,n){var r;return n(e,(function(e,n,o){if(t(e,n,o))return r=n,!1})),r}function Dt(e,t,n,r){for(var o=e.length,i=n+(r?1:-1);r?i--:++i<o;)if(t(e[i],i,e))return i;return-1}function Bt(e,t,n){return t==t?function(e,t,n){for(var r=n-1,o=e.length;++r<o;)if(e[r]===t)return r;return-1}(e,t,n):Dt(e,Ft,n)}function Nt(e,t,n,r){for(var o=n-1,i=e.length;++o<i;)if(r(e[o],t))return o;return-1}function Ft(e){return e!=e}function Ht(e,t){var n=null==e?0:e.length;return n?qt(e,t)/n:f}function Vt(t){return function(n){return null==n?e:n[t]}}function Ut(t){return function(n){return null==t?e:t[n]}}function Wt(e,t,n,r,o){return o(e,(function(e,o,i){n=r?(r=!1,e):t(n,e,o,i)})),n}function qt(t,n){for(var r,o=-1,i=t.length;++o<i;){var a=n(t[o]);a!==e&&(r=r===e?a:r+a)}return r}function Kt(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r}function Gt(e){return e?e.slice(0,fn(e)+1).replace(ne,""):e}function Qt(e){return function(t){return e(t)}}function Jt(e,t){return jt(t,(function(t){return e[t]}))}function Xt(e,t){return e.has(t)}function Zt(e,t){for(var n=-1,r=e.length;++n<r&&Bt(t,e[n],0)>-1;);return n}function Yt(e,t){for(var n=e.length;n--&&Bt(t,e[n],0)>-1;);return n}var en=Ut({"À":"A","Á":"A","Â":"A","Ã":"A","Ä":"A","Å":"A","à":"a","á":"a","â":"a","ã":"a","ä":"a","å":"a","Ç":"C","ç":"c","Ð":"D","ð":"d","È":"E","É":"E","Ê":"E","Ë":"E","è":"e","é":"e","ê":"e","ë":"e","Ì":"I","Í":"I","Î":"I","Ï":"I","ì":"i","í":"i","î":"i","ï":"i","Ñ":"N","ñ":"n","Ò":"O","Ó":"O","Ô":"O","Õ":"O","Ö":"O","Ø":"O","ò":"o","ó":"o","ô":"o","õ":"o","ö":"o","ø":"o","Ù":"U","Ú":"U","Û":"U","Ü":"U","ù":"u","ú":"u","û":"u","ü":"u","Ý":"Y","ý":"y","ÿ":"y","Æ":"Ae","æ":"ae","Þ":"Th","þ":"th","ß":"ss","Ā":"A","Ă":"A","Ą":"A","ā":"a","ă":"a","ą":"a","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","ć":"c","ĉ":"c","ċ":"c","č":"c","Ď":"D","Đ":"D","ď":"d","đ":"d","Ē":"E","Ĕ":"E","Ė":"E","Ę":"E","Ě":"E","ē":"e","ĕ":"e","ė":"e","ę":"e","ě":"e","Ĝ":"G","Ğ":"G","Ġ":"G","Ģ":"G","ĝ":"g","ğ":"g","ġ":"g","ģ":"g","Ĥ":"H","Ħ":"H","ĥ":"h","ħ":"h","Ĩ":"I","Ī":"I","Ĭ":"I","Į":"I","İ":"I","ĩ":"i","ī":"i","ĭ":"i","į":"i","ı":"i","Ĵ":"J","ĵ":"j","Ķ":"K","ķ":"k","ĸ":"k","Ĺ":"L","Ļ":"L","Ľ":"L","Ŀ":"L","Ł":"L","ĺ":"l","ļ":"l","ľ":"l","ŀ":"l","ł":"l","Ń":"N","Ņ":"N","Ň":"N","Ŋ":"N","ń":"n","ņ":"n","ň":"n","ŋ":"n","Ō":"O","Ŏ":"O","Ő":"O","ō":"o","ŏ":"o","ő":"o","Ŕ":"R","Ŗ":"R","Ř":"R","ŕ":"r","ŗ":"r","ř":"r","Ś":"S","Ŝ":"S","Ş":"S","Š":"S","ś":"s","ŝ":"s","ş":"s","š":"s","Ţ":"T","Ť":"T","Ŧ":"T","ţ":"t","ť":"t","ŧ":"t","Ũ":"U","Ū":"U","Ŭ":"U","Ů":"U","Ű":"U","Ų":"U","ũ":"u","ū":"u","ŭ":"u","ů":"u","ű":"u","ų":"u","Ŵ":"W","ŵ":"w","Ŷ":"Y","ŷ":"y","Ÿ":"Y","Ź":"Z","Ż":"Z","Ž":"Z","ź":"z","ż":"z","ž":"z","Ĳ":"IJ","ĳ":"ij","Œ":"Oe","œ":"oe","ŉ":"'n","ſ":"s"}),tn=Ut({"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"});function nn(e){return"\\"+at[e]}function rn(e){return et.test(e)}function on(e){var t=-1,n=Array(e.size);return e.forEach((function(e,r){n[++t]=[r,e]})),n}function an(e,t){return function(n){return e(t(n))}}function sn(e,t){for(var n=-1,o=e.length,i=0,a=[];++n<o;){var s=e[n];s!==t&&s!==r||(e[n]=r,a[i++]=n)}return a}function ln(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=e})),n}function cn(e){return rn(e)?function(e){for(var t=Ze.lastIndex=0;Ze.test(e);)++t;return t}(e):zt(e)}function un(e){return rn(e)?function(e){return e.match(Ze)||[]}(e):function(e){return e.split("")}(e)}function fn(e){for(var t=e.length;t--&&re.test(e.charAt(t)););return t}var pn=Ut({"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"}),dn=function re(_e){var we,Se=(_e=null==_e?ft:dn.defaults(ft.Object(),_e,dn.pick(ft,nt))).Array,xe=_e.Date,Ce=_e.Error,ke=_e.Function,Ee=_e.Math,Ae=_e.Object,Te=_e.RegExp,Oe=_e.String,Ie=_e.TypeError,je=Se.prototype,Le=ke.prototype,Me=Ae.prototype,Pe=_e["__core-js_shared__"],Re=Le.toString,ze=Me.hasOwnProperty,$e=0,De=(we=/[^.]+$/.exec(Pe&&Pe.keys&&Pe.keys.IE_PROTO||""))?"Symbol(src)_1."+we:"",Be=Me.toString,Ne=Re.call(Ae),Fe=ft._,He=Te("^"+Re.call(ze).replace(ee,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Ve=ht?_e.Buffer:e,Ue=_e.Symbol,We=_e.Uint8Array,qe=Ve?Ve.allocUnsafe:e,Ke=an(Ae.getPrototypeOf,Ae),Ge=Ae.create,Qe=Me.propertyIsEnumerable,Ze=je.splice,et=Ue?Ue.isConcatSpreadable:e,at=Ue?Ue.iterator:e,ct=Ue?Ue.toStringTag:e,ut=function(){try{var e=pi(Ae,"defineProperty");return e({},"",{}),e}catch($v){}}(),pt=_e.clearTimeout!==ft.clearTimeout&&_e.clearTimeout,dt=xe&&xe.now!==ft.Date.now&&xe.now,gt=_e.setTimeout!==ft.setTimeout&&_e.setTimeout,mt=Ee.ceil,zt=Ee.floor,Ut=Ae.getOwnPropertySymbols,hn=Ve?Ve.isBuffer:e,gn=_e.isFinite,mn=je.join,vn=an(Ae.keys,Ae),yn=Ee.max,bn=Ee.min,_n=xe.now,wn=_e.parseInt,Sn=Ee.random,xn=je.reverse,Cn=pi(_e,"DataView"),kn=pi(_e,"Map"),En=pi(_e,"Promise"),An=pi(_e,"Set"),Tn=pi(_e,"WeakMap"),On=pi(Ae,"create"),In=Tn&&new Tn,jn={},Ln=Fi(Cn),Mn=Fi(kn),Pn=Fi(En),Rn=Fi(An),zn=Fi(Tn),$n=Ue?Ue.prototype:e,Dn=$n?$n.valueOf:e,Bn=$n?$n.toString:e;function Nn(e){if(os(e)&&!Ka(e)&&!(e instanceof Un)){if(e instanceof Vn)return e;if(ze.call(e,"__wrapped__"))return Hi(e)}return new Vn(e)}var Fn=function(){function t(){}return function(n){if(!rs(n))return{};if(Ge)return Ge(n);t.prototype=n;var r=new t;return t.prototype=e,r}}();function Hn(){}function Vn(t,n){this.__wrapped__=t,this.__actions__=[],this.__chain__=!!n,this.__index__=0,this.__values__=e}function Un(e){this.__wrapped__=e,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=p,this.__views__=[]}function Wn(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function qn(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function Kn(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function Gn(e){var t=-1,n=null==e?0:e.length;for(this.__data__=new Kn;++t<n;)this.add(e[t])}function Qn(e){var t=this.__data__=new qn(e);this.size=t.size}function Jn(e,t){var n=Ka(e),r=!n&&qa(e),o=!n&&!r&&Xa(e),i=!n&&!r&&!o&&ps(e),a=n||r||o||i,s=a?Kt(e.length,Oe):[],l=s.length;for(var c in e)!t&&!ze.call(e,c)||a&&("length"==c||o&&("offset"==c||"parent"==c)||i&&("buffer"==c||"byteLength"==c||"byteOffset"==c)||bi(c,l))||s.push(c);return s}function Xn(t){var n=t.length;return n?t[Gr(0,n-1)]:e}function Zn(e,t){return Ri(Io(e),sr(t,0,e.length))}function Yn(e){return Ri(Io(e))}function er(t,n,r){(r!==e&&!Va(t[n],r)||r===e&&!(n in t))&&ir(t,n,r)}function tr(t,n,r){var o=t[n];ze.call(t,n)&&Va(o,r)&&(r!==e||n in t)||ir(t,n,r)}function nr(e,t){for(var n=e.length;n--;)if(Va(e[n][0],t))return n;return-1}function rr(e,t,n,r){return pr(e,(function(e,o,i){t(r,e,n(e),i)})),r}function or(e,t){return e&&jo(t,Ps(t),e)}function ir(e,t,n){"__proto__"==t&&ut?ut(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n}function ar(t,n){for(var r=-1,o=n.length,i=Se(o),a=null==t;++r<o;)i[r]=a?e:Os(t,n[r]);return i}function sr(t,n,r){return t==t&&(r!==e&&(t=t<=r?t:r),n!==e&&(t=t>=n?t:n)),t}function lr(t,n,r,o,i,a){var s,l=1&n,c=2&n,u=4&n;if(r&&(s=i?r(t,o,i,a):r(t)),s!==e)return s;if(!rs(t))return t;var f=Ka(t);if(f){if(s=function(e){var t=e.length,n=new e.constructor(t);return t&&"string"==typeof e[0]&&ze.call(e,"index")&&(n.index=e.index,n.input=e.input),n}(t),!l)return Io(t,s)}else{var p=gi(t),d=p==b||p==_;if(Xa(t))return Co(t,l);if(p==x||p==h||d&&!i){if(s=c||d?{}:vi(t),!l)return c?function(e,t){return jo(e,hi(e),t)}(t,function(e,t){return e&&jo(t,Rs(t),e)}(s,t)):function(e,t){return jo(e,di(e),t)}(t,or(s,t))}else{if(!it[p])return i?t:{};s=function(e,t,n){var r,o=e.constructor;switch(t){case I:return ko(e);case m:case v:return new o(+e);case j:return function(e,t){var n=t?ko(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.byteLength)}(e,n);case L:case M:case P:case R:case z:case $:case D:case B:case N:return Eo(e,n);case w:return new o;case S:case A:return new o(e);case k:return function(e){var t=new e.constructor(e.source,fe.exec(e));return t.lastIndex=e.lastIndex,t}(e);case E:return new o;case T:return r=e,Dn?Ae(Dn.call(r)):{}}}(t,p,l)}}a||(a=new Qn);var g=a.get(t);if(g)return g;a.set(t,s),cs(t)?t.forEach((function(e){s.add(lr(e,n,r,e,t,a))})):is(t)&&t.forEach((function(e,o){s.set(o,lr(e,n,r,o,t,a))}));var y=f?e:(u?c?ii:oi:c?Rs:Ps)(t);return kt(y||t,(function(e,o){y&&(e=t[o=e]),tr(s,o,lr(e,n,r,o,t,a))})),s}function cr(t,n,r){var o=r.length;if(null==t)return!o;for(t=Ae(t);o--;){var i=r[o],a=n[i],s=t[i];if(s===e&&!(i in t)||!a(s))return!1}return!0}function ur(n,r,o){if("function"!=typeof n)throw new Ie(t);return ji((function(){n.apply(e,o)}),r)}function fr(e,t,n,r){var o=-1,i=Ot,a=!0,s=e.length,l=[],c=t.length;if(!s)return l;n&&(t=jt(t,Qt(n))),r?(i=It,a=!1):t.length>=200&&(i=Xt,a=!1,t=new Gn(t));e:for(;++o<s;){var u=e[o],f=null==n?u:n(u);if(u=r||0!==u?u:0,a&&f==f){for(var p=c;p--;)if(t[p]===f)continue e;l.push(u)}else i(t,f,r)||l.push(u)}return l}Nn.templateSettings={escape:G,evaluate:Q,interpolate:J,variable:"",imports:{_:Nn}},Nn.prototype=Hn.prototype,Nn.prototype.constructor=Nn,Vn.prototype=Fn(Hn.prototype),Vn.prototype.constructor=Vn,Un.prototype=Fn(Hn.prototype),Un.prototype.constructor=Un,Wn.prototype.clear=function(){this.__data__=On?On(null):{},this.size=0},Wn.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},Wn.prototype.get=function(t){var r=this.__data__;if(On){var o=r[t];return o===n?e:o}return ze.call(r,t)?r[t]:e},Wn.prototype.has=function(t){var n=this.__data__;return On?n[t]!==e:ze.call(n,t)},Wn.prototype.set=function(t,r){var o=this.__data__;return this.size+=this.has(t)?0:1,o[t]=On&&r===e?n:r,this},qn.prototype.clear=function(){this.__data__=[],this.size=0},qn.prototype.delete=function(e){var t=this.__data__,n=nr(t,e);return!(n<0||(n==t.length-1?t.pop():Ze.call(t,n,1),--this.size,0))},qn.prototype.get=function(t){var n=this.__data__,r=nr(n,t);return r<0?e:n[r][1]},qn.prototype.has=function(e){return nr(this.__data__,e)>-1},qn.prototype.set=function(e,t){var n=this.__data__,r=nr(n,e);return r<0?(++this.size,n.push([e,t])):n[r][1]=t,this},Kn.prototype.clear=function(){this.size=0,this.__data__={hash:new Wn,map:new(kn||qn),string:new Wn}},Kn.prototype.delete=function(e){var t=ui(this,e).delete(e);return this.size-=t?1:0,t},Kn.prototype.get=function(e){return ui(this,e).get(e)},Kn.prototype.has=function(e){return ui(this,e).has(e)},Kn.prototype.set=function(e,t){var n=ui(this,e),r=n.size;return n.set(e,t),this.size+=n.size==r?0:1,this},Gn.prototype.add=Gn.prototype.push=function(e){return this.__data__.set(e,n),this},Gn.prototype.has=function(e){return this.__data__.has(e)},Qn.prototype.clear=function(){this.__data__=new qn,this.size=0},Qn.prototype.delete=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n},Qn.prototype.get=function(e){return this.__data__.get(e)},Qn.prototype.has=function(e){return this.__data__.has(e)},Qn.prototype.set=function(e,t){var n=this.__data__;if(n instanceof qn){var r=n.__data__;if(!kn||r.length<199)return r.push([e,t]),this.size=++n.size,this;n=this.__data__=new Kn(r)}return n.set(e,t),this.size=n.size,this};var pr=Po(_r),dr=Po(wr,!0);function hr(e,t){var n=!0;return pr(e,(function(e,r,o){return n=!!t(e,r,o)})),n}function gr(t,n,r){for(var o=-1,i=t.length;++o<i;){var a=t[o],s=n(a);if(null!=s&&(l===e?s==s&&!fs(s):r(s,l)))var l=s,c=a}return c}function mr(e,t){var n=[];return pr(e,(function(e,r,o){t(e,r,o)&&n.push(e)})),n}function vr(e,t,n,r,o){var i=-1,a=e.length;for(n||(n=yi),o||(o=[]);++i<a;){var s=e[i];t>0&&n(s)?t>1?vr(s,t-1,n,r,o):Lt(o,s):r||(o[o.length]=s)}return o}var yr=Ro(),br=Ro(!0);function _r(e,t){return e&&yr(e,t,Ps)}function wr(e,t){return e&&br(e,t,Ps)}function Sr(e,t){return Tt(t,(function(t){return es(e[t])}))}function xr(t,n){for(var r=0,o=(n=_o(n,t)).length;null!=t&&r<o;)t=t[Ni(n[r++])];return r&&r==o?t:e}function Cr(e,t,n){var r=t(e);return Ka(e)?r:Lt(r,n(e))}function kr(t){return null==t?t===e?"[object Undefined]":"[object Null]":ct&&ct in Ae(t)?function(t){var n=ze.call(t,ct),r=t[ct];try{t[ct]=e;var o=!0}catch($v){}var i=Be.call(t);return o&&(n?t[ct]=r:delete t[ct]),i}(t):function(e){return Be.call(e)}(t)}function Er(e,t){return e>t}function Ar(e,t){return null!=e&&ze.call(e,t)}function Tr(e,t){return null!=e&&t in Ae(e)}function Or(t,n,r){for(var o=r?It:Ot,i=t[0].length,a=t.length,s=a,l=Se(a),c=1/0,u=[];s--;){var f=t[s];s&&n&&(f=jt(f,Qt(n))),c=bn(f.length,c),l[s]=!r&&(n||i>=120&&f.length>=120)?new Gn(s&&f):e}f=t[0];var p=-1,d=l[0];e:for(;++p<i&&u.length<c;){var h=f[p],g=n?n(h):h;if(h=r||0!==h?h:0,!(d?Xt(d,g):o(u,g,r))){for(s=a;--s;){var m=l[s];if(!(m?Xt(m,g):o(t[s],g,r)))continue e}d&&d.push(g),u.push(h)}}return u}function Ir(t,n,r){var o=null==(t=Ti(t,n=_o(n,t)))?t:t[Ni(Yi(n))];return null==o?e:xt(o,t,r)}function jr(e){return os(e)&&kr(e)==h}function Lr(t,n,r,o,i){return t===n||(null==t||null==n||!os(t)&&!os(n)?t!=t&&n!=n:function(t,n,r,o,i,a){var s=Ka(t),l=Ka(n),c=s?g:gi(t),u=l?g:gi(n),f=(c=c==h?x:c)==x,p=(u=u==h?x:u)==x,d=c==u;if(d&&Xa(t)){if(!Xa(n))return!1;s=!0,f=!1}if(d&&!f)return a||(a=new Qn),s||ps(t)?ni(t,n,r,o,i,a):function(e,t,n,r,o,i,a){switch(n){case j:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case I:return!(e.byteLength!=t.byteLength||!i(new We(e),new We(t)));case m:case v:case S:return Va(+e,+t);case y:return e.name==t.name&&e.message==t.message;case k:case A:return e==t+"";case w:var s=on;case E:var l=1&r;if(s||(s=ln),e.size!=t.size&&!l)return!1;var c=a.get(e);if(c)return c==t;r|=2,a.set(e,t);var u=ni(s(e),s(t),r,o,i,a);return a.delete(e),u;case T:if(Dn)return Dn.call(e)==Dn.call(t)}return!1}(t,n,c,r,o,i,a);if(!(1&r)){var b=f&&ze.call(t,"__wrapped__"),_=p&&ze.call(n,"__wrapped__");if(b||_){var C=b?t.value():t,O=_?n.value():n;return a||(a=new Qn),i(C,O,r,o,a)}}return!!d&&(a||(a=new Qn),function(t,n,r,o,i,a){var s=1&r,l=oi(t),c=l.length,u=oi(n),f=u.length;if(c!=f&&!s)return!1;for(var p=c;p--;){var d=l[p];if(!(s?d in n:ze.call(n,d)))return!1}var h=a.get(t),g=a.get(n);if(h&&g)return h==n&&g==t;var m=!0;a.set(t,n),a.set(n,t);for(var v=s;++p<c;){var y=t[d=l[p]],b=n[d];if(o)var _=s?o(b,y,d,n,t,a):o(y,b,d,t,n,a);if(!(_===e?y===b||i(y,b,r,o,a):_)){m=!1;break}v||(v="constructor"==d)}if(m&&!v){var w=t.constructor,S=n.constructor;w==S||!("constructor"in t)||!("constructor"in n)||"function"==typeof w&&w instanceof w&&"function"==typeof S&&S instanceof S||(m=!1)}return a.delete(t),a.delete(n),m}(t,n,r,o,i,a))}(t,n,r,o,Lr,i))}function Mr(t,n,r,o){var i=r.length,a=i,s=!o;if(null==t)return!a;for(t=Ae(t);i--;){var l=r[i];if(s&&l[2]?l[1]!==t[l[0]]:!(l[0]in t))return!1}for(;++i<a;){var c=(l=r[i])[0],u=t[c],f=l[1];if(s&&l[2]){if(u===e&&!(c in t))return!1}else{var p=new Qn;if(o)var d=o(u,f,c,t,n,p);if(!(d===e?Lr(f,u,3,o,p):d))return!1}}return!0}function Pr(e){return!(!rs(e)||(t=e,De&&De in t))&&(es(e)?He:he).test(Fi(e));var t}function Rr(e){return"function"==typeof e?e:null==e?al:"object"==typeof e?Ka(e)?Fr(e[0],e[1]):Nr(e):gl(e)}function zr(e){if(!Ci(e))return vn(e);var t=[];for(var n in Ae(e))ze.call(e,n)&&"constructor"!=n&&t.push(n);return t}function $r(e){if(!rs(e))return function(e){var t=[];if(null!=e)for(var n in Ae(e))t.push(n);return t}(e);var t=Ci(e),n=[];for(var r in e)("constructor"!=r||!t&&ze.call(e,r))&&n.push(r);return n}function Dr(e,t){return e<t}function Br(e,t){var n=-1,r=Qa(e)?Se(e.length):[];return pr(e,(function(e,o,i){r[++n]=t(e,o,i)})),r}function Nr(e){var t=fi(e);return 1==t.length&&t[0][2]?Ei(t[0][0],t[0][1]):function(n){return n===e||Mr(n,e,t)}}function Fr(t,n){return wi(t)&&ki(n)?Ei(Ni(t),n):function(r){var o=Os(r,t);return o===e&&o===n?Is(r,t):Lr(n,o,3)}}function Hr(t,n,r,o,i){t!==n&&yr(n,(function(a,s){if(i||(i=new Qn),rs(a))!function(t,n,r,o,i,a,s){var l=Oi(t,r),c=Oi(n,r),u=s.get(c);if(u)er(t,r,u);else{var f=a?a(l,c,r+"",t,n,s):e,p=f===e;if(p){var d=Ka(c),h=!d&&Xa(c),g=!d&&!h&&ps(c);f=c,d||h||g?Ka(l)?f=l:Ja(l)?f=Io(l):h?(p=!1,f=Co(c,!0)):g?(p=!1,f=Eo(c,!0)):f=[]:ss(c)||qa(c)?(f=l,qa(l)?f=_s(l):rs(l)&&!es(l)||(f=vi(c))):p=!1}p&&(s.set(c,f),i(f,c,o,a,s),s.delete(c)),er(t,r,f)}}(t,n,s,r,Hr,o,i);else{var l=o?o(Oi(t,s),a,s+"",t,n,i):e;l===e&&(l=a),er(t,s,l)}}),Rs)}function Vr(t,n){var r=t.length;if(r)return bi(n+=n<0?r:0,r)?t[n]:e}function Ur(e,t,n){t=t.length?jt(t,(function(e){return Ka(e)?function(t){return xr(t,1===e.length?e[0]:e)}:e})):[al];var r=-1;return t=jt(t,Qt(ci())),function(e,t){var n=e.length;for(e.sort(t);n--;)e[n]=e[n].value;return e}(Br(e,(function(e,n,o){return{criteria:jt(t,(function(t){return t(e)})),index:++r,value:e}})),(function(e,t){return function(e,t,n){for(var r=-1,o=e.criteria,i=t.criteria,a=o.length,s=n.length;++r<a;){var l=Ao(o[r],i[r]);if(l)return r>=s?l:l*("desc"==n[r]?-1:1)}return e.index-t.index}(e,t,n)}))}function Wr(e,t,n){for(var r=-1,o=t.length,i={};++r<o;){var a=t[r],s=xr(e,a);n(s,a)&&Yr(i,_o(a,e),s)}return i}function qr(e,t,n,r){var o=r?Nt:Bt,i=-1,a=t.length,s=e;for(e===t&&(t=Io(t)),n&&(s=jt(e,Qt(n)));++i<a;)for(var l=0,c=t[i],u=n?n(c):c;(l=o(s,u,l,r))>-1;)s!==e&&Ze.call(s,l,1),Ze.call(e,l,1);return e}function Kr(e,t){for(var n=e?t.length:0,r=n-1;n--;){var o=t[n];if(n==r||o!==i){var i=o;bi(o)?Ze.call(e,o,1):fo(e,o)}}return e}function Gr(e,t){return e+zt(Sn()*(t-e+1))}function Qr(e,t){var n="";if(!e||t<1||t>u)return n;do{t%2&&(n+=e),(t=zt(t/2))&&(e+=e)}while(t);return n}function Jr(e,t){return Li(Ai(e,t,al),e+"")}function Xr(e){return Xn(Vs(e))}function Zr(e,t){var n=Vs(e);return Ri(n,sr(t,0,n.length))}function Yr(t,n,r,o){if(!rs(t))return t;for(var i=-1,a=(n=_o(n,t)).length,s=a-1,l=t;null!=l&&++i<a;){var c=Ni(n[i]),u=r;if("__proto__"===c||"constructor"===c||"prototype"===c)return t;if(i!=s){var f=l[c];(u=o?o(f,c,l):e)===e&&(u=rs(f)?f:bi(n[i+1])?[]:{})}tr(l,c,u),l=l[c]}return t}var eo=In?function(e,t){return In.set(e,t),e}:al,to=ut?function(e,t){return ut(e,"toString",{configurable:!0,enumerable:!1,value:rl(t),writable:!0})}:al;function no(e){return Ri(Vs(e))}function ro(e,t,n){var r=-1,o=e.length;t<0&&(t=-t>o?0:o+t),(n=n>o?o:n)<0&&(n+=o),o=t>n?0:n-t>>>0,t>>>=0;for(var i=Se(o);++r<o;)i[r]=e[r+t];return i}function oo(e,t){var n;return pr(e,(function(e,r,o){return!(n=t(e,r,o))})),!!n}function io(e,t,n){var r=0,o=null==e?r:e.length;if("number"==typeof t&&t==t&&o<=2147483647){for(;r<o;){var i=r+o>>>1,a=e[i];null!==a&&!fs(a)&&(n?a<=t:a<t)?r=i+1:o=i}return o}return ao(e,t,al,n)}function ao(t,n,r,o){var i=0,a=null==t?0:t.length;if(0===a)return 0;for(var s=(n=r(n))!=n,l=null===n,c=fs(n),u=n===e;i<a;){var f=zt((i+a)/2),p=r(t[f]),d=p!==e,h=null===p,g=p==p,m=fs(p);if(s)var v=o||g;else v=u?g&&(o||d):l?g&&d&&(o||!h):c?g&&d&&!h&&(o||!m):!h&&!m&&(o?p<=n:p<n);v?i=f+1:a=f}return bn(a,4294967294)}function so(e,t){for(var n=-1,r=e.length,o=0,i=[];++n<r;){var a=e[n],s=t?t(a):a;if(!n||!Va(s,l)){var l=s;i[o++]=0===a?0:a}}return i}function lo(e){return"number"==typeof e?e:fs(e)?f:+e}function co(e){if("string"==typeof e)return e;if(Ka(e))return jt(e,co)+"";if(fs(e))return Bn?Bn.call(e):"";var t=e+"";return"0"==t&&1/e==-1/0?"-0":t}function uo(e,t,n){var r=-1,o=Ot,i=e.length,a=!0,s=[],l=s;if(n)a=!1,o=It;else if(i>=200){var c=t?null:Jo(e);if(c)return ln(c);a=!1,o=Xt,l=new Gn}else l=t?[]:s;e:for(;++r<i;){var u=e[r],f=t?t(u):u;if(u=n||0!==u?u:0,a&&f==f){for(var p=l.length;p--;)if(l[p]===f)continue e;t&&l.push(f),s.push(u)}else o(l,f,n)||(l!==s&&l.push(f),s.push(u))}return s}function fo(e,t){return null==(e=Ti(e,t=_o(t,e)))||delete e[Ni(Yi(t))]}function po(e,t,n,r){return Yr(e,t,n(xr(e,t)),r)}function ho(e,t,n,r){for(var o=e.length,i=r?o:-1;(r?i--:++i<o)&&t(e[i],i,e););return n?ro(e,r?0:i,r?i+1:o):ro(e,r?i+1:0,r?o:i)}function go(e,t){var n=e;return n instanceof Un&&(n=n.value()),Mt(t,(function(e,t){return t.func.apply(t.thisArg,Lt([e],t.args))}),n)}function mo(e,t,n){var r=e.length;if(r<2)return r?uo(e[0]):[];for(var o=-1,i=Se(r);++o<r;)for(var a=e[o],s=-1;++s<r;)s!=o&&(i[o]=fr(i[o]||a,e[s],t,n));return uo(vr(i,1),t,n)}function vo(t,n,r){for(var o=-1,i=t.length,a=n.length,s={};++o<i;){var l=o<a?n[o]:e;r(s,t[o],l)}return s}function yo(e){return Ja(e)?e:[]}function bo(e){return"function"==typeof e?e:al}function _o(e,t){return Ka(e)?e:wi(e,t)?[e]:Bi(ws(e))}var wo=Jr;function So(t,n,r){var o=t.length;return r=r===e?o:r,!n&&r>=o?t:ro(t,n,r)}var xo=pt||function(e){return ft.clearTimeout(e)};function Co(e,t){if(t)return e.slice();var n=e.length,r=qe?qe(n):new e.constructor(n);return e.copy(r),r}function ko(e){var t=new e.constructor(e.byteLength);return new We(t).set(new We(e)),t}function Eo(e,t){var n=t?ko(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.length)}function Ao(t,n){if(t!==n){var r=t!==e,o=null===t,i=t==t,a=fs(t),s=n!==e,l=null===n,c=n==n,u=fs(n);if(!l&&!u&&!a&&t>n||a&&s&&c&&!l&&!u||o&&s&&c||!r&&c||!i)return 1;if(!o&&!a&&!u&&t<n||u&&r&&i&&!o&&!a||l&&r&&i||!s&&i||!c)return-1}return 0}function To(e,t,n,r){for(var o=-1,i=e.length,a=n.length,s=-1,l=t.length,c=yn(i-a,0),u=Se(l+c),f=!r;++s<l;)u[s]=t[s];for(;++o<a;)(f||o<i)&&(u[n[o]]=e[o]);for(;c--;)u[s++]=e[o++];return u}function Oo(e,t,n,r){for(var o=-1,i=e.length,a=-1,s=n.length,l=-1,c=t.length,u=yn(i-s,0),f=Se(u+c),p=!r;++o<u;)f[o]=e[o];for(var d=o;++l<c;)f[d+l]=t[l];for(;++a<s;)(p||o<i)&&(f[d+n[a]]=e[o++]);return f}function Io(e,t){var n=-1,r=e.length;for(t||(t=Se(r));++n<r;)t[n]=e[n];return t}function jo(t,n,r,o){var i=!r;r||(r={});for(var a=-1,s=n.length;++a<s;){var l=n[a],c=o?o(r[l],t[l],l,r,t):e;c===e&&(c=t[l]),i?ir(r,l,c):tr(r,l,c)}return r}function Lo(e,t){return function(n,r){var o=Ka(n)?Ct:rr,i=t?t():{};return o(n,e,ci(r,2),i)}}function Mo(t){return Jr((function(n,r){var o=-1,i=r.length,a=i>1?r[i-1]:e,s=i>2?r[2]:e;for(a=t.length>3&&"function"==typeof a?(i--,a):e,s&&_i(r[0],r[1],s)&&(a=i<3?e:a,i=1),n=Ae(n);++o<i;){var l=r[o];l&&t(n,l,o,a)}return n}))}function Po(e,t){return function(n,r){if(null==n)return n;if(!Qa(n))return e(n,r);for(var o=n.length,i=t?o:-1,a=Ae(n);(t?i--:++i<o)&&!1!==r(a[i],i,a););return n}}function Ro(e){return function(t,n,r){for(var o=-1,i=Ae(t),a=r(t),s=a.length;s--;){var l=a[e?s:++o];if(!1===n(i[l],l,i))break}return t}}function zo(t){return function(n){var r=rn(n=ws(n))?un(n):e,o=r?r[0]:n.charAt(0),i=r?So(r,1).join(""):n.slice(1);return o[t]()+i}}function $o(e){return function(t){return Mt(el(qs(t).replace(Je,"")),e,"")}}function Do(e){return function(){var t=arguments;switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3]);case 5:return new e(t[0],t[1],t[2],t[3],t[4]);case 6:return new e(t[0],t[1],t[2],t[3],t[4],t[5]);case 7:return new e(t[0],t[1],t[2],t[3],t[4],t[5],t[6])}var n=Fn(e.prototype),r=e.apply(n,t);return rs(r)?r:n}}function Bo(t){return function(n,r,o){var i=Ae(n);if(!Qa(n)){var a=ci(r,3);n=Ps(n),r=function(e){return a(i[e],e,i)}}var s=t(n,r,o);return s>-1?i[a?n[s]:s]:e}}function No(n){return ri((function(r){var o=r.length,i=o,a=Vn.prototype.thru;for(n&&r.reverse();i--;){var s=r[i];if("function"!=typeof s)throw new Ie(t);if(a&&!l&&"wrapper"==si(s))var l=new Vn([],!0)}for(i=l?i:o;++i<o;){var c=si(s=r[i]),u="wrapper"==c?ai(s):e;l=u&&Si(u[0])&&424==u[1]&&!u[4].length&&1==u[9]?l[si(u[0])].apply(l,u[3]):1==s.length&&Si(s)?l[c]():l.thru(s)}return function(){var e=arguments,t=e[0];if(l&&1==e.length&&Ka(t))return l.plant(t).value();for(var n=0,i=o?r[n].apply(this,e):t;++n<o;)i=r[n].call(this,i);return i}}))}function Fo(t,n,r,o,i,a,l,c,u,f){var p=n&s,d=1&n,h=2&n,g=24&n,m=512&n,v=h?e:Do(t);return function s(){for(var y=arguments.length,b=Se(y),_=y;_--;)b[_]=arguments[_];if(g)var w=li(s),S=function(e,t){for(var n=e.length,r=0;n--;)e[n]===t&&++r;return r}(b,w);if(o&&(b=To(b,o,i,g)),a&&(b=Oo(b,a,l,g)),y-=S,g&&y<f){var x=sn(b,w);return Go(t,n,Fo,s.placeholder,r,b,x,c,u,f-y)}var C=d?r:this,k=h?C[t]:t;return y=b.length,c?b=function(t,n){for(var r=t.length,o=bn(n.length,r),i=Io(t);o--;){var a=n[o];t[o]=bi(a,r)?i[a]:e}return t}(b,c):m&&y>1&&b.reverse(),p&&u<y&&(b.length=u),this&&this!==ft&&this instanceof s&&(k=v||Do(k)),k.apply(C,b)}}function Ho(e,t){return function(n,r){return function(e,t,n,r){return _r(e,(function(e,o,i){t(r,n(e),o,i)})),r}(n,e,t(r),{})}}function Vo(t,n){return function(r,o){var i;if(r===e&&o===e)return n;if(r!==e&&(i=r),o!==e){if(i===e)return o;"string"==typeof r||"string"==typeof o?(r=co(r),o=co(o)):(r=lo(r),o=lo(o)),i=t(r,o)}return i}}function Uo(e){return ri((function(t){return t=jt(t,Qt(ci())),Jr((function(n){var r=this;return e(t,(function(e){return xt(e,r,n)}))}))}))}function Wo(t,n){var r=(n=n===e?" ":co(n)).length;if(r<2)return r?Qr(n,t):n;var o=Qr(n,mt(t/cn(n)));return rn(n)?So(un(o),0,t).join(""):o.slice(0,t)}function qo(t){return function(n,r,o){return o&&"number"!=typeof o&&_i(n,r,o)&&(r=o=e),n=ms(n),r===e?(r=n,n=0):r=ms(r),function(e,t,n,r){for(var o=-1,i=yn(mt((t-e)/(n||1)),0),a=Se(i);i--;)a[r?i:++o]=e,e+=n;return a}(n,r,o=o===e?n<r?1:-1:ms(o),t)}}function Ko(e){return function(t,n){return"string"==typeof t&&"string"==typeof n||(t=bs(t),n=bs(n)),e(t,n)}}function Go(t,n,r,o,s,l,c,u,f,p){var d=8&n;n|=d?i:a,4&(n&=~(d?a:i))||(n&=-4);var h=[t,n,s,d?l:e,d?c:e,d?e:l,d?e:c,u,f,p],g=r.apply(e,h);return Si(t)&&Ii(g,h),g.placeholder=o,Mi(g,t,n)}function Qo(e){var t=Ee[e];return function(e,n){if(e=bs(e),(n=null==n?0:bn(vs(n),292))&&gn(e)){var r=(ws(e)+"e").split("e");return+((r=(ws(t(r[0]+"e"+(+r[1]+n)))+"e").split("e"))[0]+"e"+(+r[1]-n))}return t(e)}}var Jo=An&&1/ln(new An([,-0]))[1]==c?function(e){return new An(e)}:fl;function Xo(e){return function(t){var n=gi(t);return n==w?on(t):n==E?function(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=[e,e]})),n}(t):function(e,t){return jt(t,(function(t){return[t,e[t]]}))}(t,e(t))}}function Zo(n,c,u,f,p,d,h,g){var m=2&c;if(!m&&"function"!=typeof n)throw new Ie(t);var v=f?f.length:0;if(v||(c&=-97,f=p=e),h=h===e?h:yn(vs(h),0),g=g===e?g:vs(g),v-=p?p.length:0,c&a){var y=f,b=p;f=p=e}var _=m?e:ai(n),w=[n,c,u,f,p,y,b,d,h,g];if(_&&function(e,t){var n=e[1],o=t[1],i=n|o,a=i<131,c=o==s&&8==n||o==s&&n==l&&e[7].length<=t[8]||384==o&&t[7].length<=t[8]&&8==n;if(!a&&!c)return e;1&o&&(e[2]=t[2],i|=1&n?0:4);var u=t[3];if(u){var f=e[3];e[3]=f?To(f,u,t[4]):u,e[4]=f?sn(e[3],r):t[4]}(u=t[5])&&(f=e[5],e[5]=f?Oo(f,u,t[6]):u,e[6]=f?sn(e[5],r):t[6]),(u=t[7])&&(e[7]=u),o&s&&(e[8]=null==e[8]?t[8]:bn(e[8],t[8])),null==e[9]&&(e[9]=t[9]),e[0]=t[0],e[1]=i}(w,_),n=w[0],c=w[1],u=w[2],f=w[3],p=w[4],!(g=w[9]=w[9]===e?m?0:n.length:yn(w[9]-v,0))&&24&c&&(c&=-25),c&&1!=c)S=8==c||c==o?function(t,n,r){var o=Do(t);return function i(){for(var a=arguments.length,s=Se(a),l=a,c=li(i);l--;)s[l]=arguments[l];var u=a<3&&s[0]!==c&&s[a-1]!==c?[]:sn(s,c);return(a-=u.length)<r?Go(t,n,Fo,i.placeholder,e,s,u,e,e,r-a):xt(this&&this!==ft&&this instanceof i?o:t,this,s)}}(n,c,g):c!=i&&33!=c||p.length?Fo.apply(e,w):function(e,t,n,r){var o=1&t,i=Do(e);return function t(){for(var a=-1,s=arguments.length,l=-1,c=r.length,u=Se(c+s),f=this&&this!==ft&&this instanceof t?i:e;++l<c;)u[l]=r[l];for(;s--;)u[l++]=arguments[++a];return xt(f,o?n:this,u)}}(n,c,u,f);else var S=function(e,t,n){var r=1&t,o=Do(e);return function t(){return(this&&this!==ft&&this instanceof t?o:e).apply(r?n:this,arguments)}}(n,c,u);return Mi((_?eo:Ii)(S,w),n,c)}function Yo(t,n,r,o){return t===e||Va(t,Me[r])&&!ze.call(o,r)?n:t}function ei(t,n,r,o,i,a){return rs(t)&&rs(n)&&(a.set(n,t),Hr(t,n,e,ei,a),a.delete(n)),t}function ti(t){return ss(t)?e:t}function ni(t,n,r,o,i,a){var s=1&r,l=t.length,c=n.length;if(l!=c&&!(s&&c>l))return!1;var u=a.get(t),f=a.get(n);if(u&&f)return u==n&&f==t;var p=-1,d=!0,h=2&r?new Gn:e;for(a.set(t,n),a.set(n,t);++p<l;){var g=t[p],m=n[p];if(o)var v=s?o(m,g,p,n,t,a):o(g,m,p,t,n,a);if(v!==e){if(v)continue;d=!1;break}if(h){if(!Rt(n,(function(e,t){if(!Xt(h,t)&&(g===e||i(g,e,r,o,a)))return h.push(t)}))){d=!1;break}}else if(g!==m&&!i(g,m,r,o,a)){d=!1;break}}return a.delete(t),a.delete(n),d}function ri(t){return Li(Ai(t,e,Gi),t+"")}function oi(e){return Cr(e,Ps,di)}function ii(e){return Cr(e,Rs,hi)}var ai=In?function(e){return In.get(e)}:fl;function si(e){for(var t=e.name+"",n=jn[t],r=ze.call(jn,t)?n.length:0;r--;){var o=n[r],i=o.func;if(null==i||i==e)return o.name}return t}function li(e){return(ze.call(Nn,"placeholder")?Nn:e).placeholder}function ci(){var e=Nn.iteratee||sl;return e=e===sl?Rr:e,arguments.length?e(arguments[0],arguments[1]):e}function ui(e,t){var n,r,o=e.__data__;return("string"==(r=typeof(n=t))||"number"==r||"symbol"==r||"boolean"==r?"__proto__"!==n:null===n)?o["string"==typeof t?"string":"hash"]:o.map}function fi(e){for(var t=Ps(e),n=t.length;n--;){var r=t[n],o=e[r];t[n]=[r,o,ki(o)]}return t}function pi(t,n){var r=function(t,n){return null==t?e:t[n]}(t,n);return Pr(r)?r:e}var di=Ut?function(e){return null==e?[]:(e=Ae(e),Tt(Ut(e),(function(t){return Qe.call(e,t)})))}:yl,hi=Ut?function(e){for(var t=[];e;)Lt(t,di(e)),e=Ke(e);return t}:yl,gi=kr;function mi(e,t,n){for(var r=-1,o=(t=_o(t,e)).length,i=!1;++r<o;){var a=Ni(t[r]);if(!(i=null!=e&&n(e,a)))break;e=e[a]}return i||++r!=o?i:!!(o=null==e?0:e.length)&&ns(o)&&bi(a,o)&&(Ka(e)||qa(e))}function vi(e){return"function"!=typeof e.constructor||Ci(e)?{}:Fn(Ke(e))}function yi(e){return Ka(e)||qa(e)||!!(et&&e&&e[et])}function bi(e,t){var n=typeof e;return!!(t=null==t?u:t)&&("number"==n||"symbol"!=n&&me.test(e))&&e>-1&&e%1==0&&e<t}function _i(e,t,n){if(!rs(n))return!1;var r=typeof t;return!!("number"==r?Qa(n)&&bi(t,n.length):"string"==r&&t in n)&&Va(n[t],e)}function wi(e,t){if(Ka(e))return!1;var n=typeof e;return!("number"!=n&&"symbol"!=n&&"boolean"!=n&&null!=e&&!fs(e))||Z.test(e)||!X.test(e)||null!=t&&e in Ae(t)}function Si(e){var t=si(e),n=Nn[t];if("function"!=typeof n||!(t in Un.prototype))return!1;if(e===n)return!0;var r=ai(n);return!!r&&e===r[0]}(Cn&&gi(new Cn(new ArrayBuffer(1)))!=j||kn&&gi(new kn)!=w||En&&gi(En.resolve())!=C||An&&gi(new An)!=E||Tn&&gi(new Tn)!=O)&&(gi=function(t){var n=kr(t),r=n==x?t.constructor:e,o=r?Fi(r):"";if(o)switch(o){case Ln:return j;case Mn:return w;case Pn:return C;case Rn:return E;case zn:return O}return n});var xi=Pe?es:bl;function Ci(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||Me)}function ki(e){return e==e&&!rs(e)}function Ei(t,n){return function(r){return null!=r&&r[t]===n&&(n!==e||t in Ae(r))}}function Ai(t,n,r){return n=yn(n===e?t.length-1:n,0),function(){for(var e=arguments,o=-1,i=yn(e.length-n,0),a=Se(i);++o<i;)a[o]=e[n+o];o=-1;for(var s=Se(n+1);++o<n;)s[o]=e[o];return s[n]=r(a),xt(t,this,s)}}function Ti(e,t){return t.length<2?e:xr(e,ro(t,0,-1))}function Oi(e,t){if(("constructor"!==t||"function"!=typeof e[t])&&"__proto__"!=t)return e[t]}var Ii=Pi(eo),ji=gt||function(e,t){return ft.setTimeout(e,t)},Li=Pi(to);function Mi(e,t,n){var r=t+"";return Li(e,function(e,t){var n=t.length;if(!n)return e;var r=n-1;return t[r]=(n>1?"& ":"")+t[r],t=t.join(n>2?", ":" "),e.replace(oe,"{\n/* [wrapped with "+t+"] */\n")}(r,function(e,t){return kt(d,(function(n){var r="_."+n[0];t&n[1]&&!Ot(e,r)&&e.push(r)})),e.sort()}(function(e){var t=e.match(ie);return t?t[1].split(ae):[]}(r),n)))}function Pi(t){var n=0,r=0;return function(){var o=_n(),i=16-(o-r);if(r=o,i>0){if(++n>=800)return arguments[0]}else n=0;return t.apply(e,arguments)}}function Ri(t,n){var r=-1,o=t.length,i=o-1;for(n=n===e?o:n;++r<n;){var a=Gr(r,i),s=t[a];t[a]=t[r],t[r]=s}return t.length=n,t}var zi,$i,Di,Bi=(zi=function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(Y,(function(e,n,r,o){t.push(r?o.replace(ce,"$1"):n||e)})),t},$i=$a(zi,(function(e){return 500===Di.size&&Di.clear(),e})),Di=$i.cache,$i);function Ni(e){if("string"==typeof e||fs(e))return e;var t=e+"";return"0"==t&&1/e==-1/0?"-0":t}function Fi(e){if(null!=e){try{return Re.call(e)}catch($v){}try{return e+""}catch($v){}}return""}function Hi(e){if(e instanceof Un)return e.clone();var t=new Vn(e.__wrapped__,e.__chain__);return t.__actions__=Io(e.__actions__),t.__index__=e.__index__,t.__values__=e.__values__,t}var Vi=Jr((function(e,t){return Ja(e)?fr(e,vr(t,1,Ja,!0)):[]})),Ui=Jr((function(t,n){var r=Yi(n);return Ja(r)&&(r=e),Ja(t)?fr(t,vr(n,1,Ja,!0),ci(r,2)):[]})),Wi=Jr((function(t,n){var r=Yi(n);return Ja(r)&&(r=e),Ja(t)?fr(t,vr(n,1,Ja,!0),e,r):[]}));function qi(e,t,n){var r=null==e?0:e.length;if(!r)return-1;var o=null==n?0:vs(n);return o<0&&(o=yn(r+o,0)),Dt(e,ci(t,3),o)}function Ki(t,n,r){var o=null==t?0:t.length;if(!o)return-1;var i=o-1;return r!==e&&(i=vs(r),i=r<0?yn(o+i,0):bn(i,o-1)),Dt(t,ci(n,3),i,!0)}function Gi(e){return null!=e&&e.length?vr(e,1):[]}function Qi(t){return t&&t.length?t[0]:e}var Ji=Jr((function(e){var t=jt(e,yo);return t.length&&t[0]===e[0]?Or(t):[]})),Xi=Jr((function(t){var n=Yi(t),r=jt(t,yo);return n===Yi(r)?n=e:r.pop(),r.length&&r[0]===t[0]?Or(r,ci(n,2)):[]})),Zi=Jr((function(t){var n=Yi(t),r=jt(t,yo);return(n="function"==typeof n?n:e)&&r.pop(),r.length&&r[0]===t[0]?Or(r,e,n):[]}));function Yi(t){var n=null==t?0:t.length;return n?t[n-1]:e}var ea=Jr(ta);function ta(e,t){return e&&e.length&&t&&t.length?qr(e,t):e}var na=ri((function(e,t){var n=null==e?0:e.length,r=ar(e,t);return Kr(e,jt(t,(function(e){return bi(e,n)?+e:e})).sort(Ao)),r}));function ra(e){return null==e?e:xn.call(e)}var oa=Jr((function(e){return uo(vr(e,1,Ja,!0))})),ia=Jr((function(t){var n=Yi(t);return Ja(n)&&(n=e),uo(vr(t,1,Ja,!0),ci(n,2))})),aa=Jr((function(t){var n=Yi(t);return n="function"==typeof n?n:e,uo(vr(t,1,Ja,!0),e,n)}));function sa(e){if(!e||!e.length)return[];var t=0;return e=Tt(e,(function(e){if(Ja(e))return t=yn(e.length,t),!0})),Kt(t,(function(t){return jt(e,Vt(t))}))}function la(t,n){if(!t||!t.length)return[];var r=sa(t);return null==n?r:jt(r,(function(t){return xt(n,e,t)}))}var ca=Jr((function(e,t){return Ja(e)?fr(e,t):[]})),ua=Jr((function(e){return mo(Tt(e,Ja))})),fa=Jr((function(t){var n=Yi(t);return Ja(n)&&(n=e),mo(Tt(t,Ja),ci(n,2))})),pa=Jr((function(t){var n=Yi(t);return n="function"==typeof n?n:e,mo(Tt(t,Ja),e,n)})),da=Jr(sa),ha=Jr((function(t){var n=t.length,r=n>1?t[n-1]:e;return r="function"==typeof r?(t.pop(),r):e,la(t,r)}));function ga(e){var t=Nn(e);return t.__chain__=!0,t}function ma(e,t){return t(e)}var va=ri((function(t){var n=t.length,r=n?t[0]:0,o=this.__wrapped__,i=function(e){return ar(e,t)};return!(n>1||this.__actions__.length)&&o instanceof Un&&bi(r)?((o=o.slice(r,+r+(n?1:0))).__actions__.push({func:ma,args:[i],thisArg:e}),new Vn(o,this.__chain__).thru((function(t){return n&&!t.length&&t.push(e),t}))):this.thru(i)})),ya=Lo((function(e,t,n){ze.call(e,n)?++e[n]:ir(e,n,1)})),ba=Bo(qi),_a=Bo(Ki);function wa(e,t){return(Ka(e)?kt:pr)(e,ci(t,3))}function Sa(e,t){return(Ka(e)?Et:dr)(e,ci(t,3))}var xa=Lo((function(e,t,n){ze.call(e,n)?e[n].push(t):ir(e,n,[t])})),Ca=Jr((function(e,t,n){var r=-1,o="function"==typeof t,i=Qa(e)?Se(e.length):[];return pr(e,(function(e){i[++r]=o?xt(t,e,n):Ir(e,t,n)})),i})),ka=Lo((function(e,t,n){ir(e,n,t)}));function Ea(e,t){return(Ka(e)?jt:Br)(e,ci(t,3))}var Aa=Lo((function(e,t,n){e[n?0:1].push(t)}),(function(){return[[],[]]})),Ta=Jr((function(e,t){if(null==e)return[];var n=t.length;return n>1&&_i(e,t[0],t[1])?t=[]:n>2&&_i(t[0],t[1],t[2])&&(t=[t[0]]),Ur(e,vr(t,1),[])})),Oa=dt||function(){return ft.Date.now()};function Ia(t,n,r){return n=r?e:n,n=t&&null==n?t.length:n,Zo(t,s,e,e,e,e,n)}function ja(n,r){var o;if("function"!=typeof r)throw new Ie(t);return n=vs(n),function(){return--n>0&&(o=r.apply(this,arguments)),n<=1&&(r=e),o}}var La=Jr((function(e,t,n){var r=1;if(n.length){var o=sn(n,li(La));r|=i}return Zo(e,r,t,n,o)})),Ma=Jr((function(e,t,n){var r=3;if(n.length){var o=sn(n,li(Ma));r|=i}return Zo(t,r,e,n,o)}));function Pa(n,r,o){var i,a,s,l,c,u,f=0,p=!1,d=!1,h=!0;if("function"!=typeof n)throw new Ie(t);function g(t){var r=i,o=a;return i=a=e,f=t,l=n.apply(o,r)}function m(t){var n=t-u;return u===e||n>=r||n<0||d&&t-f>=s}function v(){var e=Oa();if(m(e))return y(e);c=ji(v,function(e){var t=r-(e-u);return d?bn(t,s-(e-f)):t}(e))}function y(t){return c=e,h&&i?g(t):(i=a=e,l)}function b(){var t=Oa(),n=m(t);if(i=arguments,a=this,u=t,n){if(c===e)return function(e){return f=e,c=ji(v,r),p?g(e):l}(u);if(d)return xo(c),c=ji(v,r),g(u)}return c===e&&(c=ji(v,r)),l}return r=bs(r)||0,rs(o)&&(p=!!o.leading,s=(d="maxWait"in o)?yn(bs(o.maxWait)||0,r):s,h="trailing"in o?!!o.trailing:h),b.cancel=function(){c!==e&&xo(c),f=0,i=u=a=c=e},b.flush=function(){return c===e?l:y(Oa())},b}var Ra=Jr((function(e,t){return ur(e,1,t)})),za=Jr((function(e,t,n){return ur(e,bs(t)||0,n)}));function $a(e,n){if("function"!=typeof e||null!=n&&"function"!=typeof n)throw new Ie(t);var r=function(){var t=arguments,o=n?n.apply(this,t):t[0],i=r.cache;if(i.has(o))return i.get(o);var a=e.apply(this,t);return r.cache=i.set(o,a)||i,a};return r.cache=new($a.Cache||Kn),r}function Da(e){if("function"!=typeof e)throw new Ie(t);return function(){var t=arguments;switch(t.length){case 0:return!e.call(this);case 1:return!e.call(this,t[0]);case 2:return!e.call(this,t[0],t[1]);case 3:return!e.call(this,t[0],t[1],t[2])}return!e.apply(this,t)}}$a.Cache=Kn;var Ba=wo((function(e,t){var n=(t=1==t.length&&Ka(t[0])?jt(t[0],Qt(ci())):jt(vr(t,1),Qt(ci()))).length;return Jr((function(r){for(var o=-1,i=bn(r.length,n);++o<i;)r[o]=t[o].call(this,r[o]);return xt(e,this,r)}))})),Na=Jr((function(t,n){var r=sn(n,li(Na));return Zo(t,i,e,n,r)})),Fa=Jr((function(t,n){var r=sn(n,li(Fa));return Zo(t,a,e,n,r)})),Ha=ri((function(t,n){return Zo(t,l,e,e,e,n)}));function Va(e,t){return e===t||e!=e&&t!=t}var Ua=Ko(Er),Wa=Ko((function(e,t){return e>=t})),qa=jr(function(){return arguments}())?jr:function(e){return os(e)&&ze.call(e,"callee")&&!Qe.call(e,"callee")},Ka=Se.isArray,Ga=vt?Qt(vt):function(e){return os(e)&&kr(e)==I};function Qa(e){return null!=e&&ns(e.length)&&!es(e)}function Ja(e){return os(e)&&Qa(e)}var Xa=hn||bl,Za=yt?Qt(yt):function(e){return os(e)&&kr(e)==v};function Ya(e){if(!os(e))return!1;var t=kr(e);return t==y||"[object DOMException]"==t||"string"==typeof e.message&&"string"==typeof e.name&&!ss(e)}function es(e){if(!rs(e))return!1;var t=kr(e);return t==b||t==_||"[object AsyncFunction]"==t||"[object Proxy]"==t}function ts(e){return"number"==typeof e&&e==vs(e)}function ns(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=u}function rs(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}function os(e){return null!=e&&"object"==typeof e}var is=bt?Qt(bt):function(e){return os(e)&&gi(e)==w};function as(e){return"number"==typeof e||os(e)&&kr(e)==S}function ss(e){if(!os(e)||kr(e)!=x)return!1;var t=Ke(e);if(null===t)return!0;var n=ze.call(t,"constructor")&&t.constructor;return"function"==typeof n&&n instanceof n&&Re.call(n)==Ne}var ls=_t?Qt(_t):function(e){return os(e)&&kr(e)==k},cs=wt?Qt(wt):function(e){return os(e)&&gi(e)==E};function us(e){return"string"==typeof e||!Ka(e)&&os(e)&&kr(e)==A}function fs(e){return"symbol"==typeof e||os(e)&&kr(e)==T}var ps=St?Qt(St):function(e){return os(e)&&ns(e.length)&&!!ot[kr(e)]},ds=Ko(Dr),hs=Ko((function(e,t){return e<=t}));function gs(e){if(!e)return[];if(Qa(e))return us(e)?un(e):Io(e);if(at&&e[at])return function(e){for(var t,n=[];!(t=e.next()).done;)n.push(t.value);return n}(e[at]());var t=gi(e);return(t==w?on:t==E?ln:Vs)(e)}function ms(e){return e?(e=bs(e))===c||e===-1/0?17976931348623157e292*(e<0?-1:1):e==e?e:0:0===e?e:0}function vs(e){var t=ms(e),n=t%1;return t==t?n?t-n:t:0}function ys(e){return e?sr(vs(e),0,p):0}function bs(e){if("number"==typeof e)return e;if(fs(e))return f;if(rs(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=rs(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=Gt(e);var n=de.test(e);return n||ge.test(e)?lt(e.slice(2),n?2:8):pe.test(e)?f:+e}function _s(e){return jo(e,Rs(e))}function ws(e){return null==e?"":co(e)}var Ss=Mo((function(e,t){if(Ci(t)||Qa(t))jo(t,Ps(t),e);else for(var n in t)ze.call(t,n)&&tr(e,n,t[n])})),xs=Mo((function(e,t){jo(t,Rs(t),e)})),Cs=Mo((function(e,t,n,r){jo(t,Rs(t),e,r)})),ks=Mo((function(e,t,n,r){jo(t,Ps(t),e,r)})),Es=ri(ar),As=Jr((function(t,n){t=Ae(t);var r=-1,o=n.length,i=o>2?n[2]:e;for(i&&_i(n[0],n[1],i)&&(o=1);++r<o;)for(var a=n[r],s=Rs(a),l=-1,c=s.length;++l<c;){var u=s[l],f=t[u];(f===e||Va(f,Me[u])&&!ze.call(t,u))&&(t[u]=a[u])}return t})),Ts=Jr((function(t){return t.push(e,ei),xt($s,e,t)}));function Os(t,n,r){var o=null==t?e:xr(t,n);return o===e?r:o}function Is(e,t){return null!=e&&mi(e,t,Tr)}var js=Ho((function(e,t,n){null!=t&&"function"!=typeof t.toString&&(t=Be.call(t)),e[t]=n}),rl(al)),Ls=Ho((function(e,t,n){null!=t&&"function"!=typeof t.toString&&(t=Be.call(t)),ze.call(e,t)?e[t].push(n):e[t]=[n]}),ci),Ms=Jr(Ir);function Ps(e){return Qa(e)?Jn(e):zr(e)}function Rs(e){return Qa(e)?Jn(e,!0):$r(e)}var zs=Mo((function(e,t,n){Hr(e,t,n)})),$s=Mo((function(e,t,n,r){Hr(e,t,n,r)})),Ds=ri((function(e,t){var n={};if(null==e)return n;var r=!1;t=jt(t,(function(t){return t=_o(t,e),r||(r=t.length>1),t})),jo(e,ii(e),n),r&&(n=lr(n,7,ti));for(var o=t.length;o--;)fo(n,t[o]);return n})),Bs=ri((function(e,t){return null==e?{}:function(e,t){return Wr(e,t,(function(t,n){return Is(e,n)}))}(e,t)}));function Ns(e,t){if(null==e)return{};var n=jt(ii(e),(function(e){return[e]}));return t=ci(t),Wr(e,n,(function(e,n){return t(e,n[0])}))}var Fs=Xo(Ps),Hs=Xo(Rs);function Vs(e){return null==e?[]:Jt(e,Ps(e))}var Us=$o((function(e,t,n){return t=t.toLowerCase(),e+(n?Ws(t):t)}));function Ws(e){return Ys(ws(e).toLowerCase())}function qs(e){return(e=ws(e))&&e.replace(ve,en).replace(Xe,"")}var Ks=$o((function(e,t,n){return e+(n?"-":"")+t.toLowerCase()})),Gs=$o((function(e,t,n){return e+(n?" ":"")+t.toLowerCase()})),Qs=zo("toLowerCase"),Js=$o((function(e,t,n){return e+(n?"_":"")+t.toLowerCase()})),Xs=$o((function(e,t,n){return e+(n?" ":"")+Ys(t)})),Zs=$o((function(e,t,n){return e+(n?" ":"")+t.toUpperCase()})),Ys=zo("toUpperCase");function el(t,n,r){return t=ws(t),(n=r?e:n)===e?function(e){return tt.test(e)}(t)?function(e){return e.match(Ye)||[]}(t):function(e){return e.match(se)||[]}(t):t.match(n)||[]}var tl=Jr((function(t,n){try{return xt(t,e,n)}catch($v){return Ya($v)?$v:new Ce($v)}})),nl=ri((function(e,t){return kt(t,(function(t){t=Ni(t),ir(e,t,La(e[t],e))})),e}));function rl(e){return function(){return e}}var ol=No(),il=No(!0);function al(e){return e}function sl(e){return Rr("function"==typeof e?e:lr(e,1))}var ll=Jr((function(e,t){return function(n){return Ir(n,e,t)}})),cl=Jr((function(e,t){return function(n){return Ir(e,n,t)}}));function ul(e,t,n){var r=Ps(t),o=Sr(t,r);null!=n||rs(t)&&(o.length||!r.length)||(n=t,t=e,e=this,o=Sr(t,Ps(t)));var i=!(rs(n)&&"chain"in n&&!n.chain),a=es(e);return kt(o,(function(n){var r=t[n];e[n]=r,a&&(e.prototype[n]=function(){var t=this.__chain__;if(i||t){var n=e(this.__wrapped__);return(n.__actions__=Io(this.__actions__)).push({func:r,args:arguments,thisArg:e}),n.__chain__=t,n}return r.apply(e,Lt([this.value()],arguments))})})),e}function fl(){}var pl=Uo(jt),dl=Uo(At),hl=Uo(Rt);function gl(e){return wi(e)?Vt(Ni(e)):function(e){return function(t){return xr(t,e)}}(e)}var ml=qo(),vl=qo(!0);function yl(){return[]}function bl(){return!1}var _l,wl=Vo((function(e,t){return e+t}),0),Sl=Qo("ceil"),xl=Vo((function(e,t){return e/t}),1),Cl=Qo("floor"),kl=Vo((function(e,t){return e*t}),1),El=Qo("round"),Al=Vo((function(e,t){return e-t}),0);return Nn.after=function(e,n){if("function"!=typeof n)throw new Ie(t);return e=vs(e),function(){if(--e<1)return n.apply(this,arguments)}},Nn.ary=Ia,Nn.assign=Ss,Nn.assignIn=xs,Nn.assignInWith=Cs,Nn.assignWith=ks,Nn.at=Es,Nn.before=ja,Nn.bind=La,Nn.bindAll=nl,Nn.bindKey=Ma,Nn.castArray=function(){if(!arguments.length)return[];var e=arguments[0];return Ka(e)?e:[e]},Nn.chain=ga,Nn.chunk=function(t,n,r){n=(r?_i(t,n,r):n===e)?1:yn(vs(n),0);var o=null==t?0:t.length;if(!o||n<1)return[];for(var i=0,a=0,s=Se(mt(o/n));i<o;)s[a++]=ro(t,i,i+=n);return s},Nn.compact=function(e){for(var t=-1,n=null==e?0:e.length,r=0,o=[];++t<n;){var i=e[t];i&&(o[r++]=i)}return o},Nn.concat=function(){var e=arguments.length;if(!e)return[];for(var t=Se(e-1),n=arguments[0],r=e;r--;)t[r-1]=arguments[r];return Lt(Ka(n)?Io(n):[n],vr(t,1))},Nn.cond=function(e){var n=null==e?0:e.length,r=ci();return e=n?jt(e,(function(e){if("function"!=typeof e[1])throw new Ie(t);return[r(e[0]),e[1]]})):[],Jr((function(t){for(var r=-1;++r<n;){var o=e[r];if(xt(o[0],this,t))return xt(o[1],this,t)}}))},Nn.conforms=function(e){return function(e){var t=Ps(e);return function(n){return cr(n,e,t)}}(lr(e,1))},Nn.constant=rl,Nn.countBy=ya,Nn.create=function(e,t){var n=Fn(e);return null==t?n:or(n,t)},Nn.curry=function t(n,r,o){var i=Zo(n,8,e,e,e,e,e,r=o?e:r);return i.placeholder=t.placeholder,i},Nn.curryRight=function t(n,r,i){var a=Zo(n,o,e,e,e,e,e,r=i?e:r);return a.placeholder=t.placeholder,a},Nn.debounce=Pa,Nn.defaults=As,Nn.defaultsDeep=Ts,Nn.defer=Ra,Nn.delay=za,Nn.difference=Vi,Nn.differenceBy=Ui,Nn.differenceWith=Wi,Nn.drop=function(t,n,r){var o=null==t?0:t.length;return o?ro(t,(n=r||n===e?1:vs(n))<0?0:n,o):[]},Nn.dropRight=function(t,n,r){var o=null==t?0:t.length;return o?ro(t,0,(n=o-(n=r||n===e?1:vs(n)))<0?0:n):[]},Nn.dropRightWhile=function(e,t){return e&&e.length?ho(e,ci(t,3),!0,!0):[]},Nn.dropWhile=function(e,t){return e&&e.length?ho(e,ci(t,3),!0):[]},Nn.fill=function(t,n,r,o){var i=null==t?0:t.length;return i?(r&&"number"!=typeof r&&_i(t,n,r)&&(r=0,o=i),function(t,n,r,o){var i=t.length;for((r=vs(r))<0&&(r=-r>i?0:i+r),(o=o===e||o>i?i:vs(o))<0&&(o+=i),o=r>o?0:ys(o);r<o;)t[r++]=n;return t}(t,n,r,o)):[]},Nn.filter=function(e,t){return(Ka(e)?Tt:mr)(e,ci(t,3))},Nn.flatMap=function(e,t){return vr(Ea(e,t),1)},Nn.flatMapDeep=function(e,t){return vr(Ea(e,t),c)},Nn.flatMapDepth=function(t,n,r){return r=r===e?1:vs(r),vr(Ea(t,n),r)},Nn.flatten=Gi,Nn.flattenDeep=function(e){return null!=e&&e.length?vr(e,c):[]},Nn.flattenDepth=function(t,n){return null!=t&&t.length?vr(t,n=n===e?1:vs(n)):[]},Nn.flip=function(e){return Zo(e,512)},Nn.flow=ol,Nn.flowRight=il,Nn.fromPairs=function(e){for(var t=-1,n=null==e?0:e.length,r={};++t<n;){var o=e[t];r[o[0]]=o[1]}return r},Nn.functions=function(e){return null==e?[]:Sr(e,Ps(e))},Nn.functionsIn=function(e){return null==e?[]:Sr(e,Rs(e))},Nn.groupBy=xa,Nn.initial=function(e){return null!=e&&e.length?ro(e,0,-1):[]},Nn.intersection=Ji,Nn.intersectionBy=Xi,Nn.intersectionWith=Zi,Nn.invert=js,Nn.invertBy=Ls,Nn.invokeMap=Ca,Nn.iteratee=sl,Nn.keyBy=ka,Nn.keys=Ps,Nn.keysIn=Rs,Nn.map=Ea,Nn.mapKeys=function(e,t){var n={};return t=ci(t,3),_r(e,(function(e,r,o){ir(n,t(e,r,o),e)})),n},Nn.mapValues=function(e,t){var n={};return t=ci(t,3),_r(e,(function(e,r,o){ir(n,r,t(e,r,o))})),n},Nn.matches=function(e){return Nr(lr(e,1))},Nn.matchesProperty=function(e,t){return Fr(e,lr(t,1))},Nn.memoize=$a,Nn.merge=zs,Nn.mergeWith=$s,Nn.method=ll,Nn.methodOf=cl,Nn.mixin=ul,Nn.negate=Da,Nn.nthArg=function(e){return e=vs(e),Jr((function(t){return Vr(t,e)}))},Nn.omit=Ds,Nn.omitBy=function(e,t){return Ns(e,Da(ci(t)))},Nn.once=function(e){return ja(2,e)},Nn.orderBy=function(t,n,r,o){return null==t?[]:(Ka(n)||(n=null==n?[]:[n]),Ka(r=o?e:r)||(r=null==r?[]:[r]),Ur(t,n,r))},Nn.over=pl,Nn.overArgs=Ba,Nn.overEvery=dl,Nn.overSome=hl,Nn.partial=Na,Nn.partialRight=Fa,Nn.partition=Aa,Nn.pick=Bs,Nn.pickBy=Ns,Nn.property=gl,Nn.propertyOf=function(t){return function(n){return null==t?e:xr(t,n)}},Nn.pull=ea,Nn.pullAll=ta,Nn.pullAllBy=function(e,t,n){return e&&e.length&&t&&t.length?qr(e,t,ci(n,2)):e},Nn.pullAllWith=function(t,n,r){return t&&t.length&&n&&n.length?qr(t,n,e,r):t},Nn.pullAt=na,Nn.range=ml,Nn.rangeRight=vl,Nn.rearg=Ha,Nn.reject=function(e,t){return(Ka(e)?Tt:mr)(e,Da(ci(t,3)))},Nn.remove=function(e,t){var n=[];if(!e||!e.length)return n;var r=-1,o=[],i=e.length;for(t=ci(t,3);++r<i;){var a=e[r];t(a,r,e)&&(n.push(a),o.push(r))}return Kr(e,o),n},Nn.rest=function(n,r){if("function"!=typeof n)throw new Ie(t);return Jr(n,r=r===e?r:vs(r))},Nn.reverse=ra,Nn.sampleSize=function(t,n,r){return n=(r?_i(t,n,r):n===e)?1:vs(n),(Ka(t)?Zn:Zr)(t,n)},Nn.set=function(e,t,n){return null==e?e:Yr(e,t,n)},Nn.setWith=function(t,n,r,o){return o="function"==typeof o?o:e,null==t?t:Yr(t,n,r,o)},Nn.shuffle=function(e){return(Ka(e)?Yn:no)(e)},Nn.slice=function(t,n,r){var o=null==t?0:t.length;return o?(r&&"number"!=typeof r&&_i(t,n,r)?(n=0,r=o):(n=null==n?0:vs(n),r=r===e?o:vs(r)),ro(t,n,r)):[]},Nn.sortBy=Ta,Nn.sortedUniq=function(e){return e&&e.length?so(e):[]},Nn.sortedUniqBy=function(e,t){return e&&e.length?so(e,ci(t,2)):[]},Nn.split=function(t,n,r){return r&&"number"!=typeof r&&_i(t,n,r)&&(n=r=e),(r=r===e?p:r>>>0)?(t=ws(t))&&("string"==typeof n||null!=n&&!ls(n))&&!(n=co(n))&&rn(t)?So(un(t),0,r):t.split(n,r):[]},Nn.spread=function(e,n){if("function"!=typeof e)throw new Ie(t);return n=null==n?0:yn(vs(n),0),Jr((function(t){var r=t[n],o=So(t,0,n);return r&&Lt(o,r),xt(e,this,o)}))},Nn.tail=function(e){var t=null==e?0:e.length;return t?ro(e,1,t):[]},Nn.take=function(t,n,r){return t&&t.length?ro(t,0,(n=r||n===e?1:vs(n))<0?0:n):[]},Nn.takeRight=function(t,n,r){var o=null==t?0:t.length;return o?ro(t,(n=o-(n=r||n===e?1:vs(n)))<0?0:n,o):[]},Nn.takeRightWhile=function(e,t){return e&&e.length?ho(e,ci(t,3),!1,!0):[]},Nn.takeWhile=function(e,t){return e&&e.length?ho(e,ci(t,3)):[]},Nn.tap=function(e,t){return t(e),e},Nn.throttle=function(e,n,r){var o=!0,i=!0;if("function"!=typeof e)throw new Ie(t);return rs(r)&&(o="leading"in r?!!r.leading:o,i="trailing"in r?!!r.trailing:i),Pa(e,n,{leading:o,maxWait:n,trailing:i})},Nn.thru=ma,Nn.toArray=gs,Nn.toPairs=Fs,Nn.toPairsIn=Hs,Nn.toPath=function(e){return Ka(e)?jt(e,Ni):fs(e)?[e]:Io(Bi(ws(e)))},Nn.toPlainObject=_s,Nn.transform=function(e,t,n){var r=Ka(e),o=r||Xa(e)||ps(e);if(t=ci(t,4),null==n){var i=e&&e.constructor;n=o?r?new i:[]:rs(e)&&es(i)?Fn(Ke(e)):{}}return(o?kt:_r)(e,(function(e,r,o){return t(n,e,r,o)})),n},Nn.unary=function(e){return Ia(e,1)},Nn.union=oa,Nn.unionBy=ia,Nn.unionWith=aa,Nn.uniq=function(e){return e&&e.length?uo(e):[]},Nn.uniqBy=function(e,t){return e&&e.length?uo(e,ci(t,2)):[]},Nn.uniqWith=function(t,n){return n="function"==typeof n?n:e,t&&t.length?uo(t,e,n):[]},Nn.unset=function(e,t){return null==e||fo(e,t)},Nn.unzip=sa,Nn.unzipWith=la,Nn.update=function(e,t,n){return null==e?e:po(e,t,bo(n))},Nn.updateWith=function(t,n,r,o){return o="function"==typeof o?o:e,null==t?t:po(t,n,bo(r),o)},Nn.values=Vs,Nn.valuesIn=function(e){return null==e?[]:Jt(e,Rs(e))},Nn.without=ca,Nn.words=el,Nn.wrap=function(e,t){return Na(bo(t),e)},Nn.xor=ua,Nn.xorBy=fa,Nn.xorWith=pa,Nn.zip=da,Nn.zipObject=function(e,t){return vo(e||[],t||[],tr)},Nn.zipObjectDeep=function(e,t){return vo(e||[],t||[],Yr)},Nn.zipWith=ha,Nn.entries=Fs,Nn.entriesIn=Hs,Nn.extend=xs,Nn.extendWith=Cs,ul(Nn,Nn),Nn.add=wl,Nn.attempt=tl,Nn.camelCase=Us,Nn.capitalize=Ws,Nn.ceil=Sl,Nn.clamp=function(t,n,r){return r===e&&(r=n,n=e),r!==e&&(r=(r=bs(r))==r?r:0),n!==e&&(n=(n=bs(n))==n?n:0),sr(bs(t),n,r)},Nn.clone=function(e){return lr(e,4)},Nn.cloneDeep=function(e){return lr(e,5)},Nn.cloneDeepWith=function(t,n){return lr(t,5,n="function"==typeof n?n:e)},Nn.cloneWith=function(t,n){return lr(t,4,n="function"==typeof n?n:e)},Nn.conformsTo=function(e,t){return null==t||cr(e,t,Ps(t))},Nn.deburr=qs,Nn.defaultTo=function(e,t){return null==e||e!=e?t:e},Nn.divide=xl,Nn.endsWith=function(t,n,r){t=ws(t),n=co(n);var o=t.length,i=r=r===e?o:sr(vs(r),0,o);return(r-=n.length)>=0&&t.slice(r,i)==n},Nn.eq=Va,Nn.escape=function(e){return(e=ws(e))&&K.test(e)?e.replace(W,tn):e},Nn.escapeRegExp=function(e){return(e=ws(e))&&te.test(e)?e.replace(ee,"\\$&"):e},Nn.every=function(t,n,r){var o=Ka(t)?At:hr;return r&&_i(t,n,r)&&(n=e),o(t,ci(n,3))},Nn.find=ba,Nn.findIndex=qi,Nn.findKey=function(e,t){return $t(e,ci(t,3),_r)},Nn.findLast=_a,Nn.findLastIndex=Ki,Nn.findLastKey=function(e,t){return $t(e,ci(t,3),wr)},Nn.floor=Cl,Nn.forEach=wa,Nn.forEachRight=Sa,Nn.forIn=function(e,t){return null==e?e:yr(e,ci(t,3),Rs)},Nn.forInRight=function(e,t){return null==e?e:br(e,ci(t,3),Rs)},Nn.forOwn=function(e,t){return e&&_r(e,ci(t,3))},Nn.forOwnRight=function(e,t){return e&&wr(e,ci(t,3))},Nn.get=Os,Nn.gt=Ua,Nn.gte=Wa,Nn.has=function(e,t){return null!=e&&mi(e,t,Ar)},Nn.hasIn=Is,Nn.head=Qi,Nn.identity=al,Nn.includes=function(e,t,n,r){e=Qa(e)?e:Vs(e),n=n&&!r?vs(n):0;var o=e.length;return n<0&&(n=yn(o+n,0)),us(e)?n<=o&&e.indexOf(t,n)>-1:!!o&&Bt(e,t,n)>-1},Nn.indexOf=function(e,t,n){var r=null==e?0:e.length;if(!r)return-1;var o=null==n?0:vs(n);return o<0&&(o=yn(r+o,0)),Bt(e,t,o)},Nn.inRange=function(t,n,r){return n=ms(n),r===e?(r=n,n=0):r=ms(r),function(e,t,n){return e>=bn(t,n)&&e<yn(t,n)}(t=bs(t),n,r)},Nn.invoke=Ms,Nn.isArguments=qa,Nn.isArray=Ka,Nn.isArrayBuffer=Ga,Nn.isArrayLike=Qa,Nn.isArrayLikeObject=Ja,Nn.isBoolean=function(e){return!0===e||!1===e||os(e)&&kr(e)==m},Nn.isBuffer=Xa,Nn.isDate=Za,Nn.isElement=function(e){return os(e)&&1===e.nodeType&&!ss(e)},Nn.isEmpty=function(e){if(null==e)return!0;if(Qa(e)&&(Ka(e)||"string"==typeof e||"function"==typeof e.splice||Xa(e)||ps(e)||qa(e)))return!e.length;var t=gi(e);if(t==w||t==E)return!e.size;if(Ci(e))return!zr(e).length;for(var n in e)if(ze.call(e,n))return!1;return!0},Nn.isEqual=function(e,t){return Lr(e,t)},Nn.isEqualWith=function(t,n,r){var o=(r="function"==typeof r?r:e)?r(t,n):e;return o===e?Lr(t,n,e,r):!!o},Nn.isError=Ya,Nn.isFinite=function(e){return"number"==typeof e&&gn(e)},Nn.isFunction=es,Nn.isInteger=ts,Nn.isLength=ns,Nn.isMap=is,Nn.isMatch=function(e,t){return e===t||Mr(e,t,fi(t))},Nn.isMatchWith=function(t,n,r){return r="function"==typeof r?r:e,Mr(t,n,fi(n),r)},Nn.isNaN=function(e){return as(e)&&e!=+e},Nn.isNative=function(e){if(xi(e))throw new Ce("Unsupported core-js use. Try https://npms.io/search?q=ponyfill.");return Pr(e)},Nn.isNil=function(e){return null==e},Nn.isNull=function(e){return null===e},Nn.isNumber=as,Nn.isObject=rs,Nn.isObjectLike=os,Nn.isPlainObject=ss,Nn.isRegExp=ls,Nn.isSafeInteger=function(e){return ts(e)&&e>=-9007199254740991&&e<=u},Nn.isSet=cs,Nn.isString=us,Nn.isSymbol=fs,Nn.isTypedArray=ps,Nn.isUndefined=function(t){return t===e},Nn.isWeakMap=function(e){return os(e)&&gi(e)==O},Nn.isWeakSet=function(e){return os(e)&&"[object WeakSet]"==kr(e)},Nn.join=function(e,t){return null==e?"":mn.call(e,t)},Nn.kebabCase=Ks,Nn.last=Yi,Nn.lastIndexOf=function(t,n,r){var o=null==t?0:t.length;if(!o)return-1;var i=o;return r!==e&&(i=(i=vs(r))<0?yn(o+i,0):bn(i,o-1)),n==n?function(e,t,n){for(var r=n+1;r--;)if(e[r]===t)return r;return r}(t,n,i):Dt(t,Ft,i,!0)},Nn.lowerCase=Gs,Nn.lowerFirst=Qs,Nn.lt=ds,Nn.lte=hs,Nn.max=function(t){return t&&t.length?gr(t,al,Er):e},Nn.maxBy=function(t,n){return t&&t.length?gr(t,ci(n,2),Er):e},Nn.mean=function(e){return Ht(e,al)},Nn.meanBy=function(e,t){return Ht(e,ci(t,2))},Nn.min=function(t){return t&&t.length?gr(t,al,Dr):e},Nn.minBy=function(t,n){return t&&t.length?gr(t,ci(n,2),Dr):e},Nn.stubArray=yl,Nn.stubFalse=bl,Nn.stubObject=function(){return{}},Nn.stubString=function(){return""},Nn.stubTrue=function(){return!0},Nn.multiply=kl,Nn.nth=function(t,n){return t&&t.length?Vr(t,vs(n)):e},Nn.noConflict=function(){return ft._===this&&(ft._=Fe),this},Nn.noop=fl,Nn.now=Oa,Nn.pad=function(e,t,n){e=ws(e);var r=(t=vs(t))?cn(e):0;if(!t||r>=t)return e;var o=(t-r)/2;return Wo(zt(o),n)+e+Wo(mt(o),n)},Nn.padEnd=function(e,t,n){e=ws(e);var r=(t=vs(t))?cn(e):0;return t&&r<t?e+Wo(t-r,n):e},Nn.padStart=function(e,t,n){e=ws(e);var r=(t=vs(t))?cn(e):0;return t&&r<t?Wo(t-r,n)+e:e},Nn.parseInt=function(e,t,n){return n||null==t?t=0:t&&(t=+t),wn(ws(e).replace(ne,""),t||0)},Nn.random=function(t,n,r){if(r&&"boolean"!=typeof r&&_i(t,n,r)&&(n=r=e),r===e&&("boolean"==typeof n?(r=n,n=e):"boolean"==typeof t&&(r=t,t=e)),t===e&&n===e?(t=0,n=1):(t=ms(t),n===e?(n=t,t=0):n=ms(n)),t>n){var o=t;t=n,n=o}if(r||t%1||n%1){var i=Sn();return bn(t+i*(n-t+st("1e-"+((i+"").length-1))),n)}return Gr(t,n)},Nn.reduce=function(e,t,n){var r=Ka(e)?Mt:Wt,o=arguments.length<3;return r(e,ci(t,4),n,o,pr)},Nn.reduceRight=function(e,t,n){var r=Ka(e)?Pt:Wt,o=arguments.length<3;return r(e,ci(t,4),n,o,dr)},Nn.repeat=function(t,n,r){return n=(r?_i(t,n,r):n===e)?1:vs(n),Qr(ws(t),n)},Nn.replace=function(){var e=arguments,t=ws(e[0]);return e.length<3?t:t.replace(e[1],e[2])},Nn.result=function(t,n,r){var o=-1,i=(n=_o(n,t)).length;for(i||(i=1,t=e);++o<i;){var a=null==t?e:t[Ni(n[o])];a===e&&(o=i,a=r),t=es(a)?a.call(t):a}return t},Nn.round=El,Nn.runInContext=re,Nn.sample=function(e){return(Ka(e)?Xn:Xr)(e)},Nn.size=function(e){if(null==e)return 0;if(Qa(e))return us(e)?cn(e):e.length;var t=gi(e);return t==w||t==E?e.size:zr(e).length},Nn.snakeCase=Js,Nn.some=function(t,n,r){var o=Ka(t)?Rt:oo;return r&&_i(t,n,r)&&(n=e),o(t,ci(n,3))},Nn.sortedIndex=function(e,t){return io(e,t)},Nn.sortedIndexBy=function(e,t,n){return ao(e,t,ci(n,2))},Nn.sortedIndexOf=function(e,t){var n=null==e?0:e.length;if(n){var r=io(e,t);if(r<n&&Va(e[r],t))return r}return-1},Nn.sortedLastIndex=function(e,t){return io(e,t,!0)},Nn.sortedLastIndexBy=function(e,t,n){return ao(e,t,ci(n,2),!0)},Nn.sortedLastIndexOf=function(e,t){if(null!=e&&e.length){var n=io(e,t,!0)-1;if(Va(e[n],t))return n}return-1},Nn.startCase=Xs,Nn.startsWith=function(e,t,n){return e=ws(e),n=null==n?0:sr(vs(n),0,e.length),t=co(t),e.slice(n,n+t.length)==t},Nn.subtract=Al,Nn.sum=function(e){return e&&e.length?qt(e,al):0},Nn.sumBy=function(e,t){return e&&e.length?qt(e,ci(t,2)):0},Nn.template=function(t,n,r){var o=Nn.templateSettings;r&&_i(t,n,r)&&(n=e),t=ws(t),n=Cs({},n,o,Yo);var i,a,s=Cs({},n.imports,o.imports,Yo),l=Ps(s),c=Jt(s,l),u=0,f=n.interpolate||ye,p="__p += '",d=Te((n.escape||ye).source+"|"+f.source+"|"+(f===J?ue:ye).source+"|"+(n.evaluate||ye).source+"|$","g"),h="//# sourceURL="+(ze.call(n,"sourceURL")?(n.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++rt+"]")+"\n";t.replace(d,(function(e,n,r,o,s,l){return r||(r=o),p+=t.slice(u,l).replace(be,nn),n&&(i=!0,p+="' +\n__e("+n+") +\n'"),s&&(a=!0,p+="';\n"+s+";\n__p += '"),r&&(p+="' +\n((__t = ("+r+")) == null ? '' : __t) +\n'"),u=l+e.length,e})),p+="';\n";var g=ze.call(n,"variable")&&n.variable;if(g){if(le.test(g))throw new Ce("Invalid `variable` option passed into `_.template`")}else p="with (obj) {\n"+p+"\n}\n";p=(a?p.replace(F,""):p).replace(H,"$1").replace(V,"$1;"),p="function("+(g||"obj")+") {\n"+(g?"":"obj || (obj = {});\n")+"var __t, __p = ''"+(i?", __e = _.escape":"")+(a?", __j = Array.prototype.join;\nfunction print() { __p += __j.call(arguments, '') }\n":";\n")+p+"return __p\n}";var m=tl((function(){return ke(l,h+"return "+p).apply(e,c)}));if(m.source=p,Ya(m))throw m;return m},Nn.times=function(e,t){if((e=vs(e))<1||e>u)return[];var n=p,r=bn(e,p);t=ci(t),e-=p;for(var o=Kt(r,t);++n<e;)t(n);return o},Nn.toFinite=ms,Nn.toInteger=vs,Nn.toLength=ys,Nn.toLower=function(e){return ws(e).toLowerCase()},Nn.toNumber=bs,Nn.toSafeInteger=function(e){return e?sr(vs(e),-9007199254740991,u):0===e?e:0},Nn.toString=ws,Nn.toUpper=function(e){return ws(e).toUpperCase()},Nn.trim=function(t,n,r){if((t=ws(t))&&(r||n===e))return Gt(t);if(!t||!(n=co(n)))return t;var o=un(t),i=un(n);return So(o,Zt(o,i),Yt(o,i)+1).join("")},Nn.trimEnd=function(t,n,r){if((t=ws(t))&&(r||n===e))return t.slice(0,fn(t)+1);if(!t||!(n=co(n)))return t;var o=un(t);return So(o,0,Yt(o,un(n))+1).join("")},Nn.trimStart=function(t,n,r){if((t=ws(t))&&(r||n===e))return t.replace(ne,"");if(!t||!(n=co(n)))return t;var o=un(t);return So(o,Zt(o,un(n))).join("")},Nn.truncate=function(t,n){var r=30,o="...";if(rs(n)){var i="separator"in n?n.separator:i;r="length"in n?vs(n.length):r,o="omission"in n?co(n.omission):o}var a=(t=ws(t)).length;if(rn(t)){var s=un(t);a=s.length}if(r>=a)return t;var l=r-cn(o);if(l<1)return o;var c=s?So(s,0,l).join(""):t.slice(0,l);if(i===e)return c+o;if(s&&(l+=c.length-l),ls(i)){if(t.slice(l).search(i)){var u,f=c;for(i.global||(i=Te(i.source,ws(fe.exec(i))+"g")),i.lastIndex=0;u=i.exec(f);)var p=u.index;c=c.slice(0,p===e?l:p)}}else if(t.indexOf(co(i),l)!=l){var d=c.lastIndexOf(i);d>-1&&(c=c.slice(0,d))}return c+o},Nn.unescape=function(e){return(e=ws(e))&&q.test(e)?e.replace(U,pn):e},Nn.uniqueId=function(e){var t=++$e;return ws(e)+t},Nn.upperCase=Zs,Nn.upperFirst=Ys,Nn.each=wa,Nn.eachRight=Sa,Nn.first=Qi,ul(Nn,(_l={},_r(Nn,(function(e,t){ze.call(Nn.prototype,t)||(_l[t]=e)})),_l),{chain:!1}),Nn.VERSION="4.17.21",kt(["bind","bindKey","curry","curryRight","partial","partialRight"],(function(e){Nn[e].placeholder=Nn})),kt(["drop","take"],(function(t,n){Un.prototype[t]=function(r){r=r===e?1:yn(vs(r),0);var o=this.__filtered__&&!n?new Un(this):this.clone();return o.__filtered__?o.__takeCount__=bn(r,o.__takeCount__):o.__views__.push({size:bn(r,p),type:t+(o.__dir__<0?"Right":"")}),o},Un.prototype[t+"Right"]=function(e){return this.reverse()[t](e).reverse()}})),kt(["filter","map","takeWhile"],(function(e,t){var n=t+1,r=1==n||3==n;Un.prototype[e]=function(e){var t=this.clone();return t.__iteratees__.push({iteratee:ci(e,3),type:n}),t.__filtered__=t.__filtered__||r,t}})),kt(["head","last"],(function(e,t){var n="take"+(t?"Right":"");Un.prototype[e]=function(){return this[n](1).value()[0]}})),kt(["initial","tail"],(function(e,t){var n="drop"+(t?"":"Right");Un.prototype[e]=function(){return this.__filtered__?new Un(this):this[n](1)}})),Un.prototype.compact=function(){return this.filter(al)},Un.prototype.find=function(e){return this.filter(e).head()},Un.prototype.findLast=function(e){return this.reverse().find(e)},Un.prototype.invokeMap=Jr((function(e,t){return"function"==typeof e?new Un(this):this.map((function(n){return Ir(n,e,t)}))})),Un.prototype.reject=function(e){return this.filter(Da(ci(e)))},Un.prototype.slice=function(t,n){t=vs(t);var r=this;return r.__filtered__&&(t>0||n<0)?new Un(r):(t<0?r=r.takeRight(-t):t&&(r=r.drop(t)),n!==e&&(r=(n=vs(n))<0?r.dropRight(-n):r.take(n-t)),r)},Un.prototype.takeRightWhile=function(e){return this.reverse().takeWhile(e).reverse()},Un.prototype.toArray=function(){return this.take(p)},_r(Un.prototype,(function(t,n){var r=/^(?:filter|find|map|reject)|While$/.test(n),o=/^(?:head|last)$/.test(n),i=Nn[o?"take"+("last"==n?"Right":""):n],a=o||/^find/.test(n);i&&(Nn.prototype[n]=function(){var n=this.__wrapped__,s=o?[1]:arguments,l=n instanceof Un,c=s[0],u=l||Ka(n),f=function(e){var t=i.apply(Nn,Lt([e],s));return o&&p?t[0]:t};u&&r&&"function"==typeof c&&1!=c.length&&(l=u=!1);var p=this.__chain__,d=!!this.__actions__.length,h=a&&!p,g=l&&!d;if(!a&&u){n=g?n:new Un(this);var m=t.apply(n,s);return m.__actions__.push({func:ma,args:[f],thisArg:e}),new Vn(m,p)}return h&&g?t.apply(this,s):(m=this.thru(f),h?o?m.value()[0]:m.value():m)})})),kt(["pop","push","shift","sort","splice","unshift"],(function(e){var t=je[e],n=/^(?:push|sort|unshift)$/.test(e)?"tap":"thru",r=/^(?:pop|shift)$/.test(e);Nn.prototype[e]=function(){var e=arguments;if(r&&!this.__chain__){var o=this.value();return t.apply(Ka(o)?o:[],e)}return this[n]((function(n){return t.apply(Ka(n)?n:[],e)}))}})),_r(Un.prototype,(function(e,t){var n=Nn[t];if(n){var r=n.name+"";ze.call(jn,r)||(jn[r]=[]),jn[r].push({name:t,func:n})}})),jn[Fo(e,2).name]=[{name:"wrapper",func:e}],Un.prototype.clone=function(){var e=new Un(this.__wrapped__);return e.__actions__=Io(this.__actions__),e.__dir__=this.__dir__,e.__filtered__=this.__filtered__,e.__iteratees__=Io(this.__iteratees__),e.__takeCount__=this.__takeCount__,e.__views__=Io(this.__views__),e},Un.prototype.reverse=function(){if(this.__filtered__){var e=new Un(this);e.__dir__=-1,e.__filtered__=!0}else(e=this.clone()).__dir__*=-1;return e},Un.prototype.value=function(){var e=this.__wrapped__.value(),t=this.__dir__,n=Ka(e),r=t<0,o=n?e.length:0,i=function(e,t,n){for(var r=-1,o=n.length;++r<o;){var i=n[r],a=i.size;switch(i.type){case"drop":e+=a;break;case"dropRight":t-=a;break;case"take":t=bn(t,e+a);break;case"takeRight":e=yn(e,t-a)}}return{start:e,end:t}}(0,o,this.__views__),a=i.start,s=i.end,l=s-a,c=r?s:a-1,u=this.__iteratees__,f=u.length,p=0,d=bn(l,this.__takeCount__);if(!n||!r&&o==l&&d==l)return go(e,this.__actions__);var h=[];e:for(;l--&&p<d;){for(var g=-1,m=e[c+=t];++g<f;){var v=u[g],y=v.iteratee,b=v.type,_=y(m);if(2==b)m=_;else if(!_){if(1==b)continue e;break e}}h[p++]=m}return h},Nn.prototype.at=va,Nn.prototype.chain=function(){return ga(this)},Nn.prototype.commit=function(){return new Vn(this.value(),this.__chain__)},Nn.prototype.next=function(){this.__values__===e&&(this.__values__=gs(this.value()));var t=this.__index__>=this.__values__.length;return{done:t,value:t?e:this.__values__[this.__index__++]}},Nn.prototype.plant=function(t){for(var n,r=this;r instanceof Hn;){var o=Hi(r);o.__index__=0,o.__values__=e,n?i.__wrapped__=o:n=o;var i=o;r=r.__wrapped__}return i.__wrapped__=t,n},Nn.prototype.reverse=function(){var t=this.__wrapped__;if(t instanceof Un){var n=t;return this.__actions__.length&&(n=new Un(this)),(n=n.reverse()).__actions__.push({func:ma,args:[ra],thisArg:e}),new Vn(n,this.__chain__)}return this.thru(ra)},Nn.prototype.toJSON=Nn.prototype.valueOf=Nn.prototype.value=function(){return go(this.__wrapped__,this.__actions__)},Nn.prototype.first=Nn.prototype.head,at&&(Nn.prototype[at]=function(){return this}),Nn}();dt?((dt.exports=dn)._=dn,pt._=dn):ft._=dn}.call(cu);const gu=hu.exports;var mu=1,vu=2,yu=3,bu=4,_u=5,wu=6,Su=7,xu=8,Cu=9,ku=10,Eu=function(e,t){if("object"==typeof e&&"function"==typeof e.send){var n=this;this.transport=e,window.webChannel=n,this.send=function(e){"string"!=typeof e&&(e=JSON.stringify(e)),n.transport.send(e)},this.transport.onmessage=function(e){var t=e.data;switch("string"==typeof t&&(t=JSON.parse(t)),t.type){case mu:n.handleSignal(t);break;case ku:n.handleResponse(t);break;case vu:n.handlePropertyUpdate(t);break;default:console.error("invalid message received:",e.data)}},this.execCallbacks={},this.execId=0,this.exec=function(e,t){t?(n.execId===Number.MAX_VALUE&&(n.execId=Number.MIN_VALUE),Object.prototype.hasOwnProperty.call(e,"id")?console.error("Cannot exec message with property id: "+JSON.stringify(e)):(e.id=n.execId++,n.execCallbacks[e.id]=t,n.send(e))):n.send(e)},this.objects={},this.handleSignal=function(e){var t=n.objects[e.object];t?t.signalEmitted(e.signal,e.args):console.warn("Unhandled signal: "+e.object+"::"+e.signal)},this.handleResponse=function(e){Object.prototype.hasOwnProperty.call(e,"id")?(n.execCallbacks[e.id](e.data),delete n.execCallbacks[e.id]):console.error("Invalid response message received: ",JSON.stringify(e))},this.handlePropertyUpdate=function(e){for(var t in e.data){var r=e.data[t],o=n.objects[r.object];o?o.propertyUpdate(r.signals,r.properties):console.warn("Unhandled property update: "+r.object+"::"+r.signal)}n.exec({type:bu})},this.debug=function(e){n.send({type:_u,data:e})},n.exec({type:yu},(function(e){for(var r in e)new Au(r,e[r],n);for(const t in n.objects)n.objects[t].unwrapProperties();t&&t(n),n.exec({type:bu})}))}else console.error("The QWebChannel expects a transport object with a send function and onmessage callback property. Given is: transport: "+typeof e+", transport.send: "+typeof e.send)};function Au(e,t,n){this.__id__=e,n.objects[e]=this,this.__objectSignals__={},this.__propertyCache__={};var r=this;function o(e,t){var o=e[0],i=e[1];r[o]={connect:function(e){"function"==typeof e?(r.__objectSignals__[i]=r.__objectSignals__[i]||[],r.__objectSignals__[i].push(e),t||"destroyed"===o||n.exec({type:Su,object:r.__id__,signal:i})):console.error("Bad callback given to connect to signal "+o)},disconnect:function(e){if("function"==typeof e){r.__objectSignals__[i]=r.__objectSignals__[i]||[];var a=r.__objectSignals__[i].indexOf(e);-1!==a?(r.__objectSignals__[i].splice(a,1),t||0!==r.__objectSignals__[i].length||n.exec({type:xu,object:r.__id__,signal:i})):console.error("Cannot find connection of signal "+o+" to "+e.name)}else console.error("Bad callback given to disconnect from signal "+o)}}}function i(e,t){var n=r.__objectSignals__[e];n&&n.forEach((function(e){e.apply(e,t)}))}this.unwrapQObject=function(e){if(e instanceof Array){for(var t=new Array(e.length),o=0;o<e.length;++o)t[o]=r.unwrapQObject(e[o]);return t}if(!e||!e["__QObject*__"]||void 0===e.id)return e;var i=e.id;if(n.objects[i])return n.objects[i];if(e.data){var a=new Au(i,e.data,n);return a.destroyed.connect((function(){if(n.objects[i]===a){delete n.objects[i];var e=[];for(var t in a)e.push(t);for(var r in e)delete a[e[r]]}})),a.unwrapProperties(),a}console.error("Cannot unwrap unknown QObject "+i+" without data.")},this.unwrapProperties=function(){for(var e in r.__propertyCache__)r.__propertyCache__[e]=r.unwrapQObject(r.__propertyCache__[e])},this.propertyUpdate=function(e,t){for(var n in t){var o=t[n];r.__propertyCache__[n]=o}for(var a in e)i(a,e[a])},this.signalEmitted=function(e,t){i(e,this.unwrapQObject(t))},t.methods.forEach((function(e){var t=e[0],o=e[1];r[t]=function(){for(var e,t=[],i=0;i<arguments.length;++i){var a=arguments[i];"function"==typeof a?e=a:a instanceof Au&&void 0!==n.objects[a.__id__]?t.push({id:a.__id__}):t.push(a)}n.exec({type:wu,object:r.__id__,method:o,args:t},(function(t){if(void 0!==t){var n=r.unwrapQObject(t);e&&e(n)}}))}})),t.properties.forEach((function(e){var t=e[0],i=e[1],a=e[2];r.__propertyCache__[t]=e[3],a&&(1===a[0]&&(a[0]=i+"Changed"),o(a,!0)),Object.defineProperty(r,i,{configurable:!0,get:function(){var e=r.__propertyCache__[t];return void 0===e&&console.warn('Undefined value in property cache for property "'+i+'" in object '+r.__id__),e},set:function(e){if(void 0!==e){r.__propertyCache__[t]=e;var o=e;o instanceof Au&&void 0!==n.objects[o.__id__]&&(o={id:o.__id__}),n.exec({type:Cu,object:r.__id__,property:t,value:o})}else console.warn("Property setter for "+i+" called with undefined value!")}})})),t.signals.forEach((function(e){o(e,!1)}));for(const a in t.enums)r[a]=t.enums[a]}const Tu=function(){logger.log("QT对象：",window.qt),Ou()||(window.qt={webChannelTransport:{send(){var e;e="QWebChannel simulator activated !",logger.log(`%c${e}`,"font-weight: bold;")},onmessage(){}}})},Ou=function(){return navigator.userAgent.includes("QtWebEngine")&&void 0!==window.qt};class Iu{constructor(e=e=>{}){Tu(),this.sendQueue=[],this.eventQueue=[],this.send=({module:e,action:t,strSerial:n,data:r=""})=>new Promise(((o,i)=>{this.sendQueue.push({module:e,action:t,strSerial:n,data:r,promise:{resolve:o,reject:i}})})),this.on=(e,t,n)=>{this.eventQueue.push({module:e,event:t,callback:n})},this.off=(e,t,n)=>{console.log("尚未初始化！")},new Eu(window.qt.webChannelTransport,(t=>{if(!Object.keys(t).includes("objects"))throw new Error("js与qt初始化失败");const n=t.objects;this.send=function(e){return({module:t,action:n,strSerial:r,data:o="",promise:i=null})=>new Promise(((a,s)=>(i&&i.reject&&i.resolve&&(a=i.resolve,s=i.reject),Object.keys(e).includes(t)?Object.keys(e[t]).includes(n)?"function"!=typeof e[t][n]?s(new Error("function"==typeof e[t][n].connect?`[SENDER]: ${n} 不是一个QT信号或者QT方法`:`[SENDER]:  action : ${n} 不是一个QT函数 !`)):void(-1===r?e[t][n](o,a):e[t][n](r,o,a)):s(new Error("[SENDER]: 该action"+n+" 不存在 !")):s(new Error("[SENDER]: 该module"+t+" 不存在 !")))))}(n),this.on=function(e){return(t,n,r)=>{if(!gu.get(e,`${t}.${n}`))throw new Error(`[LISTENER]: ${n} is not a Qt signa!`);if("function"!=typeof e[t][n].connect)throw new Error("[LISTENER]: No Connect Function!");e[t][n].connect(r)}}(n),this.off=function(e){return(t,n,r)=>Object.keys(e).includes(n)?Object.keys(e[n]).includes("disconnect")?"function"!=typeof e[n].disconnect?reject(new Error("[LISTENER]: No Disconnect Function!")):void e[t][n].disconnect(r):reject(new Error(`[LISTENER]: ${n} is not a Qt signa!`)):reject(new Error("[LISTENER]: Unknown event name!"))}(n),this.sendQueue.length>0&&(this.sendQueue.forEach((e=>{this.send({module:e.module,action:e.action,strSerial:e.strSerial,data:e.data,promise:e.promise})})),this.sendQueue=[]),this.eventQueue.length>0&&(this.eventQueue.forEach((e=>{this.on(e.module,e.event,e.callback)})),this.eventQueue=[]),e(n)}))}}const ju={paramsToString:e=>(function e(t){if("[object Array]"===Object.prototype.toString.call(t))t.forEach(((n,r)=>{"number"==typeof n?t[r]=n+"":"object"==typeof n&&e(n)}));else if("[object Object]"===Object.prototype.toString.call(t))for(const n in t)Object.prototype.hasOwnProperty.call(t,n)&&("number"==typeof t[n]?t[n]+="":"object"==typeof t[n]&&e(t[n]))}(e),e),serialId:0,getStrSerialId(){return this.serialId++,this.serialId%10==0&&this.serialId++,this.serialId>9e8&&(this.serialId=1),this.serialId},getStrSerial(e=0){const t=1e8*this.getStrSerialId();return String(parseInt(String(t).substr(0,9))+parseInt(e))},interceptors(e,t){const n=this.sortParamsKey(e.strBody);if(this.paramsToString(e),n){const t=[];n.forEach((n=>{const r={Name:n,Value:encodeURIComponent(e.strBody[n])};t.push(r)})),e.strBody={Argument:t}}return JSON.stringify(e)},sortParamsKey(e){if(!e||"{}"===JSON.stringify(e.strBody))return"";return Object.keys(e).sort(((e,t)=>{e=gu.toString(e),t=gu.toString(t);const n=gu.max([e.length,t.length]);for(let i=0;i<n;i++){const n=(r=e.charAt(i),o=t.charAt(i),r>o?1:r<o?-1:0);if(0!==n)return n}var r,o;return 0}))},getStrLen(e){let t=0;if(!e)return t;for(var n=0;n<e.length;n++){var r=e.charCodeAt(n);t+=r>=0&&r<=128?1:2}return t},formatNum(e,t){let n=""+e;const r=t-n.length;for(var o=0;o<r;o++)n="0"+n;return n},getSubStr(e,t,n){let r=e.indexOf(t);if(-1===parseInt(r))return"";r+=t.length;const o=e.indexOf(n,r);return-1===parseInt(o)?"":e.substring(r,o)}},Lu=e=>(e=gu.merge({time:15e3,timeoutReturn:{overtime:!0}},e),new Promise(((t,n)=>{setTimeout((()=>{t(e.timeoutReturn)}),e.time)})));const Mu={all:Pu=Pu||new Map,on:function(e,t){var n=Pu.get(e);n?n.push(t):Pu.set(e,[t])},off:function(e,t){var n=Pu.get(e);n&&(t?n.splice(n.indexOf(t)>>>0,1):Pu.set(e,[]))},emit:function(e,t){var n=Pu.get(e);n&&n.slice().map((function(e){e(t)})),(n=Pu.get("*"))&&n.slice().map((function(n){n(e,t)}))}};var Pu,Ru=TypeError;const zu=fu(Object.freeze(Object.defineProperty({__proto__:null,default:{}},Symbol.toStringTag,{value:"Module"})));var $u="function"==typeof Map&&Map.prototype,Du=Object.getOwnPropertyDescriptor&&$u?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,Bu=$u&&Du&&"function"==typeof Du.get?Du.get:null,Nu=$u&&Map.prototype.forEach,Fu="function"==typeof Set&&Set.prototype,Hu=Object.getOwnPropertyDescriptor&&Fu?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,Vu=Fu&&Hu&&"function"==typeof Hu.get?Hu.get:null,Uu=Fu&&Set.prototype.forEach,Wu="function"==typeof WeakMap&&WeakMap.prototype?WeakMap.prototype.has:null,qu="function"==typeof WeakSet&&WeakSet.prototype?WeakSet.prototype.has:null,Ku="function"==typeof WeakRef&&WeakRef.prototype?WeakRef.prototype.deref:null,Gu=Boolean.prototype.valueOf,Qu=Object.prototype.toString,Ju=Function.prototype.toString,Xu=String.prototype.match,Zu=String.prototype.slice,Yu=String.prototype.replace,ef=String.prototype.toUpperCase,tf=String.prototype.toLowerCase,nf=RegExp.prototype.test,rf=Array.prototype.concat,of=Array.prototype.join,af=Array.prototype.slice,sf=Math.floor,lf="function"==typeof BigInt?BigInt.prototype.valueOf:null,cf=Object.getOwnPropertySymbols,uf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol.prototype.toString:null,ff="function"==typeof Symbol&&"object"==typeof Symbol.iterator,pf="function"==typeof Symbol&&Symbol.toStringTag&&(typeof Symbol.toStringTag===ff||"symbol")?Symbol.toStringTag:null,df=Object.prototype.propertyIsEnumerable,hf=("function"==typeof Reflect?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(e){return e.__proto__}:null);function gf(e,t){if(e===1/0||e===-1/0||e!=e||e&&e>-1e3&&e<1e3||nf.call(/e/,t))return t;var n=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if("number"==typeof e){var r=e<0?-sf(-e):sf(e);if(r!==e){var o=String(r),i=Zu.call(t,o.length+1);return Yu.call(o,n,"$&_")+"."+Yu.call(Yu.call(i,/([0-9]{3})/g,"$&_"),/_$/,"")}}return Yu.call(t,n,"$&_")}var mf=zu,vf=mf.custom,yf=Af(vf)?vf:null,bf={__proto__:null,double:'"',single:"'"},_f={__proto__:null,double:/(["\\])/g,single:/(['\\])/g},wf=function e(t,n,r,o){var i=n||{};if(Of(i,"quoteStyle")&&!Of(bf,i.quoteStyle))throw new TypeError('option "quoteStyle" must be "single" or "double"');if(Of(i,"maxStringLength")&&("number"==typeof i.maxStringLength?i.maxStringLength<0&&i.maxStringLength!==1/0:null!==i.maxStringLength))throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var a=!Of(i,"customInspect")||i.customInspect;if("boolean"!=typeof a&&"symbol"!==a)throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(Of(i,"indent")&&null!==i.indent&&"\t"!==i.indent&&!(parseInt(i.indent,10)===i.indent&&i.indent>0))throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(Of(i,"numericSeparator")&&"boolean"!=typeof i.numericSeparator)throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');var s=i.numericSeparator;if(void 0===t)return"undefined";if(null===t)return"null";if("boolean"==typeof t)return t?"true":"false";if("string"==typeof t)return Lf(t,i);if("number"==typeof t){if(0===t)return 1/0/t>0?"0":"-0";var l=String(t);return s?gf(t,l):l}if("bigint"==typeof t){var c=String(t)+"n";return s?gf(t,c):c}var u=void 0===i.depth?5:i.depth;if(void 0===r&&(r=0),r>=u&&u>0&&"object"==typeof t)return kf(t)?"[Array]":"[Object]";var f=function(e,t){var n;if("\t"===e.indent)n="\t";else{if(!("number"==typeof e.indent&&e.indent>0))return null;n=of.call(Array(e.indent+1)," ")}return{base:n,prev:of.call(Array(t+1),n)}}(i,r);if(void 0===o)o=[];else if(jf(o,t)>=0)return"[Circular]";function p(t,n,a){if(n&&(o=af.call(o)).push(n),a){var s={depth:i.depth};return Of(i,"quoteStyle")&&(s.quoteStyle=i.quoteStyle),e(t,s,r+1,o)}return e(t,i,r+1,o)}if("function"==typeof t&&!Ef(t)){var d=function(e){if(e.name)return e.name;var t=Xu.call(Ju.call(e),/^function\s*([\w$]+)/);if(t)return t[1];return null}(t),h=Df(t,p);return"[Function"+(d?": "+d:" (anonymous)")+"]"+(h.length>0?" { "+of.call(h,", ")+" }":"")}if(Af(t)){var g=ff?Yu.call(String(t),/^(Symbol\(.*\))_[^)]*$/,"$1"):uf.call(t);return"object"!=typeof t||ff?g:Pf(g)}if(function(e){if(!e||"object"!=typeof e)return!1;if("undefined"!=typeof HTMLElement&&e instanceof HTMLElement)return!0;return"string"==typeof e.nodeName&&"function"==typeof e.getAttribute}(t)){for(var m="<"+tf.call(String(t.nodeName)),v=t.attributes||[],y=0;y<v.length;y++)m+=" "+v[y].name+"="+Sf(xf(v[y].value),"double",i);return m+=">",t.childNodes&&t.childNodes.length&&(m+="..."),m+="</"+tf.call(String(t.nodeName))+">"}if(kf(t)){if(0===t.length)return"[]";var b=Df(t,p);return f&&!function(e){for(var t=0;t<e.length;t++)if(jf(e[t],"\n")>=0)return!1;return!0}(b)?"["+$f(b,f)+"]":"[ "+of.call(b,", ")+" ]"}if(function(e){return"[object Error]"===If(e)&&Cf(e)}(t)){var _=Df(t,p);return"cause"in Error.prototype||!("cause"in t)||df.call(t,"cause")?0===_.length?"["+String(t)+"]":"{ ["+String(t)+"] "+of.call(_,", ")+" }":"{ ["+String(t)+"] "+of.call(rf.call("[cause]: "+p(t.cause),_),", ")+" }"}if("object"==typeof t&&a){if(yf&&"function"==typeof t[yf]&&mf)return mf(t,{depth:u-r});if("symbol"!==a&&"function"==typeof t.inspect)return t.inspect()}if(function(e){if(!Bu||!e||"object"!=typeof e)return!1;try{Bu.call(e);try{Vu.call(e)}catch(m){return!0}return e instanceof Map}catch($v){}return!1}(t)){var w=[];return Nu&&Nu.call(t,(function(e,n){w.push(p(n,t,!0)+" => "+p(e,t))})),zf("Map",Bu.call(t),w,f)}if(function(e){if(!Vu||!e||"object"!=typeof e)return!1;try{Vu.call(e);try{Bu.call(e)}catch(t){return!0}return e instanceof Set}catch($v){}return!1}(t)){var S=[];return Uu&&Uu.call(t,(function(e){S.push(p(e,t))})),zf("Set",Vu.call(t),S,f)}if(function(e){if(!Wu||!e||"object"!=typeof e)return!1;try{Wu.call(e,Wu);try{qu.call(e,qu)}catch(m){return!0}return e instanceof WeakMap}catch($v){}return!1}(t))return Rf("WeakMap");if(function(e){if(!qu||!e||"object"!=typeof e)return!1;try{qu.call(e,qu);try{Wu.call(e,Wu)}catch(m){return!0}return e instanceof WeakSet}catch($v){}return!1}(t))return Rf("WeakSet");if(function(e){if(!Ku||!e||"object"!=typeof e)return!1;try{return Ku.call(e),!0}catch($v){}return!1}(t))return Rf("WeakRef");if(function(e){return"[object Number]"===If(e)&&Cf(e)}(t))return Pf(p(Number(t)));if(function(e){if(!e||"object"!=typeof e||!lf)return!1;try{return lf.call(e),!0}catch($v){}return!1}(t))return Pf(p(lf.call(t)));if(function(e){return"[object Boolean]"===If(e)&&Cf(e)}(t))return Pf(Gu.call(t));if(function(e){return"[object String]"===If(e)&&Cf(e)}(t))return Pf(p(String(t)));if("undefined"!=typeof window&&t===window)return"{ [object Window] }";if("undefined"!=typeof globalThis&&t===globalThis||void 0!==cu&&t===cu)return"{ [object globalThis] }";if(!function(e){return"[object Date]"===If(e)&&Cf(e)}(t)&&!Ef(t)){var x=Df(t,p),C=hf?hf(t)===Object.prototype:t instanceof Object||t.constructor===Object,k=t instanceof Object?"":"null prototype",E=!C&&pf&&Object(t)===t&&pf in t?Zu.call(If(t),8,-1):k?"Object":"",A=(C||"function"!=typeof t.constructor?"":t.constructor.name?t.constructor.name+" ":"")+(E||k?"["+of.call(rf.call([],E||[],k||[]),": ")+"] ":"");return 0===x.length?A+"{}":f?A+"{"+$f(x,f)+"}":A+"{ "+of.call(x,", ")+" }"}return String(t)};function Sf(e,t,n){var r=n.quoteStyle||t,o=bf[r];return o+e+o}function xf(e){return Yu.call(String(e),/"/g,"&quot;")}function Cf(e){return!pf||!("object"==typeof e&&(pf in e||void 0!==e[pf]))}function kf(e){return"[object Array]"===If(e)&&Cf(e)}function Ef(e){return"[object RegExp]"===If(e)&&Cf(e)}function Af(e){if(ff)return e&&"object"==typeof e&&e instanceof Symbol;if("symbol"==typeof e)return!0;if(!e||"object"!=typeof e||!uf)return!1;try{return uf.call(e),!0}catch($v){}return!1}var Tf=Object.prototype.hasOwnProperty||function(e){return e in this};function Of(e,t){return Tf.call(e,t)}function If(e){return Qu.call(e)}function jf(e,t){if(e.indexOf)return e.indexOf(t);for(var n=0,r=e.length;n<r;n++)if(e[n]===t)return n;return-1}function Lf(e,t){if(e.length>t.maxStringLength){var n=e.length-t.maxStringLength,r="... "+n+" more character"+(n>1?"s":"");return Lf(Zu.call(e,0,t.maxStringLength),t)+r}var o=_f[t.quoteStyle||"single"];return o.lastIndex=0,Sf(Yu.call(Yu.call(e,o,"\\$1"),/[\x00-\x1f]/g,Mf),"single",t)}function Mf(e){var t=e.charCodeAt(0),n={8:"b",9:"t",10:"n",12:"f",13:"r"}[t];return n?"\\"+n:"\\x"+(t<16?"0":"")+ef.call(t.toString(16))}function Pf(e){return"Object("+e+")"}function Rf(e){return e+" { ? }"}function zf(e,t,n,r){return e+" ("+t+") {"+(r?$f(n,r):of.call(n,", "))+"}"}function $f(e,t){if(0===e.length)return"";var n="\n"+t.prev+t.base;return n+of.call(e,","+n)+"\n"+t.prev}function Df(e,t){var n=kf(e),r=[];if(n){r.length=e.length;for(var o=0;o<e.length;o++)r[o]=Of(e,o)?t(e[o],e):""}var i,a="function"==typeof cf?cf(e):[];if(ff){i={};for(var s=0;s<a.length;s++)i["$"+a[s]]=a[s]}for(var l in e)Of(e,l)&&(n&&String(Number(l))===l&&l<e.length||ff&&i["$"+l]instanceof Symbol||(nf.call(/[^\w$]/,l)?r.push(t(l,e)+": "+t(e[l],e)):r.push(l+": "+t(e[l],e))));if("function"==typeof cf)for(var c=0;c<a.length;c++)df.call(e,a[c])&&r.push("["+t(a[c])+"]: "+t(e[a[c]],e));return r}var Bf=wf,Nf=Ru,Ff=function(e,t,n){for(var r,o=e;null!=(r=o.next);o=r)if(r.key===t)return o.next=r.next,n||(r.next=e.next,e.next=r),r},Hf=Object,Vf=Error,Uf=EvalError,Wf=RangeError,qf=ReferenceError,Kf=SyntaxError,Gf=URIError,Qf=Math.abs,Jf=Math.floor,Xf=Math.max,Zf=Math.min,Yf=Math.pow,ep=Math.round,tp=Number.isNaN||function(e){return e!=e},np=Object.getOwnPropertyDescriptor;if(np)try{np([],"length")}catch($v){np=null}var rp=np,op=Object.defineProperty||!1;if(op)try{op({},"a",{value:1})}catch($v){op=!1}var ip,ap,sp,lp,cp,up,fp,pp,dp,hp,gp,mp,vp,yp,bp,_p,wp=op;function Sp(){return up?cp:(up=1,cp="undefined"!=typeof Reflect&&Reflect.getPrototypeOf||null)}function xp(){return pp?fp:(pp=1,fp=Hf.getPrototypeOf||null)}function Cp(){if(mp)return gp;mp=1;var e=function(){if(hp)return dp;hp=1;var e=Object.prototype.toString,t=Math.max,n=function(e,t){for(var n=[],r=0;r<e.length;r+=1)n[r]=e[r];for(var o=0;o<t.length;o+=1)n[o+e.length]=t[o];return n};return dp=function(r){var o=this;if("function"!=typeof o||"[object Function]"!==e.apply(o))throw new TypeError("Function.prototype.bind called on incompatible "+o);for(var i,a=function(e){for(var t=[],n=1||0,r=0;n<e.length;n+=1,r+=1)t[r]=e[n];return t}(arguments),s=t(0,o.length-a.length),l=[],c=0;c<s;c++)l[c]="$"+c;if(i=Function("binder","return function ("+function(e,t){for(var n="",r=0;r<e.length;r+=1)n+=e[r],r+1<e.length&&(n+=t);return n}(l,",")+"){ return binder.apply(this,arguments); }")((function(){if(this instanceof i){var e=o.apply(this,n(a,arguments));return Object(e)===e?e:this}return o.apply(r,n(a,arguments))})),o.prototype){var u=function(){};u.prototype=o.prototype,i.prototype=new u,u.prototype=null}return i},dp}();return gp=Function.prototype.bind||e}function kp(){return yp?vp:(yp=1,vp=Function.prototype.call)}function Ep(){return _p?bp:(_p=1,bp=Function.prototype.apply)}var Ap,Tp,Op,Ip,jp,Lp,Mp,Pp="undefined"!=typeof Reflect&&Reflect&&Reflect.apply,Rp=Cp(),zp=Ep(),$p=kp(),Dp=Pp||Rp.call($p,zp),Bp=Cp(),Np=Ru,Fp=kp(),Hp=Dp,Vp=function(e){if(e.length<1||"function"!=typeof e[0])throw new Np("a function is required");return Hp(Bp,Fp,e)};var Up=Hf,Wp=Vf,qp=Uf,Kp=Wf,Gp=qf,Qp=Kf,Jp=Ru,Xp=Gf,Zp=Qf,Yp=Jf,ed=Xf,td=Zf,nd=Yf,rd=ep,od=function(e){return tp(e)||0===e?e:e<0?-1:1},id=Function,ad=function(e){try{return id('"use strict"; return ('+e+").constructor;")()}catch($v){}},sd=rp,ld=wp,cd=function(){throw new Jp},ud=sd?function(){try{return cd}catch(e){try{return sd(arguments,"callee").get}catch(t){return cd}}}():cd,fd=function(){if(lp)return sp;lp=1;var e="undefined"!=typeof Symbol&&Symbol,t=ap?ip:(ap=1,ip=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var e={},t=Symbol("test"),n=Object(t);if("string"==typeof t)return!1;if("[object Symbol]"!==Object.prototype.toString.call(t))return!1;if("[object Symbol]"!==Object.prototype.toString.call(n))return!1;for(var r in e[t]=42,e)return!1;if("function"==typeof Object.keys&&0!==Object.keys(e).length)return!1;if("function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(e).length)return!1;var o=Object.getOwnPropertySymbols(e);if(1!==o.length||o[0]!==t)return!1;if(!Object.prototype.propertyIsEnumerable.call(e,t))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var i=Object.getOwnPropertyDescriptor(e,t);if(42!==i.value||!0!==i.enumerable)return!1}return!0});return sp=function(){return"function"==typeof e&&("function"==typeof Symbol&&("symbol"==typeof e("foo")&&("symbol"==typeof Symbol("bar")&&t())))}}()(),pd=function(){if(Ip)return Op;Ip=1;var e=Sp(),t=xp(),n=function(){if(Tp)return Ap;Tp=1;var e,t=Vp,n=rp;try{e=[].__proto__===Array.prototype}catch($v){if(!$v||"object"!=typeof $v||!("code"in $v)||"ERR_PROTO_ACCESS"!==$v.code)throw $v}var r=!!e&&n&&n(Object.prototype,"__proto__"),o=Object,i=o.getPrototypeOf;return Ap=r&&"function"==typeof r.get?t([r.get]):"function"==typeof i&&function(e){return i(null==e?e:o(e))}}();return Op=e?function(t){return e(t)}:t?function(e){if(!e||"object"!=typeof e&&"function"!=typeof e)throw new TypeError("getProto: not an object");return t(e)}:n?function(e){return n(e)}:null}(),dd=xp(),hd=Sp(),gd=Ep(),md=kp(),vd={},yd="undefined"!=typeof Uint8Array&&pd?pd(Uint8Array):Mp,bd={__proto__:null,"%AggregateError%":"undefined"==typeof AggregateError?Mp:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?Mp:ArrayBuffer,"%ArrayIteratorPrototype%":fd&&pd?pd([][Symbol.iterator]()):Mp,"%AsyncFromSyncIteratorPrototype%":Mp,"%AsyncFunction%":vd,"%AsyncGenerator%":vd,"%AsyncGeneratorFunction%":vd,"%AsyncIteratorPrototype%":vd,"%Atomics%":"undefined"==typeof Atomics?Mp:Atomics,"%BigInt%":"undefined"==typeof BigInt?Mp:BigInt,"%BigInt64Array%":"undefined"==typeof BigInt64Array?Mp:BigInt64Array,"%BigUint64Array%":"undefined"==typeof BigUint64Array?Mp:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?Mp:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":Wp,"%eval%":eval,"%EvalError%":qp,"%Float16Array%":"undefined"==typeof Float16Array?Mp:Float16Array,"%Float32Array%":"undefined"==typeof Float32Array?Mp:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?Mp:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?Mp:FinalizationRegistry,"%Function%":id,"%GeneratorFunction%":vd,"%Int8Array%":"undefined"==typeof Int8Array?Mp:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?Mp:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?Mp:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":fd&&pd?pd(pd([][Symbol.iterator]())):Mp,"%JSON%":"object"==typeof JSON?JSON:Mp,"%Map%":"undefined"==typeof Map?Mp:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&fd&&pd?pd((new Map)[Symbol.iterator]()):Mp,"%Math%":Math,"%Number%":Number,"%Object%":Up,"%Object.getOwnPropertyDescriptor%":sd,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?Mp:Promise,"%Proxy%":"undefined"==typeof Proxy?Mp:Proxy,"%RangeError%":Kp,"%ReferenceError%":Gp,"%Reflect%":"undefined"==typeof Reflect?Mp:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?Mp:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&fd&&pd?pd((new Set)[Symbol.iterator]()):Mp,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?Mp:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":fd&&pd?pd(""[Symbol.iterator]()):Mp,"%Symbol%":fd?Symbol:Mp,"%SyntaxError%":Qp,"%ThrowTypeError%":ud,"%TypedArray%":yd,"%TypeError%":Jp,"%Uint8Array%":"undefined"==typeof Uint8Array?Mp:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?Mp:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?Mp:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?Mp:Uint32Array,"%URIError%":Xp,"%WeakMap%":"undefined"==typeof WeakMap?Mp:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?Mp:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?Mp:WeakSet,"%Function.prototype.call%":md,"%Function.prototype.apply%":gd,"%Object.defineProperty%":ld,"%Object.getPrototypeOf%":dd,"%Math.abs%":Zp,"%Math.floor%":Yp,"%Math.max%":ed,"%Math.min%":td,"%Math.pow%":nd,"%Math.round%":rd,"%Math.sign%":od,"%Reflect.getPrototypeOf%":hd};if(pd)try{null.error}catch($v){var _d=pd(pd($v));bd["%Error.prototype%"]=_d}var wd=function e(t){var n;if("%AsyncFunction%"===t)n=ad("async function () {}");else if("%GeneratorFunction%"===t)n=ad("function* () {}");else if("%AsyncGeneratorFunction%"===t)n=ad("async function* () {}");else if("%AsyncGenerator%"===t){var r=e("%AsyncGeneratorFunction%");r&&(n=r.prototype)}else if("%AsyncIteratorPrototype%"===t){var o=e("%AsyncGenerator%");o&&pd&&(n=pd(o.prototype))}return bd[t]=n,n},Sd={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},xd=Cp(),Cd=function(){if(Lp)return jp;Lp=1;var e=Function.prototype.call,t=Object.prototype.hasOwnProperty,n=Cp();return jp=n.call(e,t)}(),kd=xd.call(md,Array.prototype.concat),Ed=xd.call(gd,Array.prototype.splice),Ad=xd.call(md,String.prototype.replace),Td=xd.call(md,String.prototype.slice),Od=xd.call(md,RegExp.prototype.exec),Id=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,jd=/\\(\\)?/g,Ld=function(e,t){var n,r=e;if(Cd(Sd,r)&&(r="%"+(n=Sd[r])[0]+"%"),Cd(bd,r)){var o=bd[r];if(o===vd&&(o=wd(r)),void 0===o&&!t)throw new Jp("intrinsic "+e+" exists, but is not available. Please file an issue!");return{alias:n,name:r,value:o}}throw new Qp("intrinsic "+e+" does not exist!")},Md=function(e,t){if("string"!=typeof e||0===e.length)throw new Jp("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof t)throw new Jp('"allowMissing" argument must be a boolean');if(null===Od(/^%?[^%]*%?$/,e))throw new Qp("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var n=function(e){var t=Td(e,0,1),n=Td(e,-1);if("%"===t&&"%"!==n)throw new Qp("invalid intrinsic syntax, expected closing `%`");if("%"===n&&"%"!==t)throw new Qp("invalid intrinsic syntax, expected opening `%`");var r=[];return Ad(e,Id,(function(e,t,n,o){r[r.length]=n?Ad(o,jd,"$1"):t||e})),r}(e),r=n.length>0?n[0]:"",o=Ld("%"+r+"%",t),i=o.name,a=o.value,s=!1,l=o.alias;l&&(r=l[0],Ed(n,kd([0,1],l)));for(var c=1,u=!0;c<n.length;c+=1){var f=n[c],p=Td(f,0,1),d=Td(f,-1);if(('"'===p||"'"===p||"`"===p||'"'===d||"'"===d||"`"===d)&&p!==d)throw new Qp("property names with quotes must have matching quotes");if("constructor"!==f&&u||(s=!0),Cd(bd,i="%"+(r+="."+f)+"%"))a=bd[i];else if(null!=a){if(!(f in a)){if(!t)throw new Jp("base intrinsic for "+e+" exists, but the property is not available.");return}if(sd&&c+1>=n.length){var h=sd(a,f);a=(u=!!h)&&"get"in h&&!("originalValue"in h.get)?h.get:a[f]}else u=Cd(a,f),a=a[f];u&&!s&&(bd[i]=a)}}return a},Pd=Md,Rd=Vp,zd=Rd([Pd("%String.prototype.indexOf%")]),$d=function(e,t){var n=Pd(e,!!t);return"function"==typeof n&&zd(e,".prototype.")>-1?Rd([n]):n},Dd=$d,Bd=wf,Nd=Ru,Fd=Md("%Map%",!0),Hd=Dd("Map.prototype.get",!0),Vd=Dd("Map.prototype.set",!0),Ud=Dd("Map.prototype.has",!0),Wd=Dd("Map.prototype.delete",!0),qd=Dd("Map.prototype.size",!0),Kd=!!Fd&&function(){var e,t={assert:function(e){if(!t.has(e))throw new Nd("Side channel does not contain "+Bd(e))},delete:function(t){if(e){var n=Wd(e,t);return 0===qd(e)&&(e=void 0),n}return!1},get:function(t){if(e)return Hd(e,t)},has:function(t){return!!e&&Ud(e,t)},set:function(t,n){e||(e=new Fd),Vd(e,t,n)}};return t},Gd=$d,Qd=wf,Jd=Kd,Xd=Ru,Zd=Md("%WeakMap%",!0),Yd=Gd("WeakMap.prototype.get",!0),eh=Gd("WeakMap.prototype.set",!0),th=Gd("WeakMap.prototype.has",!0),nh=Gd("WeakMap.prototype.delete",!0),rh=Ru,oh=wf,ih=(Zd?function(){var e,t,n={assert:function(e){if(!n.has(e))throw new Xd("Side channel does not contain "+Qd(e))},delete:function(n){if(Zd&&n&&("object"==typeof n||"function"==typeof n)){if(e)return nh(e,n)}else if(Jd&&t)return t.delete(n);return!1},get:function(n){return Zd&&n&&("object"==typeof n||"function"==typeof n)&&e?Yd(e,n):t&&t.get(n)},has:function(n){return Zd&&n&&("object"==typeof n||"function"==typeof n)&&e?th(e,n):!!t&&t.has(n)},set:function(n,r){Zd&&n&&("object"==typeof n||"function"==typeof n)?(e||(e=new Zd),eh(e,n,r)):Jd&&(t||(t=Jd()),t.set(n,r))}};return n}:Jd)||Kd||function(){var e,t={assert:function(e){if(!t.has(e))throw new Nf("Side channel does not contain "+Bf(e))},delete:function(t){var n=e&&e.next,r=function(e,t){if(e)return Ff(e,t,!0)}(e,t);return r&&n&&n===r&&(e=void 0),!!r},get:function(t){return function(e,t){if(e){var n=Ff(e,t);return n&&n.value}}(e,t)},has:function(t){return function(e,t){return!!e&&!!Ff(e,t)}(e,t)},set:function(t,n){e||(e={next:void 0}),function(e,t,n){var r=Ff(e,t);r?r.value=n:e.next={key:t,next:e.next,value:n}}(e,t,n)}};return t},ah=String.prototype.replace,sh=/%20/g,lh="RFC3986",ch={default:lh,formatters:{RFC1738:function(e){return ah.call(e,sh,"+")},RFC3986:function(e){return String(e)}},RFC1738:"RFC1738",RFC3986:lh},uh=ch,fh=Object.prototype.hasOwnProperty,ph=Array.isArray,dh=function(){for(var e=[],t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e}(),hh=function(e,t){for(var n=t&&t.plainObjects?{__proto__:null}:{},r=0;r<e.length;++r)void 0!==e[r]&&(n[r]=e[r]);return n},gh=1024,mh={arrayToObject:hh,assign:function(e,t){return Object.keys(t).reduce((function(e,n){return e[n]=t[n],e}),e)},combine:function(e,t){return[].concat(e,t)},compact:function(e){for(var t=[{obj:{o:e},prop:"o"}],n=[],r=0;r<t.length;++r)for(var o=t[r],i=o.obj[o.prop],a=Object.keys(i),s=0;s<a.length;++s){var l=a[s],c=i[l];"object"==typeof c&&null!==c&&-1===n.indexOf(c)&&(t.push({obj:i,prop:l}),n.push(c))}return function(e){for(;e.length>1;){var t=e.pop(),n=t.obj[t.prop];if(ph(n)){for(var r=[],o=0;o<n.length;++o)void 0!==n[o]&&r.push(n[o]);t.obj[t.prop]=r}}}(t),e},decode:function(e,t,n){var r=e.replace(/\+/g," ");if("iso-8859-1"===n)return r.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(r)}catch($v){return r}},encode:function(e,t,n,r,o){if(0===e.length)return e;var i=e;if("symbol"==typeof e?i=Symbol.prototype.toString.call(e):"string"!=typeof e&&(i=String(e)),"iso-8859-1"===n)return escape(i).replace(/%u[0-9a-f]{4}/gi,(function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"}));for(var a="",s=0;s<i.length;s+=gh){for(var l=i.length>=gh?i.slice(s,s+gh):i,c=[],u=0;u<l.length;++u){var f=l.charCodeAt(u);45===f||46===f||95===f||126===f||f>=48&&f<=57||f>=65&&f<=90||f>=97&&f<=122||o===uh.RFC1738&&(40===f||41===f)?c[c.length]=l.charAt(u):f<128?c[c.length]=dh[f]:f<2048?c[c.length]=dh[192|f>>6]+dh[128|63&f]:f<55296||f>=57344?c[c.length]=dh[224|f>>12]+dh[128|f>>6&63]+dh[128|63&f]:(u+=1,f=65536+((1023&f)<<10|1023&l.charCodeAt(u)),c[c.length]=dh[240|f>>18]+dh[128|f>>12&63]+dh[128|f>>6&63]+dh[128|63&f])}a+=c.join("")}return a},isBuffer:function(e){return!(!e||"object"!=typeof e)&&!!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e))},isRegExp:function(e){return"[object RegExp]"===Object.prototype.toString.call(e)},maybeMap:function(e,t){if(ph(e)){for(var n=[],r=0;r<e.length;r+=1)n.push(t(e[r]));return n}return t(e)},merge:function e(t,n,r){if(!n)return t;if("object"!=typeof n&&"function"!=typeof n){if(ph(t))t.push(n);else{if(!t||"object"!=typeof t)return[t,n];(r&&(r.plainObjects||r.allowPrototypes)||!fh.call(Object.prototype,n))&&(t[n]=!0)}return t}if(!t||"object"!=typeof t)return[t].concat(n);var o=t;return ph(t)&&!ph(n)&&(o=hh(t,r)),ph(t)&&ph(n)?(n.forEach((function(n,o){if(fh.call(t,o)){var i=t[o];i&&"object"==typeof i&&n&&"object"==typeof n?t[o]=e(i,n,r):t.push(n)}else t[o]=n})),t):Object.keys(n).reduce((function(t,o){var i=n[o];return fh.call(t,o)?t[o]=e(t[o],i,r):t[o]=i,t}),o)}},vh=function(){var e,t={assert:function(e){if(!t.has(e))throw new rh("Side channel does not contain "+oh(e))},delete:function(t){return!!e&&e.delete(t)},get:function(t){return e&&e.get(t)},has:function(t){return!!e&&e.has(t)},set:function(t,n){e||(e=ih()),e.set(t,n)}};return t},yh=mh,bh=ch,_h=Object.prototype.hasOwnProperty,wh={brackets:function(e){return e+"[]"},comma:"comma",indices:function(e,t){return e+"["+t+"]"},repeat:function(e){return e}},Sh=Array.isArray,xh=Array.prototype.push,Ch=function(e,t){xh.apply(e,Sh(t)?t:[t])},kh=Date.prototype.toISOString,Eh=bh.default,Ah={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,commaRoundTrip:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:yh.encode,encodeValuesOnly:!1,filter:void 0,format:Eh,formatter:bh.formatters[Eh],indices:!1,serializeDate:function(e){return kh.call(e)},skipNulls:!1,strictNullHandling:!1},Th={},Oh=function e(t,n,r,o,i,a,s,l,c,u,f,p,d,h,g,m,v,y){for(var b,_=t,w=y,S=0,x=!1;void 0!==(w=w.get(Th))&&!x;){var C=w.get(t);if(S+=1,void 0!==C){if(C===S)throw new RangeError("Cyclic object value");x=!0}void 0===w.get(Th)&&(S=0)}if("function"==typeof u?_=u(n,_):_ instanceof Date?_=d(_):"comma"===r&&Sh(_)&&(_=yh.maybeMap(_,(function(e){return e instanceof Date?d(e):e}))),null===_){if(a)return c&&!m?c(n,Ah.encoder,v,"key",h):n;_=""}if("string"==typeof(b=_)||"number"==typeof b||"boolean"==typeof b||"symbol"==typeof b||"bigint"==typeof b||yh.isBuffer(_))return c?[g(m?n:c(n,Ah.encoder,v,"key",h))+"="+g(c(_,Ah.encoder,v,"value",h))]:[g(n)+"="+g(String(_))];var k,E=[];if(void 0===_)return E;if("comma"===r&&Sh(_))m&&c&&(_=yh.maybeMap(_,c)),k=[{value:_.length>0?_.join(",")||null:void 0}];else if(Sh(u))k=u;else{var A=Object.keys(_);k=f?A.sort(f):A}var T=l?String(n).replace(/\./g,"%2E"):String(n),O=o&&Sh(_)&&1===_.length?T+"[]":T;if(i&&Sh(_)&&0===_.length)return O+"[]";for(var I=0;I<k.length;++I){var j=k[I],L="object"==typeof j&&j&&void 0!==j.value?j.value:_[j];if(!s||null!==L){var M=p&&l?String(j).replace(/\./g,"%2E"):String(j),P=Sh(_)?"function"==typeof r?r(O,M):O:O+(p?"."+M:"["+M+"]");y.set(t,S);var R=vh();R.set(Th,y),Ch(E,e(L,P,r,o,i,a,s,l,"comma"===r&&m&&Sh(_)?null:c,u,f,p,d,h,g,m,v,R))}}return E},Ih=mh,jh=Object.prototype.hasOwnProperty,Lh=Array.isArray,Mh={allowDots:!1,allowEmptyArrays:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decodeDotInKeys:!1,decoder:Ih.decode,delimiter:"&",depth:5,duplicates:"combine",ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictDepth:!1,strictNullHandling:!1,throwOnLimitExceeded:!1},Ph=function(e){return e.replace(/&#(\d+);/g,(function(e,t){return String.fromCharCode(parseInt(t,10))}))},Rh=function(e,t,n){if(e&&"string"==typeof e&&t.comma&&e.indexOf(",")>-1)return e.split(",");if(t.throwOnLimitExceeded&&n>=t.arrayLimit)throw new RangeError("Array limit exceeded. Only "+t.arrayLimit+" element"+(1===t.arrayLimit?"":"s")+" allowed in an array.");return e},zh=function(e,t,n,r){if(e){var o=n.allowDots?e.replace(/\.([^.[]+)/g,"[$1]"):e,i=/(\[[^[\]]*])/g,a=n.depth>0&&/(\[[^[\]]*])/.exec(o),s=a?o.slice(0,a.index):o,l=[];if(s){if(!n.plainObjects&&jh.call(Object.prototype,s)&&!n.allowPrototypes)return;l.push(s)}for(var c=0;n.depth>0&&null!==(a=i.exec(o))&&c<n.depth;){if(c+=1,!n.plainObjects&&jh.call(Object.prototype,a[1].slice(1,-1))&&!n.allowPrototypes)return;l.push(a[1])}if(a){if(!0===n.strictDepth)throw new RangeError("Input depth exceeded depth option of "+n.depth+" and strictDepth is true");l.push("["+o.slice(a.index)+"]")}return function(e,t,n,r){var o=0;if(e.length>0&&"[]"===e[e.length-1]){var i=e.slice(0,-1).join("");o=Array.isArray(t)&&t[i]?t[i].length:0}for(var a=r?t:Rh(t,n,o),s=e.length-1;s>=0;--s){var l,c=e[s];if("[]"===c&&n.parseArrays)l=n.allowEmptyArrays&&(""===a||n.strictNullHandling&&null===a)?[]:Ih.combine([],a);else{l=n.plainObjects?{__proto__:null}:{};var u="["===c.charAt(0)&&"]"===c.charAt(c.length-1)?c.slice(1,-1):c,f=n.decodeDotInKeys?u.replace(/%2E/g,"."):u,p=parseInt(f,10);n.parseArrays||""!==f?!isNaN(p)&&c!==f&&String(p)===f&&p>=0&&n.parseArrays&&p<=n.arrayLimit?(l=[])[p]=a:"__proto__"!==f&&(l[f]=a):l={0:a}}a=l}return a}(l,t,n,r)}};const $h={formats:ch,parse:function(e,t){var n=function(e){if(!e)return Mh;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.decodeDotInKeys&&"boolean"!=typeof e.decodeDotInKeys)throw new TypeError("`decodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.decoder&&void 0!==e.decoder&&"function"!=typeof e.decoder)throw new TypeError("Decoder has to be a function.");if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");if(void 0!==e.throwOnLimitExceeded&&"boolean"!=typeof e.throwOnLimitExceeded)throw new TypeError("`throwOnLimitExceeded` option must be a boolean");var t=void 0===e.charset?Mh.charset:e.charset,n=void 0===e.duplicates?Mh.duplicates:e.duplicates;if("combine"!==n&&"first"!==n&&"last"!==n)throw new TypeError("The duplicates option must be either combine, first, or last");return{allowDots:void 0===e.allowDots?!0===e.decodeDotInKeys||Mh.allowDots:!!e.allowDots,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:Mh.allowEmptyArrays,allowPrototypes:"boolean"==typeof e.allowPrototypes?e.allowPrototypes:Mh.allowPrototypes,allowSparse:"boolean"==typeof e.allowSparse?e.allowSparse:Mh.allowSparse,arrayLimit:"number"==typeof e.arrayLimit?e.arrayLimit:Mh.arrayLimit,charset:t,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:Mh.charsetSentinel,comma:"boolean"==typeof e.comma?e.comma:Mh.comma,decodeDotInKeys:"boolean"==typeof e.decodeDotInKeys?e.decodeDotInKeys:Mh.decodeDotInKeys,decoder:"function"==typeof e.decoder?e.decoder:Mh.decoder,delimiter:"string"==typeof e.delimiter||Ih.isRegExp(e.delimiter)?e.delimiter:Mh.delimiter,depth:"number"==typeof e.depth||!1===e.depth?+e.depth:Mh.depth,duplicates:n,ignoreQueryPrefix:!0===e.ignoreQueryPrefix,interpretNumericEntities:"boolean"==typeof e.interpretNumericEntities?e.interpretNumericEntities:Mh.interpretNumericEntities,parameterLimit:"number"==typeof e.parameterLimit?e.parameterLimit:Mh.parameterLimit,parseArrays:!1!==e.parseArrays,plainObjects:"boolean"==typeof e.plainObjects?e.plainObjects:Mh.plainObjects,strictDepth:"boolean"==typeof e.strictDepth?!!e.strictDepth:Mh.strictDepth,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:Mh.strictNullHandling,throwOnLimitExceeded:"boolean"==typeof e.throwOnLimitExceeded&&e.throwOnLimitExceeded}}(t);if(""===e||null==e)return n.plainObjects?{__proto__:null}:{};for(var r="string"==typeof e?function(e,t){var n={__proto__:null},r=t.ignoreQueryPrefix?e.replace(/^\?/,""):e;r=r.replace(/%5B/gi,"[").replace(/%5D/gi,"]");var o=t.parameterLimit===1/0?void 0:t.parameterLimit,i=r.split(t.delimiter,t.throwOnLimitExceeded?o+1:o);if(t.throwOnLimitExceeded&&i.length>o)throw new RangeError("Parameter limit exceeded. Only "+o+" parameter"+(1===o?"":"s")+" allowed.");var a,s=-1,l=t.charset;if(t.charsetSentinel)for(a=0;a<i.length;++a)0===i[a].indexOf("utf8=")&&("utf8=%E2%9C%93"===i[a]?l="utf-8":"utf8=%26%2310003%3B"===i[a]&&(l="iso-8859-1"),s=a,a=i.length);for(a=0;a<i.length;++a)if(a!==s){var c,u,f=i[a],p=f.indexOf("]="),d=-1===p?f.indexOf("="):p+1;-1===d?(c=t.decoder(f,Mh.decoder,l,"key"),u=t.strictNullHandling?null:""):(c=t.decoder(f.slice(0,d),Mh.decoder,l,"key"),u=Ih.maybeMap(Rh(f.slice(d+1),t,Lh(n[c])?n[c].length:0),(function(e){return t.decoder(e,Mh.decoder,l,"value")}))),u&&t.interpretNumericEntities&&"iso-8859-1"===l&&(u=Ph(String(u))),f.indexOf("[]=")>-1&&(u=Lh(u)?[u]:u);var h=jh.call(n,c);h&&"combine"===t.duplicates?n[c]=Ih.combine(n[c],u):h&&"last"!==t.duplicates||(n[c]=u)}return n}(e,n):e,o=n.plainObjects?{__proto__:null}:{},i=Object.keys(r),a=0;a<i.length;++a){var s=i[a],l=zh(s,r[s],n,"string"==typeof e);o=Ih.merge(o,l,n)}return!0===n.allowSparse?o:Ih.compact(o)},stringify:function(e,t){var n,r=e,o=function(e){if(!e)return Ah;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.encodeDotInKeys&&"boolean"!=typeof e.encodeDotInKeys)throw new TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw new TypeError("Encoder has to be a function.");var t=e.charset||Ah.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var n=bh.default;if(void 0!==e.format){if(!_h.call(bh.formatters,e.format))throw new TypeError("Unknown format option provided.");n=e.format}var r,o=bh.formatters[n],i=Ah.filter;if(("function"==typeof e.filter||Sh(e.filter))&&(i=e.filter),r=e.arrayFormat in wh?e.arrayFormat:"indices"in e?e.indices?"indices":"repeat":Ah.arrayFormat,"commaRoundTrip"in e&&"boolean"!=typeof e.commaRoundTrip)throw new TypeError("`commaRoundTrip` must be a boolean, or absent");var a=void 0===e.allowDots?!0===e.encodeDotInKeys||Ah.allowDots:!!e.allowDots;return{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:Ah.addQueryPrefix,allowDots:a,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:Ah.allowEmptyArrays,arrayFormat:r,charset:t,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:Ah.charsetSentinel,commaRoundTrip:!!e.commaRoundTrip,delimiter:void 0===e.delimiter?Ah.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:Ah.encode,encodeDotInKeys:"boolean"==typeof e.encodeDotInKeys?e.encodeDotInKeys:Ah.encodeDotInKeys,encoder:"function"==typeof e.encoder?e.encoder:Ah.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:Ah.encodeValuesOnly,filter:i,format:n,formatter:o,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:Ah.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:Ah.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:Ah.strictNullHandling}}(t);"function"==typeof o.filter?r=(0,o.filter)("",r):Sh(o.filter)&&(n=o.filter);var i=[];if("object"!=typeof r||null===r)return"";var a=wh[o.arrayFormat],s="comma"===a&&o.commaRoundTrip;n||(n=Object.keys(r)),o.sort&&n.sort(o.sort);for(var l=vh(),c=0;c<n.length;++c){var u=n[c],f=r[u];o.skipNulls&&null===f||Ch(i,Oh(f,u,a,s,o.allowEmptyArrays,o.strictNullHandling,o.skipNulls,o.encodeDotInKeys,o.encode?o.encoder:null,o.filter,o.sort,o.allowDots,o.serializeDate,o.format,o.formatter,o.encodeValuesOnly,o.charset,l))}var p=i.join(o.delimiter),d=!0===o.addQueryPrefix?"?":"";return o.charsetSentinel&&("iso-8859-1"===o.charset?d+="utf8=%26%2310003%3B&":d+="utf8=%E2%9C%93&"),p.length>0?d+p:""}};class Dh{constructor(e){return this.responseEvent="AsecCall_ResponseToWeb",this.callbackList={},this.qtObject=null,this.processId=0,this.initProcessId(),this.initIpcInstance()}initProcessId(){const e=$h.parse(location.search.substring(1));this.processId=gu.get(e,"ProcessId",0)}async initIpcInstance(){return Ou()?new Promise((e=>{this.ipcInstance=new Iu((t=>{this.addResponseListener(t,this.responseEvent),e(this)}))})):(this.ipcInstance=null,this)}send(e,t,n,r){let o={},i=(i,a)=>{if(r.isNeedId){n.id=ju.getStrSerial(this.processId);const r=Ou()?(new Error).stack.split("\n"):[],o={resolve:i,reject:a,request:{module:e,action:t,request:n,startTime:(new Date).getTime()},stackTrace:r};this.callbackList[n.id]=o}try{o=ju.interceptors(n,t)}catch(s){throw console.log(s),new Error("参数转换错误")}logger.log("调用客户端接口：",e,t,o),this.ipcInstance.send({module:e,action:t,strSerial:r.isNeedId?n.id:-1,data:o,resolve:i,reject:a})};if(gu.isSafeInteger(gu.get(r,"timeout.time"))){i=(async e=>{if(e=gu.merge({request:null,callback:null,time:15e3,timeoutReturn:{errcode:11100003,errmsg:""},retry:3,retryDelay:1e3},e),gu.isNil(e.callback))return!1;let t;for(let n=0;n<e.retry&&(t=await Promise.race([new Promise(e.callback),Lu(e)]),console.log("overtime:request:result",{result:t,nowTry:n}),gu.get(t,"overtime",!1));n++)console.error("overtime:request:fail"),console.error(JSON.stringify({...gu.omit(e,["callback"]),nowTry:n})),n===e.retry-1?t=e.timeoutReturn:await Lu({time:e.retryDelay});return t})(gu.merge({callback:i,request:{module:e,action:t,data:o}},r.timeout))}else i=new Promise(i);return i}on(e,t,n){this.ipcInstance.on(e,t,n)}off(e,t,n){this.ipcInstance.off(e,t,n)}handleBusinessCtrl(e){try{let t={};gu.isNil(e)||gu.isEmpty(e)||(t=gu.isString(e)?JSON.parse(e):e),logger.log("处理客户端主动调用，解析后的响应:",t),"TunStateChanged"===t.Action&&(logger.log("检测到 TunStateChanged 动作，触发客户端隧道状态同步"),this.emitTunStateChangedEvent(t.Data))}catch(t){console.error("处理客户端主动调用失败:",t)}}handleClientActiveCall(e){try{let t={};gu.isNil(e)||gu.isEmpty(e)||(t=gu.isString(e)?JSON.parse(e):e),logger.log("处理客户端主动调用，解析后的响应:",t),"WebAuthToken"===t.Action?(logger.log("检测到 WebAuthToken 动作，触发客户端登录状态同步"),this.emitClientAuthEvent()):"WebLogout"===t.Action&&(logger.log("检测到 WebLogout 动作，触发客户端退出登录"),this.emitClientLogoutEvent())}catch(t){console.error("处理客户端主动调用失败:",t)}}emitTunStateChangedEvent(e){"undefined"!=typeof window&&(Mu.emit("refreshTunnelStatus",{timestamp:Date.now(),source:"app-notice",data:e}),logger.log("已触发 refreshTunnelStatus 事件"))}emitClientAuthEvent(){if("undefined"!=typeof window){const e=new CustomEvent("clientAuthTokenReceived",{detail:{timestamp:Date.now()}});window.dispatchEvent(e),logger.log("已触发 clientAuthTokenReceived 事件")}}emitClientLogoutEvent(){if("undefined"!=typeof window){const e=new CustomEvent("clientLogoutReceived",{detail:{timestamp:Date.now()}});window.dispatchEvent(e),logger.log("已触发 clientLogoutReceived 事件")}}addResponseListener(e,t){logger.log("启动监听客户端接口返回：",e,t);const n=(e,t=null,n=null)=>{try{if(logger.log("客户端接口返回：",e,t,n),"LocalHostHelper"===t)return logger.log("检测到客户端主动调用，type:",t,"responseStr:",n),void this.handleClientActiveCall(n);if("BusinessCtrl"===t)return logger.log("检测到客户端主动调用，type:",t,"responseStr:",n),void this.handleBusinessCtrl(n);if("IsAgent"===t)return logger.log("检测到准入状态变更，type:",t,"responseStr:",n),void this.handleAgentStatusChange(n);let r={};if(gu.isNil(n)||gu.isEmpty(n)||(r=gu.isString(n)?JSON.parse(n):n),gu.isUndefined(e)&&gu.isEmpty(e))throw new Error("serial 为空或者未定义");const o=this.callbackList[e];if(gu.isUndefined(o))logger.log("未找到对应的请求回调:",e);else{const e=r.Result||r;if(e&&0===e.ErrorCode){const t=e.ResponseData||e;logger.log("成功解析客户端响应，返回数据:",t),o.resolve(t)}else{const t=(null==e?void 0:e.ErrorMsg)||"客户端调用失败";logger.log("客户端响应错误:",t),o.reject(new Error(t))}o.request.response=e||{},o.request.endTime=(new Date).getTime()}delete this.callbackList[e]}catch($v){console.error("小助手返回错误="),console.error($v)}};if(gu.isObject(e))if(e.AsecMainFrame)this.ipcInstance.on("AsecMainFrame",t,n),logger.log("强制在模块 AsecMainFrame 上设置响应监听器");else{const r=Object.keys(e);if(r.length>0){const e=r[0];this.ipcInstance.on(e,t,n),logger.log(`在模块 ${e} 上设置响应监听器`)}}}handleAgentStatusChange(e){try{logger.log("处理准入状态变更:",e);let t={};if(t="string"==typeof e?JSON.parse(e):e,"StatusChange"===t.Action&&t.Data&&void 0!==t.Data.DeviceStatus){const e=t.Data.DeviceStatus;logger.log("提取到设备状态:",e),this.emitAgentStatusChangeEvent(e)}else logger.warn("准入状态变更数据格式不正确:",t)}catch(t){logger.error("处理准入状态变更失败:",t)}}emitAgentStatusChangeEvent(e){try{if("undefined"!=typeof window){const t=new CustomEvent("agentStatusChanged",{detail:{timestamp:Date.now(),deviceStatus:e}});window.dispatchEvent(t),logger.log("已触发 agentStatusChanged 事件，设备状态:",e)}}catch(t){logger.error("触发准入状态变更事件失败:",t)}}}const Bh={init:()=>(new Dh).then((e=>{const t={$ipcSend:(t,n,r={},o={})=>{if(gu.isNil(t)||gu.isNil(n)||gu.isEmpty(t)||gu.isEmpty(n))throw new Error("module或action不能为空");if(r&&!gu.isObject(r))throw new Error("params必须为object类型");return o=gu.merge({isNeedId:!0,timeout:{time:15e3}},o),e.send(t,n,r,o)},$ipcOn:(t,n,r)=>{e.on(t,n,r)},$ipcOff:(t,n,r)=>{e.off(t,n,r)},$processId:e.processId};return t}))},Nh={_ipcClient:null,_initPromise:null,_isClient:null,_ClientType:null,isClient(){if(null!==this._isClient)return this._isClient;globalUrlHashParams.forEach(((e,t)=>{logger.log(`Url参数: ${t}: ${e}`)}));let e=/QtWebEngine/.test(navigator.userAgent);return e||globalUrlHashParams&&globalUrlHashParams.get("AsecClient")&&(e=!0),this._isClient=e,logger.log("是否是客户端:",e),this._isClient},getClientType(){if(null!==this._ClientType)return this._ClientType;let e="web";if(this.isClient()){const t=globalUrlHashParams?globalUrlHashParams.get("ClientType"):"";t&&(e=t)}return logger.log("客户端类型:",e),this._ClientType=e,this._ClientType},getClientParams(){const e={t:1};return globalUrlHashParams&&["WebUrl","ClientType","AsecDebug","AsecClient"].forEach((t=>{const n=globalUrlHashParams.get(t);n&&(e[t]=n)})),e},getNativeJsPath:e=>("qrc:"===window.location.protocol&&(e="/Resource/dist"+e),e),async initIpcClient(){return this._initPromise||(this._initPromise=this._doInit()),this._initPromise},async _doInit(){if(!this._ipcClient)try{this.isClient()?(logger.log("开始初始化 IPC 客户端..."),this._ipcClient=await Bh.init(),logger.log("IPC 初始化成功，监听器已设置完成")):(console.warn("非 QT 环境，使用模拟 IPC 客户端"),this._ipcClient=this._createMockIpcClient())}catch(e){console.error("IPC 初始化失败:",e),this._ipcClient=this._createMockIpcClient()}return this._ipcClient},_createMockIpcClient:()=>({$ipcSend:(e,t,n={})=>(console.warn(`模拟 IPC 调用: ${e}.${t}`,n),Promise.reject(new Error(`IPC not available in current environment (${e}.${t})`))),$ipcOn:(e,t,n)=>{console.warn(`模拟 IPC 监听: ${e}.${t}`)},$ipcOff:(e,t,n)=>{console.warn(`模拟 IPC 取消监听: ${e}.${t}`)},$processId:0}),async normalnizeWnd(){return await this.initIpcClient(),this._ipcClient.$ipcSend("AsecMainFrame","WebCall_MainFrameTitleBar",{Action:{Type:"Normal"}})},async maximizeWnd(){return await this.initIpcClient(),this._ipcClient.$ipcSend("AsecMainFrame","WebCall_MainFrameTitleBar",{Action:{Type:"Max"}})},async minimizeWnd(){return await this.initIpcClient(),this._ipcClient.$ipcSend("AsecMainFrame","WebCall_MainFrameTitleBar",{Action:{Type:"Min"}})},async hideWend(){return await this.initIpcClient(),this._ipcClient.$ipcSend("AsecMainFrame","WebCall_MainFrameTitleBar",{Action:{Type:"Hide"}})},async setWidthHeight(e,t){return await this.initIpcClient(),this._ipcClient.$ipcSend("AsecMainFrame","WebCall_MainFrameDimension",{TitleBar:{Height:t,Width:e}})},async getClientConfig(){await this.initIpcClient();return await this._ipcClient.$ipcSend("Module_DataProxy","WebCall_Settings",{Settings:{Type:"Load"}})},async getSidecarProxyPortIpc(){await this.initIpcClient();return await this._ipcClient.$ipcSend("Module_DataProxy","WebCall_GetSidecarPort",{Request:{Type:"GetProxyPort"}})},async setClientConfig(e){return await this.initIpcClient(),this._ipcClient.$ipcSend("Module_DataProxy","WebCall_Settings",{Settings:{Type:"Save",Data:e}})},async getChannelStatus(){return await this.initIpcClient(),this._ipcClient.$ipcSend("Module_Instruction","WebCall_AsecInstruction",{Instruction:{Object:"AsecService",Type:"GetChannelStatus"}})},async setLoginStatus(e){return await this.initIpcClient(),this._ipcClient.$ipcSend("Module_Instruction","WebCall_AsecInstruction",{Instruction:{Object:"AsecService",Type:"SetLoginStatus",Data:e}})},async connectTunnel(){return await this.initIpcClient(),this._ipcClient.$ipcSend("Module_Instruction","WebCall_AsecInstruction",{Instruction:{Object:"AsecService",Type:"EstablishChannel"}})},async disconnectTunnel(){return await this.initIpcClient(),this._ipcClient.$ipcSend("Module_Instruction","WebCall_AsecInstruction",{Instruction:{Object:"AsecService",Type:"DisconnectChannel"}})},async openResource(e){return await this.initIpcClient(),this._ipcClient.$ipcSend("Module_Resource","WebCall_OpenResource",{Resource:e})},async openAsecPage(e){return logger.log("打开浏览器:",e),await this.initIpcClient(),this._ipcClient.$ipcSend("Module_Resource","WebCall_OpenResource",{Resource:{Type:"URL",Data:{URL:e}}})},async getAccessStatus(){return await this.initIpcClient(),this._ipcClient.$ipcSend("Module_Instruction","WebCall_IsaInstruction",{Instruction:{Object:"IsaHelp",Type:"QueryInfo"}})},async openAccessDetail(){return await this.initIpcClient(),this._ipcClient.$ipcSend("Module_Instruction","WebCall_IsaInstruction",{Instruction:{Object:"AssUI",Type:"Open"}})}},Fh=[{path:"/",redirect:"/login"},{path:"/status",name:"Status",component:()=>Al((()=>import("./status.8c7548b6.js")),["./status.8c7548b6.js","./status.d41f43c3.css"],import.meta.url)},{path:"/verify",name:"verify",component:()=>Al((()=>import("./verify.bbab3465.js")),[],import.meta.url)},{path:"/appverify",name:"appverify",component:()=>Al((()=>import("./appverify.b67a22c0.js")),["./appverify.b67a22c0.js","./appverify.1430be1b.css"],import.meta.url)},{path:"/error",name:"Error",component:()=>Al((()=>import("./index.3f331206.js")),["./index.3f331206.js","./index.4f4a44b0.css"],import.meta.url)},{path:"/login",name:"Login",component:()=>Al((()=>import("./index.ff864b50.js")),["./index.ff864b50.js","./config.a6088397.js","./index.061cf045.css"],import.meta.url)},{path:"/client",name:"Client",component:()=>Al((()=>import("./index.42b1f964.js")),["./index.42b1f964.js","./header.165b7e39.js","./logo.b56ac4ae.js","./header.fe927db8.css","./menu.bb2c1778.js","./menu.8dc27dc1.css","./index.6a2877f0.css"],import.meta.url),children:[{path:"/client/login",name:"ClientNewLogin",component:()=>Al((()=>import("./login.f2ae2ddd.js")),["./login.f2ae2ddd.js","./index.ff864b50.js","./config.a6088397.js","./index.061cf045.css"],import.meta.url)},{path:"/client/main",name:"ClientMain",component:()=>Al((()=>import("./main.62dc6be3.js")),["./main.62dc6be3.js","./index.54b02c0d.js","./index.17e94d33.css","./main.8de49897.css"],import.meta.url)},{path:"/client/setting",name:"ClientSetting",component:()=>Al((()=>import("./setting.88922075.js")),["./setting.88922075.js","./setting.08e84a26.css"],import.meta.url)}]},{path:"/clientLogin",name:"ClientLogin",component:()=>Al((()=>import("./clientLogin.8578c8c4.js")),[],import.meta.url)},{path:"/downloadWin",name:"downloadWin",component:()=>Al((()=>import("./downloadWin.79752223.js")),["./downloadWin.79752223.js","./logo.b56ac4ae.js","./system.9d57df9d.js","./downloadWin.7c3e0f71.css"],import.meta.url)},{path:"/wx_oauth_callback",name:"WxOAuthCallback",component:()=>Al((()=>import("./wx_oauth_callback.15412cb4.js")),[],import.meta.url)},{path:"/oauth2_premises",name:"OAuth2Premises",component:()=>Al((()=>import("./oauth2_premises.c05495d8.js")),["./oauth2_premises.c05495d8.js","./oauth2_premises.8c48799d.css"],import.meta.url)}],Hh=function(e){const t=zc(e.routes,e),n=e.parseQuery||Uc,r=e.stringifyQuery||Wc,o=e.history,i=Zc(),a=Zc(),s=Zc(),l=Ct(ic,!0);let c=ic;Tl&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=jl.bind(null,(e=>""+e)),f=jl.bind(null,Jl),p=jl.bind(null,Xl);function d(e,i){if(i=Il({},i||l.value),"string"==typeof e){const r=Yl(n,e,i.path),a=t.resolve({path:r.path},i),s=o.createHref(r.fullPath);return Il(r,a,{params:p(a.params),hash:Xl(r.hash),redirectedFrom:void 0,href:s})}let a;if(null!=e.path)a=Il({},e,{path:Yl(n,e.path,i.path).path});else{const t=Il({},e.params);for(const e in t)null==t[e]&&delete t[e];a=Il({},e,{params:f(t)}),i.params=f(i.params)}const s=t.resolve(a,i),c=e.hash||"";s.params=u(p(s.params));const d=function(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}(r,Il({},e,{hash:(h=c,Gl(h).replace(Ul,"{").replace(ql,"}").replace(Hl,"^")),path:s.path}));var h;const g=o.createHref(d);return Il({fullPath:d,hash:c,query:r===Wc?qc(e.query):e.query||{}},s,{redirectedFrom:void 0,href:g})}function h(e){return"string"==typeof e?Yl(n,e,l.value.path):Il({},e)}function g(e,t){if(c!==e)return kc(8,{from:t,to:e})}function m(e){return y(e)}function v(e){const t=e.matched[e.matched.length-1];if(t&&t.redirect){const{redirect:n}=t;let r="function"==typeof n?n(e):n;return"string"==typeof r&&(r=r.includes("?")||r.includes("#")?r=h(r):{path:r},r.params={}),Il({query:e.query,hash:e.hash,params:null!=r.path?{}:e.params},r)}}function y(e,t){const n=c=d(e),o=l.value,i=e.state,a=e.force,s=!0===e.replace,u=v(n);if(u)return y(Il(h(u),{state:"object"==typeof u?Il({},i,u.state):i,force:a,replace:s}),t||n);const f=n;let p;return f.redirectedFrom=t,!a&&function(e,t,n){const r=t.matched.length-1,o=n.matched.length-1;return r>-1&&r===o&&tc(t.matched[r],n.matched[o])&&nc(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}(r,o,n)&&(p=kc(16,{to:f,from:o}),j(o,o,!0,!1)),(p?Promise.resolve(p):w(f,o)).catch((e=>Ec(e)?Ec(e,2)?e:I(e):O(e,f,o))).then((e=>{if(e){if(Ec(e,2))return y(Il({replace:s},h(e.to),{state:"object"==typeof e.to?Il({},i,e.to.state):i,force:a}),t||f)}else e=x(f,o,!0,s,i);return S(f,o,e),e}))}function b(e,t){const n=g(e,t);return n?Promise.reject(n):Promise.resolve()}function _(e){const t=P.values().next().value;return t&&"function"==typeof t.runWithContext?t.runWithContext(e):e()}function w(e,t){let n;const[r,o,s]=function(e,t){const n=[],r=[],o=[],i=Math.max(t.matched.length,e.matched.length);for(let a=0;a<i;a++){const i=t.matched[a];i&&(e.matched.find((e=>tc(e,i)))?r.push(i):n.push(i));const s=e.matched[a];s&&(t.matched.find((e=>tc(e,s)))||o.push(s))}return[n,r,o]}(e,t);n=eu(r.reverse(),"beforeRouteLeave",e,t);for(const i of r)i.leaveGuards.forEach((r=>{n.push(Yc(r,e,t))}));const l=b.bind(null,e,t);return n.push(l),z(n).then((()=>{n=[];for(const r of i.list())n.push(Yc(r,e,t));return n.push(l),z(n)})).then((()=>{n=eu(o,"beforeRouteUpdate",e,t);for(const r of o)r.updateGuards.forEach((r=>{n.push(Yc(r,e,t))}));return n.push(l),z(n)})).then((()=>{n=[];for(const r of s)if(r.beforeEnter)if(Ml(r.beforeEnter))for(const o of r.beforeEnter)n.push(Yc(o,e,t));else n.push(Yc(r.beforeEnter,e,t));return n.push(l),z(n)})).then((()=>(e.matched.forEach((e=>e.enterCallbacks={})),n=eu(s,"beforeRouteEnter",e,t,_),n.push(l),z(n)))).then((()=>{n=[];for(const r of a.list())n.push(Yc(r,e,t));return n.push(l),z(n)})).catch((e=>Ec(e,8)?e:Promise.reject(e)))}function S(e,t,n){s.list().forEach((r=>_((()=>r(e,t,n)))))}function x(e,t,n,r,i){const a=g(e,t);if(a)return a;const s=t===ic,c=Tl?history.state:{};n&&(r||s?o.replace(e.fullPath,Il({scroll:s&&c&&c.scroll},i)):o.push(e.fullPath,i)),l.value=e,j(e,t,n,s),I()}let C;function k(){C||(C=o.listen(((e,t,n)=>{if(!R.listening)return;const r=d(e),i=v(r);if(i)return void y(Il(i,{replace:!0,force:!0}),r).catch(Ll);c=r;const a=l.value;var s,u;Tl&&(s=gc(a.fullPath,n.delta),u=dc(),mc.set(s,u)),w(r,a).catch((e=>Ec(e,12)?e:Ec(e,2)?(y(Il(h(e.to),{force:!0}),r).then((e=>{Ec(e,20)&&!n.delta&&n.type===ac.pop&&o.go(-1,!1)})).catch(Ll),Promise.reject()):(n.delta&&o.go(-n.delta,!1),O(e,r,a)))).then((e=>{(e=e||x(r,a,!1))&&(n.delta&&!Ec(e,8)?o.go(-n.delta,!1):n.type===ac.pop&&Ec(e,20)&&o.go(-1,!1)),S(r,a,e)})).catch(Ll)})))}let E,A=Zc(),T=Zc();function O(e,t,n){I(e);const r=T.list();return r.length?r.forEach((r=>r(e,t,n))):console.error(e),Promise.reject(e)}function I(e){return E||(E=!e,k(),A.list().forEach((([t,n])=>e?n(e):t())),A.reset()),e}function j(t,n,r,o){const{scrollBehavior:i}=e;if(!Tl||!i)return Promise.resolve();const a=!r&&function(e){const t=mc.get(e);return mc.delete(e),t}(gc(t.fullPath,0))||(o||!r)&&history.state&&history.state.scroll||null;return Kt().then((()=>i(t,n,a))).then((e=>e&&hc(e))).catch((e=>O(e,t,n)))}const L=e=>o.go(e);let M;const P=new Set,R={currentRoute:l,listening:!0,addRoute:function(e,n){let r,o;return wc(e)?(r=t.getRecordMatcher(e),o=n):o=e,t.addRoute(o,r)},removeRoute:function(e){const n=t.getRecordMatcher(e);n&&t.removeRoute(n)},clearRoutes:t.clearRoutes,hasRoute:function(e){return!!t.getRecordMatcher(e)},getRoutes:function(){return t.getRoutes().map((e=>e.record))},resolve:d,options:e,push:m,replace:function(e){return m(Il(h(e),{replace:!0}))},go:L,back:()=>L(-1),forward:()=>L(1),beforeEach:i.add,beforeResolve:a.add,afterEach:s.add,onError:T.add,isReady:function(){return E&&l.value!==ic?Promise.resolve():new Promise(((e,t)=>{A.add([e,t])}))},install(e){e.component("RouterLink",nu),e.component("RouterView",au),e.config.globalProperties.$router=this,Object.defineProperty(e.config.globalProperties,"$route",{enumerable:!0,get:()=>Et(l)}),Tl&&!M&&l.value===ic&&(M=!0,m(o.location).catch((e=>{})));const t={};for(const r in ic)Object.defineProperty(t,r,{get:()=>l.value[r],enumerable:!0});e.provide(Qc,this),e.provide(Jc,ft(t)),e.provide(Xc,l);const n=e.unmount;P.add(e),e.unmount=function(){P.delete(e),P.size<1&&(c=ic,C&&C(),C=null,l.value=ic,M=!1,E=!1),n()}}};function z(e){return e.reduce(((e,t)=>e.then((()=>_(t)))),Promise.resolve())}return R}({history:((Vh=location.host?Vh||location.pathname+location.search:"").includes("#")||(Vh+="#"),_c(Vh)),routes:Fh});var Vh;Hh.beforeEach((async(e,t,n)=>{const r=window.location.href,o=window.location.origin;if(logger.log("页面跳转:",r,"origin:",o),Nh.isClient())return logger.log("客户端不优化URL"),void n();if(!r.startsWith(o+"/#/")){console.log("Hash is not at the correct position");const e=r.indexOf("#");let t;if(-1===e)t=`${o}/#${r.substring(o.length)}`;else{let n=r.substring(o.length,e);const i=r.substring(e);n=n.replace(/^\/\?/,"&"),console.log("beforeHash:",n),console.log("afterHash:",i),t=`${o}/${i}${n}`}return console.log("Final new URL:",t),void window.location.replace(t)}logger.log("Proceeding with normal navigation"),n()}));var Uh={exports:{}},Wh={exports:{}},qh=function(e,t){return function(){for(var n=new Array(arguments.length),r=0;r<n.length;r++)n[r]=arguments[r];return e.apply(t,n)}},Kh=qh,Gh=Object.prototype.toString;function Qh(e){return"[object Array]"===Gh.call(e)}function Jh(e){return void 0===e}function Xh(e){return null!==e&&"object"==typeof e}function Zh(e){return"[object Function]"===Gh.call(e)}function Yh(e,t){if(null!=e)if("object"!=typeof e&&(e=[e]),Qh(e))for(var n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.call(null,e[o],o,e)}var eg={isArray:Qh,isArrayBuffer:function(e){return"[object ArrayBuffer]"===Gh.call(e)},isBuffer:function(e){return null!==e&&!Jh(e)&&null!==e.constructor&&!Jh(e.constructor)&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)},isFormData:function(e){return"undefined"!=typeof FormData&&e instanceof FormData},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&e.buffer instanceof ArrayBuffer},isString:function(e){return"string"==typeof e},isNumber:function(e){return"number"==typeof e},isObject:Xh,isUndefined:Jh,isDate:function(e){return"[object Date]"===Gh.call(e)},isFile:function(e){return"[object File]"===Gh.call(e)},isBlob:function(e){return"[object Blob]"===Gh.call(e)},isFunction:Zh,isStream:function(e){return Xh(e)&&Zh(e.pipe)},isURLSearchParams:function(e){return"undefined"!=typeof URLSearchParams&&e instanceof URLSearchParams},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product&&"NativeScript"!==navigator.product&&"NS"!==navigator.product)&&("undefined"!=typeof window&&"undefined"!=typeof document)},forEach:Yh,merge:function e(){var t={};function n(n,r){"object"==typeof t[r]&&"object"==typeof n?t[r]=e(t[r],n):t[r]=n}for(var r=0,o=arguments.length;r<o;r++)Yh(arguments[r],n);return t},deepMerge:function e(){var t={};function n(n,r){"object"==typeof t[r]&&"object"==typeof n?t[r]=e(t[r],n):t[r]="object"==typeof n?e({},n):n}for(var r=0,o=arguments.length;r<o;r++)Yh(arguments[r],n);return t},extend:function(e,t,n){return Yh(t,(function(t,r){e[r]=n&&"function"==typeof t?Kh(t,n):t})),e},trim:function(e){return e.replace(/^\s*/,"").replace(/\s*$/,"")}},tg=eg;function ng(e){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}var rg=function(e,t,n){if(!t)return e;var r;if(n)r=n(t);else if(tg.isURLSearchParams(t))r=t.toString();else{var o=[];tg.forEach(t,(function(e,t){null!=e&&(tg.isArray(e)?t+="[]":e=[e],tg.forEach(e,(function(e){tg.isDate(e)?e=e.toISOString():tg.isObject(e)&&(e=JSON.stringify(e)),o.push(ng(t)+"="+ng(e))})))})),r=o.join("&")}if(r){var i=e.indexOf("#");-1!==i&&(e=e.slice(0,i)),e+=(-1===e.indexOf("?")?"?":"&")+r}return e},og=eg;function ig(){this.handlers=[]}ig.prototype.use=function(e,t){return this.handlers.push({fulfilled:e,rejected:t}),this.handlers.length-1},ig.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},ig.prototype.forEach=function(e){og.forEach(this.handlers,(function(t){null!==t&&e(t)}))};var ag,sg,lg=ig,cg=eg;function ug(){return sg?ag:(sg=1,ag=function(e){return!(!e||!e.__CANCEL__)})}var fg,pg,dg,hg,gg,mg,vg,yg,bg,_g,wg,Sg,xg,Cg,kg,Eg,Ag,Tg,Og,Ig,jg=eg;function Lg(){if(hg)return dg;hg=1;var e=pg?fg:(pg=1,fg=function(e,t,n,r,o){return e.config=t,n&&(e.code=n),e.request=r,e.response=o,e.isAxiosError=!0,e.toJSON=function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code}},e});return dg=function(t,n,r,o,i){var a=new Error(t);return e(a,n,r,o,i)}}function Mg(){if(Sg)return wg;Sg=1;var e=yg?vg:(yg=1,vg=function(e){return/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(e)}),t=_g?bg:(_g=1,bg=function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e});return wg=function(n,r){return n&&!e(r)?t(n,r):r}}function Pg(){if(Ig)return Og;Ig=1;var e=eg,t=function(){if(mg)return gg;mg=1;var e=Lg();return gg=function(t,n,r){var o=r.config.validateStatus;!o||o(r.status)?t(r):n(e("Request failed with status code "+r.status,r.config,null,r.request,r))}}(),n=rg,r=Mg(),o=function(){if(Cg)return xg;Cg=1;var e=eg,t=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"];return xg=function(n){var r,o,i,a={};return n?(e.forEach(n.split("\n"),(function(n){if(i=n.indexOf(":"),r=e.trim(n.substr(0,i)).toLowerCase(),o=e.trim(n.substr(i+1)),r){if(a[r]&&t.indexOf(r)>=0)return;a[r]="set-cookie"===r?(a[r]?a[r]:[]).concat([o]):a[r]?a[r]+", "+o:o}})),a):a}}(),i=function(){if(Eg)return kg;Eg=1;var e=eg;return kg=e.isStandardBrowserEnv()?function(){var t,n=/(msie|trident)/i.test(navigator.userAgent),r=document.createElement("a");function o(e){var t=e;return n&&(r.setAttribute("href",t),t=r.href),r.setAttribute("href",t),{href:r.href,protocol:r.protocol?r.protocol.replace(/:$/,""):"",host:r.host,search:r.search?r.search.replace(/^\?/,""):"",hash:r.hash?r.hash.replace(/^#/,""):"",hostname:r.hostname,port:r.port,pathname:"/"===r.pathname.charAt(0)?r.pathname:"/"+r.pathname}}return t=o(window.location.href),function(n){var r=e.isString(n)?o(n):n;return r.protocol===t.protocol&&r.host===t.host}}():function(){return!0}}(),a=Lg();return Og=function(s){return new Promise((function(l,c){var u=s.data,f=s.headers;e.isFormData(u)&&delete f["Content-Type"];var p=new XMLHttpRequest;if(s.auth){var d=s.auth.username||"",h=s.auth.password||"";f.Authorization="Basic "+btoa(d+":"+h)}var g=r(s.baseURL,s.url);if(p.open(s.method.toUpperCase(),n(g,s.params,s.paramsSerializer),!0),p.timeout=s.timeout,p.onreadystatechange=function(){if(p&&4===p.readyState&&(0!==p.status||p.responseURL&&0===p.responseURL.indexOf("file:"))){var e="getAllResponseHeaders"in p?o(p.getAllResponseHeaders()):null,n={data:s.responseType&&"text"!==s.responseType?p.response:p.responseText,status:p.status,statusText:p.statusText,headers:e,config:s,request:p};t(l,c,n),p=null}},p.onabort=function(){p&&(c(a("Request aborted",s,"ECONNABORTED",p)),p=null)},p.onerror=function(){c(a("Network Error",s,null,p)),p=null},p.ontimeout=function(){var e="timeout of "+s.timeout+"ms exceeded";s.timeoutErrorMessage&&(e=s.timeoutErrorMessage),c(a(e,s,"ECONNABORTED",p)),p=null},e.isStandardBrowserEnv()){var m=function(){if(Tg)return Ag;Tg=1;var e=eg;return Ag=e.isStandardBrowserEnv()?{write:function(t,n,r,o,i,a){var s=[];s.push(t+"="+encodeURIComponent(n)),e.isNumber(r)&&s.push("expires="+new Date(r).toGMTString()),e.isString(o)&&s.push("path="+o),e.isString(i)&&s.push("domain="+i),!0===a&&s.push("secure"),document.cookie=s.join("; ")},read:function(e){var t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}}}(),v=(s.withCredentials||i(g))&&s.xsrfCookieName?m.read(s.xsrfCookieName):void 0;v&&(f[s.xsrfHeaderName]=v)}if("setRequestHeader"in p&&e.forEach(f,(function(e,t){void 0===u&&"content-type"===t.toLowerCase()?delete f[t]:p.setRequestHeader(t,e)})),e.isUndefined(s.withCredentials)||(p.withCredentials=!!s.withCredentials),s.responseType)try{p.responseType=s.responseType}catch($v){if("json"!==s.responseType)throw $v}"function"==typeof s.onDownloadProgress&&p.addEventListener("progress",s.onDownloadProgress),"function"==typeof s.onUploadProgress&&p.upload&&p.upload.addEventListener("progress",s.onUploadProgress),s.cancelToken&&s.cancelToken.promise.then((function(e){p&&(p.abort(),c(e),p=null)})),void 0===u&&(u=null),p.send(u)}))}}var Rg=eg,zg=function(e,t){jg.forEach(e,(function(n,r){r!==t&&r.toUpperCase()===t.toUpperCase()&&(e[t]=n,delete e[r])}))},$g={"Content-Type":"application/x-www-form-urlencoded"};function Dg(e,t){!Rg.isUndefined(e)&&Rg.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}var Bg,Ng={adapter:(("undefined"!=typeof XMLHttpRequest||"undefined"!=typeof process&&"[object process]"===Object.prototype.toString.call(process))&&(Bg=Pg()),Bg),transformRequest:[function(e,t){return zg(t,"Accept"),zg(t,"Content-Type"),Rg.isFormData(e)||Rg.isArrayBuffer(e)||Rg.isBuffer(e)||Rg.isStream(e)||Rg.isFile(e)||Rg.isBlob(e)?e:Rg.isArrayBufferView(e)?e.buffer:Rg.isURLSearchParams(e)?(Dg(t,"application/x-www-form-urlencoded;charset=utf-8"),e.toString()):Rg.isObject(e)?(Dg(t,"application/json;charset=utf-8"),JSON.stringify(e)):e}],transformResponse:[function(e){if("string"==typeof e)try{e=JSON.parse(e)}catch($v){}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,validateStatus:function(e){return e>=200&&e<300}};Ng.headers={common:{Accept:"application/json, text/plain, */*"}},Rg.forEach(["delete","get","head"],(function(e){Ng.headers[e]={}})),Rg.forEach(["post","put","patch"],(function(e){Ng.headers[e]=Rg.merge($g)}));var Fg=Ng,Hg=eg,Vg=function(e,t,n){return cg.forEach(n,(function(n){e=n(e,t)})),e},Ug=ug(),Wg=Fg;function qg(e){e.cancelToken&&e.cancelToken.throwIfRequested()}var Kg,Gg,Qg,Jg,Xg,Zg,Yg=eg,em=function(e,t){t=t||{};var n={},r=["url","method","params","data"],o=["headers","auth","proxy"],i=["baseURL","url","transformRequest","transformResponse","paramsSerializer","timeout","withCredentials","adapter","responseType","xsrfCookieName","xsrfHeaderName","onUploadProgress","onDownloadProgress","maxContentLength","validateStatus","maxRedirects","httpAgent","httpsAgent","cancelToken","socketPath"];Yg.forEach(r,(function(e){void 0!==t[e]&&(n[e]=t[e])})),Yg.forEach(o,(function(r){Yg.isObject(t[r])?n[r]=Yg.deepMerge(e[r],t[r]):void 0!==t[r]?n[r]=t[r]:Yg.isObject(e[r])?n[r]=Yg.deepMerge(e[r]):void 0!==e[r]&&(n[r]=e[r])})),Yg.forEach(i,(function(r){void 0!==t[r]?n[r]=t[r]:void 0!==e[r]&&(n[r]=e[r])}));var a=r.concat(o).concat(i),s=Object.keys(t).filter((function(e){return-1===a.indexOf(e)}));return Yg.forEach(s,(function(r){void 0!==t[r]?n[r]=t[r]:void 0!==e[r]&&(n[r]=e[r])})),n},tm=eg,nm=rg,rm=lg,om=function(e){return qg(e),e.headers=e.headers||{},e.data=Vg(e.data,e.headers,e.transformRequest),e.headers=Hg.merge(e.headers.common||{},e.headers[e.method]||{},e.headers),Hg.forEach(["delete","get","head","post","put","patch","common"],(function(t){delete e.headers[t]})),(e.adapter||Wg.adapter)(e).then((function(t){return qg(e),t.data=Vg(t.data,t.headers,e.transformResponse),t}),(function(t){return Ug(t)||(qg(e),t&&t.response&&(t.response.data=Vg(t.response.data,t.response.headers,e.transformResponse))),Promise.reject(t)}))},im=em;function am(e){this.defaults=e,this.interceptors={request:new rm,response:new rm}}function sm(){if(Gg)return Kg;function e(e){this.message=e}return Gg=1,e.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},e.prototype.__CANCEL__=!0,Kg=e}am.prototype.request=function(e){"string"==typeof e?(e=arguments[1]||{}).url=arguments[0]:e=e||{},(e=im(this.defaults,e)).method?e.method=e.method.toLowerCase():this.defaults.method?e.method=this.defaults.method.toLowerCase():e.method="get";var t=[om,void 0],n=Promise.resolve(e);for(this.interceptors.request.forEach((function(e){t.unshift(e.fulfilled,e.rejected)})),this.interceptors.response.forEach((function(e){t.push(e.fulfilled,e.rejected)}));t.length;)n=n.then(t.shift(),t.shift());return n},am.prototype.getUri=function(e){return e=im(this.defaults,e),nm(e.url,e.params,e.paramsSerializer).replace(/^\?/,"")},tm.forEach(["delete","get","head","options"],(function(e){am.prototype[e]=function(t,n){return this.request(tm.merge(n||{},{method:e,url:t}))}})),tm.forEach(["post","put","patch"],(function(e){am.prototype[e]=function(t,n,r){return this.request(tm.merge(r||{},{method:e,url:t,data:n}))}}));var lm=eg,cm=qh,um=am,fm=em;function pm(e){var t=new um(e),n=cm(um.prototype.request,t);return lm.extend(n,um.prototype,t),lm.extend(n,t),n}var dm=pm(Fg);dm.Axios=um,dm.create=function(e){return pm(fm(dm.defaults,e))},dm.Cancel=sm(),dm.CancelToken=function(){if(Jg)return Qg;Jg=1;var e=sm();function t(t){if("function"!=typeof t)throw new TypeError("executor must be a function.");var n;this.promise=new Promise((function(e){n=e}));var r=this;t((function(t){r.reason||(r.reason=new e(t),n(r.reason))}))}return t.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},t.source=function(){var e;return{token:new t((function(t){e=t})),cancel:e}},Qg=t}(),dm.isCancel=ug(),dm.all=function(e){return Promise.all(e)},dm.spread=Zg?Xg:(Zg=1,Xg=function(e){return function(t){return e.apply(null,t)}}),Wh.exports=dm,Wh.exports.default=dm;const hm=uu(Uh.exports=Wh.exports),gm=async()=>{if(Nh.isClient())try{console.log("Getting sidecar proxy port from AsecClientNew backend...");const e=await Nh.getSidecarProxyPortIpc();if(console.log("IPC response:",e),e&&e.Data&&"number"==typeof e.Data.ProxyPort){const t=e.Data.ProxyPort;return console.log("Extracted sidecar proxy port:",t),t}return console.warn("Invalid IPC response format, using default port 28080"),28080}catch(e){return console.warn("Failed to get sidecar proxy port from IPC:",e),28080}return 28080};let mm=(()=>{if(Nh.isClient()){const t=globalUrlHashParams?globalUrlHashParams.get("WebUrl"):"";if(t)try{const e=new URL(t);return`${e.protocol}//${e.host}`}catch(e){console.warn("解析 WebUrl 参数失败:",e)}return""}return document.location.protocol+"//"+document.location.host})();const vm=async e=>{if(!e)return!1;try{const t=new URL(e),n=`${t.protocol}//${t.host}`;if(mm=n,Nh.isClient()){const e=await gm();_m.defaults.baseURL=`http://127.0.0.1:${e}`}else _m.defaults.baseURL=n;return!0}catch(t){return console.error("无效的服务器地址:",t),!1}},ym=()=>mm;let bm="";Nh.isClient()?(bm="http://127.0.0.1:28080",gm().then((e=>{const t=`http://127.0.0.1:${e}`;_m.defaults.baseURL=t,console.log("Initial dynamic proxy port set:",e)})).catch((e=>{console.warn("Failed to get initial dynamic proxy port:",e)}))):bm=mm;const _m=hm.create({baseURL:bm,timeout:15e3});_m.interceptors.request.use((async e=>{const t=av();return e.url.match(/(\w+\/){0}\w+/)[0],e.headers={"Content-Type":"application/json",...e.headers},t.token.accessToken&&(e.url.includes("refresh_token")?e.headers.Authorization=`${t.token.tokenType} ${t.token.refreshToken}`:e.headers.Authorization=`${t.token.tokenType} ${t.token.accessToken}`),e}),(e=>(ml({showClose:!0,message:e,type:"error"}),e))),_m.interceptors.response.use((e=>{const t=av();return e.headers["new-token"]&&t.setToken(e.headers["new-token"]),logger.log("请求："+bm+e.config.url,{"响应：":e}),200===e.status||204===e.status||201===e.status||"true"===e.headers.success?e:(ml({showClose:!0,message:e.data.msg||decodeURI(e.headers.msg),type:"error"}),e.data.data&&e.data.data.reload&&(t.token="",localStorage.clear(),Hh.push({name:"Login",replace:!0})),e.data.msg?e.data:e)}),(e=>{const t=av();if(!e.response)return console.error("检测到请求错误",e),e;switch(e.response.status){case 500:if(Nh.isClient()){ml({showClose:!0,message:e.response.data.error,type:"error"});break}yl.confirm(`\n        <p>检测到接口错误${e}</p>\n        <p>错误码<span style="color:red"> 500 </span>：此类错误内容常见于后台panic，请先查看后台日志，如果影响您正常使用可强制登出清理缓存</p>\n        `,"接口报错",{dangerouslyUseHTMLString:!0,distinguishCancelAndClose:!0,confirmButtonText:"清理缓存",cancelButtonText:"取消"}).then((()=>{av().token="",localStorage.clear(),Hh.push({name:"Login",replace:!0})}));break;case 404:ml({showClose:!0,message:e.response.data.error,type:"error"});break;case 401:{t.authFailureLoginOut();const e=window.localStorage.getItem("refresh_times")||0;window.localStorage.setItem("refresh_times",Number(e)+1);break}default:console.log(e.response),ml({showClose:!0,message:e.response.data.errorMessage||e.response.data.error,type:"error"})}return e}));const wm=e=>_m({url:"/auth/user/v1/password",method:"put",data:e});
/*!
 * pinia v2.3.1
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */
let Sm;const xm=e=>Sm=e,Cm=Symbol();function km(e){return e&&"object"==typeof e&&"[object Object]"===Object.prototype.toString.call(e)&&"function"!=typeof e.toJSON}var Em,Am;(Am=Em||(Em={})).direct="direct",Am.patchObject="patch object",Am.patchFunction="patch function";const Tm=()=>{};function Om(e,t,n,r=Tm){e.push(t);const o=()=>{const n=e.indexOf(t);n>-1&&(e.splice(n,1),r())};var i;return!n&&re()&&(i=o,Y&&Y.cleanups.push(i)),o}function Im(e,...t){e.slice().forEach((e=>{e(...t)}))}const jm=e=>e(),Lm=Symbol(),Mm=Symbol();function Pm(e,t){e instanceof Map&&t instanceof Map?t.forEach(((t,n)=>e.set(n,t))):e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const r=t[n],o=e[n];km(o)&&km(r)&&e.hasOwnProperty(n)&&!St(r)&&!ht(r)?e[n]=Pm(o,r):e[n]=r}return e}const Rm=Symbol();const{assign:zm}=Object;function $m(e,t,n,r){const{state:o,actions:i,getters:a}=t,s=n.state.value[e];let l;return l=Dm(e,(function(){s||(n.state.value[e]=o?o():{});const t=function(e){const t=p(e)?new Array(e.length):{};for(const n in e)t[n]=It(e,n);return t}(n.state.value[e]);return zm(t,i,Object.keys(a||{}).reduce(((t,r)=>(t[r]=bt(fi((()=>{xm(n);const t=n._s.get(e);return a[r].call(t,t)}))),t)),{}))}),t,n,r,!0),l}function Dm(e,t,n={},r,o,i){let a;const s=zm({actions:{}},n),l={deep:!0};let c,u,f,p=[],d=[];const h=r.state.value[e];let g;function m(t){let n;c=u=!1,"function"==typeof t?(t(r.state.value[e]),n={type:Em.patchFunction,storeId:e,events:f}):(Pm(r.state.value[e],t),n={type:Em.patchObject,payload:t,storeId:e,events:f});const o=g=Symbol();Kt().then((()=>{g===o&&(c=!0)})),u=!0,Im(p,n,r.state.value[e])}i||h||(r.state.value[e]={}),xt({});const v=i?function(){const{state:e}=n,t=e?e():{};this.$patch((e=>{zm(e,t)}))}:Tm;const y=(t,n="")=>{if(Lm in t)return t[Mm]=n,t;const o=function(){xm(r);const n=Array.from(arguments),i=[],a=[];let s;Im(d,{args:n,name:o[Mm],store:b,after:function(e){i.push(e)},onError:function(e){a.push(e)}});try{s=t.apply(this&&this.$id===e?this:b,n)}catch(l){throw Im(a,l),l}return s instanceof Promise?s.then((e=>(Im(i,e),e))).catch((e=>(Im(a,e),Promise.reject(e)))):(Im(i,s),s)};return o[Lm]=!0,o[Mm]=n,o},b=ut({_p:r,$id:e,$onAction:Om.bind(null,d),$patch:m,$reset:v,$subscribe(t,n={}){const o=Om(p,t,n.detached,(()=>i())),i=a.run((()=>co((()=>r.state.value[e]),(r=>{("sync"===n.flush?u:c)&&t({storeId:e,type:Em.direct,events:f},r)}),zm({},l,n))));return o},$dispose:function(){a.stop(),p=[],d=[],r._s.delete(e)}});r._s.set(e,b);const _=(r._a&&r._a.runWithContext||jm)((()=>r._e.run((()=>(a=ne()).run((()=>t({action:y})))))));for(const x in _){const t=_[x];if(St(t)&&(!St(S=t)||!S.effect)||ht(t))i||(!h||km(w=t)&&w.hasOwnProperty(Rm)||(St(t)?t.value=h[x]:Pm(t,h[x])),r.state.value[e][x]=t);else if("function"==typeof t){const e=y(t,x);_[x]=e,s.actions[x]=t}}var w,S;return zm(b,_),zm(yt(b),_),Object.defineProperty(b,"$state",{get:()=>r.state.value[e],set:e=>{m((t=>{zm(t,e)}))}}),r._p.forEach((e=>{zm(b,a.run((()=>e({store:b,app:r._a,pinia:r,options:s}))))})),h&&i&&n.hydrate&&n.hydrate(b.$state,h),c=!0,u=!0,b}
/*! #__NO_SIDE_EFFECTS__ */function Bm(e,t,n){let r,o;const i="function"==typeof t;function a(e,n){(e=e||(!!(Xo||tn||$r)?Br(Cm,null):null))&&xm(e),(e=Sm)._s.has(r)||(i?Dm(r,t,o,e):$m(r,o,e));return e._s.get(r)}return"string"==typeof e?(r=e,o=i?n:t):(o=e,r=e.id),a.$id=r,a}const Nm=e=>_m({url:"/auth/login/v1/send_sms",method:"post",data:e}),Fm=e=>_m({url:"/auth/login/v1/sms_verify",method:"post",data:e}),Hm=e=>_m({url:"/auth/login/v1/sms_key",method:"post",data:e});
/*! js-cookie v3.0.5 | MIT */
function Vm(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)e[r]=n[r]}return e}var Um=function e(t,n){function r(e,r,o){if("undefined"!=typeof document){"number"==typeof(o=Vm({},n,o)).expires&&(o.expires=new Date(Date.now()+864e5*o.expires)),o.expires&&(o.expires=o.expires.toUTCString()),e=encodeURIComponent(e).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var i="";for(var a in o)o[a]&&(i+="; "+a,!0!==o[a]&&(i+="="+o[a].split(";")[0]));return document.cookie=e+"="+t.write(r,e)+i}}return Object.create({set:r,get:function(e){if("undefined"!=typeof document&&(!arguments.length||e)){for(var n=document.cookie?document.cookie.split("; "):[],r={},o=0;o<n.length;o++){var i=n[o].split("="),a=i.slice(1).join("=");try{var s=decodeURIComponent(i[0]);if(r[s]=t.read(a,s),e===s)break}catch($v){}}return e?r[e]:r}},remove:function(e,t){r(e,"",Vm({},t,{expires:-1}))},withAttributes:function(t){return e(this.converter,Vm({},this.attributes,t))},withConverter:function(t){return e(Vm({},this.converter,t),this.attributes)}},{attributes:{value:Object.freeze(n)},converter:{value:Object.freeze(t)}})}({read:function(e){return'"'===e[0]&&(e=e.slice(1,-1)),e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(e){return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"});const Wm=1,qm=3,Km=2,Gm=3,Qm="broadcast";const Jm=new class{constructor(){this.connection=null,this.defaultPort=50001,this.defaultHost="127.0.0.1",this.messageHandlers=[],this.reconnectTimer=null,this.reconnectDelay=3e3,this.loginSyncHandlers=[],this.logoutSyncHandlers=[],this.isAutoSyncEnabled=!0}createConnection(e={}){const{port:t=this.defaultPort,host:n=this.defaultHost,useSSL:r=this._shouldUseSSL(),timeout:o=2e3,persistent:i=!0,isReconnect:a=!1,onOpen:s,onMessage:l,onError:c,onClose:u}=e;return new Promise(((e,f)=>{if(this.connection){if(this.connection.ws.readyState===WebSocket.OPEN)return logger.log("使用现有的打开连接"),l&&this.addMessageHandler(l),void e(this.connection);if(this.connection.ws.readyState===WebSocket.CONNECTING)return logger.log("连接正在建立中，等待完成"),this.connection.ws.addEventListener("open",(()=>{logger.log("等待的连接已打开"),l&&this.addMessageHandler(l),e(this.connection)})),void this.connection.ws.addEventListener("error",(e=>{logger.log("等待的连接失败",e),f(e)}));logger.log(`移除无效连接，状态: ${this.connection.ws.readyState}`),this.connection=null,this.messageHandlers=[]}const p=`${r?"wss":"ws"}://${n}:${t}`;logger.log(`创建新的WebSocket连接: ${p}`);const d=new WebSocket(p),h={ws:d,url:p,connecting:xt(!0),connected:xt(!1),persistent:i,port:t,host:n,useSSL:r};let g=null;o>0&&(g=setTimeout((()=>{logger.log(`WebSocket连接超时: ${p}`),d.close(),h.connecting.value=!1,f(new Error("WebSocket connection timeout"))}),o)),d.onopen=()=>{logger.log(`WebSocket连接成功: ${p}`),g&&clearTimeout(g),h.connecting.value=!1,h.connected.value=!0,this.connection=h,this._clearReconnectTimer(),logger.log(a?"重连已建立":"连接已建立"),a&&(this._notifyReconnectSuccess(),setTimeout((()=>{this.sendMessage({action:2,msg:{type:"auto_sync_request",timestamp:Date.now()},platform:document.location.hostname})}),1e3)),l&&this.addMessageHandler(l),s&&s(h),e(h)},d.onmessage=e=>{logger.log(`WebSocket收到消息: ${e.data}`),this._handleSyncMessage(e,h),this.messageHandlers.forEach((t=>{try{t(e,h)}catch(n){logger.log("消息处理器执行失败:",n)}}))},d.onerror=e=>{logger.log(`WebSocket连接错误: ${p}`,e),g&&clearTimeout(g),h.connecting.value=!1,h.connected.value=!1,c&&c(e,h),i?this._attemptReconnect(h):f(e)},d.onclose=e=>{logger.log(`WebSocket连接关闭: ${p}`,e.code,e.reason),g&&clearTimeout(g),h.connecting.value=!1,h.connected.value=!1,u&&u(h),i&&1e3!==e.code?this._attemptReconnect(h):(this.connection=null,this.messageHandlers=[],this._clearReconnectTimer())}}))}addMessageHandler(e){this.messageHandlers.push(e)}removeMessageHandler(e){const t=this.messageHandlers.indexOf(e);t>-1&&this.messageHandlers.splice(t,1)}_attemptReconnect(e){this._clearReconnectTimer(),logger.log("WebSocket开始重连"),this.reconnectTimer=setTimeout((()=>{this.createConnection({port:e.port,host:e.host,useSSL:e.useSSL,persistent:e.persistent,isReconnect:!0}).catch((t=>{logger.log("WebSocket重连失败",t),this._attemptReconnect(e)}))}),this.reconnectDelay)}_clearReconnectTimer(){this.reconnectTimer&&(clearTimeout(this.reconnectTimer),this.reconnectTimer=null)}sendMessage(e){if(!this.connection||this.connection.ws.readyState!==WebSocket.OPEN)return logger.log("WebSocket连接不可用"),!1;try{const t="string"==typeof e?e:JSON.stringify(e);return this.connection.ws.send(t),logger.log(`WebSocket发送消息: ${t}`),!0}catch(t){return logger.log("WebSocket发送消息失败:",t),!1}}closeConnection(){this.connection&&(logger.log("关闭WebSocket连接"),this.connection.ws.close(),this.connection=null)}closeAllConnections(){logger.log("关闭所有WebSocket连接"),this._clearReconnectTimer(),this.connection&&this.connection.ws.close(),this.connection=null,this.messageHandlers=[]}getConnection(){return this.connection}getConnectionStatus(){return this.connection?this.connection.ws.readyState:qm}_handleSyncMessage(e,t){try{const t=JSON.parse(e.data);if(0===t.action||1===t.action)if(t.msg){if(t.msg.type===Qm&&t.msg.senderId===Zm)return void logger.log("忽略自己发送的同步消息:",t);if(0===t.action){logger.log("收到登录同步消息:",t);const e={token:t.msg.token,refreshToken:t.msg.refreshToken,realm:"default",timestamp:t.msg.timestamp||Date.now(),source:t.msg.source||"client"};this._triggerLoginSync(e)}else if(1===t.action){logger.log("收到退出同步消息:",t);const e={reason:t.msg.reason,timestamp:t.msg.timestamp||Date.now(),source:t.msg.source||"client"};this._triggerLogoutSync(e)}else logger.log("action异常消息:",t)}else logger.log("msg异常消息:",t);else logger.log("忽略非同步消息:",t)}catch(n){logger.log("不是JSON格式或不是同步消息，忽略:",e.data)}}_triggerLoginSync(e){logger.log("触发登录同步，处理器数量:",this.loginSyncHandlers.length),logger.log("登录数据:",e),0!==this.loginSyncHandlers.length?this.loginSyncHandlers.forEach(((t,n)=>{try{logger.log(`执行登录同步处理器 ${n+1}`),t(e),logger.log(`登录同步处理器 ${n+1} 执行完成`)}catch(r){logger.log(`登录同步处理器 ${n+1} 执行失败:`,r)}})):logger.log("警告：没有注册登录同步处理器")}_triggerLogoutSync(e){this.logoutSyncHandlers.forEach((t=>{try{t(e)}catch(n){logger.log("退出同步处理器执行失败:",n)}}))}addLoginSyncHandler(e){logger.log("添加登录同步处理器，当前数量:",this.loginSyncHandlers.length),this.loginSyncHandlers.push(e),logger.log("登录同步处理器已添加，新数量:",this.loginSyncHandlers.length)}removeLoginSyncHandler(e){const t=this.loginSyncHandlers.indexOf(e);t>-1&&(this.loginSyncHandlers.splice(t,1),logger.log("登录同步处理器已移除，当前数量:",this.loginSyncHandlers.length))}addLogoutSyncHandler(e){this.logoutSyncHandlers.push(e),logger.log("退出同步处理器已添加，当前数量:",this.logoutSyncHandlers.length)}removeLogoutSyncHandler(e){const t=this.logoutSyncHandlers.indexOf(e);t>-1&&(this.logoutSyncHandlers.splice(t,1),logger.log("退出同步处理器已移除，当前数量:",this.logoutSyncHandlers.length))}_notifyReconnectSuccess(){logger.log("WebSocket重连成功，通知外部重新注册处理器"),"undefined"!=typeof window&&window.dispatchEvent(new CustomEvent("websocket-reconnected"))}sendLoginSync(e){const t={action:0,msg:{token:e.token||e.accessToken,refreshToken:e.refreshToken,realm:"default",type:Qm,senderId:Zm,timestamp:Date.now(),source:e.source||"browser"},platform:document.location.hostname};return this.sendMessage(t)}sendLogoutSync(e){const t={action:1,msg:{type:Qm,senderId:Zm,timestamp:Date.now(),reason:e.reason||"user_logout",source:e.source||"browser"},platform:document.location.hostname};return this.sendMessage(t)}_shouldUseSSL(){const e=navigator.platform;return 0===e.indexOf("Mac")||"MacIntel"===e}},Xm=50001,Zm=`page-${Date.now()}-${Math.random().toString(36).substring(2,11)}`;let Ym=null;const ev=async(e=Xm)=>{if(Ym){logger.log(`等待现有连接创建完成，连接ID: ${Zm}`);try{return await Ym}catch(t){Ym=null}}if(Jm.connection){if(Jm.connection.ws.readyState===WebSocket.OPEN)return logger.log("使用现有的打开连接"),Jm.connection;if(Jm.connection.ws.readyState===WebSocket.CONNECTING)return logger.log("连接正在建立中，等待完成"),new Promise(((e,t)=>{Jm.connection.ws.addEventListener("open",(()=>{logger.log("等待的连接已打开"),e(Jm.connection)})),Jm.connection.ws.addEventListener("error",(e=>{logger.log("等待的连接失败",e),t(e)}))}));logger.log(`清理无效连接，状态: ${Jm.connection.ws.readyState}`),Jm.connection=null,Jm.messageHandlers=[]}return logger.log("创建新的页面共享连接"),Ym=Jm.createConnection({port:e,persistent:!0,timeout:5e3}).then((e=>(logger.log("页面共享连接创建成功"),Ym=null,e))).catch((e=>{throw logger.log("页面共享连接创建失败:",e),Ym=null,e})),Ym},tv=(e,t=Xm)=>new Promise(((n,r)=>{let o,i=!1;const a=e=>{try{const t=e.data;logger.log("应用启动响应:",t),i=!0,o&&clearTimeout(o),Jm.removeMessageHandler(a),t.startsWith("Ok")?n(t):t.startsWith("Failed")?r(new Error(t)):n(t)}catch(t){logger.log("解析应用启动响应失败:",t),o&&clearTimeout(o),Jm.removeMessageHandler(a),r(t)}};(async()=>{try{await ev(t),Jm.addMessageHandler(a);const n={action:Gm,msg:e};if(!Jm.sendMessage(n))return Jm.removeMessageHandler(a),void r(new Error("发送启动应用消息失败"));o=setTimeout((()=>{i||(logger.log("应用启动响应超时"),Jm.removeMessageHandler(a),r(new Error("应用启动响应超时")))}),1e4)}catch(n){o&&clearTimeout(o),Jm.removeMessageHandler(a),r(n)}})()})),nv=()=>Zm,rv=e=>{Jm.addLoginSyncHandler(e)},ov=e=>{Jm.addLogoutSyncHandler(e)};const iv=new class{constructor(){this.isInitialized=!1,this.userStore=null,this.loginSyncHandler=null,this.logoutSyncHandler=null,this.initOptions=null,"undefined"!=typeof window&&window.addEventListener("websocket-reconnected",this._handleWebSocketReconnected.bind(this))}async init(e={}){if(this.initOptions=e,this.isInitialized)logger.log("登录同步已经初始化");else try{this.userStore=av(),this.loginSyncHandler=this._handleLoginSync.bind(this),this.logoutSyncHandler=this._handleLogoutSync.bind(this),await(async(e={})=>{const{onLogin:t,onLogout:n,port:r=Xm}=e;logger.log("开始初始化登录同步，选项:",e),logger.log("onLogin 处理器:",typeof t),logger.log("onLogout 处理器:",typeof n);try{return logger.log("尝试建立页面共享连接，端口:",r),await ev(r),logger.log("登录同步连接已建立"),t?(logger.log("添加登录同步处理器"),rv(t),logger.log("登录同步处理器添加完成")):logger.log("警告：没有提供登录同步处理器"),n?(logger.log("添加退出同步处理器"),ov(n),logger.log("退出同步处理器添加完成")):logger.log("警告：没有提供退出同步处理器"),logger.log("登录同步初始化完成"),!0}catch(o){throw logger.log("初始化登录同步失败:",o),o}})({onLogin:this.loginSyncHandler,onLogout:this.logoutSyncHandler,port:e.port}),this.isInitialized=!0,logger.log("登录同步初始化成功")}catch(t){logger.log("登录同步初始化失败:",t)}}_handleWebSocketReconnected(){if(logger.log("收到WebSocket重连事件，重新注册登录同步处理器"),this.loginSyncHandler&&this.logoutSyncHandler)if(this.initOptions)try{logger.log("重新注册登录同步处理器"),rv(this.loginSyncHandler),ov(this.logoutSyncHandler),logger.log("登录同步处理器重新注册完成")}catch(e){logger.log("重新注册登录同步处理器失败:",e)}else logger.log("没有保存的初始化选项，跳过重新注册");else logger.log("处理器未创建，跳过重新注册")}async _handleLoginSync(e){try{if(logger.log("开始处理登录同步:",e),!e)return void logger.log("登录数据为空，跳过处理");if(!e.token)return void logger.log("登录数据中没有token，跳过处理");logger.log("准备设置用户token");const t={accessToken:e.token,refreshToken:e.refreshToken,tokenType:"Bearer"};logger.log("调用 setToken，数据:",t),await this.userStore.setToken(t),logger.log("setToken 完成"),logger.log("开始执行登录后设置"),await this.userStore.handlePostLoginSetup(),logger.log("登录后设置完成"),logger.log("登录同步处理完成")}catch(t){logger.log("处理登录同步失败:",t),logger.log("错误详情:",t.stack)}}async _handleLogoutSync(e){try{logger.log("处理退出同步:",e),await this.userStore.LoginOut(!0),logger.log("退出同步处理完成")}catch(t){logger.log("处理退出同步失败:",t)}await this.userStore.handlePostLogoutSetup()}broadcastLogin(e){if(Jm.getConnectionStatus()!==Wm){const t={action:0,msg:{token:e.accessToken,refreshToken:e.refreshToken,realm:"default"},platform:document.location.hostname};return window.location.href=`asecagent://?web=${JSON.stringify(t)}`,logger.log("登录同步未初始化，无法广播登录信息"),!1}const t={token:e.accessToken,refreshToken:e.refreshToken,userInfo:e.userInfo,timestamp:Date.now(),source:"browser",pageId:nv()};return logger.log("广播登录信息:",t),(e=>Jm.sendLoginSync(e))(t)}broadcastLogout(e={}){if(!this.isInitialized)return logger.log("登录同步未初始化，无法广播退出信息"),!1;const t={...e,timestamp:Date.now(),source:"browser",pageId:nv()};return logger.log("广播退出信息:",t),((e={})=>Jm.sendLogoutSync(e))(t)}destroy(){var e;this.loginSyncHandler&&(e=this.loginSyncHandler,Jm.removeLoginSyncHandler(e),this.loginSyncHandler=null),this.logoutSyncHandler&&((e=>{Jm.removeLogoutSyncHandler(e)})(this.logoutSyncHandler),this.logoutSyncHandler=null),this.isInitialized=!1,this.userStore=null,logger.log("登录同步已销毁")}},av=Bm("user",(()=>{const e=xt(null),t=()=>{const e=window.location.hostname;let t=e;if(!/^(\d{1,3}\.){3}\d{1,3}$/.test(e)&&"localhost"!==e){const n=e.split("."),r=["com.cn","net.cn","org.cn","gov.cn","edu.cn","co.uk","co.jp"],o=n.slice(-2).join("."),i=n.slice(-3).join(".");n.length>=4&&r.includes(o)?t="."+i:n.length>2&&(t="."+n.slice(-2).join(".")),t.split(".").length<3&&(t=e)}return{currentDomain:e,cookieDomain:t,shouldSetDomain:t!==e}},n=xt({id:"",name:"",groupId:"",groupName:"",corpId:"",sourceId:"",phone:"",email:"",avatar:"",roles:[],sideMode:"dark",activeColor:"#4D70FF",baseColor:"#fff"}),r=xt(window.localStorage.getItem("token")||""),o=xt(window.localStorage.getItem("loginType")||""),i=xt(0);try{if(r.value=r.value?JSON.parse(r.value):"",r.value&&r.value.accessToken){const e=Um.get("asec_token");if(e&&e===r.value.accessToken)logger.log("Cookie已存在且与localStorage同步");else{let e=7;r.value.refreshExpireIn&&"number"==typeof r.value.refreshExpireIn?e=Math.max(1,Math.ceil(r.value.refreshExpireIn/86400)):r.value.expireIn&&"number"==typeof r.value.expireIn&&(e=Math.max(1,Math.ceil(r.value.expireIn/86400)));try{const{cookieDomain:n,shouldSetDomain:o}=t(),i={expires:e,secure:"https:"===window.location.protocol,sameSite:"lax",path:"/"};o&&(i.domain=n),Um.set("asec_token",r.value.accessToken,i),logger.log(`初始化时同步token到Cookie，域名: ${n}，过期时间: ${e}天`)}catch(p){logger.error("初始化时设置Cookie失败:",p)}}}}catch($v){logger.log("---清理localStorage中的token---"),window.localStorage.removeItem("token"),Um.remove("asec_token"),r.value=""}const a=e=>{if(logger.log("setToken 被调用，参数:",e),r.value=e,logger.log("token.value 已设置为:",r.value),e&&e.accessToken){logger.log("开始设置Cookie，accessToken:",e.accessToken);let n=7;e.refreshExpireIn&&"number"==typeof e.refreshExpireIn?n=Math.max(1,Math.ceil(e.refreshExpireIn/86400)):e.expireIn&&"number"==typeof e.expireIn&&(n=Math.max(1,Math.ceil(e.expireIn/86400)));try{const{cookieDomain:r,shouldSetDomain:o}=t(),i={expires:n,secure:"https:"===window.location.protocol,sameSite:"lax",path:"/"};o&&(i.domain=r),Um.set("asec_token",e.accessToken,i);Um.get("asec_token")===e.accessToken?logger.log(`认证成功时设置asec_token Cookie，域名: ${r}，过期时间: ${n}天`):logger.warn("Cookie设置可能失败，请检查浏览器Cookie设置")}catch(p){logger.error("设置Cookie时出错:",p)}}else try{Um.remove("asec_token");Um.get("asec_token")?logger.warn("Cookie清除可能失败"):logger.log("已成功清除asec_token Cookie")}catch(p){logger.error("清除Cookie时出错:",p)}},s=e=>{o.value=e},l=async e=>{logger.log("GetUserInfo 开始执行，id:",e);try{const e=await _m({url:"/auth/user/v1/login_user",method:"get"});return logger.log("getUserInfo API 响应:",e),200===e.status?(logger.log("设置用户信息:",e.data.userInfo),t=e.data.userInfo,n.value=t):logger.log("获取用户信息失败，状态码:",e.status),e}catch(p){throw logger.log("GetUserInfo 执行失败:",p),p}var t},c=async()=>{if(f(),Nh.isClient()){const e=Nh.getClientParams();Hh.push({name:"ClientNewLogin",query:e})}else Hh.push({name:"Login",replace:!0})},u=async(t="")=>{var n,r,o,i,a,s,c,u,f,d,h,g,m;logger.log("handlePostLoginSetup 开始执行，auth_type:",t),e.value?logger.log("登录加载实例已存在"):(logger.log("创建登录加载实例"),e.value=hl.service({fullscreen:!0,text:"登录中，请稍候..."})),logger.log("导入路由模块");const{useRouterStore:v}=await Al((()=>Promise.resolve().then((()=>yv))),void 0,import.meta.url),y=(await Al((()=>import("./index.4ca58614.js").then((e=>e.i))),[],import.meta.url)).default;logger.log("开始获取用户信息"),await l(),logger.log("用户信息获取完成"),logger.log("开始设置异步路由");const b=v();await b.SetAsyncRouter();const _=b.asyncRouters;logger.log("异步路由数量:",_.length),_.forEach((e=>{Hh.addRoute(e)})),logger.log("异步路由添加完成");const w=y(window.location.href.replace(/#/g,"&"),!0);let S={},x=null,C=null;try{const e=localStorage.getItem("client_params");if(e){const t=JSON.parse(e);x=t.type,C=t.wp}}catch($v){console.warn("LoginIn: 获取localStorage参数失败:",$v)}if(null==(n=w.query)?void 0:n.oidc_redirect){const t=decodeURIComponent(w.query.oidc_redirect),n=localStorage.getItem("token")||sessionStorage.getItem("token");if(n)try{const r="string"==typeof n?JSON.parse(n):n,o=r.accessToken||r;try{const n=await fetch(t,{method:"GET",headers:{Authorization:"Bearer "+o,"Content-Type":"application/json"},credentials:"include"});if(n.ok)try{const t=await n.json();if(t.RedirectUrl||t.redirectUrl||t.redirect_url){const n=t.RedirectUrl||t.redirectUrl||t.redirect_url;return window.location.href=n,e.value.close(),{oidcRedirect:!0,success:!0}}return window.location.href="/#/dashboard",e.value.close(),{oidcRedirect:!0,success:!0}}catch(E){return window.location.href=t,e.value.close(),{oidcRedirect:!0,success:!0}}else{if(302===n.status||n.redirected)return window.location.href=t,e.value.close(),{oidcRedirect:!0,success:!0};try{const r=await n.text();return r.includes("error?code=access_denied")||r.includes("访问被拒绝")?(window.location.href=t,e.value.close(),{oidcRedirect:!0,success:!0}):(window.location.href="/#/dashboard",e.value.close(),{oidcRedirect:!0,success:!0})}catch(A){return window.location.href="/#/dashboard",e.value.close(),{oidcRedirect:!0,success:!0}}}}catch(p){return"TypeError"===p.name&&p.message.includes("Failed to fetch"),window.location.href=t,e.value.close(),{oidcRedirect:!0,success:!0}}}catch($v){return window.location.href=t,e.value.close(),{oidcRedirect:!0,success:!0}}return window.location.href=t,e.value.close(),{oidcRedirect:!0,success:!0}}if((null==(r=w.query)?void 0:r.redirect)||(null==(o=w.query)?void 0:o.redirect_url)){let n="";return(null==(i=w.query)?void 0:i.redirect)?n=(null==(a=w.query)?void 0:a.redirect.indexOf("?"))>-1?null==(c=w.query)?void 0:c.redirect.substring((null==(s=w.query)?void 0:s.redirect.indexOf("?"))+1):"":(null==(u=w.query)?void 0:u.redirect_url)&&(n=(null==(f=w.query)?void 0:f.redirect_url.indexOf("?"))>-1?null==(h=w.query)?void 0:h.redirect_url.substring((null==(d=w.query)?void 0:d.redirect_url.indexOf("?"))+1):""),n.split("&").forEach((function(e){const t=e.split("=");S[t[0]]=t[1]})),x&&(S.type=x),C&&(S.wp=C),e.value.close(),"qiyewx_oauth"===t||(window.location.href=(null==(g=w.query)?void 0:g.redirect)||(null==(m=w.query)?void 0:m.redirect_url)),!0}S={type:x||w.query.type},(C||w.query.wp)&&(S.wp=C||w.query.wp),w.query.wp&&(S.wp=w.query.wp);const k=globalUrlHashParams.get("ClientType");if(k&&(S.type=k),logger.log("检查是否为客户端环境"),Nh.isClient()){logger.log("客户端环境，跳转到 ClientMain");const e=Nh.getClientParams();logger.log("客户端参数:",e),Hh.push({name:"ClientMain",query:e})}else logger.log("浏览器环境，跳转到 dashboard，参数:",S),await Hh.push({name:"dashboard",query:S}),logger.log("跳转到 dashboard 完成");return logger.log("关闭加载实例"),e.value.close(),logger.log("handlePostLoginSetup 执行完成"),!0},f=async()=>{sessionStorage.clear(),window.localStorage.removeItem("userInfo"),window.localStorage.removeItem("token");try{Um.remove("asec_token");Um.get("asec_token")?logger.warn("Cookie清理可能不完整"):logger.log("已清理所有认证数据，包括Cookie")}catch(p){logger.error("清理Cookie时出错:",p)}r.value=""};return co((()=>r.value),(()=>{window.localStorage.setItem("token",JSON.stringify(r.value))})),{userInfo:n,token:r,loginType:o,tunState:i,ResetUserInfo:(e={})=>{n.value={...n.value,...e}},GetUserInfo:l,LoginIn:async(t,r,i)=>{var l,c,f,d;e.value=hl.service({fullscreen:!0,text:"登录中，请稍候..."});let h=!1;try{if(Nh.isClient())try{const e=await Nh.getClientConfig();e&&(e.security_code&&""!==e.security_code.trim()?(t.security_code=e.security_code,logger.log("登录时使用安全码:",e.security_code)):e.activation_code&&""!==e.activation_code.trim()?(t.activation_code=e.activation_code,h=!0,logger.log("登录时使用激活码:",e.activation_code)):logger.log("未找到安全码或激活码，不传认证码参数"))}catch(p){logger.log("获取客户端配置失败，继续登录流程:",p)}let m="";switch(r){case"qiyewx":case"qiyewx_oauth":case"feishu":case"dingtalk":case"oauth2":case"cas":case"msad":case"ldap":m=await(g=t,_m({url:"/auth/login/v1/user/third",method:"post",data:g})),s(i);break;case"accessory":m=await Fm(t);break;default:m=await(e=>_m({url:"/auth/login/v1/user",method:"post",data:JSON.stringify(e)}))(t),s(i)}if(200===m.status){if(-1===m.data.code||1===(null==(c=null==(l=m.data)?void 0:l.data)?void 0:c.status))return ml({showClose:!0,message:null==(f=m.data)?void 0:f.msg,type:"error"}),e.value.close(),{code:-1};if(window.localStorage.setItem("loginType",o.value),window.localStorage.setItem("refresh_times",0),m.data.data){if(m.data.data.secondary)return e.value.close(),{isSecondary:!0,secondary:m.data.data.secondary,uniqKey:m.data.data.uniqKey,contactType:m.data.data.contactType,qrcode:m.data.data.qrcode,CurrentSecret:m.data.data.CurrentSecret,hasContactInfo:m.data.data.hasContactInfo,secondaryType:m.data.secondaryType,userName:m.data.data.userName,user_id:m.data.data.userID};if(Nh.isClient()){const e={token:m.data.data.accessToken,refreshToken:m.data.data.refreshToken,realm:"default"};await Nh.setLoginStatus({Token:JSON.stringify(e),IsActivationCodeLogin:h}),sessionStorage.setItem("autoConnectChecked","login")}a(m.data.data);try{(e=>{iv.broadcastLogin(e)})({accessToken:m.data.data.accessToken,refreshToken:m.data.data.refreshToken,userInfo:n.value}),logger.log("登录信息已广播到所有页面")}catch(p){logger.log("广播登录信息失败:",p)}}return await u(r)}ml({showClose:!0,message:(null==(d=m.data)?void 0:d.msg)||"服务器不通，请检查网络！",type:"error"}),e.value.close()}catch($v){console.warn("LoginIn: 登录失败:",$v),"Network Error"===$v.message?ml.error("服务器不通，请检查网络！"):ml.error("认证失败，请检查终端时间！"),e.value.close()}var g},LoginOut:async(e=!1)=>{if(e)logger.log("来自广播通知的退出登录，跳过广播发送");else try{t={reason:"user_logout",timestamp:Date.now()},iv.broadcastLogout(t),logger.log("退出信息已广播到所有页面")}catch(p){logger.log("广播退出信息失败:",p)}var t;const n=await _m({url:"/auth/user/v1/logout",method:"post",data:""});if(logger.log("登出res",n),200===n.status)if(-1===n.data.code)ml({showClose:!0,message:n.data.msg,type:"error"});else if(n.data.redirectUrl)if(logger.log("检测到OAuth2登出URL，正在重定向:",n.data.redirectUrl),f(),Nh.isClient()){const e=Nh.getClientParams();Hh.push({name:"ClientNewLogin",query:e})}else window.location.href=n.data.redirectUrl;else await c();else console.warn("LoginOut: 登出失败:",n),await c()},authFailureLoginOut:async()=>{if(f(),Nh.isClient()){const e=Nh.getClientParams();Hh.push({name:"ClientNewLogin",query:e})}else Hh.push({name:"Login",replace:!0}),window.location.reload()},mode:"dark",sideMode:"#273444",setToken:a,setTunState:e=>{i.value=e},baseColor:"#fff",activeColor:"#4D70FF",loadingInstance:e,ClearStorage:f,isTokenValid:()=>{try{if(!r.value||"object"!=typeof r.value)return logger.log("Token不存在或格式错误"),!1;if(!r.value.accessToken||"string"!=typeof r.value.accessToken)return logger.log("AccessToken不存在或格式错误"),!1;const t=r.value.accessToken.split(".");if(3!==t.length)return logger.log("Token格式错误，不是有效的JWT格式"),!1;try{const e=JSON.parse(atob(t[1].replace(/-/g,"+").replace(/_/g,"/")));if(e.exp){const t=Math.floor(Date.now()/1e3);if(e.exp<t)return logger.log("Token已过期",{exp:e.exp,current:t}),!1}if(e.nbf){const t=Math.floor(Date.now()/1e3);if(e.nbf>t)return logger.log("Token尚未生效",{nbf:e.nbf,current:t}),!1}return logger.log("Token验证通过"),!0}catch(e){return logger.log("Token payload解析失败:",e),!1}}catch(p){return logger.log("Token验证过程中发生错误:",p),!1}},handlePostLoginSetup:u,handlePostLogoutSetup:c}})),sv=Object.assign({"../view/app/index.vue":()=>Al((()=>import("./index.54b02c0d.js")),["./index.54b02c0d.js","./index.17e94d33.css"],import.meta.url),"../view/client/download.vue":()=>Al((()=>import("./download.8ce9cf46.js")),["./download.8ce9cf46.js","./system.9d57df9d.js","./download.ba148294.css"],import.meta.url),"../view/client/header.vue":()=>Al((()=>import("./header.165b7e39.js")),["./header.165b7e39.js","./logo.b56ac4ae.js","./header.fe927db8.css"],import.meta.url),"../view/client/index.vue":()=>Al((()=>import("./index.42b1f964.js")),["./index.42b1f964.js","./header.165b7e39.js","./logo.b56ac4ae.js","./header.fe927db8.css","./menu.bb2c1778.js","./menu.8dc27dc1.css","./index.6a2877f0.css"],import.meta.url),"../view/client/login.vue":()=>Al((()=>import("./login.f2ae2ddd.js")),["./login.f2ae2ddd.js","./index.ff864b50.js","./config.a6088397.js","./index.061cf045.css"],import.meta.url),"../view/client/main.vue":()=>Al((()=>import("./main.62dc6be3.js")),["./main.62dc6be3.js","./index.54b02c0d.js","./index.17e94d33.css","./main.8de49897.css"],import.meta.url),"../view/client/menu.vue":()=>Al((()=>import("./menu.bb2c1778.js")),["./menu.bb2c1778.js","./menu.8dc27dc1.css"],import.meta.url),"../view/client/setting.vue":()=>Al((()=>import("./setting.88922075.js")),["./setting.88922075.js","./setting.08e84a26.css"],import.meta.url),"../view/error/index.vue":()=>Al((()=>import("./index.3f331206.js")),["./index.3f331206.js","./index.4f4a44b0.css"],import.meta.url),"../view/error/reload.vue":()=>Al((()=>import("./reload.fd448dca.js")),[],import.meta.url),"../view/layout/aside/asideComponent/asyncSubmenu.vue":()=>Al((()=>import("./asyncSubmenu.f9ede66e.js")),["./asyncSubmenu.f9ede66e.js","./asyncSubmenu.b249e8c2.css"],import.meta.url),"../view/layout/aside/asideComponent/index.vue":()=>Al((()=>import("./index.842f4476.js")),["./index.842f4476.js","./menuItem.2d18ac6b.js","./menuItem.fc1ac8f5.css","./asyncSubmenu.f9ede66e.js","./asyncSubmenu.b249e8c2.css"],import.meta.url),"../view/layout/aside/asideComponent/menuItem.vue":()=>Al((()=>import("./menuItem.2d18ac6b.js")),["./menuItem.2d18ac6b.js","./menuItem.fc1ac8f5.css"],import.meta.url),"../view/layout/aside/historyComponent/history.vue":()=>Al((()=>import("./history.805b54fd.js")),["./history.805b54fd.js","./index-browser-esm.c2d3b5c9.js","./history.a6ae9cc3.css"],import.meta.url),"../view/layout/aside/index.vue":()=>Al((()=>import("./index.859b3a39.js")),["./index.859b3a39.js","./index.842f4476.js","./menuItem.2d18ac6b.js","./menuItem.fc1ac8f5.css","./asyncSubmenu.f9ede66e.js","./asyncSubmenu.b249e8c2.css","./index.40d0fce7.css"],import.meta.url),"../view/layout/bottomInfo/bottomInfo.vue":()=>Al((()=>import("./bottomInfo.95a0e90b.js")),["./bottomInfo.95a0e90b.js","./bottomInfo.844a8d22.css"],import.meta.url),"../view/layout/index.vue":()=>Al((()=>import("./index.e41de280.js")),["./index.e41de280.js","./index.859b3a39.js","./index.842f4476.js","./menuItem.2d18ac6b.js","./menuItem.fc1ac8f5.css","./asyncSubmenu.f9ede66e.js","./asyncSubmenu.b249e8c2.css","./index.40d0fce7.css","./logo.b56ac4ae.js","./index.a3b5b19f.css"],import.meta.url),"../view/layout/main.vue":()=>Al((()=>import("./main.2cf4a453.js")),["./main.2cf4a453.js","./index.54b02c0d.js","./index.17e94d33.css","./main.e37cf67c.css"],import.meta.url),"../view/layout/screenfull/index.vue":()=>Al((()=>import("./index.97fa159d.js")),["./index.97fa159d.js","./index.69bec4e1.css"],import.meta.url),"../view/layout/search/search.vue":()=>Al((()=>import("./search.364de7e2.js")),["./search.364de7e2.js","./index.97fa159d.js","./index.69bec4e1.css","./search.83c559bf.css"],import.meta.url),"../view/layout/setting/index.vue":()=>Al((()=>import("./index.651f6b75.js")),["./index.651f6b75.js","./index.5d8ac823.css"],import.meta.url),"../view/login/clientLogin.vue":()=>Al((()=>import("./clientLogin.8578c8c4.js")),[],import.meta.url),"../view/login/dingtalk/dingtalk.vue":()=>Al((()=>import("./dingtalk.c9acb664.js")),["./dingtalk.c9acb664.js","./secondaryAuth.466a8088.js","./dingtalk.99b25b30.css"],import.meta.url),"../view/login/downloadWin.vue":()=>Al((()=>import("./downloadWin.79752223.js")),["./downloadWin.79752223.js","./logo.b56ac4ae.js","./system.9d57df9d.js","./downloadWin.7c3e0f71.css"],import.meta.url),"../view/login/feishu/feishu.vue":()=>Al((()=>import("./feishu.5784214e.js")),["./feishu.5784214e.js","./secondaryAuth.466a8088.js","./feishu.082a6c41.css"],import.meta.url),"../view/login/index.vue":()=>Al((()=>import("./index.ff864b50.js")),["./index.ff864b50.js","./config.a6088397.js","./index.061cf045.css"],import.meta.url),"../view/login/localLogin/localLogin.vue":()=>Al((()=>import("./localLogin.153245e1.js")),["./localLogin.153245e1.js","./config.a6088397.js","./secondaryAuth.466a8088.js","./localLogin.ec82520e.css"],import.meta.url),"../view/login/oauth2/oauth2.vue":()=>Al((()=>import("./oauth2.7afa2b32.js")),["./oauth2.7afa2b32.js","./secondaryAuth.466a8088.js","./oauth2.62c69fac.css"],import.meta.url),"../view/login/oauth2/oauth2_premises.vue":()=>Al((()=>import("./oauth2_premises.c05495d8.js")),["./oauth2_premises.c05495d8.js","./oauth2_premises.8c48799d.css"],import.meta.url),"../view/login/secondaryAuth/secondaryAuth.vue":()=>Al((()=>import("./secondaryAuth.ec3badbb.js")),["./secondaryAuth.ec3badbb.js","./verifyCode.d8786d24.js","./verifyCode.af46968b.css","./secondaryAuth.7fb1a277.css"],import.meta.url),"../view/login/secondaryAuth/verifyCode.vue":()=>Al((()=>import("./verifyCode.d8786d24.js")),["./verifyCode.d8786d24.js","./verifyCode.af46968b.css"],import.meta.url),"../view/login/serverConfig/serverConfig.vue":()=>Al((()=>import("./serverConfig.fd355c15.js")),["./serverConfig.fd355c15.js","./serverConfig.0abff367.css"],import.meta.url),"../view/login/verify.vue":()=>Al((()=>import("./verify.bbab3465.js")),[],import.meta.url),"../view/login/wx/status.vue":()=>Al((()=>import("./status.8c7548b6.js")),["./status.8c7548b6.js","./status.d41f43c3.css"],import.meta.url),"../view/login/wx/wechat.vue":()=>Al((()=>import("./wechat.6517574c.js")),["./wechat.6517574c.js","./secondaryAuth.466a8088.js","./wechat.94fb94a0.css"],import.meta.url),"../view/login/wx/wx_oauth_callback.vue":()=>Al((()=>import("./wx_oauth_callback.15412cb4.js")),[],import.meta.url),"../view/person/person.vue":()=>Al((()=>import("./person.d204c087.js")),["./person.d204c087.js","./index-browser-esm.c2d3b5c9.js","./person.bb768a09.css"],import.meta.url),"../view/resource/appverify.vue":()=>Al((()=>import("./appverify.b67a22c0.js")),["./appverify.b67a22c0.js","./appverify.1430be1b.css"],import.meta.url),"../view/routerHolder.vue":()=>Al((()=>import("./routerHolder.737b5729.js")),[],import.meta.url)}),lv=Object.assign({}),cv=e=>{e.forEach((e=>{e.component?"view"===e.component.split("/")[0]?e.component=uv(sv,e.component):"plugin"===e.component.split("/")[0]&&(e.component=uv(lv,e.component)):delete e.component,e.children&&cv(e.children)}))};function uv(e,t){return e[Object.keys(e).filter((e=>e.replace("../","")===t))[0]]}const fv=[],pv=[],dv=[],hv={},gv=(e,t)=>{e&&e.forEach((e=>{e.children&&!e.children.every((e=>e.hidden))||"404"===e.name||e.hidden||fv.push({label:e.meta.title,value:e.name}),e.meta.btns=e.btns,e.meta.hidden=e.hidden,!0===e.meta.defaultMenu?pv.push({...e,path:`/${e.path}`}):(t[e.name]=e,e.children&&e.children.length>0&&gv(e.children,t))}))},mv=e=>{e&&e.forEach((e=>{(e.children&&e.children.some((e=>e.meta.keepAlive))||e.meta.keepAlive)&&e.component&&e.component().then((t=>{dv.push(t.default.name),hv[e.name]=t.default.name})),e.children&&e.children.length>0&&mv(e.children)}))},vv=Bm("router",(()=>{const e=xt([]);Mu.on("setKeepAlive",(t=>{const n=[];t.forEach((e=>{hv[e.name]&&n.push(hv[e.name])})),e.value=Array.from(new Set(n))}));const t=xt([]),n=xt(fv),r={};return{asyncRouters:t,routerList:n,keepAliveRouters:e,SetAsyncRouter:async()=>{const e=[{path:"/layout",name:"layout",component:"view/layout/index.vue",meta:{title:"底层layout"},children:[]},{path:"/appverify",name:"appverify",component:"view/resource/appverify.vue",meta:{title:"appverify"},children:[]}],o=(await new Promise((function(e,t){e({code:0,data:{menus:[{ID:9,CreatedAt:"2022-09-21T21:35:16.381+08:00",UpdatedAt:"2022-09-21T21:35:16.381+08:00",parentId:"0",path:"clientLogin",name:"clientLogin",hidden:!0,component:"view/login/clientLogin.vue",sort:1,meta:{keepAlive:!1,defaultMenu:!1,title:"客户端登陆",topTitle:"客户端登陆",icon:"message",closeTab:!1},authoritys:null,menuBtn:null,menuId:"9",children:null,parameters:[],btns:null},{ID:0,CreatedAt:"2022-07-09T19:02:48.587+08:00",UpdatedAt:"2022-07-09T19:02:48.587+08:00",parentId:"0",path:"dashboard",name:"dashboard",hidden:!1,component:"view/layout/main.vue",sort:1,meta:{keepAlive:!1,defaultMenu:!1,title:"应用门户",topTitle:"",icon:"icon-yingyongliebiao",closeTab:!1},authoritys:null,menuBtn:null,menuId:"0",children:null,parameters:[],btns:null},{ID:0,CreatedAt:"2022-07-09T19:02:48.587+08:00",UpdatedAt:"2022-07-09T19:02:48.587+08:00",parentId:"0",path:"download",name:"download",hidden:!1,component:"view/client/download.vue",sort:1,meta:{keepAlive:!1,defaultMenu:!1,title:"客户端下载",topTitle:"客户端下载",icon:"icon-kehuduanxiazai",closeTab:!1},authoritys:null,menuBtn:null,menuId:"0",children:null,parameters:[],btns:null},{ID:8,CreatedAt:"2022-09-21T21:35:16.381+08:00",UpdatedAt:"2022-09-21T21:35:16.381+08:00",parentId:"0",path:"person",name:"person",hidden:!0,component:"view/person/person.vue",sort:1,meta:{keepAlive:!1,defaultMenu:!1,title:"个人信息",topTitle:"个人信息",icon:"message",closeTab:!1},authoritys:null,menuBtn:null,menuId:"8",children:null,parameters:[],btns:null}]},msg:"获取成功"})}))).data.menus;return o&&o.push({path:"404",name:"404",hidden:!0,meta:{title:"",closeTab:!0},component:"view/error/index.vue"},{path:"reload",name:"Reload",hidden:!0,meta:{title:"",closeTab:!0},component:"view/error/reload.vue"}),gv(o,r),e[0].children=o,0!==pv.length&&e.push(...pv),e.push({path:"/:catchAll(.*)",redirect:"/error"}),cv(e),mv(o),t.value=e,n.value=fv,logger.log({asyncRouters:t.value}),logger.log({routerList:n.value}),!0},routeMap:r}})),yv=Object.freeze(Object.defineProperty({__proto__:null,useRouterStore:vv},Symbol.toStringTag,{value:"Module"})),bv=(e,t)=>{const n=/\$\{(.+?)\}/,r=e.match(/\$\{(.+?)\}/g);return r&&r.forEach((r=>{const o=r.match(n)[1],i=t.params[o]||t.query[o];e=e.replace(r,i)})),e};function _v(e,t){if(e){return`${bv(e,t)} - ${Cl.appName}`}return`${Cl.appName}`}let wv=0;const Sv=["Login","Init","ClientLogin","Status","downloadWin","WxOAuthCallback","OAuth2Result","OAuth2Premises","Error"],xv=async e=>{logger.log("----getRouter---");const t=vv();await t.SetAsyncRouter(),await e.GetUserInfo();t.asyncRouters.forEach((e=>{Hh.addRoute(e)}))};async function Cv(e){if(e.matched.some((e=>e.meta.keepAlive))&&e.matched&&e.matched.length>2)for(let t=1;t<e.matched.length;t++){const n=e.matched[t-1];"layout"===n.name&&(e.matched.splice(t,1),await Cv(e)),"function"==typeof n.components.default&&(await n.components.default(),await Cv(e))}}const kv=async(e,t,n)=>{try{const r=vv();await r.SetAsyncRouter(),await e.GetUserInfo();if(r.asyncRouters.forEach((e=>{Hh.addRoute(e)})),e.userInfo){const e=t.query.redirect||t.query.redirect_url;return e?e.startsWith("http")?n?(setTimeout((()=>{window.location.href=e}),100),!1):(window.location.href=e,!0):{path:e,replace:!0}:{name:"dashboard",replace:!0}}return await e.ClearStorage(),null}catch(r){return logger.log("跳转工作台失败:",r),await e.ClearStorage(),null}},Ev=async e=>{logger.log("socket连接开始");try{const t=await((e=Xm,t=2e3)=>new Promise((n=>{const r={action:Km,msg:"",platform:document.location.hostname};let o=null,i=!1;const a=async e=>{var t;try{const r=JSON.parse(e.data);if(logger.log("Token响应:",r),i=!0,o&&clearTimeout(o),Jm.removeMessageHandler(a),null==(t=null==r?void 0:r.msg)?void 0:t.token){const e={accessToken:r.msg.token,refreshToken:r.msg.refreshToken,expiresAt:new Date((new Date).getTime()+6048e5),tokenType:"Bearer"};n(e)}else n(null)}catch(r){logger.log("解析Token响应失败:",r),i=!0,o&&clearTimeout(o),Jm.removeMessageHandler(a),n(null)}};o=setTimeout((()=>{i||(logger.log(`Token获取超时 (${t}ms)，认为没有获取到`),Jm.removeMessageHandler(a),n(null))}),t),ev(e).then((()=>{Jm.addMessageHandler(a),Jm.sendMessage(r)})).catch((()=>{logger.log("Token获取连接错误"),o&&clearTimeout(o),Jm.removeMessageHandler(a),n(null)}))})))();t?(await e.setToken(t),logger.log("Token设置成功")):logger.log("未获取到Token")}catch(t){logger.log("获取Token失败:",t)}},Av=async e=>{logger.log("clientToken 开始");try{const n=await Nh.getChannelStatus();if(logger.log("客户端查询登录结果:",n),!n||!n.Token)return logger.log("客户端未获取到token"),void(await e.setToken(""));const r=JSON.parse(n.Token);if(!r.token)return logger.log("客户端token为空"),void(await e.setToken(""));e.setTunState(n.TunState);const o={accessToken:r.token,refreshToken:r.refreshToken,tokenType:"Bearer"};if(await e.setToken(o),e.isTokenValid())logger.log("客户端token验证通过");else{logger.log("客户端token无效，尝试刷新token");try{const t=await _m({url:"/auth/authz/v1/user/refresh_token",method:"get",donNotShowLoading:!0,timeout:1e3});if(logger.log("刷新token结果:",t),!t||200!==t.status||!t.data||-1===t.data.code)return logger.log("刷新token失败，清空token"),void(await e.setToken(""));{await e.setToken(t.data);const n={token:t.data.accessToken,refreshToken:t.data.refreshToken,realm:"default"};await Nh.setLoginStatus({Token:JSON.stringify(n),IsActivationCodeLogin:!1}),logger.log("刷新后的token验证通过")}}catch(t){return logger.log("刷新token过程中发生错误:",t),void(await e.setToken(""))}}return n}catch(n){throw await e.setToken(""),logger.log("clientToken 执行失败:",n),n}};Hh.beforeEach((async(e,t)=>{if(Nh.isClient())return Tv(e);const n=av();e.meta.matched=[...e.matched],await Cv(e);let r=n.token;const o=window.localStorage.getItem("refresh_times")||0;if("Login"===e.name){if(r){logger.log("Login页面获取到token，跳转到工作台");const t=await kv(n,e,!0);if(null===t)r=null;else{if(!0===t)return!1;if(void 0!==t)return t}}if(logger.log("Login页面异步调用scoketToken：",o),Number(o)<5)try{if(await Ev(n),r=n.token,r&&'""'!==r){logger.log("Login页面从客户端获取到token，跳转到工作台");const t=await kv(n,e,!0);if(null===t)r=null;else if(void 0!==t)return t}}catch(i){logger.log("异步获取token失败:",i)}}return document.title=_v(e.meta.title,e),"WxOAuthCallback"===e.name||"verify"===e.name?document.title="":document.title=_v(e.meta.title,e),logger.log("路由参数：",{whiteList:Sv,to:e,from:t}),(!r||'""'===r)&&Number(o)<5&&"Login"!==e.name&&(await Ev(n),r=n.token),Sv.includes(e.name)?r&&!["downloadWin","Login","WxOAuthCallback","OAuth2Callback","Error"].includes(e.name)?(!wv&&Sv.indexOf(t.name)<0&&(wv++,await xv(n),logger.log("getRouter")),n.userInfo?(logger.log("dashboard"),{name:"dashboard"}):(await n.ClearStorage(),logger.log("强制退出账号"),{name:"Login",query:{redirect:window.location.href}})):(logger.log("直接返回"),!0):(logger.log("不在白名单中:",r),r?!wv&&Sv.indexOf(t.name)<0?(wv++,await xv(n),logger.log("初始化动态路由:",n.token),n.token?(logger.log("返回to"),{...e,replace:!1}):(logger.log("返回login"),{name:"Login",query:{redirect:window.location.href}})):e.matched.length?(logger.log("返回refresh"),!0):(console.log("404:",e.matched),{name:"Error",query:{code:"404",message:"页面未找到"}}):(logger.log("不在白名单中并且未登录的时候"),{name:"Login",query:{redirect:document.location.hash}}))})),Hh.afterEach((()=>{document.title=_v()})),Hh.onError((()=>{console.error("路由发生错误")}));const Tv=async e=>{if(["/client","/client/login","/client/setting","/status","/login","/oauth2_premises","/error"].includes(e.path))return logger.log("客户端直接返回:",e.path),!0;if(!e.path.startsWith("/client")&&window.self===window.top)return logger.log("非客户端路径，使用浏览器打开:",window.location.href),Nh.openAsecPage(window.location.href),!1;const t=av();return await Av(t),((e,t,n)=>{if(e.token)return logger.log("客户端已登录直接返回:",t.path),!0;logger.log("客户端未登录重定向到登录页:",t.path);const r=Nh.getClientParams();if(r.redirect=t.href,n)return{name:"ClientNewLogin",query:r};Hh.push({name:"ClientNewLogin",query:r})})(t,e,!0)};Nh.isClient()&&"undefined"!=typeof window&&(window.addEventListener("clientAuthTokenReceived",(async e=>{if(logger.log("收到客户端认证事件:",e.detail),Nh.isClient())try{const e=av();if(logger.log("客户端未登录，开始执行 clientToken 获取登录状态"),await Av(e),e.token){logger.log("客户端登录状态同步成功，跳转到主页");const e=Nh.getClientParams();Hh.push({name:"ClientMain",query:e})}else logger.log("客户端未获取到有效登录状态")}catch(t){logger.log("处理客户端认证事件失败:",t)}else logger.log("非客户端环境，忽略认证事件")})),logger.log("已注册客户端认证事件监听器"));const Ov={install:e=>{const t=av();e.directive("auth",{mounted:function(e,n){const r=t.userInfo;let o="";switch(Object.prototype.toString.call(n.value)){case"[object Array]":o="Array";break;case"[object String]":o="String";break;case"[object Number]":o="Number";break;default:o=""}if(""===o)return void e.parentNode.removeChild(e);let i=n.value.toString().split(",").some((e=>Number(e)===r.id));n.modifiers.not&&(i=!i),i||e.parentNode.removeChild(e)}})}},Iv={install:e=>{e.directive("click-outside",{mounted(e,t){e._clickOutsideHandler=n=>{e===n.target||e.contains(n.target)||"function"==typeof t.value&&t.value(n)},document.addEventListener("click",e._clickOutsideHandler)},unmounted(e){e._clickOutsideHandler&&(document.removeEventListener("click",e._clickOutsideHandler),delete e._clickOutsideHandler)}})}},jv=function(){const e=ne(!0),t=e.run((()=>xt({})));let n=[],r=[];const o=bt({install(e){xm(o),o._a=e,e.provide(Cm,o),e.config.globalProperties.$pinia=o,r.forEach((e=>n.push(e))),r=[]},use(e){return this._a?n.push(e):r.push(e),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return o}(),Lv={id:"app"};const Mv=Sa({name:"App",created(){}},[["render",function(e,t,n,r,o,i){const a=lr("router-view");return Oo(),Mo("div",Lv,[No(a)])}]]);const Pv=document.documentElement.classList.contains("qt-env");let Rv=null;Pv&&(Rv=hl.service({fullscreen:!0,text:""})),logger.log(navigator.userAgent),logger.log(document.location.href);if(/msie|trident/i.test(navigator.userAgent)){alert("\n    对不起，您正在使用的浏览器版本过低。\n    本网站不支持IE浏览器，请使用现代浏览器（如Chrome、Firefox、Edge等）以获得更好的浏览体验。\n  ")}const zv=wa(Mv);zv.config.productionTip=!1,function(){if("undefined"!=typeof document){const e=document.createElement("div");e.innerHTML='\n<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="display: none;">\n  \x3c!-- 基础图标 --\x3e\n  <symbol id="icon-search" viewBox="0 0 1024 1024">\n    <path d="M1024.876251 512.146286a512 512 0 0 1-512 511.853714A509.513143 509.513143 0 0 1 147.088823 868.644571v82.285715a43.885714 43.885714 0 0 1-87.771429 0v-204.8c0-24.356571 19.602286-43.812571 43.885715-43.812572h204.873142a43.885714 43.885714 0 0 1 0 87.771429H194.046537a421.814857 421.814857 0 0 0 318.902857 146.285714A424.228571 424.228571 0 0 0 937.03168 512v-3.803429a31.158857 31.158857 0 0 1 2.925714-10.752 43.593143 43.593143 0 0 1 40.96-29.184 43.885714 43.885714 0 0 1 40.96 29.257143s2.925714 9.654857 2.925715 14.482286v0.219429h0.073142zM922.403109 321.828571H717.529966a43.812571 43.812571 0 1 1 0-87.625142h114.249143A421.156571 421.156571 0 0 0 512.876251 87.771429a424.082286 424.082286 0 0 0-422.326857 389.12c-0.292571 3.437714-4.827429 20.48-4.827428 20.48a43.446857 43.446857 0 0 1-81.92 0S0.803109 485.156571 0.803109 482.889143c0-5.412571 3.876571-23.186286 4.242285-27.136C33.278537 199.753143 249.342537 0 513.022537 0c143.506286 0 272.822857 59.318857 365.714286 154.624V73.142857a44.032 44.032 0 0 1 87.771428 0v204.8a43.885714 43.885714 0 0 1-44.032 43.885714H922.403109z" fill="currentColor" p-id="14073"></path>\n  </symbol>\n  \n  <symbol id="icon-plus" viewBox="0 0 1024 1024">\n    <path d="M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z M176 474h672q8 0 8 8v60q0 8-8 8H176q-8 0-8-8v-60q0-8 8-8z"/>\n  </symbol>\n  \n  <symbol id="icon-document" viewBox="0 0 1024 1024">\n    <path d="M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0 0 42 42h216v494z"/>\n  </symbol>\n\n  <symbol id="icon-qingkong" viewBox="0 0 1024 1024"><path d="M309.466102 91.72075h-11.337094c6.802257 0 11.337094-4.388553 11.337094-10.971382v10.971382H714.602652v-10.971382c0 6.582829 4.534838 10.971382 11.337094 10.971382H714.602652v91.647607h95.597304V80.749368C810.126814 34.962136 771.361266 0 725.866603 0H298.129008c-47.835224 0-84.26021 37.08327-84.26021 80.749368v102.618989h95.597304V91.72075z m671.302266 91.647607H43.154101c-22.747331 0-43.227243 17.55421-43.227244 41.544965v41.471822c0 6.582829 4.534838 10.898239 11.410237 10.898239h79.579088l34.157568 668.083994A83.528785 83.528785 0 0 0 209.33396 1023.995611h605.327691c45.494662 0 84.113925-34.962136 84.113926-78.628234l31.890149-668.083994h81.919649c6.875399 0 11.410237-4.388553 11.410236-10.971381v-41.39868c0-24.063897-18.212493-41.544965-43.227243-41.544965z m-177.516953 746.785371H220.744197L188.854048 275.015964h648.554935l-34.157568 654.991479z" fill="#686E84" ></path></symbol>\n  \n  \x3c!-- 项目特定图标 --\x3e\n  <symbol id="icon-jieru" viewBox="0 0 1024 1024">\n    <path d="M63.994976 1024C28.669749 1024 0 993.947959 0 956.882069v-257.208609c0-37.06589 28.669749-67.117931 63.994976-67.169128h895.92967c35.325227 0.051196 63.994976 30.103237 63.994976 67.169128v257.208609c0 37.06589-28.669749 67.117931-63.994976 67.117931H63.994976z m277.32863-215.739864v39.932865c0 6.706674 2.508603 13.106171 7.01385 17.867397a23.447759 23.447759 0 0 0 16.945869 7.372222h463.989177a23.447759 23.447759 0 0 0 16.94587-7.372222 25.802774 25.802774 0 0 0 7.065045-17.816201v-39.984061c0-6.655478-2.559799-13.106171-7.065045-17.816202a23.447759 23.447759 0 0 0-16.94587-7.372221H365.283325a24.574071 24.574071 0 0 0-23.959719 25.188423z m-199.152366 19.966432c0.25598 24.727659 19.454473 44.540504 43.004624 44.386916 23.498955-0.153588 42.492664-20.222413 42.390272-44.898876-0.102392-24.676463-19.147297-44.642896-42.697448-44.642895-23.652543 0.153588-42.748644 20.376-42.646252 45.154855z m314.957675-364.003426a57.953851 57.953851 0 1 1 57.032323 47.817047 58.670594 58.670594 0 0 1-57.032323-47.817047z m240.109152-176.882114c19.608061 18.942513 20.376 50.172061 1.689467 69.984906a46.946715 46.946715 0 0 1-35.120443 15.256402 43.209408 43.209408 0 0 1-32.765428-13.720523c-38.294594-37.014694-77.357127-55.496444-116.470857-55.496443h-2.355015c-65.428464 1.638271-115.702917 53.090232-116.470857 53.909368a49.608906 49.608906 0 0 1-84.985329-32.458252 48.840966 48.840966 0 0 1 13.208563-35.069247l1.79186-2.047839C338.047063 274.798012 409.567849 207.475297 507.403369 204.50593c68.602615-2.406211 131.624867 25.751579 189.885894 82.835098z m157.888406-133.570315c19.608061 18.942513 20.324805 50.172061 1.638271 69.984906a48.840966 48.840966 0 0 1-69.370554 1.638272c-87.749912-86.009248-181.080185-128.706697-276.81667-126.198094C355.044129 102.47234 239.341212 222.270936 238.47088 223.858011a50.018474 50.018474 0 0 1-36.246755 15.717166 47.407479 47.407479 0 0 1-33.021407-13.413347 49.864886 49.864886 0 0 1-2.457408-69.984906l2.04784-2.047839 4.249266-4.300463C202.736085 120.698109 330.82843 4.432036 506.533037 0.18277c122.870355-2.457407 240.109151 49.04575 348.644632 153.587943z" p-id="12510"></path>\n  </symbol>\n  \n  <symbol id="icon-shezhi" viewBox="0 0 1024 1024">\n    <path d="M506.9312 0c45.6192 0 96.3072 5.12 136.8576 15.3088-5.0688 61.1328 20.2752 112.0768 70.9632 142.6432 50.688 25.4464 111.5648 20.3776 157.184-10.24 65.8944 66.2528 116.5824 147.7632 141.9264 234.3936-50.688 20.3776-81.1008 71.3216-81.1008 127.3344-5.12 56.064 30.4128 107.008 91.2384 127.3856-30.4128 86.5792-76.032 168.0896-141.9264 234.3424-40.5504-35.6864-106.496-40.7552-157.184-10.24-50.688 30.6176-76.032 86.6304-70.9632 142.6944-45.568 15.2576-96.256 20.3776-141.9264 20.3776-50.688 0-96.3072-10.1888-141.9264-20.3776 5.0688-61.1328-20.2752-112.128-70.9632-142.6432a157.4912 157.4912 0 0 0-157.184 10.1888C76.032 804.864 25.344 723.4048 0 636.8256c50.688-20.3776 81.1008-71.3216 81.1008-127.3856C86.2208 453.4272 50.688 402.432 0 382.1056c25.344-91.6992 76.032-168.1408 136.8576-229.2736 40.5504 35.6864 106.496 40.7552 157.184 10.24 50.688-30.6176 76.032-86.6304 70.9632-142.6944C410.5728 5.12 461.2608 0 506.88 0zM512 307.2a204.8 204.8 0 1 0 0 409.6 204.8 204.8 0 0 0 0-409.6z m0 76.8a128 128 0 1 1 0 256 128 128 0 0 1 0-256z" p-id="12368"></path>\n  </symbol>\n  \n  <symbol id="icon-windows" viewBox="0 0 1072 1024"><path d="M562.91961898 14.336h464.408381v464.408381h-464.408381z" fill="#80BE05" ></path><path d="M52.08990498 14.336h464.408381v464.408381H52.08990498z" fill="#F65312" ></path><path d="M562.91961898 525.165714h464.408381v464.408381h-464.408381z" fill="#FFBB08" ></path><path d="M52.08990498 525.165714h464.408381v464.408381H52.08990498z" fill="#05A3F2" ></path></symbol>\n  \n  <symbol id="icon-mac" viewBox="0 0 1193 1024"><path d="M1070.73554851 1047.96544811H133.43295168C67.81730006 1047.96544811 14.62750067 993.79230811 14.62750067 926.92513109V96.27031673C14.62750067 29.40314075 67.81730006-24.76999925 133.52234591-24.76999925h937.2132026c65.61565164 0 118.805451 54.17314 118.80544996 121.04031598v830.74420963c0 66.77778175-53.18979938 120.95092175-118.89484523 120.95092175z" fill="#E1E1E1" ></path><path d="M1077.9765129-24.76999925H725.22533969a2.41365445 2.41365445 0 0 0-2.234866 1.78789281 1898.652346 1898.652346 0 0 0-66.77778071 256.294377 1877.28703181 1877.28703181 0 0 0-28.60627856 259.60197785c0 0.71515692 0.26818371 1.43031382 0.71515691 1.87728703 0.4469732 0.53636742 1.07273589 0.8939464 1.78789175 0.89394639h106.20080927c11.17432793 0 21.99107687 5.18488787 29.50022497 14.30313928 7.59854231 9.2970409 11.17432793 21.54410367 10.10159203 33.79116645-7.86672707 89.39462026-7.68793759 179.59379271 0.53636743 268.98841297a2.68183816 2.68183816 0 0 0 0.89394639 1.7878928 2.234866 2.234866 0 0 0 1.96668229 0.53636743 469.32175821 469.32175821 0 0 0 223.48655119-116.03421759c11.88948483-10.72735472 29.50022497-8.93946192 39.15484381 4.02275881a32.80782582 32.80782582 0 0 1-3.30760085 43.26699579 526.53431535 526.53431535 0 0 1-252.71859245 129.89038366c-1.25152434 0.26818371-2.05607652 1.60910331-1.87728702 2.95002292 7.77733181 56.31861073 18.77287025 112.01145982 32.80782582 166.81036146 0.26818371 1.16213011 1.25152434 1.96668124 2.32426022 1.9666823h256.7413502c62.75502399 0 113.53116787-56.13982125 113.53116787-125.24186426V98.23699902a129.62219995 129.62219995 0 0 0-32.62903632-86.98096582A106.55838824 106.55838824 0 0 0 1077.9765129-24.76999925z m-134.80708784 365.4452092c0 16.89558322-12.51524649 30.66235508-27.80172743 30.66235507-15.37587517 0-27.89112166-13.76677186-27.89112166-30.66235507V241.00020815c0-16.98497744 12.51524649-30.7517493 27.89112166-30.75175036 15.19708568 0 27.80172743 13.85616609 27.80172743 30.75175036v99.6750018z" fill="#E1E1E1" ></path><path d="M831.96251637 396.01048004V269.60648671c0-21.45470946 11.44251163-38.97605431 25.56686143-38.97605536 14.03495557 0 25.47746721 17.52134591 25.47746722 38.97605536v126.40399333c0 21.45470946-11.44251163 38.97605431-25.47746722 38.97605433-14.1243498 0-25.56686143-17.52134591-25.56686143-38.97605433z" fill="#00B7F9" ></path><path d="M1025.23368672 746.34799777a614.67741127 614.67741127 0 0 1-284.09610443 130.42675109c8.67127821 57.65953034 21.36531417 115.05087696 37.54574049 171.19069925H138.70723378c-32.89722004 0-64.54291574-12.96221968-87.78551695-35.93663823A122.20244608 122.20244608 0 0 1 14.62750067 925.13723933V98.32639324C14.62750067 30.38648138 70.32034976-24.76999925 139.06481276-24.76999925h535.02680407c-31.64569571 84.0309429-56.76558393 170.29675181-75.09148097 258.08226981a1693.40229764 1693.40229764 0 0 0-31.91388048 262.37321127h121.30850074a43.62457476 43.62457476 0 0 1 44.07154795 48.18369995 1349.59058634 1349.59058634 0 0 0 0.89394641 271.75964744 547.18447262 547.18447262 0 0 0 251.82464603-116.57058605c13.40919288-10.72735472 32.89722004-8.93946192 43.80336427 4.02275881a30.39417138 30.39417138 0 0 1-3.75457406 43.26699579z" fill="#00B7F9" ></path><path d="M730.94659499 822.24403092c-0.35757898-2.14547073-0.35757898-4.29094147-0.71515692-6.43641325a1206.82737828 1206.82737828 0 0 1-0.98334061-271.7596464 40.76394711 40.76394711 0 0 0-12.51524649-33.70177222 52.74282617 52.74282617 0 0 0-36.74118936-14.30313928H544.38002265c4.20154726-88.05370171 16.3592158-176.10740237 35.75784769-262.28381601 13.58798237-58.28529303 41.65789352-144.46170667 84.03094394-258.52924301h-73.39298449c-39.69121123 108.7932532-65.88383535 190.94690907-78.66726554 246.55036395a1548.94059097 1548.94059097 0 0 0-37.72452998 290.44312138c-0.35757898 11.97887906 4.46973096 23.2426012 14.30313928 31.91388046 8.93946192 8.22430502 22.08047109 13.14100917 35.66845348 13.14100917h133.82374721c-8.40309449 88.67946335-7.3303586 178.16347888 3.03941713 266.48536326 0.35757898 1.51970805 0.35757898 3.39699507 0.71515691 4.91670415 2.41365445 18.77287025 4.82730995 37.45634626 8.31369923 55.87163859 14.1243498 84.83549508 4.38033673 81.61728846 35.75784874 162.7876037L780.7393993 1047.96544811a1222.91840931 1222.91840931 0 0 1-49.88219853-225.72141719z" fill="#00A0D1" ></path><path d="M1025.68065888 747.51012788a564.25884533 564.25884533 0 0 1-271.75964641 137.22074228 514.46604102 514.46604102 0 0 1-58.82166043 8.04551656 428.46841688 428.46841688 0 0 1-41.83668198 1.96668125c-190.23175217 0-323.78731566-103.07199791-372.50738407-147.23294009a34.32753386 34.32753386 0 0 1-3.84396827-45.59125602 28.33809485 28.33809485 0 0 1 41.83668197-4.20154724c43.62457476 40.04879021 163.59215589 132.39343339 334.51467037 132.39343234 9.20764563 0 20.73955148-0.26818371 34.14874542-1.25152434a502.66595145 502.66595145 0 0 0 300.09774126-131.05251379 28.24869959 28.24869959 0 0 1 41.74728774 4.11215303 34.23813964 34.23813964 0 0 1-3.5757856 45.59125602zM346.63912134 434.98653437c-6.70459696 0-13.23040339-4.11215303-18.05771334-11.44251164a50.95493336 50.95493336 0 0 1-7.41975387-27.53354269V269.60648671c0-13.9455603 4.82730995-26.81838577 12.69403703-33.79116749a18.23650283 18.23650283 0 0 1 25.56686143 0c7.9561213 6.97278068 12.78343124 19.84560614 12.78343018 33.79116749v126.40399333a50.95493336 50.95493336 0 0 1-7.50914809 27.53354269c-4.73791468 7.3303586-11.26372215 11.44251163-18.05771334 11.44251164zM883.00684502 269.60648671v126.40399333c0 21.45470946-11.44251163 38.97605431-25.47746722 38.97605433-14.1243498 0-25.56686143-17.52134591-25.56686143-38.97605433V269.60648671c0-21.45470946 11.44251163-38.97605431 25.56686143-38.97605536 14.03495557 0 25.47746721 17.52134591 25.47746722 38.97605536z" fill="#37474F" ></path></symbol>\n  \n  <symbol id="icon-ios" viewBox="0 0 1024 1024"><path d="M652.60232014 166.32266639c32.97600042-42.48000028 57.96-102.45600028 48.96000014-163.65599972-54.00000014 3.74400042-117.00000014 38.15999972-153.72 82.94399958-33.55199958 40.60799958-61.20000042 100.94400028-50.4 159.55200056 59.04000014 1.87199972 119.80799972-33.33600014 155.15999986-78.84000042zM943.91432014 742.25066709c-23.61599986 52.34399986-34.92000028 75.81600042-65.30400056 122.18399944-42.48000028 64.79999958-102.23999986 145.43999958-176.4 146.01599972-65.73600042 0.72000042-82.80000028-42.91200014-172.07999944-42.4799993-89.28000014 0.57600014-108.00000028 43.344-173.80799986 42.69599972-74.16000014-0.72000042-130.68000028-73.43999972-173.08800042-138.24000028C64.65031958 691.41866625 52.19431986 478.94666667 125.27431986 365.90666639 177.33032014 285.62666709 259.33831986 238.82666625 336.45031986 238.82666625c78.47999972 0 127.79999958 43.12799958 192.81600056 43.12800056 63 0 101.37600014-43.19999972 192.09599916-43.19999972 68.68800028 0 141.47999972 37.44000028 193.32000056 102.23999986-169.92000014 93.24-142.34400042 336.24000014 29.232 401.3279993v-0.07199916z" fill="#000000" ></path></symbol>\n  \n  <symbol id="icon-android" viewBox="0 0 1024 1024"><path d="M908.798299 342.014534a54.125482 54.125482 0 0 1 38.472978 14.847937c10.532526 9.874243 15.798789 21.942763 15.79879 36.278701V694.854165a46.811228 46.811228 0 0 1-15.79879 35.254706 54.125482 54.125482 0 0 1-38.472978 14.847936h-17.773638a54.125482 54.125482 0 0 1-38.472978-14.847936 46.811228 46.811228 0 0 1-15.798789-35.254706V393.141172c0-14.262796 5.266263-26.331316 15.798789-36.278701a54.125482 54.125482 0 0 1 38.472978-14.847937h17.773638z m-800.179427 0a50.760925 50.760925 0 0 1 52.296918 51.199781v301.566707a50.760925 50.760925 0 0 1-52.296918 50.102643H90.845234a54.125482 54.125482 0 0 1-38.472978-14.847937A46.811228 46.811228 0 0 1 36.573466 694.854165V393.141172c0-14.262796 5.266263-26.331316 15.79879-36.278701a54.125482 54.125482 0 0 1 38.472978-14.847937h17.773638zM653.165109 104.813265c11.849092 5.485691 24.137039 12.94623 37.010127 22.527904 12.799945 9.508531 25.161035 20.772482 37.010127 33.718712 11.77595 12.94623 22.235333 27.135884 31.085582 42.422676 8.850248 15.359934 15.286792 31.524436 19.236489 48.566649H217.162406c10.459384-36.132417 27.062741-65.828289 49.73693-88.941334 22.747331-23.186186 45.640947-41.544965 68.607706-55.22262L285.258115 26.111888C283.868406 24.72218 283.795264 22.162191 284.746117 18.431921c0.950853-3.73027 4.388553-7.679967 10.313098-11.702807 5.266263-4.827408 10.239956-7.021684 14.847937-6.729114 4.60798 0.365713 7.533682 1.243423 8.850248 2.559989l51.272923 83.894498A282.037648 282.037648 0 0 1 491.520088 58.806605c44.690094 0 86.454487 9.508531 125.293177 28.671877l52.296919-84.84535c1.316566-1.462851 3.73027-2.121134 7.314254-2.121134 3.657127 0 9.142818 2.047991 16.310787 6.143973 6.582829 3.4377 10.386241 6.509686 11.410237 9.215961 0.950853 2.706274 0.731425 4.754265-0.511998 6.143974L653.165109 104.813265zM377.929717 192.730603a29.03759 29.03759 0 0 0 21.723336-9.142818 31.158724 31.158724 0 0 0 8.92339-22.527904 31.158724 31.158724 0 0 0-8.92339-22.454761 29.03759 29.03759 0 0 0-21.723336-9.21596 29.03759 29.03759 0 0 0-21.650192 9.21596 31.158724 31.158724 0 0 0-8.923391 22.454761c0 8.850248 2.925702 16.38393 8.923391 22.527904 5.851403 6.070831 13.165658 9.142818 21.650192 9.142818z m242.760103-4.095983a29.03759 29.03759 0 0 0 21.723335-9.142818 31.158724 31.158724 0 0 0 8.850248-22.527903 31.158724 31.158724 0 0 0-8.850248-22.454761 29.03759 29.03759 0 0 0-21.723335-9.215961 29.03759 29.03759 0 0 0-21.723336 9.215961 31.158724 31.158724 0 0 0-8.850247 22.454761c0 8.850248 2.925702 16.38393 8.850247 22.527903 5.924546 6.143974 13.165658 9.142818 21.723336 9.142818z m156.890756 127.853166l0.950853 460.0666a53.394057 53.394057 0 0 1-14.262796 37.814695 46.079803 46.079803 0 0 1-35.108421 15.359934h-13.750798v143.139958c0 13.604513-4.754265 25.59989-14.335939 35.839847a46.079803 46.079803 0 0 1-35.035278 15.286791h-28.525592a44.982664 44.982664 0 0 1-34.084425-15.359934 50.834068 50.834068 0 0 1-14.335939-35.766704v-143.139958H407.552448v143.139958c0 13.604513-4.754265 25.59989-14.335939 35.839847a46.079803 46.079803 0 0 1-34.962136 15.286791h-28.671877a44.982664 44.982664 0 0 1-34.011283-15.359934 50.834068 50.834068 0 0 1-14.335938-35.766704v-143.139958h-10.825097a46.079803 46.079803 0 0 1-35.035278-15.359934 53.394057 53.394057 0 0 1-14.262796-37.814695v-460.0666h556.468472z" fill="#00A870" ></path></symbol>\n  \n  <symbol id="icon-xiazai" viewBox="0 0 1024 1024">\n    <path d="M505.7 661a8 8 0 0 0 12.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"/>\n  </symbol>\n\n  \x3c!-- 认证方式图标 --\x3e\n  \x3c!-- 用户相关图标 --\x3e\n  <symbol id="icon-auth-local" viewBox="0 0 1024 1024"><path d="M512 566.3720904c178.78570014 0 335.52253993 59.78791984 422.64443932 149.46979906 42.94159994 38.31711986 70.93621966 85.63545956 72.67039982 143.35888019l0.16516057 6.19349967v113.46491978a41.28999954 41.28999954 0 0 1-41.29000059 41.29000058H57.81000088a41.28999954 41.28999954 0 0 1-41.29000059-41.29000058v-113.46491978c0-60.53113976 28.49010037-109.91397968 72.83556039-149.4698001C176.56004088 626.07742942 333.21429986 566.28950958 512 566.28950958z m13.70827966 125.19127893h-27.49914012c-15.19471949 0-27.49914012 10.15733958-27.49913908 22.62692078v205.12871947c0 12.46958015 12.38700038 22.62691972 27.49913908 22.62691972h27.58172092c15.19471949 0 27.49914012-10.15733958 27.49913908-22.62691972V714.19029011c0-12.46958015-12.38700038-22.62691972-27.49913908-22.62692078z" fill="#0052D9" ></path><path d="M512 250.66875004m-247.73999933 0a247.73999933 247.73999933 0 1 0 495.47999866 0 247.73999933 247.73999933 0 1 0-495.47999866 0Z" fill="#0052D9" ></path></symbol>\n\n  <symbol id="icon-username" viewBox="0 0 1024 1024"><path d="M512 548.544c-148.48 0-268.8-122.752-268.8-274.24S363.52 0 512 0c148.48 0 268.8 122.816 268.8 274.304 0 151.488-120.32 274.24-268.8 274.24z m0-91.392c98.944 0 179.2-81.92 179.2-182.848 0-100.992-80.256-182.848-179.2-182.848-98.944 0-179.2 81.856-179.2 182.848 0 100.992 80.256 182.848 179.2 182.848z m-313.6 274.304a44.352 44.352 0 0 0-31.68 13.376 46.208 46.208 0 0 0-13.12 32.32v45.696c0 12.16 4.736 23.744 13.12 32.32s19.84 13.44 31.68 13.44h627.2a44.352 44.352 0 0 0 31.68-13.44 46.208 46.208 0 0 0 13.12-32.32v-45.696a46.208 46.208 0 0 0-13.12-32.32 44.352 44.352 0 0 0-31.68-13.44H198.4zM198.4 640h627.2c74.24 0 134.4 61.44 134.4 137.152v45.696c0 36.352-14.144 71.296-39.36 96.96A133.056 133.056 0 0 1 825.6 960H198.4C124.16 960 64 898.56 64 822.848v-45.696C64 701.376 124.16 640 198.4 640z" fill="#B3B6C1" p-id="12794"></path></symbol>\n\n  <symbol id="icon-password" viewBox="0 0 1024 1024"><path d="M860.415782 418.879738H163.584218v511.99968h696.831564v-511.99968zM263.104156 325.823796V232.703855C263.104156 104.191935 374.528086 0 512 0c137.471914 0 248.895844 104.191935 248.895844 232.703855v93.119941h149.311907c27.519983 0 49.791969 20.863987 49.791969 46.527971v605.119622a45.055972 45.055972 0 0 1-14.591991 32.89598 51.583968 51.583968 0 0 1-35.199978 13.631991H113.792249a51.583968 51.583968 0 0 1-35.199978-13.631991 45.055972 45.055972 0 0 1-14.591991-32.89598V372.351767c0-25.663984 22.271986-46.527971 49.791969-46.527971H263.040156z m99.583937 0h298.623814V232.703855c0-77.119952-66.815958-139.583913-149.311907-139.583913-82.495948 0-149.311907 62.463961-149.311907 139.583913v93.119941z m99.519938 232.703855h99.583938v232.767854H462.208031V558.527651z" fill="#B3B6C1" p-id="13362"></path></symbol>\n\n  <symbol id="icon-password-hidden" viewBox="0 0 1024 1024"><path d="M512 146.285714c233.618286 0 437.76 142.921143 508.123429 349.257143l2.121142 6.290286 1.755429 5.485714-0.292571 10.313143-1.462858 4.534857C953.929143 731.867429 747.958857 877.714286 512 877.714286 278.381714 877.714286 74.24 734.793143 3.876571 528.457143l-2.048-6.290286-1.462857-4.534857L0 507.318857l1.828571-5.485714C70.070857 292.132571 276.041143 146.285714 512 146.285714z m0 69.046857c-197.558857 0-370.761143 118.491429-434.761143 290.742858l-1.901714 5.266285L75.117714 512l0.219429 0.585143c61.074286 171.666286 230.107429 291.181714 424.594286 295.936l5.997714 0.146286H512c197.558857 0 370.761143-118.491429 434.761143-290.742858l1.901714-5.266285 0.219429-0.658286-0.219429-0.585143c-61.147429-171.666286-230.107429-291.181714-424.667428-295.936l-5.851429-0.146286H512z m0 89.526858c121.197714 0 219.428571 92.745143 219.428571 207.140571 0 114.395429-98.230857 207.140571-219.428571 207.140571S292.571429 626.395429 292.571429 512c0-114.395429 98.230857-207.140571 219.428571-207.140571z m0 69.046857C431.542857 373.613714 366.08 435.346286 365.714286 512c0.365714 76.580571 65.828571 138.386286 146.285714 138.093714 80.457143 0.292571 145.92-61.44 146.285714-138.093714-0.365714-76.580571-65.828571-138.386286-146.285714-138.093714z" fill="#B3B6C1" p-id="13504"></path></symbol>\n\n  <symbol id="icon-password-show" viewBox="0 0 1024 1024"><path d="M820.381989 517.851429l92.745142 128.219428a46.299429 46.299429 0 0 1-12.8 66.267429 51.492571 51.492571 0 0 1-69.485714-12.141715l-105.837714-146.285714c-51.565714 15.140571-106.349714 24.868571-162.962286 28.525714v174.445715c0 26.331429-22.381714 47.689143-49.956571 47.689143-27.574857 0-49.956571-21.357714-49.956572-47.689143V582.363429a747.446857 747.446857 0 0 1-162.962285-28.525715L193.328274 700.196571a51.492571 51.492571 0 0 1-69.485714 12.141715 46.299429 46.299429 0 0 1-12.8-66.267429l92.745143-128.146286C125.37856 481.206857 58.599131 430.445714 10.105417 368.859429a46.226286 46.226286 0 0 1 9.947429-66.779429 51.565714 51.565714 0 0 1 69.997714 9.508571C174.896274 419.547429 334.567131 488.594286 512.084846 488.594286c177.517714 0 337.188571-69.12 422.034285-177.005715a51.565714 51.565714 0 0 1 69.997715-9.508571 46.226286 46.226286 0 0 1 9.947428 66.779429c-48.493714 61.586286-115.273143 112.347429-193.682285 149.065142z" fill="#B3B6C1" p-id="13646"></path></symbol>\n\n  <symbol id="icon-auth-qiyewx" viewBox="0 0 1229 1024"><path d="M713.34752601 849.39828524c-76.68142846 30.67257139-158.47495215 35.78466661-240.26847585 30.67257139-35.78466661-5.11209524-71.56933323-10.22419047-107.35399984-20.44838094-5.11209524 0-10.22419047 0-15.3362857 5.11209524-46.00885708 20.44838092-92.01771415 46.00885708-132.914476 66.45723799-15.33628569 10.22419047-30.67257139 10.22419047-46.00885708 0s-15.33628569-25.56047615-15.3362857-46.00885708c10.22419047-35.78466661 10.22419047-71.56933323 15.3362857-107.35399983 0-5.11209524-5.11209524-10.22419047-5.11209523-15.33628571-51.12095231-51.12095231-92.01771415-102.24190462-122.69028554-168.6991426-51.12095231-122.69028554-40.89676184-245.38057108 30.67257139-357.84666616C145.90495539 113.256572 258.37105046 46.799334 391.28552646 16.12676261S652.00238324 0.79047691 774.69266877 62.13561969c112.46609508 56.23304755 194.25961878 143.13866646 230.0442854 265.828952 15.33628569 46.00885708 20.44838092 92.01771415 15.33628569 138.02657123-25.56047615-25.56047615-56.23304755-30.67257139-86.90561892-15.33628569 0-30.67257139 0-61.34514277-10.22419047-92.01771416-20.44838092-71.56933323-61.34514277-127.80238077-112.46609506-173.81123785-86.90561893-71.56933323-194.25961878-102.24190462-306.72571386-102.24190461-117.5781903 10.22419047-219.82009493 51.12095231-301.61361862 132.914476-66.45723799 66.45723799-102.24190462 148.2507617-97.1298094 245.38057108 5.11209524 81.7935237 40.89676184 148.2507617 92.01771417 204.48380924l40.89676184 40.89676185c20.44838092 15.33628569 25.56047615 30.67257139 15.33628569 51.1209523-5.11209524 20.44838092-10.22419047 46.00885708-15.33628569 66.45723801 0 5.11209524-5.11209524 10.22419047 0 10.22419046 5.11209524 5.11209524 10.22419047 0 10.22419046 0 25.56047615-15.33628569 56.23304755-30.67257139 81.7935237-51.12095231 15.33628569-10.22419047 30.67257139-10.22419047 51.12095232-5.11209523 86.90561893 25.56047615 178.92333309 25.56047615 265.82895199 0 5.11209524 0 10.22419047-5.11209524 10.22419046 5.11209523 10.22419047 30.67257139 25.56047615 51.12095231 56.23304754 66.457238z" fill="#0082EF" ></path><path d="M1224.5570491 747.15638062c0 35.78466661-25.56047615 61.34514277-56.23304755 66.457238-51.12095231 10.22419047-92.01771415 30.67257139-127.80238078 66.45723801-10.22419047 10.22419047-15.33628569 10.22419047-25.56047615 5.11209521-5.11209524-5.11209524-5.11209524-15.33628569 0-25.56047615 35.78466661-35.78466661 56.23304755-81.7935237 66.45723801-127.80238076 5.11209524-35.78466661 40.89676184-56.23304755 76.68142847-56.23304754 40.89676184 5.11209524 66.45723799 35.78466661 66.457238 71.56933323z" fill="#0081EE" ></path><path d="M963.84019231 1023.20952309c-35.78466661 0-66.45723799-25.56047615-71.56933323-56.23304754-5.11209524-51.12095231-30.67257139-92.01771415-66.457238-122.69028555-5.11209524-5.11209524-10.22419047-10.22419047-5.11209523-20.44838091 5.11209524-15.33628569 15.33628569-15.33628569 25.56047616-10.22419047 10.22419047 5.11209524 15.33628569 15.33628569 20.44838092 20.44838093 30.67257139 25.56047615 66.45723799 40.89676184 102.24190462 46.00885708 35.78466661 5.11209524 61.34514277 40.89676184 56.23304755 76.68142845 5.11209524 35.78466661-25.56047615 66.45723799-61.34514279 66.45723801z" fill="#FA6202" ></path><path d="M692.89914508 757.38057108c0-35.78466661 20.44838092-61.34514277 56.23304754-71.56933323 51.12095231-10.22419047 92.01771415-30.67257139 127.80238078-66.45723799 10.22419047-10.22419047 20.44838092-10.22419047 25.56047616 0 5.11209524 5.11209524 5.11209524 15.33628569-5.11209524 25.56047614-30.67257139 30.67257139-51.12095231 66.45723799-61.34514276 112.46609508 0 5.11209524 0 15.33628569-5.11209524 20.44838093-10.22419047 35.78466661-40.89676184 56.23304755-76.68142846 51.1209523-35.78466661-5.11209524-61.34514277-35.78466661-61.34514278-71.56933323z" fill="#FECD00" ></path><path d="M1045.63371601 578.45723799c15.33628569 30.67257139 30.67257139 56.23304755 51.12095231 76.68142847 10.22419047 10.22419047 10.22419047 20.44838092 5.11209524 25.56047615-5.11209524 10.22419047-15.33628569 10.22419047-25.56047615 0-25.56047615-30.67257139-61.34514277-51.12095231-97.12980939-61.34514275-10.22419047-5.11209524-20.44838092-5.11209524-30.6725714-5.11209524-20.44838092-5.11209524-40.89676184-15.33628569-46.00885706-40.89676185-10.22419047-25.56047615-10.22419047-51.12095231 10.22419046-71.56933324 20.44838092-25.56047615 46.00885708-30.67257139 71.56933324-25.56047614 25.56047615 10.22419047 46.00885708 25.56047615 51.1209523 56.23304753 0 15.33628569 5.11209524 30.67257139 10.22419045 46.00885707z" fill="#2CBD00" ></path></symbol>\n\n  <symbol id="icon-auth-feishu" viewBox="0 0 1024 1024"><path d="M891.318857 340.845714c4.900571 0 9.728 0.292571 14.628572 0.804572a409.965714 409.965714 0 0 1 108.836571 30.061714c10.093714 4.534857 12.580571 8.192 3.949714 17.334857-24.868571 26.624-45.494857 57.051429-61.001143 89.965714-16.822857 35.328-35.108571 69.851429-52.297142 105.033143a225.28 225.28 0 0 1-52.150858 69.412572c-53.613714 48.493714-116.150857 68.973714-187.538285 59.099428-81.92-11.337143-159.451429-38.985143-232.740572-75.483428a143.506286 143.506286 0 0 1-10.459428-5.485715 5.339429 5.339429 0 0 1 0.292571-9.216l5.12-2.706285c59.245714-31.670857 108.836571-75.849143 156.525714-122.294857 20.187429-19.529143 39.497143-40.009143 59.904-59.318858A345.014857 345.014857 0 0 1 804.571429 352.256c13.165714-3.218286 26.550857-5.778286 39.789714-8.630857h0.585143l28.233143-2.56" fill="#133C9A" ></path><path d="M317.659429 913.846857c-8.996571-0.512-31.158857-3.584-33.865143-3.949714a536.429714 536.429714 0 0 1-165.083429-48.274286c-30.208-14.116571-59.245714-30.72-88.356571-46.957714-19.163429-10.678857-27.794286-27.282286-27.648-49.883429 0.585143-83.382857 0.585143-166.765714 0-250.148571C2.413714 461.019429 0.731429 407.405714 0 353.718857c0-4.754286 0.731429-9.508571 2.194286-13.897143 3.291429-9.728 9.947429-10.24 16.530285-3.949714 7.606857 7.314286 13.677714 16.237714 21.211429 23.405714 67.291429 66.413714 138.752 127.195429 218.770286 177.225143 45.056 28.891429 91.940571 54.710857 140.434285 77.385143 77.750857 35.328 157.549714 66.486857 241.078858 86.235429 73.874286 17.481143 145.627429 6.436571 205.458285-40.374858 18.285714-15.652571 27.282286-27.062857 48.932572-55.881142a359.862857 359.862857 0 0 1-37.376 72.850285c-13.897143 21.942857-45.348571 51.2-69.193143 74.093715-36.278857 35.108571-83.748571 63.561143-128.292572 87.552-48.566857 26.185143-99.035429 47.104-152.941714 58.514285-27.648 6.948571-67.584 14.848-81.334857 15.579429-2.413714-0.146286-10.678857 1.682286-14.848 1.389714-35.547429 2.633143-57.490286 3.657143-92.891429 0z" fill="#3370FF" ></path><path d="M165.083429 110.518857a52.443429 52.443429 0 0 1 7.460571 0c152.649143 0 304.128 2.486857 456.630857 2.486857 0.292571 0 0.585143 0 0.731429 0.219429 14.189714 12.361143 27.282286 25.746286 39.277714 40.155428 34.450286 34.230857 60.123429 93.622857 77.677714 129.755429 8.777143 25.014857 21.942857 48.859429 28.16 76.8v0.438857c-15.579429 5.046857-30.72 11.190857-45.348571 18.505143-44.178286 22.381714-64.219429 38.765714-100.790857 74.752-19.968 19.529143-37.010286 37.083429-63.488 62.098286a563.346286 563.346286 0 0 1-29.769143 26.916571c-7.021714-12.434286-125.732571-244.589714-364.251429-427.300571" fill="#00D6B9" ></path></symbol>\n\n  <symbol id="icon-auth-sms" viewBox="0 0 1181 1024"><path d="M1030.268048 0H150.600044A151.072391 151.072391 0 0 0 0 150.678769v503.049807a150.36387 150.36387 0 0 0 150.600044 150.127696h52.351819v153.670301a66.679685 66.679685 0 0 0 113.127163 46.841101l201.141198-200.511402h513.047824c39.992066 0 78.252192-15.744908 106.514302-43.928293 28.183385-28.183385 44.085742-66.364787 44.085742-106.120679V150.678769A151.387289 151.387289 0 0 0 1030.268048 0z m-67.781829 396.929128a75.26066 75.26066 0 0 1-75.024486 74.158516 74.315965 74.315965 0 0 1-72.269127-74.158516c0-40.149515 32.040888-72.977648 72.269127-74.158516h0.551072a74.788312 74.788312 0 0 1 74.473414 74.158516z m-298.28728 0a74.315965 74.315965 0 0 1-74.630863 74.001067 74.315965 74.315965 0 0 1-74.237241-74.39469c0.078725-40.936761 33.457929-74.079792 74.552139-74.001067H590.434046a74.001067 74.001067 0 0 1 73.764893 74.39469z m-371.973449 74.158516a74.473414 74.473414 0 0 1-66.286062-36.449462 74.001067 74.001067 0 0 1 0-75.418108 74.473414 74.473414 0 0 1 66.286062-36.449462h0.629797a74.315965 74.315965 0 0 1 71.954229 74.39469 74.315965 74.315965 0 0 1-72.584026 73.922342z" fill="#5BABF2" ></path></symbol>\n\n  <symbol id="icon-auth-dingtalk" viewBox="0 0 1024 1024"><path d="M573.7 252.5C422.5 197.4 201.3 96.7 201.3 96.7c-15.7-4.1-17.9 11.1-17.9 11.1-5 61.1 33.6 160.5 53.6 182.8 19.9 22.3 319.1 113.7 319.1 113.7S326 357.9 270.5 341.9c-55.6-16-37.9 17.8-37.9 17.8 11.4 61.7 64.9 131.8 107.2 138.4 42.2 6.6 220.1 4 220.1 4s-35.5 4.1-93.2 11.9c-42.7 5.8-97 12.5-111.1 17.8-33.1 12.5 24 62.6 24 62.6 84.7 76.8 129.7 50.5 129.7 50.5 33.3-10.7 61.4-18.5 85.2-24.2L565 743.1h84.6L603 928l205.3-271.9H700.8l22.3-38.7c0.3 0.5 0.4 0.8 0.4 0.8S799.8 496.1 829 433.8l0.6-1h-0.1c5-10.8 8.6-19.7 10-25.8 17-71.3-114.5-99.4-265.8-154.5z" fill="#1296DB" ></path></symbol>\n\n  <symbol id="icon-auth-msad" viewBox="0 0 1194 1024"><path d="M13.54497354-8.12698413m83.12455822 0l997.49469562 0q83.12455822 0 83.12455823 83.12455823l0 831.24557917q0 83.12455822-83.12455823 83.12455822l-997.49469562 0q-83.12455822 0-83.12455822-83.12455822l0-831.24557917q0-83.12455822 83.12455822-83.12455823Z" fill="#9080FE" ></path><path d="M197.0839978 739.99403784l41.22978031-111.05441016h189.52399238L470.39754395 739.99403784h102.40945575L375.96804605 253.21662578H291.5134957L100.65951018 739.99403784h96.42448762z m199.49893892-193.51397079H268.23861875l63.17466412-170.90409143 65.16965385 170.90409143zM813.03697202 739.99403784a281.79225194 281.79225194 0 0 0 129.09243835-28.92734679c38.07104711-19.28489752 68.24526222-47.3809981 90.43951949-84.12205207 22.11113244-36.82417879 33.24982349-80.46457194 33.24982349-131.00430323 0-50.5397313-11.13869105-94.09699962-33.24982349-130.67180597a217.03822121 217.03822121 0 0 0-90.77201778-83.45705549 285.11723378 285.11723378 0 0 0-128.67681524-28.59484851H650.86095983V739.99403784h162.25913701z m-11.22181486-82.45956165h-54.52971073v-322.52328533h54.52971073c51.86972444 0 91.85263644 14.1311746 120.03186083 42.22727619 28.09610057 28.1792254 42.22727517 67.74651429 42.22727619 118.7018687 0 51.86972444-13.96492597 91.76951162-41.89477791 119.69936355-27.92985194 27.92985194-68.07901257 41.89477689-120.36435911 41.89477689z" fill="#FFFFFF" ></path></symbol>\n\n  <symbol id="icon-auth-ldap" viewBox="0 0 1102 1024"><path d="M217.559779 517.83087c19.534694 0 35.28848-15.753786 35.288479-35.28848V103.270003c0-18.116853 14.651021-32.689105 32.689105-32.689105h515.621402c18.116853 0 32.610336 14.72979 32.610336 32.689105v380.926535c0 4.332291 0.787689 8.664582 2.363068 12.603028 13.863331 36.627551 68.29266 26.545129 68.29266-12.603028V103.270003A103.187295 103.187295 0 0 0 801.079996 0.003938H285.537363a103.187295 103.187295 0 0 0-103.266064 103.187296v379.351156c0 19.534694 15.753786 35.28848 35.28848 35.28848z" fill="#0052D9" p-id="13788"></path><path d="M730.030423 219.296633c0-19.534694-15.753786-35.28848-35.367249-35.288479H389.906193a35.28848 35.28848 0 1 0 0 70.576959h304.83575c19.455925 0 35.367249-15.753786 35.367249-35.28848z m-35.367249 130.992727H389.906193a35.28848 35.28848 0 1 0 0 70.57696h304.83575a35.28848 35.28848 0 1 0 0-70.57696zM34.422021 607.076065H32.452798A32.452798 32.452798 0 0 0 0 639.607632v288.373045c0 17.880547 14.572252 32.452798 32.452798 32.452798h176.678705a32.452798 32.452798 0 0 0 0-64.905596H66.953589V639.607632a32.452798 32.452798 0 0 0-32.452799-32.452798z m508.847274 176.678705c0-56.398552-13.9421-99.406387-41.274918-129.181041-28.908197-31.58634-70.734497-47.497663-126.581667-47.497664H282.307837a32.452798 32.452798 0 0 0-32.452798 32.452798v288.373045c0 17.880547 14.572252 32.452798 32.452798 32.452798h93.104873c55.84717 0 97.752239-15.911323 126.581667-47.576432 27.411587-30.08973 41.353687-73.097565 41.353687-129.023504z m-89.402733 84.912904c-19.219618 18.904543-54.744405 27.254049-84.597828 27.254049h-52.2238V669.776132h52.2238c38.045392 0 67.032358 8.82212 84.597828 26.781435 17.092857 17.565471 25.67867 39.148157 25.678671 78.768928 0 48.994273-10.31873 78.375083-25.678671 93.341179z m181.326072-240.402767l-107.125742 288.373044c-8.034431 21.267611 7.798124 43.795524 30.404806 43.795524h2.599375c13.784562 0 25.993746-8.664582 30.562344-21.582686l23.630678-66.953589h120.673998l23.630678 66.953589a32.452798 32.452798 0 0 0 30.562344 21.582686h1.575378a32.531567 32.531567 0 0 0 30.404807-43.795524l-107.204511-288.294275a32.531567 32.531567 0 0 0-30.483575-21.267611h-18.904543a32.374029 32.374029 0 0 0-30.326037 21.267611z m0.157538 186.997434l38.91185-110.985419h1.417841l38.596774 111.064188H635.428941z m342.723605-208.186276h-109.961424a32.452798 32.452798 0 0 0-32.452798 32.452798v288.373045c0 17.880547 14.572252 32.452798 32.452798 32.452798h2.363068a32.452798 32.452798 0 0 0 32.531567-32.452798v-103.187295h74.042793c83.810139 0 125.715209-36.548783 125.715208-109.252503 0-72.309876-41.90507-108.386045-124.691212-108.386045z m50.569651 145.564979c-11.579032 8.900889-29.774655 13.863331-54.823173 13.863331h-70.892035V666.546606h70.892035c24.418368 0 42.771528 4.41106 54.35056 13.9421 11.579032 8.900889 17.801778 19.692232 17.801777 35.052173 0 18.431929-5.750132 27.726663-17.329164 37.021396z" fill="#0052D9" p-id="13789"></path></symbol>\n\n  <symbol id="icon-auth-oauth2" viewBox="0 0 1024 1024"><path d="M0.000256 148.864v237.1072c68.6592 0 124.3136 56.448 124.3136 126.08 0 69.6576-55.6544 126.1056-124.3136 126.1056v237.1072c0 11.4688 9.1392 20.736 20.4544 20.736H181.760256V128H20.582656A20.7872 20.7872 0 0 0 0.000256 148.864z m1024 236.9792V148.736a20.5568 20.5568 0 0 0-20.4544-20.736H263.577856v767.872h739.968a20.5568 20.5568 0 0 0 20.4544-20.736v-237.1072c-68.6592 0-124.3136-56.448-124.3136-126.08 0-69.6576 55.6544-126.1056 124.3136-126.1056zM727.552256 656.0512a19.456 19.456 0 0 1-19.3536 19.6352H434.995456a19.456 19.456 0 0 1-19.3536-19.6352v-47.6672a19.456 19.456 0 0 1 19.3536-19.6352h273.0752a19.456 19.456 0 0 1 19.3536 19.6352v47.6672h0.128z m0-248.832a19.456 19.456 0 0 1-19.3536 19.6352H434.995456a19.456 19.456 0 0 1-19.3536-19.6352v-47.6672a19.456 19.456 0 0 1 19.3536-19.6352h273.0752a19.456 19.456 0 0 1 19.3536 19.6352v47.6672h0.128z" fill="#8080FF" ></path></symbol>\n\n  <symbol id="icon-auth-fanwei" viewBox="0 0 1024 1024"><path d="M558.6432 63.5904v201.0112l-134.4-131.0464-136.9344 75.1104 60.0064 180.5824-193.0752-106.8288L51.2 339.6352l306.5344 164.0704 96.512-53.4272L407.296 241.664l153.9328 150.1184 109.568-61.1072V0l-112.1536 63.5904z m405.5552 661.12l-178.5088-100.48 183.552-47.9488 1.8432-153.2416-190.4128-39.5776 191.4368-109.6448L972.8 158.1312l-299.008 176.8448-0.8448 108.2368 208.7424 64.6656-210.2528 54.9376-0.5632 123.0848 293.7344 165.376-0.4096-126.5664z m-784.7936 8.7552l178.5088-100.5056-49.1776 178.9952 135.0912 78.1824 130.4576-141.0048 1.6384 216.4736L678.272 1024 670.72 683.0336l-95.744-54.784-161.7664 143.9744 56.3712-205.056-109.056-62.0288-293.7088 165.376 112.5632 62.9504z" fill="#00993E" ></path></symbol>\n\n  <symbol id="icon-auth-zhuyun" viewBox="0 0 1024 1024"><path d="M768.00032 1023.0784H253.261249C113.280484 1022.08 0.256512 908.134429 0.000512 767.769664V256.256192a256.127936 256.127936 0 0 1 74.700781-181.324755A254.719936 254.719936 0 0 1 255.539648 0.000256h510.182273a254.745536 254.745536 0 0 1 180.223955 75.238381 256.127936 256.127936 0 0 1 74.393581 180.991955V767.744064c0 140.108765-112.614372 254.054336-252.339137 255.308736z" fill="#1C8CCD" ></path><path d="M0.000512 383.43696a238.54074 238.54074 0 0 1 203.519949-7.321598c51.558387 24.243194 104.959974 137.267166 133.247967 189.439953a265.292734 265.292734 0 0 1 228.172743-106.163174c123.647969 0 232.703942 134.502366 228.147143 256.230336A152.575962 152.575962 0 0 1 1024.000256 677.196887v91.494377c0 140.646365-113.433572 254.796736-253.695937 255.308736H254.618048a254.745536 254.745536 0 0 1-180.249555-75.238381A256.127936 256.127936 0 0 1 0.000512 767.769664V383.43696z" fill="#FFFFFF" ></path><path d="M501.965187 167.475414c-8.934398 0.2304-17.279996 4.403199-22.809595 11.417597a205.823949 205.823949 0 0 0-41.08799 145.971164 169.446358 169.446358 0 0 0 56.575986 107.519973c10.956797 10.521597 15.999996 15.564796 17.356796 22.425594 2.380799 13.132797 3.763199 26.444793 4.095999 39.80799 0.128 21.913595 2.892799 43.724789 8.217598 64.972784a594.175851 594.175851 0 0 1-101.759975 107.980773 6.886398 6.886398 0 0 0 0-2.739199l4.582399-9.625598c12.287997-25.599994 32.383992-67.711983-6.860798-94.259176a52.377587 52.377587 0 0 0-29.183993-7.321598 626.073443 626.073443 0 0 0-141.491164 34.790391c-5.631999 1.792-10.572797 5.324799-14.131197 10.060797a71.551982 71.551982 0 0 0-7.295998 63.590384c2.175999 8.703998 3.711999 17.587196 4.556799 26.547194 2.739199 26.547193 5.017599 201.77915 4.556799 334.003116h188.927952V950.784018c197.580751-100.198375 306.636723-223.743944 293.427127-332.620717a30.643192 30.643192 0 0 0-20.991995-25.625593 31.411192 31.411192 0 0 0-9.139197 0 30.975992 30.975992 0 0 0-22.809595 10.060797 897.970976 897.970976 0 0 1-202.163149 183.039955 942.130964 942.130964 0 0 0 174.335956-193.561552c7.295998-13.286397 20.991995-32.947192 34.201592-52.633587 13.235197-19.660795 24.191994-34.764791 31.487992-45.747188 18.431995-9.830398 37.478391-18.380795 57.036786-25.599994a374.399906 374.399906 0 0 0 146.047963-84.223979 323.327919 323.327919 0 0 0 78.92478-155.084761 29.798393 29.798393 0 0 0-10.035197-28.825593 29.158393 29.158393 0 0 0-18.252796-5.964798 38.68159 38.68159 0 0 0-10.495997 0 1432.191642 1432.191642 0 0 0-241.40794 133.631966 678.73263 678.73263 0 0 0-72.089582 76.851181 91.750377 91.750377 0 0 0 0-15.564796A444.236689 444.236689 0 0 0 524.800381 177.536212a30.975992 30.975992 0 0 0-22.809594-10.060798z" fill="#FFFFFF" ></path><path d="M261.018047 615.424102c-15.513596 19.660795-4.556799 30.643192 0 69.990383 3.199999 27.903993 5.017599 204.543949 4.556799 337.663915h130.047967c0-31.103992 0-62.668784-2.713599-91.494377a906.802973 906.802973 0 0 0 182.527954-113.919971c78.48958-65.459184 120.47357-134.527966 112.716772-196.300751a958.84776 958.84776 0 0 1-293.427127 242.047939v-61.311985a980.684555 980.684555 0 0 0 220.415945-225.587143c15.052796-28.364793 54.271986-79.15518 67.071983-102.937574 12.774397-23.807994 162.892759-59.494385 204.441549-111.180773a296.319926 296.319926 0 0 0 71.628782-138.188765 1479.14203 1479.14203 0 0 0-230.911942 127.206368 513.407872 513.407872 0 0 0-88.524778 101.119975 747.263813 747.263813 0 0 1-45.619188 80.51198c-8.678398-31.103992 15.974396-65.433584 6.399998-137.267166a416.358296 416.358296 0 0 0-97.663975-197.631951 171.929557 171.929557 0 0 0-33.305592 122.13757c12.774397 85.555179 65.254384 87.859178 73.471981 128.588768 1.4336 38.91199 6.911998 77.542381 16.409596 115.302371a817.510196 817.510196 0 0 1-165.631958 167.910358c0-71.372782 0-71.833582 3.174399-83.276779 13.235197-28.364793 28.313593-52.607987 8.703998-65.433584-19.635195-12.799997-143.769564 32.025592-143.769564 32.025592z" fill="#75BC2E" ></path><path d="M218.112457 3.200255c0 17.843196 0 36.607991 3.6608 55.807986 4.095999 61.798385 8.678398 131.327967 8.678397 185.779154 0 80.99838-6.860798 192.639952-9.599997 240.20474v83.276779c0 10.547197 5.350399 20.351995 14.156796 26.111993 9.369598 5.017599 19.967995 7.398398 30.566393 6.835199a450.790287 450.790287 0 0 0 161.996759-39.80799c11.827197-10.367997 18.508795-25.420794 18.252796-41.16479a100.377575 100.377575 0 0 0-21.887995-66.815983 1312.921272 1312.921272 0 0 1-21.452795-224.639944c0-45.772789 5.939199-149.171163 12.774397-228.787143H255.539648c-12.518397 0.3584-25.011194 1.4336-37.401591 3.199999z" fill="#FFFFFF" ></path><path d="M250.061249 568.268914c17.356796 11.007997 143.743964-19.660795 156.979161-30.643192 13.235197-11.007997 6.399998-50.329587-8.652798-65.433584-15.078396-15.103996-24.191994-172.953557-26.470393-240.66554 0-48.511988 5.913599-150.092762 12.774397-228.787143H247.78285c4.556799 75.955181 12.313597 171.596757 12.313597 243.891139 0 102.937574-10.931197 255.743936-10.931197 262.143935l0.895999 59.494385z" fill="#75BC2E" ></path></symbol>\n\n  <symbol id="icon-auth-paila" viewBox="0 0 1024 1024"><path d="M512 0c282.7776 0 512 229.2224 512 512s-229.2224 512-512 512S0 794.7776 0 512 229.2224 0 512 0z m-89.856 506.5216l-104.1408 0.512c-57.2416 0.256-106.2144 1.0496-108.544 1.5872-3.584 0.8448-4.4288 7.0912-4.608 81.664v11.7248L204.8 608.256 204.8 793.6h82.8928v-198.144h112.4352c55.6544-0.1024 99.968-1.1008 99.968-2.0992 0-1.3056-17.6128-21.12-39.1168-44.3392l-38.8608-42.496zM531.2 230.4256L204.8 230.4v198.144h82.8928v-105.5744l9.8304 10.6752 23.936 26.2144 10.24 11.1872c18.7136 20.48 36.9152 40.2944 45.6192 49.6896 8.5504 9.3952 38.3232 41.472 65.792 71.7056 27.6992 29.9776 64.256 69.888 81.3312 88.3968 17.3568 18.7648 38.3232 41.472 46.8736 50.8416 8.5504 9.3952 20.736 22.6816 27.2128 29.184 6.4768 6.8096 21.504 23.2192 33.664 36.5312 12.1856 13.568 26.6752 29.184 32.384 35.2 5.6832 5.9904 20.736 22.144 33.408 35.968l22.784 25.0368h113.9712l-8.5248-9.6512c-22.272-26.0608-60.3648-68.0448-128.2304-141.568-20.6336-22.272-65.024-70.5536-111.36-121.1136l-13.2864-14.4896-39.3216-43.008a27416.3712 27416.3712 0 0 0-124.3136-135.0656l-11.9296-12.544 341.9136 1.5872 15.5392 7.296c18.4064 8.6016 26.9312 16.4352 39.8848 35.7376 12.6976 18.7648 16.8448 34.4064 15.2832 58.112-2.304 34.432-20.992 63.1296-50.5088 77.9776l-17.3568 8.6016-65.536 0.768c-48.4352 0.5376-65.536 1.5872-65.536 3.9424 0 1.8176 6.7584 10.1632 15.0272 18.7648 8.0384 8.6016 25.3952 27.392 38.0928 41.472l23.296 25.8048 28.4928-1.8176c36.5312-2.0992 57.5232-7.04 81.0752-19.84 25.1392-13.568 38.3488-24.2432 55.9616-44.3136 65.28-75.0848 54.144-197.888-23.808-259.4304-19.968-15.9232-52.608-31.8208-74.624-36.5056-13.568-2.816-51.456-3.7376-223.744-3.8912z" fill="#B62127" ></path></symbol>\n\n  <symbol id="icon-auth-cas" viewBox="0 0 1024 1024"><path d="M743.936 236.0832c65.4848 0 118.6048 51.84 118.6048 115.7632V499.2c0 17.4336-14.4896 31.5904-32.3584 31.5904-41.6768 0-75.4688 32.9728-75.4688 73.6512 0 40.704 33.792 73.6768 75.4688 73.6768 17.8688 0 32.3584 14.1312 32.3584 31.5648v147.3536c0 63.9232-53.12 115.7632-118.6048 115.7632H118.6048C53.0688 972.8 0 920.96 0 857.0368v-147.3536c0-17.4336 14.464-31.5648 32.3584-31.5648 41.6768 0 75.4688-32.9984 75.4688-73.6768 0-40.704-33.792-73.6768-75.4688-73.6768C14.464 530.7648 0 516.608 0 499.2v-147.3536c0-63.9232 53.0944-115.7632 118.6048-115.7632H743.936zM906.368 76.8C971.3152 76.8 1024 128.7168 1024 192.7424v147.5584c0 17.4592-14.3616 31.616-32.0768 31.616-41.344 0-74.88 33.024-74.88 73.7792s33.536 73.7792 74.88 73.7792c17.7152 0 32.0768 14.1568 32.0768 31.616v147.5584c0 61.952-49.28 112.5376-111.3088 115.7632v-496.896c0-71.3472-59.648-129.2032-133.2224-129.2032H168.576C170.9568 126.3104 222.6688 76.8 286.1312 76.8h620.2368zM569.6512 654.2336H312.9344c-17.7408 0-32.128 13.3632-32.128 29.8496 0 16.512 14.3872 29.8752 32.128 29.8752h256.7424c17.7152 0 32.1024-13.3632 32.1024-29.8752 0-16.4864-14.3872-29.8496-32.1024-29.8496z m0-159.2832H312.9344c-17.7408 0-32.128 13.3632-32.128 29.8496 0 16.4864 14.3872 29.8496 32.128 29.8496h256.7424c17.7152 0 32.1024-13.3632 32.1024-29.8496 0-16.4864-14.3872-29.8496-32.1024-29.8496z" fill="#0066CC" ></path></symbol>\n\n  <symbol id="icon-auth-web" viewBox="0 0 1024 1024"><path d="M921.6 102.4H102.4C46.08 102.4 0 148.48 0 204.8v614.4c0 56.32 46.08 102.4 102.4 102.4h819.2c56.32 0 102.4-46.08 102.4-102.4V204.8c0-56.32-46.08-102.4-102.4-102.4zM665.6 819.2H102.4v-204.8h563.2v204.8z m0-256H102.4v-204.8h563.2v204.8z m256 256h-204.8V358.4h204.8v460.8z" fill="#3399FF" ></path></symbol>\n\n  <symbol id="icon-auth-email" viewBox="0 0 1024 1024"><path d="M982.3488 759.552H116.4544c-23.04 0-41.8048-18.8928-41.8048-42.368V257.3312c0-71.4496 57.1904-129.408 127.744-129.408H896.256c70.5536 0 127.744 57.9584 127.744 129.408v459.8528c0 23.4752-18.6368 42.3424-41.6512 42.3424z" fill="#4D88FF" ></path><path d="M315.0336 723.4304h-175.872a18.8416 18.8416 0 0 1-18.7392-19.0208v-416c0-59.7504 47.7952-108.032 106.752-108.032s106.624 48.4352 106.624 108.032v416a18.8416 18.8416 0 0 1-18.7648 19.0208z" fill="#2166CC" ></path><path d="M315.904 723.4304H0V300.7232h333.824v402.7904c0 6.5536-2.0736 10.112-4.5568 13.2352a18.304 18.304 0 0 1-13.3632 6.656z" fill="#F5F5F5" ></path><path d="M0 512.1536v211.2768h315.904a18.432 18.432 0 0 0 13.3632-6.5536 18.0992 18.0992 0 0 0 4.5312-13.2096v-191.5136H0z" fill="#D8D8D8" ></path><path d="M333.824 512L0 723.4304V300.7232z" fill="#E5E5E5" ></path><path d="M561.152 1024a35.9168 35.9168 0 0 1-35.6608-36.096v-228.352h71.1424v228.352A35.584 35.584 0 0 1 561.1264 1024zM465.92 492.9792a20.9408 20.9408 0 0 0 20.8384-21.0944V0.1536h-26.5472a20.9408 20.9408 0 0 0-20.8384 21.0944v450.6368c0 11.7248 9.3952 21.0944 20.8384 21.0944h5.7088z m296.5504-252.416a21.0176 21.0176 0 0 0 20.8384-21.4016V21.0944A20.9408 20.9408 0 0 0 762.496 0h-281.6c-23.168 0-41.7792 19.0208-41.7792 42.3424v155.8528c0 23.4752 18.7648 42.3424 41.7792 42.3424h281.6z" fill="#2166CC" ></path></symbol>\n\n  <symbol id="icon-auth-verify_code" viewBox="0 0 1181 1024"><path d="M1030.268048 0H150.600044A151.072391 151.072391 0 0 0 0 150.678769v503.049807a150.36387 150.36387 0 0 0 150.600044 150.127696h52.351819v153.670301a66.679685 66.679685 0 0 0 113.127163 46.841101l201.141198-200.511402h513.047824c39.992066 0 78.252192-15.744908 106.514302-43.928293 28.183385-28.183385 44.085742-66.364787 44.085742-106.120679V150.678769A151.387289 151.387289 0 0 0 1030.268048 0z m-67.781829 396.929128a75.26066 75.26066 0 0 1-75.024486 74.158516 74.315965 74.315965 0 0 1-72.269127-74.158516c0-40.149515 32.040888-72.977648 72.269127-74.158516h0.551072a74.788312 74.788312 0 0 1 74.473414 74.158516z m-298.28728 0a74.315965 74.315965 0 0 1-74.630863 74.001067 74.315965 74.315965 0 0 1-74.237241-74.39469c0.078725-40.936761 33.457929-74.079792 74.552139-74.001067H590.434046a74.001067 74.001067 0 0 1 73.764893 74.39469z m-371.973449 74.158516a74.473414 74.473414 0 0 1-66.286062-36.449462 74.001067 74.001067 0 0 1 0-75.418108 74.473414 74.473414 0 0 1 66.286062-36.449462h0.629797a74.315965 74.315965 0 0 1 71.954229 74.39469 74.315965 74.315965 0 0 1-72.584026 73.922342z" fill="#5BABF2" ></path></symbol>\n\n  <symbol id="icon-auth-zhezhendingscan" viewBox="0 0 1024 1024"><path d="M577.28 17.4848l330.0352 190.592A131.1488 131.1488 0 0 1 972.8 321.664v380.672a131.1488 131.1488 0 0 1-65.4848 113.5872L577.28 1006.4896a130.4832 130.4832 0 0 1-130.56 0L116.6848 815.9232A131.1488 131.1488 0 0 1 51.2 702.336v-380.672c0-46.8736 24.96-90.1888 65.4848-113.5872L446.72 17.5104a130.4832 130.4832 0 0 1 130.56 0V17.4848z m-58.7264 198.8608c-133.376 0-256.9216 39.168-357.888 105.728-7.2192 167.4496 56.************ 190.2848 475.8528a662.4768 662.4768 0 0 0 154.7264 115.3536 660.3008 660.3008 0 0 0 180.5056-128.512c129.92-129.8944 194.************ 190.72-462.4128-101.0432-66.7392-224.7424-106.0096-358.3488-106.0096z m-199.6288 79.232s128.0768 58.8288 215.6544 91.0336c87.6032 32.3328 163.7632 48.6912 153.9072 90.4192a90.4448 90.4448 0 0 1-5.8112 15.0272h0.0768l-0.384 0.6144c-16.9216 36.4032-61.0816 107.776-61.0816 107.776a1.792 1.792 0 0 0-0.256-0.4352l-12.9024 22.5792h62.2336l-118.************ 27.008-108.0832h-48.9472l16.9728-71.4752c-16.6144 4.096-33.0496 8.832-49.3312 14.1312 0 0-26.0864 15.36-75.0848-29.5168 0 0-33.0752-29.312-13.9008-36.608 8.1408-3.0976 39.552-7.0656 64.3072-10.4192 33.408-4.5568 54.016-6.912 54.016-6.912s-103.04 1.5104-127.488-2.304c-24.448-3.8656-55.4496-44.9024-62.0544-80.9472 0 0-10.1888-19.7888 21.9648-10.4192 32.1536 9.3696 165.376 36.48 165.376 36.48s-173.2608-53.4016-184.7808-66.432c-11.52-13.056-33.92-71.1168-31.0016-106.8032 0 0 1.28-8.8832 10.3168-6.5024h-0.0256z" fill="#4DADFF" ></path></symbol>\n\n  <symbol id="icon-auth-zhezhendingmobile" viewBox="0 0 1024 1024"><path d="M577.28 17.4848l330.0352 190.592A131.1488 131.1488 0 0 1 972.8 321.664v380.672a131.1488 131.1488 0 0 1-65.4848 113.5872L577.28 1006.4896a130.4832 130.4832 0 0 1-130.56 0L116.6848 815.9232A131.1488 131.1488 0 0 1 51.2 702.336v-380.672c0-46.8736 24.96-90.1888 65.4848-113.5872L446.72 17.5104a130.4832 130.4832 0 0 1 130.56 0V17.4848z m-58.7264 198.8608c-133.376 0-256.9216 39.168-357.888 105.728-7.2192 167.4496 56.************ 190.2848 475.8528a662.4768 662.4768 0 0 0 154.7264 115.3536 660.3008 660.3008 0 0 0 180.5056-128.512c129.92-129.8944 194.************ 190.72-462.4128-101.0432-66.7392-224.7424-106.0096-358.3488-106.0096z m-199.6288 79.232s128.0768 58.8288 215.6544 91.0336c87.6032 32.3328 163.7632 48.6912 153.9072 90.4192a90.4448 90.4448 0 0 1-5.8112 15.0272h0.0768l-0.384 0.6144c-16.9216 36.4032-61.0816 107.776-61.0816 107.776a1.792 1.792 0 0 0-0.256-0.4352l-12.9024 22.5792h62.2336l-118.************ 27.008-108.0832h-48.9472l16.9728-71.4752c-16.6144 4.096-33.0496 8.832-49.3312 14.1312 0 0-26.0864 15.36-75.0848-29.5168 0 0-33.0752-29.312-13.9008-36.608 8.1408-3.0976 39.552-7.0656 64.3072-10.4192 33.408-4.5568 54.016-6.912 54.016-6.912s-103.04 1.5104-127.488-2.304c-24.448-3.8656-55.4496-44.9024-62.0544-80.9472 0 0-10.1888-19.7888 21.9648-10.4192 32.1536 9.3696 165.376 36.48 165.376 36.48s-173.2608-53.4016-184.7808-66.432c-11.52-13.056-33.92-71.1168-31.0016-106.8032 0 0 1.28-8.8832 10.3168-6.5024h-0.0256z" fill="#4DADFF" ></path></symbol>\n\n  \x3c!-- 导航图标 --\x3e\n  <symbol id="icon-chevron-left" viewBox="0 0 1024 1024">\n    <path d="M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 0 0 0 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z" fill="#b3b6c1"/>\n  </symbol>\n\n  <symbol id="icon-chevron-right" viewBox="0 0 1024 1024">\n    <path d="M765.7 486.8L314.9 134.7A7.97 7.97 0 0 0 302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.8 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 0 0 0-50.4z" fill="#b3b6c1"/>\n  </symbol>\n\n  <symbol id="icon-spa" viewBox="0 0 1024 1024"><path d="M522.495456 1.489203L933.482136 121.610971a36.478108 36.478108 0 0 1 26.430629 36.606101c-0.063997 3.455821-14.719236 340.334345-14.719237 475.559331 0 192.054037-375.020546 367.532934-417.706331 386.923928a37.246068 37.246068 0 0 1-32.126334-0.639967c-42.557792-21.118904-416.490395-211.829011-416.490394-386.219964 0-154.231999-14.65524-472.2955-14.84723-475.623328a36.606101 36.606101 0 0 1 26.494625-36.734094L501.568541 1.489203a37.502055 37.502055 0 0 1 20.926915 0zM512 74.893395l-372.652669 108.794356c3.583814 82.363727 13.567296 321.839305 13.567297 450.152649 0 94.26711 214.260885 237.939657 359.789335 312.815772 145.080474-68.732435 358.381409-207.285247 358.381409-312.879769 0-45.245653 1.599917-112.76215 3.711808-182.902512l1.407927-42.237809c3.071841-91.515253 6.655655-180.790621 8.447562-224.948331L512 74.829398zM512 176.00815c94.203113 0 170.679146 75.196099 170.679146 168.119278a168.951236 168.951236 0 0 1-136.568915 164.791452v70.588338h102.394688c16.831127 0 30.718406 11.967379 33.598257 27.646566l0.57597 6.015688a33.91824 33.91824 0 0 1-34.238224 33.662254l-102.330691-0.063997v67.196514H614.394688a33.91824 33.91824 0 0 1 33.534261 33.086284 33.91824 33.91824 0 0 1-33.534261 34.238224H546.110231v33.662253a33.91824 33.91824 0 0 1-33.470264 33.022287 33.91824 33.91824 0 0 1-34.750198-33.022287V508.91888a168.823242 168.823242 0 0 1-136.568915-164.727455C341.320854 251.332242 417.73289 176.00815 512 176.00815z m0 67.26051h-1.663914A101.626728 101.626728 0 0 0 408.773355 344.959385c0.447977 55.677112 46.65358 100.474788 103.226645 100.026811 56.573065 0 102.394688-45.11766 102.394688-100.794771S568.573065 243.204664 512 243.204664z" fill="#686E84" ></path></symbol>\n\n  <symbol id="icon-error" viewBox="0 0 1024 1024"><path d="M512 0a512 512 0 1 1 0 1024A512 512 0 0 1 512 0zM297.6 297.6c-12.8 12.8-12.8 33.472 0 46.272L465.664 512 297.6 680.064a32.768 32.768 0 0 0 46.272 46.336L512 558.272l168.064 168.128a32.704 32.704 0 1 0 46.336-46.336L558.336 512.064l168.128-168.128a32.768 32.768 0 0 0-46.336-46.272L512.064 465.728 343.872 297.6c-12.8-12.8-33.472-12.8-46.272 0z" fill="#FF4D4D" ></path></symbol>\n\n  <symbol id="icon-warning" viewBox="0 0 1024 1024"><path d="M571.134461 546.558967a59.13478 59.13478 0 1 1-118.269561 0V270.852654a59.13478 59.13478 0 1 1 118.269561 0V546.558967z m-59.134781 255.99472a58.878786 58.878786 0 1 1 0.383992-117.757571A58.878786 58.878786 0 0 1 511.99968 802.553687zM511.99968 0.01024a501.749651 501.749651 0 0 0-199.419887 39.935176 509.685488 509.685488 0 0 0-272.634377 272.634377A501.749651 501.749651 0 0 0 0.01024 511.99968a501.749651 501.749651 0 0 0 39.935176 199.419887 509.685488 509.685488 0 0 0 272.634377 272.634377A501.749651 501.749651 0 0 0 511.99968 1023.98912a501.749651 501.749651 0 0 0 199.419887-39.935176 509.685488 509.685488 0 0 0 272.634377-272.634377A501.749651 501.749651 0 0 0 1023.98912 511.99968a501.749651 501.749651 0 0 0-39.935176-199.419887 509.685488 509.685488 0 0 0-272.634377-272.634377A501.749651 501.749651 0 0 0 511.99968 0.01024z" fill="#FFBF00" ></path></symbol>\n\n  <symbol id="icon-success" viewBox="0 0 1024 1024"><path d="M512 0a512 512 0 1 1 0 1024A512 512 0 0 1 512 0z m268.48 349.76a36.288 36.288 0 0 0-51.2 0L442.688 636.352l-147.968-147.84a36.288 36.288 0 0 0-51.2 51.2l171.968 171.968c7.04 7.04 16.32 10.688 25.6 10.688h3.2c9.28 0 18.56-3.584 25.6-10.688l310.592-310.528a36.224 36.224 0 0 0 0-51.392z" fill="#29CC65" ></path></symbol>\n\n  <symbol id="icon-fold" viewBox="0 0 1194 1024"><path d="M1140.394667 108.373333H55.893333a54.186667 54.186667 0 0 1 0-108.373333h1084.586667a54.186667 54.186667 0 0 1 0 108.373333z m0 432.469334H473.6a54.186667 54.186667 0 0 1 0-108.373334h666.88a54.186667 54.186667 0 0 1 0 108.373334zM55.978667 867.157333h1084.586666a54.186667 54.186667 0 0 1 0 108.458667H55.808a54.186667 54.186667 0 0 1 0-108.373333z m215.210666-108.714666L0 487.253333l271.104-271.104v542.208z" fill="currentColor" opacity=".7" ></path></symbol>\n\n  <symbol id="icon-expand" viewBox="0 0 1194 1024"><path d="M54.272 108.373333h1084.501333a54.186667 54.186667 0 0 0 0-108.373333H54.186667a54.186667 54.186667 0 0 0 0 108.373333z m0 432.469334H721.066667a54.186667 54.186667 0 0 0 0-108.373334H54.186667a54.186667 54.186667 0 0 0 0 108.373334z m1084.501333 326.314666H54.186667a54.186667 54.186667 0 0 0 0 108.458667h1084.586666a54.186667 54.186667 0 0 0 0-108.373333z m-215.210666-108.714666L1194.666667 487.253333l-271.104-271.104v542.208z" fill="currentColor" opacity=".7" ></path></symbol>\n\n  <symbol id="icon-logout" viewBox="0 0 1024 1024"><path d="M829.44 107.52C952.32 209.92 1024 353.28 1024 512c0 281.6-230.4 512-512 512s-512-230.4-512-512c0-158.72 71.68-302.08 194.56-399.36 20.48-15.36 56.32-15.36 71.68 10.24 15.36 20.48 10.24 51.2-10.24 66.56C158.72 271.36 102.4 389.12 102.4 512c0 225.28 184.32 409.6 409.6 409.6 225.28 0 409.6-184.32 409.6-409.6 0-128-56.32-240.64-153.6-322.56-20.48-15.36-25.6-51.2-10.24-71.68 15.36-20.48 51.2-25.6 71.68-10.24zM512 0c30.72 0 51.2 23.917714 51.2 59.757714v358.4c0 35.84-20.48 59.684571-51.2 59.684572-30.72 0-51.2-23.844571-51.2-59.684572v-358.4C460.8 23.917714 481.28 0 512 0z" fill="currentColor" p-id="12338"></path></symbol>\n\n  <symbol id="icon-person" viewBox="0 0 1024 1024"><path d="M512 548.544c-148.48 0-268.8-122.752-268.8-274.24S363.52 0 512 0c148.48 0 268.8 122.816 268.8 274.304 0 151.488-120.32 274.24-268.8 274.24z m0-91.392c98.944 0 179.2-81.92 179.2-182.848 0-100.992-80.256-182.848-179.2-182.848-98.944 0-179.2 81.856-179.2 182.848 0 100.992 80.256 182.848 179.2 182.848z m-313.6 274.304a44.352 44.352 0 0 0-31.68 13.376 46.208 46.208 0 0 0-13.12 32.32v45.696c0 12.16 4.736 23.744 13.12 32.32s19.84 13.44 31.68 13.44h627.2a44.352 44.352 0 0 0 31.68-13.44 46.208 46.208 0 0 0 13.12-32.32v-45.696a46.208 46.208 0 0 0-13.12-32.32 44.352 44.352 0 0 0-31.68-13.44H198.4zM198.4 640h627.2c74.24 0 134.4 61.44 134.4 137.152v45.696c0 36.352-14.144 71.296-39.36 96.96A133.056 133.056 0 0 1 825.6 960H198.4C124.16 960 64 898.56 64 822.848v-45.696C64 701.376 124.16 640 198.4 640z" fill="currentColor" p-id="12196"></path></symbol>\n\n  <symbol id="icon-yingyongliebiao" viewBox="0 0 1024 1024"><path d="M26.112 996.864c16.704 17.344 39.36 27.136 63.04 27.136h234.56c49.344 0 88.896-41.472 91.904-91.904V685.44c0-51.008-39.936-92.352-89.152-92.416H89.152C39.872 592.96 0 634.368 0 685.376v246.144c0 24.448 9.344 48 26.112 65.344z m50.88-321.28a16.96 16.96 0 0 1 12.16-5.248H326.4c9.6 0 17.28 8 17.28 17.92v246.08a17.664 17.664 0 0 1-17.28 17.856H89.152a17.6 17.6 0 0 1-17.28-17.92v-246.016c0-4.8 1.92-9.28 5.12-12.672z m12.16-183.488h234.624c49.28 0 89.152-41.344 89.152-92.416V156.288C412.8 105.28 372.928 64 323.712 64H89.152C39.872 64 0 105.408 0 156.416v243.264C0 450.752 39.936 492.16 89.152 492.16z m-12.16-348.352a16.96 16.96 0 0 1 12.16-5.184h234.56c9.536 0 17.28 8 17.28 17.92v243.2c0 9.92-7.68 17.92-17.28 17.92H89.152a17.6 17.6 0 0 1-17.28-17.92v-243.2c0-4.8 1.92-9.344 5.12-12.672v-0.064zM542.72 942.72c0 28.48 20.928 49.984 48.32 49.984h384.64c27.392 0 48.256-21.504 48.256-49.92a50.048 50.048 0 0 0-13.888-35.648 46.464 46.464 0 0 0-34.304-14.336H590.976a46.464 46.464 0 0 0-34.368 14.336 50.048 50.048 0 0 0-13.824 35.584z m0.128-265.856c0 28.416 20.864 49.92 48.192 49.92h384.64c27.456 0 48.256-21.696 48.256-49.984a50.048 50.048 0 0 0-13.824-35.648 46.464 46.464 0 0 0-34.368-14.4H591.04c-27.456 0-48.192 21.76-48.192 50.112h0.064z m0-265.92c0 28.416 20.864 50.048 48.192 50.048h384.64c27.456 0 48.256-21.76 48.256-50.112a50.048 50.048 0 0 0-13.888-35.648 46.464 46.464 0 0 0-34.304-14.336H591.04c-27.456 0-48.192 21.76-48.192 50.048h0.064z m-0.128-265.792c0 28.288 20.992 49.92 48.32 49.92h384.64c27.456 0 48.256-21.696 48.256-50.048a50.048 50.048 0 0 0-13.888-35.648 46.464 46.464 0 0 0-34.304-14.336H591.04a46.656 46.656 0 0 0-34.432 14.4 50.24 50.24 0 0 0-13.888 35.712h0.064z" fill="currentColor" p-id="11912"></path></symbol>\n\n  <symbol id="icon-kehuduanxiazai" viewBox="0 0 1024 1024"><path d="M554.88384 504.704L470.85184 583.552a52.224 52.224 0 0 0-4.864 70.336L242.88384 444.48a52.224 52.224 0 0 1-3.2-72.32 48.768 48.768 0 0 1 70.208-3.328l145.536 136.576V51.2c0-28.288 22.272-51.2 49.728-51.2 27.52 0 49.728 22.912 49.728 51.2v453.504l144.768-135.872a48.768 48.768 0 0 1 70.272 3.328 52.224 52.224 0 0 1-3.2 72.32L537.92384 659.2a48.768 48.768 0 0 1-71.936-5.312L242.88384 444.48a52.224 52.224 0 0 1-3.2-72.32 48.768 48.768 0 0 1 70.208-3.328l145.536 136.576V51.2c0-28.288 22.272-51.2 49.728-51.2 27.52 0 49.728 22.912 49.728 51.2v453.504z m-13.504 151.744z m16-151.744v116.48-116.48z m-91.392 149.12l6.4 6.656-6.4-6.592z m91.328-26.944v0zM1024.00384 896.96c0.512 69.376-53.44 126.08-120.768 127.04H120.77184C53.44384 1023.04-0.50816 966.336 0.00384 897.088v-297.6a51.392 51.392 0 0 1 24.576-44.352 48.384 48.384 0 0 1 49.6-0.256c15.36 9.024 24.96 25.856 25.024 44.16v298.112c0 12.544 9.664 22.784 21.76 23.04h781.76a20.736 20.736 0 0 0 15.104-6.592l0.064-0.128a23.808 23.808 0 0 0 6.592-16.384v-297.6a51.072 51.072 0 0 1 49.664-52.032h0.448c27.52 0.512 49.536 23.68 49.408 51.968v297.6z" fill="currentColor" p-id="12054"></path></symbol>\n</svg>\n',e.style.display="none",document.body.insertBefore(e,document.body.firstChild)}}(),function(){["dragstart","drag","dragenter","dragover","dragleave","drop","dragend"].forEach((e=>{document.addEventListener(e,bl,!0)}));const e=new MutationObserver((e=>{e.forEach((e=>{e.addedNodes.forEach((e=>{if(e.nodeType===Node.ELEMENT_NODE){e.querySelectorAll('img, svg, .base-icon, .app-icon, .avatar, [draggable="true"]').forEach((e=>{_l(e)})),e.matches&&e.matches('img, svg, .base-icon, .app-icon, .avatar, [draggable="true"]')&&_l(e)}}))}))}));e.observe(document.body,{childList:!0,subtree:!0}),function(e,t=document){t.querySelectorAll(e).forEach((e=>{_l(e)}))}('img, svg, .base-icon, .app-icon, .avatar, [draggable="true"]'),logger.log("全局防拖拽功能已初始化")}(),Pv&&(document.addEventListener("wheel",(function(e){e.ctrlKey&&e.preventDefault()}),{passive:!1}),document.addEventListener("keydown",(function(e){!e.ctrlKey||"+"!==e.key&&"-"!==e.key&&"="!==e.key&&"0"!==e.key||e.preventDefault()})),document.addEventListener("gesturestart",(function(e){e.preventDefault()})),document.addEventListener("gesturechange",(function(e){e.preventDefault()})),document.addEventListener("gestureend",(function(e){e.preventDefault()}))),zv.use(kl).use(jv).use(Ov).use(Iv).use(Hh).use(xl).mount("#app_container"),setTimeout((async()=>{try{await(e={port:50001},iv.init(e)),logger.log("登录同步功能已启动")}catch(t){logger.log("登录同步功能启动失败:",t)}var e}),10),Pv?setTimeout((()=>{logger.log("QT环境：开始页面切换过渡效果"),requestAnimationFrame((()=>{const e=document.getElementById("app_container"),t=document.getElementById("app_loading");e&&t?(logger.log("QT环境：找到页面元素，开始切换"),e.style.display="block",requestAnimationFrame((()=>{logger.log("QT环境：开始淡入淡出效果"),e.classList.add("fade-in"),t.classList.add("fade-out"),setTimeout((()=>{t&&t.parentNode&&(logger.log("QT环境：移除加载页面"),t.parentNode.removeChild(t),Rv&&Rv.close())}),1)}))):console.error("QT环境：未找到页面元素:",{appContainer:e,appLoading:t})}))}),1):logger.log("浏览器环境：跳过加载页面过渡效果");export{vv as $,hr as A,Al as B,Nh as C,_m as D,su as E,xo as F,yl as G,fr as H,an as I,Bi as J,Mu as K,hl as L,ml as M,ut as N,ma as O,nr as P,ym as Q,Av as R,Kt as S,tv as T,vm as U,Hi as V,ki as W,cu as X,gr as Y,bv as Z,Sa as _,e as __vite_legacy_guard,Oo as a,Fn as a0,Um as a1,Br as a2,ya as a3,gu as a4,gm as a5,wm as a6,Mo as b,fi as c,Bo as d,U as e,av as f,hm as g,lr as h,Ho as i,No as j,Po as k,Vo as l,Fm as m,B as n,Zn as o,Nm as p,Dn as q,xt as r,Hm as s,J as t,lu as u,co as v,on as w,Dr as x,Et as y,ur as z};
