/*! 
 Build based on gin-vue-admin 
 Time : 1754836337000 */
import{_ as e,V as t,r as a,v as n,h as o,a as s,k as u,w as l,b as r,e as c,l as i,d as f,t as m,F as d,Y as v}from"./index.57c3624b.js";const p={key:0,class:"gva-subMenu"},b=e(Object.assign({name:"AsyncSubmenu"},{props:{routerInfo:{default:function(){return null},type:Object},isCollapse:{default:function(){return!1},type:Boolean},theme:{default:function(){return{}},type:Object}},setup(e){t((e=>({"13bf2c47":x.value,"4f287234":I.value})));const b=e,h=a(b.theme.activeBackground),I=a(b.theme.activeText),x=a(b.theme.normalText);return n((()=>b.theme),(()=>{h.value=b.theme.activeBackground,I.value=b.theme.activeText,x.value=b.theme.normalText})),(t,a)=>{const n=o("base-sub-menu");return s(),u(n,{ref:"subMenu",index:e.routerInfo.name},{title:l((()=>[e.isCollapse?(s(),r(d,{key:1},[e.routerInfo.meta.icon?(s(),r("i",{key:0,class:c(["iconfont",e.routerInfo.meta.icon])},null,2)):i("",!0),f("span",null,m(e.routerInfo.meta.title),1)],64)):(s(),r("div",p,[e.routerInfo.meta.icon?(s(),r("i",{key:0,class:c(["iconfont",e.routerInfo.meta.icon])},null,2)):i("",!0),f("span",null,m(e.routerInfo.meta.title),1)]))])),default:l((()=>[v(t.$slots,"default",{},void 0,!0)])),_:3},8,["index"])}}}),[["__scopeId","data-v-ea897612"]]);export{b as default};
