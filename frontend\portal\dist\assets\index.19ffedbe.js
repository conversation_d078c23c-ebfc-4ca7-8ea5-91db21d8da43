/*! 
 Build based on gin-vue-admin 
 Time : 1754834760000 */
import{u as e,E as a,f as o,$ as t,r as s,v as l,P as n,K as r,h as c,a as u,b as i,j as d,w as f,W as v,F as m,A as p,y as b,k as h,l as g,n as k}from"./index.0f69a27d.js";import x from"./index.f59f2de8.js";import"./menuItem.a9d1cab1.js";import"./asyncSubmenu.edbb23cb.js";const y=Object.assign({name:"Aside"},{setup(y){const j=e(),T=a(),F=o(),M=t(),w=s({}),B=()=>{switch(F.sideMode){case"#fff":w.value={background:"#fff",activeBackground:"#4D70FF",activeText:"#fff",normalText:"#333",hoverBackground:"rgba(64, 158, 255, 0.08)",hoverText:"#333"};break;case"#273444":w.value={background:"#263444",activeBackground:"#4D70FF",activeText:"#fff",normalText:"#fff",hoverBackground:"rgba(64, 158, 255, 0.08)",hoverText:"#fff"}}};B();const q=s("");l((()=>j),(()=>{q.value=j.meta.activeName||j.name}),{deep:!0}),l((()=>F.sideMode),(()=>{B()}));const O=s(!1);q.value=j.meta.activeName||j.name,r.on("collapse",(e=>{console.log("aside 收到 collapse 事件:",e),O.value=e})),n((()=>{r.off("collapse")}));const _=(e,a,o,t)=>{var s,l;const n={},r={};(null==(s=M.routeMap[e])?void 0:s.parameters)&&(null==(l=M.routeMap[e])||l.parameters.forEach((e=>{"query"===e.type?n[e.key]=e.value:r[e.key]=e.value}))),e!==j.name&&(e.indexOf("http://")>-1||e.indexOf("https://")>-1?window.open(e):T.push({name:e,query:n,params:r}))};return(e,a)=>{const o=c("base-menu"),t=c("base-scrollbar");return u(),i("div",{class:"aside-container",style:k({background:b(F).sideMode})},[d(t,{height:"100%"},{default:f((()=>[d(v,{duration:{enter:800,leave:100},mode:"out-in",name:"el-fade-in-linear"},{default:f((()=>[d(o,{collapse:O.value,"collapse-transition":!1,"default-active":q.value,"background-color":w.value.background,"active-text-color":w.value.active,mode:"vertical","unique-opened":!0,onSelect:_},{default:f((()=>[(u(!0),i(m,null,p(b(M).asyncRouters[0].children,(e=>(u(),i(m,null,[e.hidden?g("",!0):(u(),h(x,{key:e.name,"is-collapse":O.value,"router-info":e,theme:w.value},null,8,["is-collapse","router-info","theme"]))],64)))),256))])),_:1},8,["collapse","default-active","background-color","active-text-color"])])),_:1})])),_:1})],4)}}});export{y as default};
