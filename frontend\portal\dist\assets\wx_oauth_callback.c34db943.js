/*! 
 Build based on gin-vue-admin 
 Time : 1754836337000 */
import{u as r,E as e,f as a,r as s,o as t,x as i,a as c,b as n,L as o,M as u}from"./index.57c3624b.js";const l=Object.assign({name:"WxOAuthCallback"},{setup(l){const y=r(),d=e(),p=a(),{code:b,state:f,redirect_url:h}=y.query,m=s(Array.isArray(f)?f[0]:f),x=s("");return t((async()=>{const r=o.service({fullscreen:!0,text:"登录中，请稍候..."});try{const r={clientId:"client_portal",grantType:"implicit",redirect_uri:"",idpId:m.value,authWeb:{authWebCode:Array.isArray(b)?b[0]:b}};!0===await p.LoginIn(r,"qiyewx_oauth",m.value)?await d.push({name:"verify",query:{redirect_url:h}}):u.error("登录失败，请重试")}catch(e){console.error("登录过程出错:",e),u.error("登录过程出错，请重试")}finally{r.close()}})),i("userName",x),(r,e)=>(c(),n("span"))}});export{l as default};
