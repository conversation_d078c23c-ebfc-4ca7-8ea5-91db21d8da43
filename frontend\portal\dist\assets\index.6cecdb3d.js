/*! 
 Build based on gin-vue-admin 
 Time : 1754836337000 */
import a from"./header.1ab4c6b2.js";import s from"./menu.1c571563.js";import{C as t,h as e,a as i,b as o,j as l,d as r,k as n}from"./index.57c3624b.js";import"./logo.b56ac4ae.js";const c={class:"layout-page"},u={class:"layout-wrap"},m={id:"layoutMain",class:"layout-main"},d=Object.assign({name:"Client"},{setup:d=>(t.initIpcClient(),(t,d)=>{const p=e("router-view");return i(),o("div",c,[l(a),r("div",u,[l(s),r("div",m,[(i(),n(p,{key:t.$route.fullPath,style:{height:"100%",display:"flex","flex-direction":"column"}}))])])])})});export{d as default};
