/*! 
 Build based on gin-vue-admin 
 Time : 1754879545000 */
import{a as e,b as o}from"./index.ab3e73c8.js";const g=Object.assign({name:"ClientLogin"},{setup(g){var l=(e=>{logger.log("1");var o=new RegExp("(^|&)"+e+"=([^&]*)(&|$)","i");logger.log(2);var g=window.location.search.substr(1).match(o);return logger.log(g),null!=g?decodeURI(g[2]):null})("type");logger.log("type"),logger.log(l);const t=window.localStorage.getItem("token")||"";return logger.log(11),logger.log(t),t&&"client"===l&&(window.location.href=`asecagent://?token=${t}`),(g,l)=>(e(),o("span"))}});export{g as default};
