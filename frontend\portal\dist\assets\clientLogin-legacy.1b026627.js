/*! 
 Build based on gin-vue-admin 
 Time : 1754834760000 */
System.register(["./index-legacy.7db06653.js"],(function(e,n){"use strict";var t,o;return{setters:[function(e){t=e.a,o=e.b}],execute:function(){e("default",Object.assign({name:"ClientLogin"},{setup:function(e){var n=function(e){logger.log("1");var n=new RegExp("(^|&)"+e+"=([^&]*)(&|$)","i");logger.log(2);var t=window.location.search.substr(1).match(n);return logger.log(t),null!=t?decodeURI(t[2]):null}("type");logger.log("type"),logger.log(n);var g=window.localStorage.getItem("token")||"";return logger.log(11),logger.log(g),g&&"client"===n&&(window.location.href="asecagent://?token=".concat(g)),function(e,n){return t(),o("span")}}}))}}}));
