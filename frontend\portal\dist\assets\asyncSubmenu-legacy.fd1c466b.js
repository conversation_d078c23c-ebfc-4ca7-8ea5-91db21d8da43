/*! 
 Build based on gin-vue-admin 
 Time : 1754879545000 */
System.register(["./index-legacy.39a66d41.js"],(function(e,t){"use strict";var n,a,u,o,s,r,i,b,c,l,d,m,f,p,v,_=document.createElement("style");return _.textContent='@charset "UTF-8";[data-v-ea897612] .base-sub-menu :deep(.base-sub-menu__title){padding:6px;color:var(--13bf2c47);color:rgba(255,255,255,.675)}[data-v-ea897612] .base-sub-menu :deep(.base-sub-menu__title) .base-sub-menu__icon{transform:rotate(0)}[data-v-ea897612] .base-sub-menu:not(.base-sub-menu--opened) :deep(.base-sub-menu__title) .base-sub-menu__icon{transform:rotate(-90deg)}[data-v-ea897612] .base-sub-menu--active:not(.base-sub-menu--opened) :deep(.base-sub-menu__title){padding-left:18px!important;flex:1;opacity:100%;height:40px;line-height:40px;border-left:4px #71BDDF solid;background:#465566!important;border-radius:4px}[data-v-ea897612] .base-sub-menu--active:not(.base-sub-menu--opened) :deep(.base-sub-menu__title) .base-sub-menu__icon{transform:rotate(-90deg)}[data-v-ea897612] .base-sub-menu--active:not(.base-sub-menu--opened) :deep(.base-sub-menu__title) i{color:var(--4f287234)}[data-v-ea897612] .base-sub-menu--active:not(.base-sub-menu--opened) :deep(.base-sub-menu__title) span{opacity:100%;color:var(--4f287234)}[data-v-ea897612] .base-sub-menu--active:not(.base-sub-menu--opened){background:#465566!important}\n',document.head.appendChild(_),{setters:[function(e){n=e._,a=e.V,u=e.r,o=e.v,s=e.h,r=e.a,i=e.k,b=e.w,c=e.b,l=e.e,d=e.l,m=e.d,f=e.t,p=e.F,v=e.Y}],execute:function(){var t={key:0,class:"gva-subMenu"},_=Object.assign({name:"AsyncSubmenu"},{props:{routerInfo:{default:function(){return null},type:Object},isCollapse:{default:function(){return!1},type:Boolean},theme:{default:function(){return{}},type:Object}},setup:function(e){a((function(e){return{"13bf2c47":x.value,"4f287234":h.value}}));var n=e,_=u(n.theme.activeBackground),h=u(n.theme.activeText),x=u(n.theme.normalText);return o((function(){return n.theme}),(function(){_.value=n.theme.activeBackground,h.value=n.theme.activeText,x.value=n.theme.normalText})),function(n,a){var u=s("base-sub-menu");return r(),i(u,{ref:"subMenu",index:e.routerInfo.name},{title:b((function(){return[e.isCollapse?(r(),c(p,{key:1},[e.routerInfo.meta.icon?(r(),c("i",{key:0,class:l(["iconfont",e.routerInfo.meta.icon])},null,2)):d("",!0),m("span",null,f(e.routerInfo.meta.title),1)],64)):(r(),c("div",t,[e.routerInfo.meta.icon?(r(),c("i",{key:0,class:l(["iconfont",e.routerInfo.meta.icon])},null,2)):d("",!0),m("span",null,f(e.routerInfo.meta.title),1)]))]})),default:b((function(){return[v(n.$slots,"default",{},void 0,!0)]})),_:3},8,["index"])}}});e("default",n(_,[["__scopeId","data-v-ea897612"]]))}}}));
