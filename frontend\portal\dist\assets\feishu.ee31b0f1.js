/*! 
 Build based on gin-vue-admin 
 Time : 1754834760000 */
import{_ as e,f as o,r as a,o as t,P as l,v as n,a as r,b as i,d as s,i as d,Q as c,L as g,C as u}from"./index.0f69a27d.js";import{u as v}from"./secondaryAuth.1b8a4c74.js";const h={style:{"text-align":"center"}},p={class:"title",style:{height:"24px","line-height":"24px",margin:"0 auto",color:"#0082ef","font-size":"20px","text-align":"center"}},f={"aria-hidden":"true",class:"icon",style:{height:"24px",width:"29px","vertical-align":"top","margin-right":"8px",display:"inline-block"}},m=["src"],w=e(Object.assign({name:"<PERSON><PERSON><PERSON>"},{props:{authInfo:{type:Array,default:function(){return[]}},authId:{type:String,default:function(){return""}}},setup(e){const w=o(),{handleSecondaryAuthResponse:y}=v(),b=a(0),_=a("https://passport.feishu.cn/suite/passport/oauth/authorize"),x=a(""),L=a(null),E=a(""),k=a(null),I=a(""),R=a(0),S=a(null),A=a(""),F=a(!1),Q=a(null),T=a(0),C=e,D=async()=>{try{logger.log("开始绘制飞书二维码"),F.value=!1,Q.value=null,T.value=0,P(),E.value=(new Date).getTime();const e=C.authInfo.fsAppId;if(!e)return console.error("飞书配置缺失:",{appid:e}),void B("飞书配置缺失");const o={deviceid:"web_"+Date.now(),appid:e,time:E.value},a=c();I.value=`${a}/auth_callback/?auth_type=feishu&${new URLSearchParams(o).toString()}`;const t={client_id:e,state:C.authId,redirect_uri:I.value,response_type:"code"};A.value=_.value+"?"+new URLSearchParams(t).toString(),logger.log("飞书认证参数:",{appid:e,redirectUrl:I.value,goto:A.value}),document.getElementById("feishu_qrcode_login").innerHTML="",R.value=0,await new Promise(((e,o)=>{if(window.QRLogin)return void e(window.QRLogin);const a=document.createElement("script");a.src=u.getNativeJsPath("/js/feishu.js"),a.async=!0,a.onload=()=>{window.QRLogin?(logger.log("飞书SDK加载成功"),e(window.QRLogin)):o(new Error("飞书SDK加载失败：QRLogin未定义"))},a.onerror=()=>{o(new Error("飞书SDK加载失败：网络错误"))},document.head.appendChild(a)})),S.value=window.QRLogin({id:"feishu_qrcode_login",goto:A.value,width:"260",height:"300",style:"border:none;background-color:#FFFFFF;"}),void 0!==window.addEventListener?window.addEventListener("message",$,!1):void 0!==window.attachEvent&&window.attachEvent("onmessage",$),logger.log("飞书二维码绘制完成")}catch(e){console.error("飞书二维码绘制失败:",e),B("二维码绘制失败: "+e.message)}},$=async e=>{try{logger.log("收到飞书消息:",e);const o=e.origin;if(S.value&&S.value.matchOrigin&&S.value.matchOrigin(o)){k.value=g.service({fullscreen:!0,text:"登录中，请稍候..."}),logger.log("飞书消息数据:",e.data);const o=e.data;logger.log("收到飞书loginTmpCode:",o);const a=`${A.value}&tmp_code=${o}`;logger.log("飞书认证URL:",a),j(a)}else if(e.data&&"feishu_auth_callback"===e.data.type){logger.log("收到callback.html认证结果:",e.data);const{code:o,state:a,error:t}=e.data;if(t)return console.error("飞书认证失败:",t),void B("认证失败: "+t);if(o&&a){if(F.value)return void logger.log("飞书认证正在进行中，忽略callback消息");if(Q.value===o)return void logger.log("飞书认证重复的callback消息，忽略");logger.log("飞书认证成功，code:",o,"state:",a),await q(o,a)}else console.error("认证结果缺少必要参数"),B("认证结果无效")}}catch(o){console.error("飞书消息处理失败:",o)}},j=e=>{logger.log("创建认证回调iframe:",e);const o=document.getElementById("feishu-callback-iframe");o&&o.remove();const a=document.createElement("iframe");a.id="feishu-callback-iframe",a.src=e,a.style.display="none",a.style.width="0",a.style.height="0",a.style.border="none",document.body.appendChild(a),logger.log("认证回调iframe已创建")},q=async(e,o)=>{try{const a=Date.now();if(logger.log("飞书认证回调触发:",{code:e,state:o,currentTime:a}),F.value)return void logger.log("飞书认证正在进行中，忽略重复调用 - 状态检查");if(Q.value===e)return void logger.log("飞书认证重复的authCode，忽略重复调用 - 认证码检查");if(a-T.value<2e3)return void logger.log("飞书认证时间间隔过短，忽略重复调用 - 时间检查");F.value=!0,Q.value=e,T.value=a,logger.log("飞书认证成功，开始处理:",{code:e,state:o});const t={clientId:"client_portal",grantType:"implicit",redirect_uri:`${c()}/auth_callback/`,idpId:Array.isArray(o)?o[0]:o,authWeb:{authWebCode:Array.isArray(e)?e[0]:e}};logger.log("调用登录接口，参数:",t),k.value&&k.value.close();const l=await w.LoginIn(t,"feishu",C.authId);if(l&&-1!==l.code){if(await y(l))return void logger.log("飞书登录成功，进入双因子验证");logger.log("飞书登录成功")}else console.error("飞书登录失败"),B("登录失败，请重试"),D();setTimeout((()=>{F.value=!1,logger.log("飞书认证状态已重置")}),5e3)}catch(a){console.error("飞书认证处理失败:",a),B("认证处理失败: "+a.message),D(),F.value=!1,Q.value=null}},P=()=>{R.value=0,L.value&&(clearInterval(L.value),L.value=null)},B=e=>{const o=document.getElementById("feishu_qrcode_login");o&&(o.innerHTML=`\n      <div style="text-align: center; padding: 20px; color: #f56c6c;">\n        <div style="margin-bottom: 10px;">飞书认证失败</div>\n        <div style="font-size: 12px; color: #909399;">${e}</div>\n        <button onclick="location.reload()" style="margin-top: 10px; padding: 5px 15px; background: #409eff; color: white; border: none; border-radius: 4px; cursor: pointer;">\n          重试\n        </button>\n      </div>\n    `)};return t((()=>{logger.log("飞书认证组件挂载"),D()})),l((()=>{logger.log("飞书认证组件卸载"),F.value=!1,Q.value=null,T.value=0;const e=document.getElementById("feishu-callback-iframe");e&&e.remove(),void 0!==window.addEventListener?window.removeEventListener("message",$,!1):void 0!==window.attachEvent&&window.detachEvent("onmessage",$)})),n(C,(()=>{logger.log("飞书认证props变化，重新绘制二维码"),b.value++,D()})),(e,o)=>(r(),i("div",{key:b.value},[s("div",h,[s("span",p,[(r(),i("svg",f,o[0]||(o[0]=[s("use",{"xlink:href":"#icon-auth-feishu"},null,-1)]))),o[1]||(o[1]=d(" 飞书认证 "))])]),o[2]||(o[2]=s("div",{id:"feishu_qrcode_login",class:"feishu-qrcode-container"},null,-1)),s("iframe",{src:x.value,style:{display:"none"}},null,8,m)]))}}),[["__scopeId","data-v-1a739d22"]]);export{w as default};
