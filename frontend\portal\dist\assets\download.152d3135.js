/*! 
 Build based on gin-vue-admin 
 Time : 1754836337000 */
import{g as t}from"./system.3614e297.js";import{_ as e,r as n,o,P as r,h as i,a,b as s,d as l,j as u,w as c,F as d,i as f,k as h,l as g,e as w,M as p}from"./index.57c3624b.js";var m={},v={},y={};let E;const C=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];y.getSymbolSize=function(t){if(!t)throw new Error('"version" cannot be null or undefined');if(t<1||t>40)throw new Error('"version" should be in range from 1 to 40');return 4*t+17},y.getSymbolTotalCodewords=function(t){return C[t]},y.getBCHDigit=function(t){let e=0;for(;0!==t;)e++,t>>>=1;return e},y.setToSJISFunction=function(t){if("function"!=typeof t)throw new Error('"toSJISFunc" is not a valid function.');E=t},y.isKanjiModeEnabled=function(){return void 0!==E},y.toSJIS=function(t){return E(t)};var A,B={};function b(){this.buffer=[],this.length=0}(A=B).L={bit:1},A.M={bit:0},A.Q={bit:3},A.H={bit:2},A.isValid=function(t){return t&&void 0!==t.bit&&t.bit>=0&&t.bit<4},A.from=function(t,e){if(A.isValid(t))return t;try{return function(t){if("string"!=typeof t)throw new Error("Param is not a string");switch(t.toLowerCase()){case"l":case"low":return A.L;case"m":case"medium":return A.M;case"q":case"quartile":return A.Q;case"h":case"high":return A.H;default:throw new Error("Unknown EC Level: "+t)}}(t)}catch(n){return e}},b.prototype={get:function(t){const e=Math.floor(t/8);return 1==(this.buffer[e]>>>7-t%8&1)},put:function(t,e){for(let n=0;n<e;n++)this.putBit(1==(t>>>e-n-1&1))},getLengthInBits:function(){return this.length},putBit:function(t){const e=Math.floor(this.length/8);this.buffer.length<=e&&this.buffer.push(0),t&&(this.buffer[e]|=128>>>this.length%8),this.length++}};var M=b;function I(t){if(!t||t<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=t,this.data=new Uint8Array(t*t),this.reservedBit=new Uint8Array(t*t)}I.prototype.set=function(t,e,n,o){const r=t*this.size+e;this.data[r]=n,o&&(this.reservedBit[r]=!0)},I.prototype.get=function(t,e){return this.data[t*this.size+e]},I.prototype.xor=function(t,e,n){this.data[t*this.size+e]^=n},I.prototype.isReserved=function(t,e){return this.reservedBit[t*this.size+e]};var T=I,P={};!function(t){const e=y.getSymbolSize;t.getRowColCoords=function(t){if(1===t)return[];const n=Math.floor(t/7)+2,o=e(t),r=145===o?26:2*Math.ceil((o-13)/(2*n-2)),i=[o-7];for(let e=1;e<n-1;e++)i[e]=i[e-1]-r;return i.push(6),i.reverse()},t.getPositions=function(e){const n=[],o=t.getRowColCoords(e),r=o.length;for(let t=0;t<r;t++)for(let e=0;e<r;e++)0===t&&0===e||0===t&&e===r-1||t===r-1&&0===e||n.push([o[t],o[e]]);return n}}(P);var N={};const R=y.getSymbolSize;N.getPositions=function(t){const e=R(t);return[[0,0],[e-7,0],[0,e-7]]};var L={};!function(t){t.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const e=3,n=3,o=40,r=10;function i(e,n,o){switch(e){case t.Patterns.PATTERN000:return(n+o)%2==0;case t.Patterns.PATTERN001:return n%2==0;case t.Patterns.PATTERN010:return o%3==0;case t.Patterns.PATTERN011:return(n+o)%3==0;case t.Patterns.PATTERN100:return(Math.floor(n/2)+Math.floor(o/3))%2==0;case t.Patterns.PATTERN101:return n*o%2+n*o%3==0;case t.Patterns.PATTERN110:return(n*o%2+n*o%3)%2==0;case t.Patterns.PATTERN111:return(n*o%3+(n+o)%2)%2==0;default:throw new Error("bad maskPattern:"+e)}}t.isValid=function(t){return null!=t&&""!==t&&!isNaN(t)&&t>=0&&t<=7},t.from=function(e){return t.isValid(e)?parseInt(e,10):void 0},t.getPenaltyN1=function(t){const n=t.size;let o=0,r=0,i=0,a=null,s=null;for(let l=0;l<n;l++){r=i=0,a=s=null;for(let u=0;u<n;u++){let n=t.get(l,u);n===a?r++:(r>=5&&(o+=e+(r-5)),a=n,r=1),n=t.get(u,l),n===s?i++:(i>=5&&(o+=e+(i-5)),s=n,i=1)}r>=5&&(o+=e+(r-5)),i>=5&&(o+=e+(i-5))}return o},t.getPenaltyN2=function(t){const e=t.size;let o=0;for(let n=0;n<e-1;n++)for(let r=0;r<e-1;r++){const e=t.get(n,r)+t.get(n,r+1)+t.get(n+1,r)+t.get(n+1,r+1);4!==e&&0!==e||o++}return o*n},t.getPenaltyN3=function(t){const e=t.size;let n=0,r=0,i=0;for(let o=0;o<e;o++){r=i=0;for(let a=0;a<e;a++)r=r<<1&2047|t.get(o,a),a>=10&&(1488===r||93===r)&&n++,i=i<<1&2047|t.get(a,o),a>=10&&(1488===i||93===i)&&n++}return n*o},t.getPenaltyN4=function(t){let e=0;const n=t.data.length;for(let o=0;o<n;o++)e+=t.data[o];return Math.abs(Math.ceil(100*e/n/5)-10)*r},t.applyMask=function(t,e){const n=e.size;for(let o=0;o<n;o++)for(let r=0;r<n;r++)e.isReserved(r,o)||e.xor(r,o,i(t,r,o))},t.getBestMask=function(e,n){const o=Object.keys(t.Patterns).length;let r=0,i=1/0;for(let a=0;a<o;a++){n(a),t.applyMask(a,e);const o=t.getPenaltyN1(e)+t.getPenaltyN2(e)+t.getPenaltyN3(e)+t.getPenaltyN4(e);t.applyMask(a,e),o<i&&(i=o,r=a)}return r}}(L);var x={};const S=B,U=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],_=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];x.getBlocksCount=function(t,e){switch(e){case S.L:return U[4*(t-1)+0];case S.M:return U[4*(t-1)+1];case S.Q:return U[4*(t-1)+2];case S.H:return U[4*(t-1)+3];default:return}},x.getTotalCodewordsCount=function(t,e){switch(e){case S.L:return _[4*(t-1)+0];case S.M:return _[4*(t-1)+1];case S.Q:return _[4*(t-1)+2];case S.H:return _[4*(t-1)+3];default:return}};var k={},z={};const D=new Uint8Array(512),F=new Uint8Array(256);!function(){let t=1;for(let e=0;e<255;e++)D[e]=t,F[t]=e,t<<=1,256&t&&(t^=285);for(let e=255;e<512;e++)D[e]=D[e-255]}(),z.log=function(t){if(t<1)throw new Error("log("+t+")");return F[t]},z.exp=function(t){return D[t]},z.mul=function(t,e){return 0===t||0===e?0:D[F[t]+F[e]]},function(t){const e=z;t.mul=function(t,n){const o=new Uint8Array(t.length+n.length-1);for(let r=0;r<t.length;r++)for(let i=0;i<n.length;i++)o[r+i]^=e.mul(t[r],n[i]);return o},t.mod=function(t,n){let o=new Uint8Array(t);for(;o.length-n.length>=0;){const t=o[0];for(let i=0;i<n.length;i++)o[i]^=e.mul(n[i],t);let r=0;for(;r<o.length&&0===o[r];)r++;o=o.slice(r)}return o},t.generateECPolynomial=function(n){let o=new Uint8Array([1]);for(let r=0;r<n;r++)o=t.mul(o,new Uint8Array([1,e.exp(r)]));return o}}(k);const H=k;function J(t){this.genPoly=void 0,this.degree=t,this.degree&&this.initialize(this.degree)}J.prototype.initialize=function(t){this.degree=t,this.genPoly=H.generateECPolynomial(this.degree)},J.prototype.encode=function(t){if(!this.genPoly)throw new Error("Encoder not initialized");const e=new Uint8Array(t.length+this.degree);e.set(t);const n=H.mod(e,this.genPoly),o=this.degree-n.length;if(o>0){const t=new Uint8Array(this.degree);return t.set(n,o),t}return n};var K=J,Y={},j={},O={isValid:function(t){return!isNaN(t)&&t>=1&&t<=40}},q={};const V="[0-9]+";let Q="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";Q=Q.replace(/u/g,"\\u");const $="(?:(?![A-Z0-9 $%*+\\-./:]|"+Q+")(?:.|[\r\n]))+";q.KANJI=new RegExp(Q,"g"),q.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),q.BYTE=new RegExp($,"g"),q.NUMERIC=new RegExp(V,"g"),q.ALPHANUMERIC=new RegExp("[A-Z $%*+\\-./:]+","g");const W=new RegExp("^"+Q+"$"),X=new RegExp("^"+V+"$"),Z=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");q.testKanji=function(t){return W.test(t)},q.testNumeric=function(t){return X.test(t)},q.testAlphanumeric=function(t){return Z.test(t)},function(t){const e=O,n=q;t.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},t.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},t.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},t.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},t.MIXED={bit:-1},t.getCharCountIndicator=function(t,n){if(!t.ccBits)throw new Error("Invalid mode: "+t);if(!e.isValid(n))throw new Error("Invalid version: "+n);return n>=1&&n<10?t.ccBits[0]:n<27?t.ccBits[1]:t.ccBits[2]},t.getBestModeForData=function(e){return n.testNumeric(e)?t.NUMERIC:n.testAlphanumeric(e)?t.ALPHANUMERIC:n.testKanji(e)?t.KANJI:t.BYTE},t.toString=function(t){if(t&&t.id)return t.id;throw new Error("Invalid mode")},t.isValid=function(t){return t&&t.bit&&t.ccBits},t.from=function(e,n){if(t.isValid(e))return e;try{return function(e){if("string"!=typeof e)throw new Error("Param is not a string");switch(e.toLowerCase()){case"numeric":return t.NUMERIC;case"alphanumeric":return t.ALPHANUMERIC;case"kanji":return t.KANJI;case"byte":return t.BYTE;default:throw new Error("Unknown mode: "+e)}}(e)}catch(o){return n}}}(j),function(t){const e=y,n=x,o=B,r=j,i=O,a=e.getBCHDigit(7973);function s(t,e){return r.getCharCountIndicator(t,e)+4}function l(t,e){let n=0;return t.forEach((function(t){const o=s(t.mode,e);n+=o+t.getBitsLength()})),n}t.from=function(t,e){return i.isValid(t)?parseInt(t,10):e},t.getCapacity=function(t,o,a){if(!i.isValid(t))throw new Error("Invalid QR Code version");void 0===a&&(a=r.BYTE);const l=8*(e.getSymbolTotalCodewords(t)-n.getTotalCodewordsCount(t,o));if(a===r.MIXED)return l;const u=l-s(a,t);switch(a){case r.NUMERIC:return Math.floor(u/10*3);case r.ALPHANUMERIC:return Math.floor(u/11*2);case r.KANJI:return Math.floor(u/13);case r.BYTE:default:return Math.floor(u/8)}},t.getBestVersionForData=function(e,n){let i;const a=o.from(n,o.M);if(Array.isArray(e)){if(e.length>1)return function(e,n){for(let o=1;o<=40;o++)if(l(e,o)<=t.getCapacity(o,n,r.MIXED))return o}(e,a);if(0===e.length)return 1;i=e[0]}else i=e;return function(e,n,o){for(let r=1;r<=40;r++)if(n<=t.getCapacity(r,o,e))return r}(i.mode,i.getLength(),a)},t.getEncodedBits=function(t){if(!i.isValid(t)||t<7)throw new Error("Invalid QR Code version");let n=t<<12;for(;e.getBCHDigit(n)-a>=0;)n^=7973<<e.getBCHDigit(n)-a;return t<<12|n}}(Y);var G={};const tt=y,et=tt.getBCHDigit(1335);G.getEncodedBits=function(t,e){const n=t.bit<<3|e;let o=n<<10;for(;tt.getBCHDigit(o)-et>=0;)o^=1335<<tt.getBCHDigit(o)-et;return 21522^(n<<10|o)};var nt={};const ot=j;function rt(t){this.mode=ot.NUMERIC,this.data=t.toString()}rt.getBitsLength=function(t){return 10*Math.floor(t/3)+(t%3?t%3*3+1:0)},rt.prototype.getLength=function(){return this.data.length},rt.prototype.getBitsLength=function(){return rt.getBitsLength(this.data.length)},rt.prototype.write=function(t){let e,n,o;for(e=0;e+3<=this.data.length;e+=3)n=this.data.substr(e,3),o=parseInt(n,10),t.put(o,10);const r=this.data.length-e;r>0&&(n=this.data.substr(e),o=parseInt(n,10),t.put(o,3*r+1))};var it=rt;const at=j,st=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function lt(t){this.mode=at.ALPHANUMERIC,this.data=t}lt.getBitsLength=function(t){return 11*Math.floor(t/2)+t%2*6},lt.prototype.getLength=function(){return this.data.length},lt.prototype.getBitsLength=function(){return lt.getBitsLength(this.data.length)},lt.prototype.write=function(t){let e;for(e=0;e+2<=this.data.length;e+=2){let n=45*st.indexOf(this.data[e]);n+=st.indexOf(this.data[e+1]),t.put(n,11)}this.data.length%2&&t.put(st.indexOf(this.data[e]),6)};var ut=lt;const ct=j;function dt(t){this.mode=ct.BYTE,this.data="string"==typeof t?(new TextEncoder).encode(t):new Uint8Array(t)}dt.getBitsLength=function(t){return 8*t},dt.prototype.getLength=function(){return this.data.length},dt.prototype.getBitsLength=function(){return dt.getBitsLength(this.data.length)},dt.prototype.write=function(t){for(let e=0,n=this.data.length;e<n;e++)t.put(this.data[e],8)};var ft=dt;const ht=j,gt=y;function wt(t){this.mode=ht.KANJI,this.data=t}wt.getBitsLength=function(t){return 13*t},wt.prototype.getLength=function(){return this.data.length},wt.prototype.getBitsLength=function(){return wt.getBitsLength(this.data.length)},wt.prototype.write=function(t){let e;for(e=0;e<this.data.length;e++){let n=gt.toSJIS(this.data[e]);if(n>=33088&&n<=40956)n-=33088;else{if(!(n>=57408&&n<=60351))throw new Error("Invalid SJIS character: "+this.data[e]+"\nMake sure your charset is UTF-8");n-=49472}n=192*(n>>>8&255)+(255&n),t.put(n,13)}};var pt,mt=wt,vt={exports:{}};vt.exports=pt={single_source_shortest_paths:function(t,e,n){var o={},r={};r[e]=0;var i,a,s,l,u,c,d,f=pt.PriorityQueue.make();for(f.push(e,0);!f.empty();)for(s in a=(i=f.pop()).value,l=i.cost,u=t[a]||{})u.hasOwnProperty(s)&&(c=l+u[s],d=r[s],(void 0===r[s]||d>c)&&(r[s]=c,f.push(s,c),o[s]=a));if(void 0!==n&&void 0===r[n]){var h=["Could not find a path from ",e," to ",n,"."].join("");throw new Error(h)}return o},extract_shortest_path_from_predecessor_list:function(t,e){for(var n=[],o=e;o;)n.push(o),t[o],o=t[o];return n.reverse(),n},find_path:function(t,e,n){var o=pt.single_source_shortest_paths(t,e,n);return pt.extract_shortest_path_from_predecessor_list(o,n)},PriorityQueue:{make:function(t){var e,n=pt.PriorityQueue,o={};for(e in t=t||{},n)n.hasOwnProperty(e)&&(o[e]=n[e]);return o.queue=[],o.sorter=t.sorter||n.default_sorter,o},default_sorter:function(t,e){return t.cost-e.cost},push:function(t,e){var n={value:t,cost:e};this.queue.push(n),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return 0===this.queue.length}}},function(t){const e=j,n=it,o=ut,r=ft,i=mt,a=q,s=y,l=vt.exports;function u(t){return unescape(encodeURIComponent(t)).length}function c(t,e,n){const o=[];let r;for(;null!==(r=t.exec(n));)o.push({data:r[0],index:r.index,mode:e,length:r[0].length});return o}function d(t){const n=c(a.NUMERIC,e.NUMERIC,t),o=c(a.ALPHANUMERIC,e.ALPHANUMERIC,t);let r,i;s.isKanjiModeEnabled()?(r=c(a.BYTE,e.BYTE,t),i=c(a.KANJI,e.KANJI,t)):(r=c(a.BYTE_KANJI,e.BYTE,t),i=[]);return n.concat(o,r,i).sort((function(t,e){return t.index-e.index})).map((function(t){return{data:t.data,mode:t.mode,length:t.length}}))}function f(t,a){switch(a){case e.NUMERIC:return n.getBitsLength(t);case e.ALPHANUMERIC:return o.getBitsLength(t);case e.KANJI:return i.getBitsLength(t);case e.BYTE:return r.getBitsLength(t)}}function h(t,a){let l;const u=e.getBestModeForData(t);if(l=e.from(a,u),l!==e.BYTE&&l.bit<u.bit)throw new Error('"'+t+'" cannot be encoded with mode '+e.toString(l)+".\n Suggested mode is: "+e.toString(u));switch(l!==e.KANJI||s.isKanjiModeEnabled()||(l=e.BYTE),l){case e.NUMERIC:return new n(t);case e.ALPHANUMERIC:return new o(t);case e.KANJI:return new i(t);case e.BYTE:return new r(t)}}t.fromArray=function(t){return t.reduce((function(t,e){return"string"==typeof e?t.push(h(e,null)):e.data&&t.push(h(e.data,e.mode)),t}),[])},t.fromString=function(n,o){const r=function(t){const n=[];for(let o=0;o<t.length;o++){const r=t[o];switch(r.mode){case e.NUMERIC:n.push([r,{data:r.data,mode:e.ALPHANUMERIC,length:r.length},{data:r.data,mode:e.BYTE,length:r.length}]);break;case e.ALPHANUMERIC:n.push([r,{data:r.data,mode:e.BYTE,length:r.length}]);break;case e.KANJI:n.push([r,{data:r.data,mode:e.BYTE,length:u(r.data)}]);break;case e.BYTE:n.push([{data:r.data,mode:e.BYTE,length:u(r.data)}])}}return n}(d(n,s.isKanjiModeEnabled())),i=function(t,n){const o={},r={start:{}};let i=["start"];for(let a=0;a<t.length;a++){const s=t[a],l=[];for(let t=0;t<s.length;t++){const u=s[t],c=""+a+t;l.push(c),o[c]={node:u,lastCount:0},r[c]={};for(let t=0;t<i.length;t++){const a=i[t];o[a]&&o[a].node.mode===u.mode?(r[a][c]=f(o[a].lastCount+u.length,u.mode)-f(o[a].lastCount,u.mode),o[a].lastCount+=u.length):(o[a]&&(o[a].lastCount=u.length),r[a][c]=f(u.length,u.mode)+4+e.getCharCountIndicator(u.mode,n))}}i=l}for(let e=0;e<i.length;e++)r[i[e]].end=0;return{map:r,table:o}}(r,o),a=l.find_path(i.map,"start","end"),c=[];for(let t=1;t<a.length-1;t++)c.push(i.table[a[t]].node);return t.fromArray(function(t){return t.reduce((function(t,e){const n=t.length-1>=0?t[t.length-1]:null;return n&&n.mode===e.mode?(t[t.length-1].data+=e.data,t):(t.push(e),t)}),[])}(c))},t.rawSplit=function(e){return t.fromArray(d(e,s.isKanjiModeEnabled()))}}(nt);const yt=y,Et=B,Ct=M,At=T,Bt=P,bt=N,Mt=L,It=x,Tt=K,Pt=Y,Nt=G,Rt=j,Lt=nt;function xt(t,e,n){const o=t.size,r=Nt.getEncodedBits(e,n);let i,a;for(i=0;i<15;i++)a=1==(r>>i&1),i<6?t.set(i,8,a,!0):i<8?t.set(i+1,8,a,!0):t.set(o-15+i,8,a,!0),i<8?t.set(8,o-i-1,a,!0):i<9?t.set(8,15-i-1+1,a,!0):t.set(8,15-i-1,a,!0);t.set(o-8,8,1,!0)}function St(t,e,n){const o=new Ct;n.forEach((function(e){o.put(e.mode.bit,4),o.put(e.getLength(),Rt.getCharCountIndicator(e.mode,t)),e.write(o)}));const r=8*(yt.getSymbolTotalCodewords(t)-It.getTotalCodewordsCount(t,e));for(o.getLengthInBits()+4<=r&&o.put(0,4);o.getLengthInBits()%8!=0;)o.putBit(0);const i=(r-o.getLengthInBits())/8;for(let a=0;a<i;a++)o.put(a%2?17:236,8);return function(t,e,n){const o=yt.getSymbolTotalCodewords(e),r=It.getTotalCodewordsCount(e,n),i=o-r,a=It.getBlocksCount(e,n),s=o%a,l=a-s,u=Math.floor(o/a),c=Math.floor(i/a),d=c+1,f=u-c,h=new Tt(f);let g=0;const w=new Array(a),p=new Array(a);let m=0;const v=new Uint8Array(t.buffer);for(let B=0;B<a;B++){const t=B<l?c:d;w[B]=v.slice(g,g+t),p[B]=h.encode(w[B]),g+=t,m=Math.max(m,t)}const y=new Uint8Array(o);let E,C,A=0;for(E=0;E<m;E++)for(C=0;C<a;C++)E<w[C].length&&(y[A++]=w[C][E]);for(E=0;E<f;E++)for(C=0;C<a;C++)y[A++]=p[C][E];return y}(o,t,e)}function Ut(t,e,n,o){let r;if(Array.isArray(t))r=Lt.fromArray(t);else{if("string"!=typeof t)throw new Error("Invalid data");{let o=e;if(!o){const e=Lt.rawSplit(t);o=Pt.getBestVersionForData(e,n)}r=Lt.fromString(t,o||40)}}const i=Pt.getBestVersionForData(r,n);if(!i)throw new Error("The amount of data is too big to be stored in a QR Code");if(e){if(e<i)throw new Error("\nThe chosen QR Code version cannot contain this amount of data.\nMinimum version required to store current data is: "+i+".\n")}else e=i;const a=St(e,n,r),s=yt.getSymbolSize(e),l=new At(s);return function(t,e){const n=t.size,o=bt.getPositions(e);for(let r=0;r<o.length;r++){const e=o[r][0],i=o[r][1];for(let o=-1;o<=7;o++)if(!(e+o<=-1||n<=e+o))for(let r=-1;r<=7;r++)i+r<=-1||n<=i+r||(o>=0&&o<=6&&(0===r||6===r)||r>=0&&r<=6&&(0===o||6===o)||o>=2&&o<=4&&r>=2&&r<=4?t.set(e+o,i+r,!0,!0):t.set(e+o,i+r,!1,!0))}}(l,e),function(t){const e=t.size;for(let n=8;n<e-8;n++){const e=n%2==0;t.set(n,6,e,!0),t.set(6,n,e,!0)}}(l),function(t,e){const n=Bt.getPositions(e);for(let o=0;o<n.length;o++){const e=n[o][0],r=n[o][1];for(let n=-2;n<=2;n++)for(let o=-2;o<=2;o++)-2===n||2===n||-2===o||2===o||0===n&&0===o?t.set(e+n,r+o,!0,!0):t.set(e+n,r+o,!1,!0)}}(l,e),xt(l,n,0),e>=7&&function(t,e){const n=t.size,o=Pt.getEncodedBits(e);let r,i,a;for(let s=0;s<18;s++)r=Math.floor(s/3),i=s%3+n-8-3,a=1==(o>>s&1),t.set(r,i,a,!0),t.set(i,r,a,!0)}(l,e),function(t,e){const n=t.size;let o=-1,r=n-1,i=7,a=0;for(let s=n-1;s>0;s-=2)for(6===s&&s--;;){for(let n=0;n<2;n++)if(!t.isReserved(r,s-n)){let o=!1;a<e.length&&(o=1==(e[a]>>>i&1)),t.set(r,s-n,o),i--,-1===i&&(a++,i=7)}if(r+=o,r<0||n<=r){r-=o,o=-o;break}}}(l,a),isNaN(o)&&(o=Mt.getBestMask(l,xt.bind(null,l,n))),Mt.applyMask(o,l),xt(l,n,o),{modules:l,version:e,errorCorrectionLevel:n,maskPattern:o,segments:r}}v.create=function(t,e){if(void 0===t||""===t)throw new Error("No input text");let n,o,r=Et.M;return void 0!==e&&(r=Et.from(e.errorCorrectionLevel,Et.M),n=Pt.from(e.version),o=Mt.from(e.maskPattern),e.toSJISFunc&&yt.setToSJISFunction(e.toSJISFunc)),Ut(t,n,r,o)};var _t={},kt={};!function(t){function e(t){if("number"==typeof t&&(t=t.toString()),"string"!=typeof t)throw new Error("Color should be defined as hex string");let e=t.slice().replace("#","").split("");if(e.length<3||5===e.length||e.length>8)throw new Error("Invalid hex color: "+t);3!==e.length&&4!==e.length||(e=Array.prototype.concat.apply([],e.map((function(t){return[t,t]})))),6===e.length&&e.push("F","F");const n=parseInt(e.join(""),16);return{r:n>>24&255,g:n>>16&255,b:n>>8&255,a:255&n,hex:"#"+e.slice(0,6).join("")}}t.getOptions=function(t){t||(t={}),t.color||(t.color={});const n=void 0===t.margin||null===t.margin||t.margin<0?4:t.margin,o=t.width&&t.width>=21?t.width:void 0,r=t.scale||4;return{width:o,scale:o?4:r,margin:n,color:{dark:e(t.color.dark||"#000000ff"),light:e(t.color.light||"#ffffffff")},type:t.type,rendererOpts:t.rendererOpts||{}}},t.getScale=function(t,e){return e.width&&e.width>=t+2*e.margin?e.width/(t+2*e.margin):e.scale},t.getImageWidth=function(e,n){const o=t.getScale(e,n);return Math.floor((e+2*n.margin)*o)},t.qrToImageData=function(e,n,o){const r=n.modules.size,i=n.modules.data,a=t.getScale(r,o),s=Math.floor((r+2*o.margin)*a),l=o.margin*a,u=[o.color.light,o.color.dark];for(let t=0;t<s;t++)for(let n=0;n<s;n++){let c=4*(t*s+n),d=o.color.light;if(t>=l&&n>=l&&t<s-l&&n<s-l){d=u[i[Math.floor((t-l)/a)*r+Math.floor((n-l)/a)]?1:0]}e[c++]=d.r,e[c++]=d.g,e[c++]=d.b,e[c]=d.a}}}(kt),function(t){const e=kt;t.render=function(t,n,o){let r=o,i=n;void 0!==r||n&&n.getContext||(r=n,n=void 0),n||(i=function(){try{return document.createElement("canvas")}catch(t){throw new Error("You need to specify a canvas element")}}()),r=e.getOptions(r);const a=e.getImageWidth(t.modules.size,r),s=i.getContext("2d"),l=s.createImageData(a,a);return e.qrToImageData(l.data,t,r),function(t,e,n){t.clearRect(0,0,e.width,e.height),e.style||(e.style={}),e.height=n,e.width=n,e.style.height=n+"px",e.style.width=n+"px"}(s,i,a),s.putImageData(l,0,0),i},t.renderToDataURL=function(e,n,o){let r=o;void 0!==r||n&&n.getContext||(r=n,n=void 0),r||(r={});const i=t.render(e,n,r),a=r.type||"image/png",s=r.rendererOpts||{};return i.toDataURL(a,s.quality)}}(_t);var zt={};const Dt=kt;function Ft(t,e){const n=t.a/255,o=e+'="'+t.hex+'"';return n<1?o+" "+e+'-opacity="'+n.toFixed(2).slice(1)+'"':o}function Ht(t,e,n){let o=t+e;return void 0!==n&&(o+=" "+n),o}zt.render=function(t,e,n){const o=Dt.getOptions(e),r=t.modules.size,i=t.modules.data,a=r+2*o.margin,s=o.color.light.a?"<path "+Ft(o.color.light,"fill")+' d="M0 0h'+a+"v"+a+'H0z"/>':"",l="<path "+Ft(o.color.dark,"stroke")+' d="'+function(t,e,n){let o="",r=0,i=!1,a=0;for(let s=0;s<t.length;s++){const l=Math.floor(s%e),u=Math.floor(s/e);l||i||(i=!0),t[s]?(a++,s>0&&l>0&&t[s-1]||(o+=i?Ht("M",l+n,.5+u+n):Ht("m",r,0),r=0,i=!1),l+1<e&&t[s+1]||(o+=Ht("h",a),a=0)):r++}return o}(i,r,o.margin)+'"/>',u='viewBox="0 0 '+a+" "+a+'"',c='<svg xmlns="http://www.w3.org/2000/svg" '+(o.width?'width="'+o.width+'" height="'+o.width+'" ':"")+u+' shape-rendering="crispEdges">'+s+l+"</svg>\n";return"function"==typeof n&&n(null,c),c};const Jt=function(){return"function"==typeof Promise&&Promise.prototype&&Promise.prototype.then},Kt=v,Yt=_t,jt=zt;function Ot(t,e,n,o,r){const i=[].slice.call(arguments,1),a=i.length,s="function"==typeof i[a-1];if(!s&&!Jt())throw new Error("Callback required as last argument");if(!s){if(a<1)throw new Error("Too few arguments provided");return 1===a?(n=e,e=o=void 0):2!==a||e.getContext||(o=n,n=e,e=void 0),new Promise((function(r,i){try{const i=Kt.create(n,o);r(t(i,e,o))}catch(a){i(a)}}))}if(a<2)throw new Error("Too few arguments provided");2===a?(r=n,n=e,e=o=void 0):3===a&&(e.getContext&&void 0===r?(r=o,o=void 0):(r=o,o=n,n=e,e=void 0));try{const i=Kt.create(n,o);r(null,t(i,e,o))}catch(l){r(l)}}m.create=Kt.create,m.toCanvas=Ot.bind(null,Yt.render),m.toDataURL=Ot.bind(null,Yt.renderToDataURL),m.toString=Ot.bind(null,(function(t,e,n){return jt.render(t,n)}));const qt={class:"client"},Vt={class:"download-container"},Qt={key:0,class:"loading-overlay"},$t={key:0,class:"loading-overlay"},Wt=e({__name:"download",setup(e){const v=n(!1),y=()=>{const t=window.innerWidth,e=navigator.userAgent.toLowerCase(),n=/android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(e);v.value=t<768||n},E=()=>{y()};o((()=>{y(),window.addEventListener("resize",E)})),r((()=>{window.removeEventListener("resize",E)}));const C=n(""),A=n(""),B=n(!1),b=n(!1),M=n(!1),I=n(!1),T=n({windows:0,darwin:0}),P=t=>100===t?"完成":`${t}%`,N=()=>{B.value=!1,b.value=!1,M.value=!1,I.value=!1,Object.keys(T.value).forEach((t=>{T.value[t]=0}))},R=n(!1),L=async e=>{if(("android"===e||"ios"===e)&&R.value)return;R.value=!0;const n={windows:B,darwin:b,ios:M,android:I}[e];n.value=!0;try{const n=await t({platform:e});if(0!==n.data.code)throw new Error(n.data.msg);if("ios"===e){const t=await m.toDataURL(n.data.data.download_url),e=document.getElementById("ioscanvas");if(A.value=t,e){const n=e.getContext("2d"),o=new Image;o.onload=()=>{e.width=o.width,e.height=o.height,n.drawImage(o,0,0)},o.src=t}}else if("android"===e){const t=window.location.port,e=new URL(n.data.data.download_url);let o;t?e.toString().includes("asec-deploy")?o=n.data.data.download_url:(e.port=t,o=e.toString()):(e.port="",o=e.toString());const r=await m.toDataURL(o),i=document.getElementById("canvas");if(C.value=r,i){const t=i.getContext("2d"),e=new Image;e.onload=()=>{i.width=e.width,i.height=e.height,t.drawImage(e,0,0)},e.src=r}}else{const t=window.location.port,o=new URL(n.data.data.download_url);let r,i;t?(o.toString().includes("asec-deploy")?r=n.data.data.download_url:(o.port=t,r=o.toString()),i=n.data.data.latest_filename.replace(/@(\d+)/,`@${t}`)):(o.port="",r=o.toString(),i=n.data.data.latest_filename);const a=await((t,e)=>new Promise(((n,o)=>{const r=new XMLHttpRequest;r.open("GET",t,!0),r.responseType="blob",r.onprogress=t=>{if(t.lengthComputable){const n=t.loaded/t.total*100;T.value[e]=Math.round(n)}},r.onload=()=>{200===r.status?n(r.response):o(new Error("下载失败"))},r.onerror=()=>{o(new Error("网络错误"))},r.send()})))(r,e);((t,e)=>{if(window.navigator.msSaveOrOpenBlob)navigator.msSaveBlob(t,e);else{const n=document.createElement("a"),o=document.querySelector("body");n.href=window.URL.createObjectURL(t),n.download=e,n.style.display="none",o.appendChild(n),n.click(),o.removeChild(n),window.URL.revokeObjectURL(n.href)}N()})(a,i)}}catch(o){p({type:"error",message:o.message||"下载失败，请联系管理员"})}finally{n.value=!1}};return(t,e)=>{const n=i("base-link"),o=i("base-progress"),r=i("base-main");return a(),s("div",null,[l("div",qt,[u(r,null,{default:c((()=>[l("div",Vt,[v.value?g("",!0):(a(),s(d,{key:0},[l("div",{class:"download-card desktop-only",onClick:e[0]||(e[0]=t=>L("windows"))},[e[10]||(e[10]=l("svg",{class:"icon window-show download-icon","aria-hidden":"true"},[l("use",{"xlink:href":"#icon-windows"})],-1)),e[11]||(e[11]=l("svg",{class:"icon window-hidden download-icon","aria-hidden":"true"},[l("use",{"xlink:href":"#icon-xiazai"})],-1)),e[12]||(e[12]=l("br",null,null,-1)),u(n,{class:"window-show download-text",underline:!1},{default:c((()=>e[8]||(e[8]=[f("Windows客户端")]))),_:1,__:[8]}),u(n,{class:"window-hidden download-text",underline:!1},{default:c((()=>e[9]||(e[9]=[f("点击下载Windows客户端")]))),_:1,__:[9]}),B.value?(a(),h(o,{key:0,percentage:T.value.windows,format:P,class:"download-progress"},null,8,["percentage"])):g("",!0)]),l("div",{class:"download-card desktop-only",onClick:e[1]||(e[1]=t=>L("darwin"))},[e[15]||(e[15]=l("svg",{class:"icon window-show download-icon","aria-hidden":"true"},[l("use",{"xlink:href":"#icon-mac"})],-1)),e[16]||(e[16]=l("svg",{class:"icon window-hidden download-icon","aria-hidden":"true"},[l("use",{"xlink:href":"#icon-xiazai"})],-1)),e[17]||(e[17]=l("br",null,null,-1)),u(n,{class:"window-show download-text",underline:!1},{default:c((()=>e[13]||(e[13]=[f("Mac客户端")]))),_:1,__:[13]}),u(n,{class:"window-hidden download-text",underline:!1},{default:c((()=>e[14]||(e[14]=[f("点击下载Mac客户端")]))),_:1,__:[14]}),b.value?(a(),h(o,{key:0,percentage:T.value.darwin,format:P,class:"download-progress"},null,8,["percentage"])):g("",!0)])],64)),l("div",{class:w(["download-card ios-container",{loading:M.value}]),onClick:e[2]||(e[2]=t=>v.value?L("ios"):null),onMousemove:e[3]||(e[3]=t=>v.value?null:L("ios")),onMouseleave:e[4]||(e[4]=t=>R.value=!1)},[M.value?(a(),s("div",Qt,e[18]||(e[18]=[l("div",{class:"loading-spinner"},[l("div",{class:"spinner"}),l("div",{class:"loading-text"},"下载码生成中...")],-1)]))):g("",!0),e[20]||(e[20]=l("svg",{class:"icon window-show download-icon","aria-hidden":"true"},[l("use",{"xlink:href":"#icon-ios"})],-1)),e[21]||(e[21]=l("br",null,null,-1)),u(n,{class:"window-show download-text",underline:!1},{default:c((()=>e[19]||(e[19]=[f("iOS客户端")]))),_:1,__:[19]}),e[22]||(e[22]=l("div",{id:"ios",class:"window-hidden qr-container"},[l("canvas",{id:"ioscanvas",class:"qr-canvas"})],-1))],34),l("div",{class:w(["download-card android-container",{loading:I.value}]),onClick:e[5]||(e[5]=t=>v.value?L("android"):null),onMousemove:e[6]||(e[6]=t=>v.value?null:L("android")),onMouseleave:e[7]||(e[7]=t=>R.value=!1)},[I.value?(a(),s("div",$t,e[23]||(e[23]=[l("div",{class:"loading-spinner"},[l("div",{class:"spinner"}),l("div",{class:"loading-text"},"下载码生成中...")],-1)]))):g("",!0),e[25]||(e[25]=l("svg",{class:"icon window-show download-icon","aria-hidden":"true"},[l("use",{"xlink:href":"#icon-android"})],-1)),e[26]||(e[26]=l("br",null,null,-1)),u(n,{class:"window-show download-text",underline:!1},{default:c((()=>e[24]||(e[24]=[f("Android客户端")]))),_:1,__:[24]}),e[27]||(e[27]=l("div",{id:"android",class:"window-hidden qr-container"},[l("canvas",{id:"canvas",class:"qr-canvas"})],-1))],34)])])),_:1})])])}}},[["__scopeId","data-v-73e3a6ee"]]);export{Wt as default};
