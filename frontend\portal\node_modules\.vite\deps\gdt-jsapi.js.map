{"version": 3, "sources": ["../../@babel/runtime/helpers/OverloadYield.js", "../../@babel/runtime/helpers/regeneratorDefine.js", "../../@babel/runtime/helpers/regenerator.js", "../../@babel/runtime/helpers/regeneratorAsyncIterator.js", "../../@babel/runtime/helpers/regeneratorAsyncGen.js", "../../@babel/runtime/helpers/regeneratorAsync.js", "../../@babel/runtime/helpers/regeneratorKeys.js", "../../@babel/runtime/helpers/typeof.js", "../../@babel/runtime/helpers/regeneratorValues.js", "../../@babel/runtime/helpers/regeneratorRuntime.js", "../../@babel/runtime/regenerator/index.js", "../../@babel/runtime/helpers/esm/extends.js", "../../@babel/runtime/helpers/esm/asyncToGenerator.js", "../../gdt-jsapi/es/invoker.js", "../../gdt-jsapi/es/utils/common.js", "../../gdt-jsapi/es/utils/transformReturn.js", "../../gdt-jsapi/es/alert.js", "../../gdt-jsapi/es/authConfig.js", "../../gdt-jsapi/es/bizContactDepartmentsPickerExternal.js", "../../gdt-jsapi/es/bizCustomContactChooseExternal.js", "../../gdt-jsapi/es/bizCustomContactMultipleChooseExternal.js", "../../gdt-jsapi/es/callPhone.js", "../../gdt-jsapi/es/canIUse.js", "../../gdt-jsapi/es/version.js", "../../gdt-jsapi/es/utils/compareVersion.js", "../../gdt-jsapi/es/checkVPNAppInstalled.js", "../../gdt-jsapi/es/checkVPNAppOnline.js", "../../gdt-jsapi/es/chooseContact.js", "../../gdt-jsapi/es/chooseContactWithComplexPicker.js", "../../gdt-jsapi/es/chooseDateRangeWithCalendar.js", "../../gdt-jsapi/es/chooseDayWithCalendar.js", "../../gdt-jsapi/es/chooseDepartments.js", "../../gdt-jsapi/es/chooseFile.js", "../../gdt-jsapi/es/chooseHalfDayWithCalendar.js", "../../gdt-jsapi/es/chooseImage.js", "../../gdt-jsapi/es/chooseInterconnectionChat.js", "../../gdt-jsapi/es/chooseLocalImage.js", "../../gdt-jsapi/es/chooseSpaceDir.js", "../../gdt-jsapi/es/chooseTimeWithCalendar.js", "../../gdt-jsapi/es/chooseVideo.js", "../../gdt-jsapi/es/closePage.js", "../../gdt-jsapi/es/complexPickerAdmin.js", "../../gdt-jsapi/es/confirm.js", "../../gdt-jsapi/es/copyToClipboard.js", "../../gdt-jsapi/es/createChatGroup.js", "../../gdt-jsapi/es/createDing.js", "../../gdt-jsapi/es/createDingV2.js", "../../gdt-jsapi/es/createVideoConf.js", "../../gdt-jsapi/es/createVideoMeeting.js", "../../gdt-jsapi/es/dealWithBackAction.js", "../../gdt-jsapi/es/disableClosePage.js", "../../gdt-jsapi/es/disablePullToRefresh.js", "../../gdt-jsapi/es/disableWebviewBounce.js", "../../gdt-jsapi/es/downloadAudio.js", "../../gdt-jsapi/es/downloadFile.js", "../../gdt-jsapi/es/enablePullToRefresh.js", "../../gdt-jsapi/es/enableVpn.js", "../../gdt-jsapi/es/enableWebviewBounce.js", "../../gdt-jsapi/es/exclusiveInvoke.js", "../../gdt-jsapi/es/faceComparison.js", "../../gdt-jsapi/es/faceRecognition.js", "../../gdt-jsapi/es/getAppInstallStatus.js", "../../gdt-jsapi/es/getAuthCode.js", "../../gdt-jsapi/es/getConfig.js", "../../gdt-jsapi/es/getContainerType.js", "../../gdt-jsapi/es/getDeviceId.js", "../../gdt-jsapi/es/getFromClipboard.js", "../../gdt-jsapi/es/getGeolocation.js", "../../gdt-jsapi/es/getGeolocationStatus.js", "../../gdt-jsapi/es/getHotspotInfo.js", "../../gdt-jsapi/es/getLanguageSetting.js", "../../gdt-jsapi/es/getLoginUser.js", "../../gdt-jsapi/es/getNetworkType.js", "../../gdt-jsapi/es/getPhoneInfo.js", "../../gdt-jsapi/es/getProxyInfo.js", "../../gdt-jsapi/es/getStorageItem.js", "../../gdt-jsapi/es/getTraceStatus.js", "../../gdt-jsapi/es/getUUID.js", "../../gdt-jsapi/es/getUserAgent.js", "../../gdt-jsapi/es/utils/getWaterMarkConfig.js", "../../gdt-jsapi/es/packages/h5-watermark/watermark.js", "../../gdt-jsapi/es/getWaterMark.js", "../../gdt-jsapi/es/getWaterMarkConfigV2.js", "../../gdt-jsapi/es/packages/h5-watermark/generateWaterMarkV2.js", "../../gdt-jsapi/es/getWaterMarkV2.js", "../../gdt-jsapi/es/getWifiStatus.js", "../../gdt-jsapi/es/getWorkbenchContext.js", "../../gdt-jsapi/es/goBack.js", "../../gdt-jsapi/es/hideLoading.js", "../../gdt-jsapi/es/hideOptionMenu.js", "../../gdt-jsapi/es/hideTitleBar.js", "../../gdt-jsapi/es/isDownloadFileExist.js", "../../gdt-jsapi/es/joinScheduleConf.js", "../../gdt-jsapi/es/joinVideoConf.js", "../../gdt-jsapi/es/joinVideoMeeting.js", "../../gdt-jsapi/es/locateOnMap.js", "../../gdt-jsapi/es/on.js", "../../gdt-jsapi/es/onAudioPlayEnd.js", "../../gdt-jsapi/es/onRecordAudioEnd.js", "../../gdt-jsapi/es/openApiInvoker.js", "../../gdt-jsapi/es/openApp.js", "../../gdt-jsapi/es/openBrowser.js", "../../gdt-jsapi/es/openChat.js", "../../gdt-jsapi/es/openDownloadFile.js", "../../gdt-jsapi/es/openLink.js", "../../gdt-jsapi/es/openPage.js", "../../gdt-jsapi/es/openSchemeUrl.js", "../../gdt-jsapi/es/openSlidePanel.js", "../../gdt-jsapi/es/openWatermarkCamera.js", "../../gdt-jsapi/es/pauseAudio.js", "../../gdt-jsapi/es/pickChat.js", "../../gdt-jsapi/es/pickChatByCorpId.js", "../../gdt-jsapi/es/pickGroupChat.js", "../../gdt-jsapi/es/pickGroupConversation.js", "../../gdt-jsapi/es/playAudio.js", "../../gdt-jsapi/es/previewDoc.js", "../../gdt-jsapi/es/previewImage.js", "../../gdt-jsapi/es/printFile.js", "../../gdt-jsapi/es/printNativeLog.js", "../../gdt-jsapi/es/prompt.js", "../../gdt-jsapi/es/pushWindow.js", "../../gdt-jsapi/es/readImageToBase64.js", "../../gdt-jsapi/es/ready.js", "../../gdt-jsapi/es/reduceImageSize.js", "../../gdt-jsapi/es/removeStorageItem.js", "../../gdt-jsapi/es/replacePage.js", "../../gdt-jsapi/es/resetView.js", "../../gdt-jsapi/es/resumeAudio.js", "../../gdt-jsapi/es/rotateView.js", "../../gdt-jsapi/es/scan.js", "../../gdt-jsapi/es/searchOnMap.js", "../../gdt-jsapi/es/sendOutData.js", "../../gdt-jsapi/es/setLocalScreenShotPolicy.js", "../../gdt-jsapi/es/setNavIcon.js", "../../gdt-jsapi/es/setNavLeftText.js", "../../gdt-jsapi/es/setOptionMenu.js", "../../gdt-jsapi/es/setProxyInfo.js", "../../gdt-jsapi/es/setStorageItem.js", "../../gdt-jsapi/es/setTitle.js", "../../gdt-jsapi/es/shareFileToMessage.js", "../../gdt-jsapi/es/shareImageToMessage.js", "../../gdt-jsapi/es/shareToMessage.js", "../../gdt-jsapi/es/shootVideo.js", "../../gdt-jsapi/es/showActionSheet.js", "../../gdt-jsapi/es/showCallMenu.js", "../../gdt-jsapi/es/showDatePicker.js", "../../gdt-jsapi/es/showDateTimePicker.js", "../../gdt-jsapi/es/showExtendModal.js", "../../gdt-jsapi/es/showHomeBottomTab.js", "../../gdt-jsapi/es/showLoading.js", "../../gdt-jsapi/es/showModal.js", "../../gdt-jsapi/es/showMultiSelect.js", "../../gdt-jsapi/es/showOnMap.js", "../../gdt-jsapi/es/showOptionMenu.js", "../../gdt-jsapi/es/showPlainInputUponKeyboard.js", "../../gdt-jsapi/es/showQuickCallMenu.js", "../../gdt-jsapi/es/showSelect.js", "../../gdt-jsapi/es/showSignature.js", "../../gdt-jsapi/es/showSocialShare.js", "../../gdt-jsapi/es/showTimePicker.js", "../../gdt-jsapi/es/showTitleBar.js", "../../gdt-jsapi/es/startFaceRecognition.js", "../../gdt-jsapi/es/startGeolocation.js", "../../gdt-jsapi/es/startListenNetworkStatus.js", "../../gdt-jsapi/es/startRecordAudio.js", "../../gdt-jsapi/es/startTraceReport.js", "../../gdt-jsapi/es/startVPNApp.js", "../../gdt-jsapi/es/startWatchShake.js", "../../gdt-jsapi/es/stopAudio.js", "../../gdt-jsapi/es/stopGeolocation.js", "../../gdt-jsapi/es/stopListenNetworkStatus.js", "../../gdt-jsapi/es/stopPullToRefresh.js", "../../gdt-jsapi/es/stopRecordAudio.js", "../../gdt-jsapi/es/stopTraceReport.js", "../../gdt-jsapi/es/stopVPNApp.js", "../../gdt-jsapi/es/stopWatchShake.js", "../../gdt-jsapi/es/subscribe.js", "../../gdt-jsapi/es/takePhoto.js", "../../gdt-jsapi/es/testProxy.js", "../../gdt-jsapi/es/toast.js", "../../gdt-jsapi/es/unlockWithSecurityVerification.js", "../../gdt-jsapi/es/unsubscribe.js", "../../gdt-jsapi/es/uploadFile.js", "../../gdt-jsapi/es/uploadFileByType.js", "../../gdt-jsapi/es/uploadLocalFile.js", "../../gdt-jsapi/es/uploadRemoteFileToDisk.js", "../../gdt-jsapi/es/ut.js", "../../gdt-jsapi/es/vibrate.js", "../../gdt-jsapi/es/index.js"], "sourcesContent": ["function _OverloadYield(e, d) {\n  this.v = e, this.k = d;\n}\nmodule.exports = _OverloadYield, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _regeneratorDefine(e, r, n, t) {\n  var i = Object.defineProperty;\n  try {\n    i({}, \"\", {});\n  } catch (e) {\n    i = 0;\n  }\n  module.exports = _regeneratorDefine = function regeneratorDefine(e, r, n, t) {\n    if (r) i ? i(e, r, {\n      value: n,\n      enumerable: !t,\n      configurable: !t,\n      writable: !t\n    }) : e[r] = n;else {\n      var o = function o(r, n) {\n        _regeneratorDefine(e, r, function (e) {\n          return this._invoke(r, n, e);\n        });\n      };\n      o(\"next\", 0), o(\"throw\", 1), o(\"return\", 2);\n    }\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports, _regeneratorDefine(e, r, n, t);\n}\nmodule.exports = _regeneratorDefine, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var regeneratorDefine = require(\"./regeneratorDefine.js\");\nfunction _regenerator() {\n  /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */\n  var e,\n    t,\n    r = \"function\" == typeof Symbol ? Symbol : {},\n    n = r.iterator || \"@@iterator\",\n    o = r.toStringTag || \"@@toStringTag\";\n  function i(r, n, o, i) {\n    var c = n && n.prototype instanceof Generator ? n : Generator,\n      u = Object.create(c.prototype);\n    return regeneratorDefine(u, \"_invoke\", function (r, n, o) {\n      var i,\n        c,\n        u,\n        f = 0,\n        p = o || [],\n        y = !1,\n        G = {\n          p: 0,\n          n: 0,\n          v: e,\n          a: d,\n          f: d.bind(e, 4),\n          d: function d(t, r) {\n            return i = t, c = 0, u = e, G.n = r, a;\n          }\n        };\n      function d(r, n) {\n        for (c = r, u = n, t = 0; !y && f && !o && t < p.length; t++) {\n          var o,\n            i = p[t],\n            d = G.p,\n            l = i[2];\n          r > 3 ? (o = l === n) && (u = i[(c = i[4]) ? 5 : (c = 3, 3)], i[4] = i[5] = e) : i[0] <= d && ((o = r < 2 && d < i[1]) ? (c = 0, G.v = n, G.n = i[1]) : d < l && (o = r < 3 || i[0] > n || n > l) && (i[4] = r, i[5] = n, G.n = l, c = 0));\n        }\n        if (o || r > 1) return a;\n        throw y = !0, n;\n      }\n      return function (o, p, l) {\n        if (f > 1) throw TypeError(\"Generator is already running\");\n        for (y && 1 === p && d(p, l), c = p, u = l; (t = c < 2 ? e : u) || !y;) {\n          i || (c ? c < 3 ? (c > 1 && (G.n = -1), d(c, u)) : G.n = u : G.v = u);\n          try {\n            if (f = 2, i) {\n              if (c || (o = \"next\"), t = i[o]) {\n                if (!(t = t.call(i, u))) throw TypeError(\"iterator result is not an object\");\n                if (!t.done) return t;\n                u = t.value, c < 2 && (c = 0);\n              } else 1 === c && (t = i[\"return\"]) && t.call(i), c < 2 && (u = TypeError(\"The iterator does not provide a '\" + o + \"' method\"), c = 1);\n              i = e;\n            } else if ((t = (y = G.n < 0) ? u : r.call(n, G)) !== a) break;\n          } catch (t) {\n            i = e, c = 1, u = t;\n          } finally {\n            f = 1;\n          }\n        }\n        return {\n          value: t,\n          done: y\n        };\n      };\n    }(r, o, i), !0), u;\n  }\n  var a = {};\n  function Generator() {}\n  function GeneratorFunction() {}\n  function GeneratorFunctionPrototype() {}\n  t = Object.getPrototypeOf;\n  var c = [][n] ? t(t([][n]())) : (regeneratorDefine(t = {}, n, function () {\n      return this;\n    }), t),\n    u = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c);\n  function f(e) {\n    return Object.setPrototypeOf ? Object.setPrototypeOf(e, GeneratorFunctionPrototype) : (e.__proto__ = GeneratorFunctionPrototype, regeneratorDefine(e, o, \"GeneratorFunction\")), e.prototype = Object.create(u), e;\n  }\n  return GeneratorFunction.prototype = GeneratorFunctionPrototype, regeneratorDefine(u, \"constructor\", GeneratorFunctionPrototype), regeneratorDefine(GeneratorFunctionPrototype, \"constructor\", GeneratorFunction), GeneratorFunction.displayName = \"GeneratorFunction\", regeneratorDefine(GeneratorFunctionPrototype, o, \"GeneratorFunction\"), regeneratorDefine(u), regeneratorDefine(u, o, \"Generator\"), regeneratorDefine(u, n, function () {\n    return this;\n  }), regeneratorDefine(u, \"toString\", function () {\n    return \"[object Generator]\";\n  }), (module.exports = _regenerator = function _regenerator() {\n    return {\n      w: i,\n      m: f\n    };\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports)();\n}\nmodule.exports = _regenerator, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var OverloadYield = require(\"./OverloadYield.js\");\nvar regeneratorDefine = require(\"./regeneratorDefine.js\");\nfunction AsyncIterator(t, e) {\n  function n(r, o, i, f) {\n    try {\n      var c = t[r](o),\n        u = c.value;\n      return u instanceof OverloadYield ? e.resolve(u.v).then(function (t) {\n        n(\"next\", t, i, f);\n      }, function (t) {\n        n(\"throw\", t, i, f);\n      }) : e.resolve(u).then(function (t) {\n        c.value = t, i(c);\n      }, function (t) {\n        return n(\"throw\", t, i, f);\n      });\n    } catch (t) {\n      f(t);\n    }\n  }\n  var r;\n  this.next || (regeneratorDefine(AsyncIterator.prototype), regeneratorDefine(AsyncIterator.prototype, \"function\" == typeof Symbol && Symbol.asyncIterator || \"@asyncIterator\", function () {\n    return this;\n  })), regeneratorDefine(this, \"_invoke\", function (t, o, i) {\n    function f() {\n      return new e(function (e, r) {\n        n(t, i, e, r);\n      });\n    }\n    return r = r ? r.then(f, f) : f();\n  }, !0);\n}\nmodule.exports = AsyncIterator, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var regenerator = require(\"./regenerator.js\");\nvar regeneratorAsyncIterator = require(\"./regeneratorAsyncIterator.js\");\nfunction _regeneratorAsyncGen(r, e, t, o, n) {\n  return new regeneratorAsyncIterator(regenerator().w(r, e, t, o), n || Promise);\n}\nmodule.exports = _regeneratorAsyncGen, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var regeneratorAsyncGen = require(\"./regeneratorAsyncGen.js\");\nfunction _regeneratorAsync(n, e, r, t, o) {\n  var a = regeneratorAsyncGen(n, e, r, t, o);\n  return a.next().then(function (n) {\n    return n.done ? n.value : a.next();\n  });\n}\nmodule.exports = _regeneratorAsync, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _regeneratorKeys(e) {\n  var n = Object(e),\n    r = [];\n  for (var t in n) r.unshift(t);\n  return function e() {\n    for (; r.length;) if ((t = r.pop()) in n) return e.value = t, e.done = !1, e;\n    return e.done = !0, e;\n  };\n}\nmodule.exports = _regeneratorKeys, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return module.exports = _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports, _typeof(o);\n}\nmodule.exports = _typeof, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var _typeof = require(\"./typeof.js\")[\"default\"];\nfunction _regeneratorValues(e) {\n  if (null != e) {\n    var t = e[\"function\" == typeof Symbol && Symbol.iterator || \"@@iterator\"],\n      r = 0;\n    if (t) return t.call(e);\n    if (\"function\" == typeof e.next) return e;\n    if (!isNaN(e.length)) return {\n      next: function next() {\n        return e && r >= e.length && (e = void 0), {\n          value: e && e[r++],\n          done: !e\n        };\n      }\n    };\n  }\n  throw new TypeError(_typeof(e) + \" is not iterable\");\n}\nmodule.exports = _regeneratorValues, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var OverloadYield = require(\"./OverloadYield.js\");\nvar regenerator = require(\"./regenerator.js\");\nvar regeneratorAsync = require(\"./regeneratorAsync.js\");\nvar regeneratorAsyncGen = require(\"./regeneratorAsyncGen.js\");\nvar regeneratorAsyncIterator = require(\"./regeneratorAsyncIterator.js\");\nvar regeneratorKeys = require(\"./regeneratorKeys.js\");\nvar regeneratorValues = require(\"./regeneratorValues.js\");\nfunction _regeneratorRuntime() {\n  \"use strict\";\n\n  var r = regenerator(),\n    e = r.m(_regeneratorRuntime),\n    t = (Object.getPrototypeOf ? Object.getPrototypeOf(e) : e.__proto__).constructor;\n  function n(r) {\n    var e = \"function\" == typeof r && r.constructor;\n    return !!e && (e === t || \"GeneratorFunction\" === (e.displayName || e.name));\n  }\n  var o = {\n    \"throw\": 1,\n    \"return\": 2,\n    \"break\": 3,\n    \"continue\": 3\n  };\n  function a(r) {\n    var e, t;\n    return function (n) {\n      e || (e = {\n        stop: function stop() {\n          return t(n.a, 2);\n        },\n        \"catch\": function _catch() {\n          return n.v;\n        },\n        abrupt: function abrupt(r, e) {\n          return t(n.a, o[r], e);\n        },\n        delegateYield: function delegateYield(r, o, a) {\n          return e.resultName = o, t(n.d, regeneratorValues(r), a);\n        },\n        finish: function finish(r) {\n          return t(n.f, r);\n        }\n      }, t = function t(r, _t, o) {\n        n.p = e.prev, n.n = e.next;\n        try {\n          return r(_t, o);\n        } finally {\n          e.next = n.n;\n        }\n      }), e.resultName && (e[e.resultName] = n.v, e.resultName = void 0), e.sent = n.v, e.next = n.n;\n      try {\n        return r.call(this, e);\n      } finally {\n        n.p = e.prev, n.n = e.next;\n      }\n    };\n  }\n  return (module.exports = _regeneratorRuntime = function _regeneratorRuntime() {\n    return {\n      wrap: function wrap(e, t, n, o) {\n        return r.w(a(e), t, n, o && o.reverse());\n      },\n      isGeneratorFunction: n,\n      mark: r.m,\n      awrap: function awrap(r, e) {\n        return new OverloadYield(r, e);\n      },\n      AsyncIterator: regeneratorAsyncIterator,\n      async: function async(r, e, t, o, u) {\n        return (n(e) ? regeneratorAsyncGen : regeneratorAsync)(a(r), e, t, o, u);\n      },\n      keys: regeneratorKeys,\n      values: regeneratorValues\n    };\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports)();\n}\nmodule.exports = _regeneratorRuntime, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "// TODO(Babel 8): Remove this file.\n\nvar runtime = require(\"../helpers/regeneratorRuntime\")();\nmodule.exports = runtime;\n\n// Copied from https://github.com/facebook/regenerator/blob/main/packages/runtime/runtime.js#L736=\ntry {\n  regeneratorRuntime = runtime;\n} catch (accidentalStrictMode) {\n  if (typeof globalThis === \"object\") {\n    globalThis.regeneratorRuntime = runtime;\n  } else {\n    Function(\"r\", \"regeneratorRuntime = r\")(runtime);\n  }\n}\n", "function _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nexport { _extends as default };", "function asyncGeneratorStep(n, t, e, r, o, a, c) {\n  try {\n    var i = n[a](c),\n      u = i.value;\n  } catch (n) {\n    return void e(n);\n  }\n  i.done ? t(u) : Promise.resolve(u).then(r, o);\n}\nfunction _asyncToGenerator(n) {\n  return function () {\n    var t = this,\n      e = arguments;\n    return new Promise(function (r, o) {\n      var a = n.apply(t, e);\n      function _next(n) {\n        asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n);\n      }\n      function _throw(n) {\n        asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n);\n      }\n      _next(void 0);\n    });\n  };\n}\nexport { _asyncToGenerator as default };", "import _extends from\"@babel/runtime/helpers/extends\";import _asyncToGenerator from\"@babel/runtime/helpers/asyncToGenerator\";import _regeneratorRuntime from\"@babel/runtime/regenerator\";import{isMiniApp}from\"./utils/common\";export var BRIDGE_ERROR_CODE;!function(e){e.CANCEL=\"-1\",e.SUCCESS=\"0\",e.API_UNDEFINED=\"1\",e.INVALID_PARAMS=\"2\",e.UNKNOWN_ERROR=\"3\",e.UNAUTHORIZED_CALL=\"4\",e.WRONG_CORP_ID=\"5\",e.CREATE_CHAT_FAILED=\"6\",e.UNAUTHORIZED_API=\"7\",e.INVALID_CORP_ID=\"8\",e.SERVER_RESPONSE_ERROR=\"9\",e.WRONG_DEVICE_INFO=\"10\",e.UPLOAD_FAIL=\"11\",e.PROCESS_FAIL=\"12\",e.DUPLICATED_CALL=\"13\",e.TOO_LARGE_PIC=\"14\",e.REQUEST_REJECT_OR_INSECURE_REQUEST=\"15\",e.PC_NOT_ALLOWED_TO_OPEN_SIDE_PANE_OR_MODAL=\"21\",e.PC_CLOSE_SIDE_PANE_OR_MODAL=\"22\",e.UNAUTHORIZED_PARAMS=\"23\",e.GESTURE_PASSWORD_DOES_NOT_EXIST=\"24\",e.NETWORK_ERROR=\"25\"}(BRIDGE_ERROR_CODE||(BRIDGE_ERROR_CODE={}));export var API_INVOKER_TYPE;!function(e){e.MOBILE=\"mobile\",e.PC=\"pc\",e.MINI_APP=\"mini\",e.UNKNOWN=\"unknown\"}(API_INVOKER_TYPE||(API_INVOKER_TYPE={}));export var PLATFORM_TYPE_ENUM;!function(e){e.ANDROID=\"android\",e.IOS=\"ios\",e.UNKNOW=\"unknow\"}(PLATFORM_TYPE_ENUM||(PLATFORM_TYPE_ENUM={}));export var CONTINUOUS_EVENT_LIST;!function(e){e.UPDATE_NETWORK_STATUS=\"DINGGOV_ON_NETWORK_TYPE_CHANGED\",e.UPDATE_LOCATION=\"DINGGOV_GEO_LOCATION_UPDATE\",e.UPDATE_TRACE=\"DINGGOV_TRACE_UPDATE\",e.ON_SHAKE=\"onShake\"}(CONTINUOUS_EVENT_LIST||(CONTINUOUS_EVENT_LIST={}));export var Container_Type_Enum;!function(e){e.isDingTalk=\"DingTalk\",e.isMpaas=\"mPaaS\",e.isUnknow=\"unknow\"}(Container_Type_Enum||(Container_Type_Enum={}));var ua=navigator&&(navigator.swuserAgent||navigator.userAgent)||\"\",Invoker=function(){function e(){this.readyFnStack=[],this.generalEventCallbackStack={},this.apiList={},this.continuousCallbackStack={},this.isH5Mobile=null,this.appType=null,this.platformType=null,this.aliBridge=window&&window.navigator&&window.AlipayJSBridge,this.isReady=!1,this.init(),console.warn(\"请将 gdt-jsapi 版本请升级到 1.9.24 版本以上的最新版本，谢谢\")}var n=e.prototype;return n.h5AndroidbridgeInit=function(){var e=this;this.h5BridgeReadyPromise=new Promise((function(n,i){var t=function(){try{window.WebViewJavascriptBridgeAndroid=window.nuva&&window.nuva.require(),e.execReadyFn()}catch(e){}};window.nuva&&(void 0===window.nuva.isReady||window.nuva.isReady)?t():(document.addEventListener(\"runtimeready\",(function(){t()}),!1),document.addEventListener(\"runtimefailed\",(function(t){var r=t&&t.detail||{errorCode:BRIDGE_ERROR_CODE.INVALID_PARAMS,errorMessage:\"unknown nuvajs bootstrap error\"};e.handleBridgeResponse(r,n,i)}),!1))}))},n.h5IosBridgeInit=function(){var e=this;this.h5BridgeReadyPromise=new Promise((function(n,i){if(\"undefined\"!=typeof WebViewJavascriptBridge)try{WebViewJavascriptBridge.init((function(e,n){})),e.execReadyFn()}catch(e){}else document.addEventListener(\"WebViewJavascriptBridgeReady\",(function(){try{WebViewJavascriptBridge&&WebViewJavascriptBridge.init((function(e,n){})),e.execReadyFn()}catch(e){}}),!1)}))},n.init=function(){var e=this,n=this.getAppType(),i=this.getContainerType();if(n===API_INVOKER_TYPE.PC&&window.dingtalk&&!window.dingtalk.isRegister&&(window.dingtalk.isRegister=!0,window.dingtalk.callbackStack={},window.dingtalk.event.register((function(n,i){if(e.continuousCallbackStack[n])e.continuousCallbackStack[n](i);else if(i){var t=\"\"+i.msgId;\"openapi.event.emit\"===n?(console.log(\"dingtalk receive event:\",i,\"identifer is\",t),window.dingtalk.callbackStack[t]&&(window.dingtalk.callbackStack[t](i),delete window.dingtalk.callbackStack[t])):\"im.fileTask.addNewTask\"===n||\"im.fileTask.updateTask\"===n?(i.msgId||i.taskId)&&\"function\"==typeof e.continuousCallbackStack[i.msgId||i.taskId]&&e.continuousCallbackStack[i.msgId||i.taskId](n,i):e.generalEventCallbackStack[n]&&e.generalEventCallbackStack[n].forEach((function(n){n.call(e,i)}))}}))),n===API_INVOKER_TYPE.MOBILE){if(i===Container_Type_Enum.isDingTalk)this.platformType===PLATFORM_TYPE_ENUM.ANDROID?!this.h5BridgeReadyPromise&&this.h5AndroidbridgeInit():this.platformType===PLATFORM_TYPE_ENUM.IOS&&!this.h5BridgeReadyPromise&&this.h5IosBridgeInit();else if(i===Container_Type_Enum.isMpaas&&n===API_INVOKER_TYPE.MOBILE)if(window.AlipayJSBridge)this.execReadyFn();else{var t=setTimeout((function(){console.warn(\"window.AlipayJSBridge 未初始化完毕，走到兜底逻辑\",e.isReady,window.AlipayJSBridge),e.isReady||e.execReadyFn.call(e)}),5200);document.addEventListener(\"AlipayJSBridgeReady\",(function(){e.isReady||(clearTimeout(t),e.execReadyFn.call(e))}),!1)}}else setTimeout((function(){e.execReadyFn()}))},n.execReadyFn=function(){this.isReady=!0;for(var e=this.readyFnStack.shift();e;)e&&e(this),e=this.readyFnStack.shift()},n.onReady=function(e){this.isReady?e&&e(this):this.readyFnStack.push(e)},n.setCurrentInvoker=function(e){this.currentInvoker=e},n.getCurrentInvoker=function(){return this.currentInvoker},n.getBridge=function(){return this.aliBridge},n.getContainerType=function(){return/TaurusApp/g.test(ua)?/DingTalk/g.test(ua)?Container_Type_Enum.isDingTalk:Container_Type_Enum.isMpaas:/DingTalk/g.test(ua)?Container_Type_Enum.isDingTalk:/mPaaSClient/g.test(ua)||/Nebula/g.test(ua)?Container_Type_Enum.isMpaas:Container_Type_Enum.isUnknow},n.getAppType=function(){return this.appType||(this.isMobile()?this.appType=API_INVOKER_TYPE.MOBILE:window&&window.navigator&&window.navigator.userAgent&&window.navigator.userAgent.indexOf(\"dingtalk-win\")>=0&&window.navigator.userAgent.indexOf(\"TaurusApp\")>=0?this.appType=API_INVOKER_TYPE.PC:isMiniApp()?this.appType=API_INVOKER_TYPE.MINI_APP:(console.warn(\"检测到页面在非专有钉钉客户端中打开，JSAPI 调用可能不会生效！\"),this.appType=API_INVOKER_TYPE.UNKNOWN)),this.appType},n.isMobile=function(){var e=/iPhone|iPad|iPod|iOS/i.test(ua),n=/Android/i.test(ua),i=window&&window.navigator&&window.navigator.userAgent||\"\";return null!==this.isH5Mobile?this.isH5Mobile:i&&i.indexOf(\"dingtalk-win\")>=0?(this.isH5Mobile=!1,!1):!(!i||!(i.includes(\"mPaaSClient\")||i.includes(\"Nebula\")||i.includes(\"DingTalk\")))&&(this.isH5Mobile=!0,this.platformType=e?PLATFORM_TYPE_ENUM.IOS:n?PLATFORM_TYPE_ENUM.ANDROID:PLATFORM_TYPE_ENUM.UNKNOW,!0)},n.registerEvent=function(e,n){var i=this;if(\"function\"==typeof n)return this.getAppType()===API_INVOKER_TYPE.PC?(this.generalEventCallbackStack[e]||(this.generalEventCallbackStack[e]=[]),this.generalEventCallbackStack[e].push(n),function(){var t=i.generalEventCallbackStack[e].findIndex((function(e){return e===n}));i.generalEventCallbackStack[e].splice(t,1)}):this.getAppType()===API_INVOKER_TYPE.MOBILE?(document.addEventListener(e,n,!1),function(){document.removeEventListener(e,n)}):void 0;console.error(\"callback 参数应该为函数\")},n.registerClientAPI=function(e,n){this.apiList[e]=n},n.registerAPI=function(e,n){this.isMobile();if(\"object\"==typeof n){var i=n,t=this.getAppType();this.registerClientAPI(e,i[t])}else this.registerClientAPI(e,n)},n.invokeMiniApp=function(){var e=_asyncToGenerator(_regeneratorRuntime.mark((function e(n,i){var t=this;return _regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return void 0===i&&(i={}),e.abrupt(\"return\",new Promise((function(e,r){i=_extends({_apiName:n},i);var a=t.apiList[n],o=t.getContainerType();if(!a)return console.warn(\"API: \"+n+\"，未注册\"),r(\"API: \"+n+\"，未注册\");if(o===Container_Type_Enum.isMpaas){if(\"function\"==typeof a)return void a.call(null,i,{context:my,resolve:e,reject:r,methodName:n});my.call(n,i,(function(n){t.handleBridgeResponse(n,e,r)}))}else if(o===Container_Type_Enum.isDingTalk){if(\"function\"==typeof a)return void a.call(null,i,{context:dd.dtBridge,resolve:e,reject:r,methodName:n,containerType:o,appType:API_INVOKER_TYPE.MINI_APP});dd.dtBridge({m:\"taurus.common.\"+n,args:i,onSuccess:function(n){t.handleBridgeResponse(n,e,r)},onFail:function(n){t.handleBridgeResponse(n,e,r)}})}})));case 2:case\"end\":return e.stop()}}),e)})));return function(n,i){return e.apply(this,arguments)}}(),n.invokeMobile=function(){var e=_asyncToGenerator(_regeneratorRuntime.mark((function e(n,i,t){var r=this;return _regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return void 0===i&&(i={}),e.abrupt(\"return\",new Promise((function(e,a){i=_extends({_apiName:n},i);var o=r.apiList[n],s=r.getContainerType();if(!o)return console.warn(\"API: \"+n+\"，未注册\"),a(\"API: \"+n+\"，未注册\");if(s===Container_Type_Enum.isDingTalk){if(r.platformType===PLATFORM_TYPE_ENUM.IOS){var c=Object.assign({},i);if(!0===c.watch&&\"undefined\"!=typeof WebViewJavascriptBridge&&WebViewJavascriptBridge.registerHandler(null!=t&&t.dingTalkAPIName?null==t?void 0:t.dingTalkAPIName:\"taurus.common.\"+n,(function(e,n){\"function\"==typeof i.onSuccess&&i.onSuccess.call(null,e),n&&n({errorCode:\"0\",errorMessage:\"success\"})})),\"function\"==typeof o)return void o.call(null,i,{context:window.WebViewJavascriptBridge,resolve:e,reject:a,methodName:n,containerType:s,appType:API_INVOKER_TYPE.MOBILE,platformType:PLATFORM_TYPE_ENUM.IOS,watch:c.watch});void 0!==window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler(\"taurus.common.\"+n,Object.assign({},c),(function(n){!c.watch&&r.handleBridgeResponse(n||{},e,a)}))}else if(r.platformType===PLATFORM_TYPE_ENUM.ANDROID){var u=n.split(\".\"),d=u.pop()||\"\",l=u.join(\".\")||\"taurus.common\";if(\"function\"==typeof o)return void o.call(null,i,{context:window.WebViewJavascriptBridgeAndroid,resolve:e,reject:a,methodName:n,containerType:s,appType:API_INVOKER_TYPE.MOBILE,platformType:PLATFORM_TYPE_ENUM.ANDROID});\"function\"==typeof window.WebViewJavascriptBridgeAndroid&&window.WebViewJavascriptBridgeAndroid((function(n){r.handleBridgeResponse(n,e,a)}),(function(n){r.handleBridgeResponse(n,e,a)}),l,d,i)}}else if(s===Container_Type_Enum.isMpaas){if(\"function\"==typeof o)return void o.call(null,i,{context:AlipayJSBridge,resolve:e,reject:a,methodName:n});AlipayJSBridge.call(n,i,(function(n){r.handleBridgeResponse(n,e,a)}))}})));case 2:case\"end\":return e.stop()}}),e)})));return function(n,i,t){return e.apply(this,arguments)}}(),n.findFitMsgId=function(e){var n,i;return null!==(n=window.dingtalk)&&void 0!==n&&null!==(i=n.callbackStack)&&void 0!==i&&i[e]?this.findFitMsgId(e+1):e},n.invokePC=function(){var e=_asyncToGenerator(_regeneratorRuntime.mark((function e(n,i,t){var r=this;return _regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return void 0===i&&(i={}),void 0===t&&(t={msgId:1}),e.abrupt(\"return\",new Promise((function(e,a){try{i=_extends({_apiName:n},i);var o=r.findFitMsgId(Date.now()),s=t.pcClientAPIName||n;if(t.msgId=o,!window.dingtalk)return Promise.reject(new Error(\"请在钉钉容器内使用 JSAPI\"));r.apiList[n]?r.apiList[n].call(null,i,t):(console.info(\"invoke bridge api:\",s,o,i),window.dingtalk.platform.invokeAPI(o,s,i)),window.dingtalk&&window.dingtalk.isRegister&&!window.dingtalk.callbackStack&&(window.dingtalk.callbackStack={}),window.dingtalk.callbackStack[\"\"+o]=function(n){var i=n;return i.body?e(i.body):e(i)}}catch(e){a(e)}})));case 3:case\"end\":return e.stop()}}),e)})));return function(n,i,t){return e.apply(this,arguments)}}(),n.handleBridgeResponse=function(e,n,i){e&&e.errorCode?e.errorCode===BRIDGE_ERROR_CODE.SUCCESS?n(e.result):(console.warn(\"API 调用失败\",e),i(e)):e&&\"false\"===e.success?i(e):n(e)},n.invoke=function(){var e=_asyncToGenerator(_regeneratorRuntime.mark((function e(n,i,t){var r;return _regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(void 0===i&&(i={}),(r=this.getAppType())!==API_INVOKER_TYPE.MOBILE){e.next=8;break}if(this.isReady){e.next=5;break}return e.abrupt(\"return\",Promise.reject(\"错误：请在 dd.ready() 回调中使用 JSAPI，当前调用函数：\"+n));case 5:return e.abrupt(\"return\",this.invokeMobile(n,i,t));case 8:if(r!==API_INVOKER_TYPE.PC){e.next=12;break}return e.abrupt(\"return\",this.invokePC(n,i,t));case 12:if(r!==API_INVOKER_TYPE.MINI_APP){e.next=16;break}return e.abrupt(\"return\",this.invokeMiniApp(n,i));case 16:return e.abrupt(\"return\",Promise.reject(\"错误：未在钉钉运行环境下调用该 API，无效，请检查运行环境\"));case 17:case\"end\":return e.stop()}}),e,this)})));return function(n,i,t){return e.apply(this,arguments)}}(),n.existEventListener=function(e){return!!this.continuousCallbackStack[e]},n.registerContinuesEvent=function(e,n){this.continuousCallbackStack[e]=n},n.removeContinuesEvent=function(e){this.existEventListener(e)&&(this.continuousCallbackStack[e](),delete this.continuousCallbackStack[e])},e}();isMiniApp()||(window._invoker=window._invoker||new Invoker);export default isMiniApp()?new Invoker:window._invoker;", "export function isUndef(n){return\"undefined\"===n}export function isMiniApp(){return!isUndef(typeof my)&&null!==my&&!isUndef(typeof my.alert)}", "import _extends from\"@babel/runtime/helpers/extends\";export function p2c(n){return function(t){var e=t.success,c=t.fail,i=t.complete;n(t).then((function(n){e&&e(n)})).catch((function(n){c&&c(n)})).finally((function(){i&&i()}))}}export function c2p(n,t){if(n)return function(e){return\"function\"==typeof e||t.includes(\"Sync\")||t.startsWith(\"create\")?n(e):new Promise((function(t,c){n(_extends({},e,{success:function(n){t(n)},fail:function(n){c(n)}}))}))}}", "import invoker,{API_INVOKER_TYPE,PLATFORM_TYPE_ENUM}from\"./invoker\";function alertHandler(e,r){var n=r.resolve,o=r.reject,a=r.context,t=r.containerType,i=r.appType,l=r.platformType;if(t){var c=function(e){invoker.handleBridgeResponse(e,n,o)},s=function(e){invoker.handleBridgeResponse(e,n,o)};i===API_INVOKER_TYPE.MINI_APP?a&&a({m:\"taurus.common.alert\",args:e,onSuccess:c,onFail:s}):l===PLATFORM_TYPE_ENUM.ANDROID?a&&a(c,s,\"taurus.common\",\"alert\",e):l===PLATFORM_TYPE_ENUM.IOS&&a.callHandler(\"taurus.common.alert\",Object.assign({},e),(function(e){n(e)}))}else a&&a.call(\"alert\",e,(function(){n()}))}invoker.registerAPI(\"alert\",{mini:alertHandler,mobile:alertHandler}),alert.version={android:\"1.3.2\",ios:\"1.3.2\"};export default function alert(e){return invoker.invoke(\"alert\",e)}", "import invoker from\"./invoker\";invoker.registerAPI(\"authConfig\",{mini:!0,mobile:!0}),authConfig.version={android:\"1.2.0\",ios:\"1.2.0\",pc:\"1.2.0\"};export default function authConfig(i){return invoker.invoke(\"authConfig\",i)}", "import invoker,{API_INVOKER_TYPE,PLATFORM_TYPE_ENUM}from\"./invoker\";function chooseContactHandler(e,n){var t=n.resolve,r=n.reject,o=n.context,a=n.containerType,i=n.appType,c=n.platformType;if(a){var s=function(e){invoker.handleBridgeResponse(e,t,r)},l=function(e){invoker.handleBridgeResponse(e,t,r)};i===API_INVOKER_TYPE.MINI_APP?o&&o({m:\"taurus.common.bizContactDepartmentsPickerExternal\",args:e,onSuccess:s,onFail:l}):c===PLATFORM_TYPE_ENUM.ANDROID?o&&o(s,l,\"taurus.common\",\"bizContactDepartmentsPickerExternal\",e):c===PLATFORM_TYPE_ENUM.IOS&&o.callHandler(\"taurus.common.bizContactDepartmentsPickerExternal\",Object.assign({},e),(function(e){invoker.handleBridgeResponse(e,t,r)}))}else o&&o.call(\"bizContactDepartmentsPickerExternal\",e,(function(e){invoker.handleBridgeResponse(e,t,r)}))}invoker.registerAPI(\"bizContactDepartmentsPickerExternal\",{mini:chooseContactHandler,mobile:chooseContactHandler,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,\"biz.contact.departmentsPickerEx\",e)}}),bizContactDepartmentsPickerExternal.version={android:\"3.0.3\",ios:\"3.0.3\",pc:\"3.0.3\"};export default function bizContactDepartmentsPickerExternal(e){return invoker.invoke(\"bizContactDepartmentsPickerExternal\",e)}", "import invoker,{API_INVOKER_TYPE,PLATFORM_TYPE_ENUM}from\"./invoker\";function chooseContactHandler(o,n){var e=n.resolve,t=n.reject,a=n.context,i=n.containerType,r=n.appType,s=n.platformType;if(i){var c=function(o){invoker.handleBridgeResponse(o,e,t)},l=function(o){invoker.handleBridgeResponse(o,e,t)};r===API_INVOKER_TYPE.MINI_APP?a&&a({m:\"taurus.common.bizCustomContactChooseExternal\",args:o,onSuccess:c,onFail:l}):s===PLATFORM_TYPE_ENUM.ANDROID?a&&a(c,l,\"taurus.common\",\"bizCustomContactChooseExternal\",o):s===PLATFORM_TYPE_ENUM.IOS&&a.callHandler(\"taurus.common.bizCustomContactChooseExternal\",Object.assign({},o),(function(o){invoker.handleBridgeResponse(o,e,t)}))}else a&&a.call(\"bizCustomContactChooseExternal\",o,(function(o){invoker.handleBridgeResponse(o,e,t)}))}invoker.registerAPI(\"bizCustomContactChooseExternal\",{mini:chooseContactHandler,mobile:chooseContactHandler,pc:function(o,n){window.dingtalk.platform.invokeAPI(n.msgId,\"biz.customContact.chooseEx\",o)}}),bizCustomContactChooseExternal.version={android:\"3.0.3\",ios:\"3.0.3\",pc:\"3.0.3\"};export default function bizCustomContactChooseExternal(o){return invoker.invoke(\"bizCustomContactChooseExternal\",o)}", "import invoker,{API_INVOKER_TYPE,PLATFORM_TYPE_ENUM}from\"./invoker\";function chooseContactHandler(o,e){var t=e.resolve,n=e.reject,i=e.context,l=e.containerType,a=e.appType,r=e.platformType;if(l){var s=function(o){invoker.handleBridgeResponse(o,t,n)},c=function(o){invoker.handleBridgeResponse(o,t,n)};a===API_INVOKER_TYPE.MINI_APP?i&&i({m:\"taurus.common.bizCustomContactMultipleChooseExternal\",args:o,onSuccess:s,onFail:c}):r===PLATFORM_TYPE_ENUM.ANDROID?i&&i(s,c,\"taurus.common\",\"bizCustomContactMultipleChooseExternal\",o):r===PLATFORM_TYPE_ENUM.IOS&&i.callHandler(\"taurus.common.bizCustomContactMultipleChooseExternal\",Object.assign({},o),(function(o){invoker.handleBridgeResponse(o,t,n)}))}else i&&i.call(\"bizCustomContactMultipleChooseExternal\",o,(function(o){invoker.handleBridgeResponse(o,t,n)}))}invoker.registerAPI(\"bizCustomContactMultipleChooseExternal\",{mini:chooseContactHandler,mobile:chooseContactHandler,pc:function(o,e){window.dingtalk.platform.invokeAPI(e.msgId,\"biz.customContact.multipleChooseEx\",o)}}),bizCustomContactMultipleChooseExternal.version={android:\"3.0.3\",ios:\"3.0.3\",pc:\"3.0.3\"};export default function bizCustomContactMultipleChooseExternal(o){return invoker.invoke(\"bizCustomContactMultipleChooseExternal\",o)}", "import invoker from\"./invoker\";invoker.registerAPI(\"callPhone\",{mini:!0,mobile:!0}),callPhone.version={android:\"1.1.0\",ios:\"1.1.0\"};export default function callPhone(o){return invoker.invoke(\"callPhone\",o)}", "import _asyncToGenerator from\"@babel/runtime/helpers/asyncToGenerator\";import _regeneratorRuntime from\"@babel/runtime/regenerator\";import version from\"./version\";import compareVersion from\"./utils/compareVersion\";import apis from\"./index\";var u=navigator&&navigator.userAgent||\"\",isAndroid=function(){return u.indexOf(\"Android\")>-1||u.indexOf(\"Adr\")>-1},isiOS=function(){return!!u.match(/\\(i[^;]+;( U;)? CPU.+Mac OS X/)},isWindows=function(){return/(windows)/i.test(navigator.userAgent)};export default function canIUse(r){return _canIUse.apply(this,arguments)}function _canIUse(){return(_canIUse=_asyncToGenerator(_regeneratorRuntime.mark((function r(e){var n,i,t,o;return _regeneratorRuntime.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(apis[e]){r.next=2;break}return r.abrupt(\"return\",!1);case 2:return r.next=4,version();case 4:return n=r.sent,i=n.version,t=apis[e].version,o=isAndroid()?\"android\":isiOS()?\"ios\":isWindows()?\"pc\":\"unknown\",r.abrupt(\"return\",!(!t||!t[o])&&compareVersion(i,t[o])>0);case 9:case\"end\":return r.stop()}}),r)})))).apply(this,arguments)}", "import invoker from\"./invoker\";invoker.registerAPI(\"version\",{mini:!0,mobile:!0,pc:function(i,n){window.dingtalk.platform.invokeAPI(n.msgId,\"version\",{})}});export default function version(){return invoker.invoke(\"version\")}", "var GT=1,LT=-1,EQ=0;function compareItem(r,t){return\"number\"!=typeof r&&(r=0),\"number\"!=typeof t&&(t=0),r>t?GT:r<t?LT:EQ}function compareVersion(r,t){void 0===r&&(r=\"\"),void 0===t&&(t=\"\");var e=/^\\d+(\\.\\d+){2,3}$/;if(!e.test(r)||!e.test(t))throw new Error(\"请传入正确的版本号格式\");for(var n=(\"\"+r).split(\".\").map((function(r){return parseInt(r,10)})),o=(\"\"+t).split(\".\").map((function(r){return parseInt(r,10)})),a=Math.max(n.length,o.length),p=0,i=0;i<a&&(p=compareItem(n[i],o[i]))===EQ;i++);return p}export default compareVersion;", "import invoker from\"./invoker\";invoker.registerAPI(\"checkVPNAppInstalled\",{mini:!0,mobile:!0}),checkVPNAppInstalled.version={android:\"1.6.0\",ios:\"1.6.0\"};export default function checkVPNAppInstalled(){return invoker.invoke(\"checkVPNAppInstalled\")}", "import invoker from\"./invoker\";invoker.registerAPI(\"checkVPNAppOnline\",{mini:!0,mobile:!0}),checkVPNAppOnline.version={android:\"1.6.0\",ios:\"1.6.0\"};export default function checkVPNAppOnline(){return invoker.invoke(\"checkVPNAppOnline\")}", "import invoker from\"./invoker\";export var SelectVersionEnum;!function(I){I[I.DEFAULT=1]=\"DEFAULT\",I[I.NEW=2]=\"NEW\"}(SelectVersionEnum||(SelectVersionEnum={}));export var PanelTypeEnum;!function(I){I[I.GLOBAL_ORG=1]=\"GLOBAL_ORG\",I[I.FRIEND=2]=\"FRIEND\",I[I.GROUP=4]=\"GROUP\",I[I.RECOMMEND=5]=\"RECOMMEND\",I[I.SPECIAL_ATTENTION=7]=\"SPECIAL_ATTENTION\",I[I.LOAD_GROUP_PERSON=8]=\"LOAD_GROUP_PERSON\",I[I.ORG=9]=\"ORG\"}(PanelTypeEnum||(PanelTypeEnum={}));export var VisibilityCodesEnum;!function(I){I.PHONE_HIDE=\"PHONE_HIDE\",I.CHAT_INVALID=\"CHAT_INVALID\",I.GROUP_CHAT_PULL_INVALID=\"GROUP_CHAT_PULL_INVALID\",I.APP_DING_INVALID=\"APP_DING_INVALID\",I.PHONE_DING_INVALID=\"PHONE_DING_INVALID\",I.SMS_DING_INVALID=\"SMS_DING_INVALID\",I.AUDIO_VIDEO_HIDE=\"AUDIO_VIDEO_HIDE\"}(VisibilityCodesEnum||(VisibilityCodesEnum={})),invoker.registerAPI(\"chooseContact\",{pc:function(I,o){window.dingtalk.platform.invokeAPI(o.msgId,\"biz.contact.choose\",I)}}),chooseContact.version={pc:\"1.1.0\"};export default function chooseContact(I){return invoker.invoke(\"chooseContact\",I)}", "import invoker,{BRIDGE_ERROR_CODE,API_INVOKER_TYPE,PLATFORM_TYPE_ENUM}from\"./invoker\";function chooseContactHandler(o,e){var n=e.resolve,c=e.reject,i=e.context,t=e.containerType,r=e.appType,a=e.platformType;if(t){var s=function(o){invoker.handleBridgeResponse(o,n,c)},l=function(o){invoker.handleBridgeResponse(o,n,c)};r===API_INVOKER_TYPE.MINI_APP?i&&i({m:\"taurus.common.chooseContactWithComplexPicker\",args:o,onSuccess:s,onFail:l}):a===PLATFORM_TYPE_ENUM.ANDROID?i&&i(s,l,\"taurus.common\",\"chooseContactWithComplexPicker\",o):a===PLATFORM_TYPE_ENUM.IOS&&i.callHandler(\"taurus.common.chooseContactWithComplexPicker\",Object.assign({},o),(function(o){invoker.handleBridgeResponse(o,n,c)}))}else i&&i.call(\"chooseContactWithComplexPicker\",o,(function(e){e.error&&e.error.toString()===BRIDGE_ERROR_CODE.API_UNDEFINED?i.call(\"complexPicker\",o,(function(o){invoker.handleBridgeResponse(o,n,c)})):invoker.handleBridgeResponse(e,n,c)}))}invoker.registerAPI(\"chooseContactWithComplexPicker\",{mini:chooseContactHandler,mobile:chooseContactHandler,pc:function(o,e){window.dingtalk.platform.invokeAPI(e.msgId,\"biz.contact.complexPicker\",o)}}),chooseContactWithComplexPicker.version={android:\"1.1.0\",ios:\"1.1.0\",pc:\"1.6.2\"};export default function chooseContactWithComplexPicker(o){return invoker.invoke(\"chooseContactWithComplexPicker\",o)}", "import invoker from\"./invoker\";invoker.registerAPI(\"chooseDateRangeWithCalendar\",{mini:!0,mobile:!0}),chooseDateRangeWithCalendar.version={android:\"1.3.10\",ios:\"1.3.10\"};export default function chooseDateRangeWithCalendar(e){return invoker.invoke(\"chooseDateRangeWithCalendar\",e)}", "import invoker from\"./invoker\";invoker.registerAPI(\"chooseDayWithCalendar\",{mini:!0,mobile:!0}),chooseDayWithCalendar.version={android:\"1.3.10\",ios:\"1.3.10\"};export default function chooseDayWithCalendar(o){return invoker.invoke(\"chooseDayWithCalendar\",o)}", "import invoker from\"./invoker\";invoker.registerAPI(\"chooseDepartments\",{mini:!0,mobile:!0,pc:function(e,o){window.dingtalk.platform.invokeAPI(o.msgId,\"biz.contact.departmentsPicker\",e)}}),chooseDepartments.version={android:\"1.1.0\",ios:\"1.1.0\",pc:\"1.6.2\"};export default function chooseDepartments(e){return invoker.invoke(\"chooseDepartments\",e)}", "import invoker from\"./invoker\";invoker.registerAPI(\"chooseFile\",{mini:!0,mobile:!0,pc:function(o,i){window.dingtalk.platform.invokeAPI(i.msgId,\"biz.util.chooseFile\",o)}}),chooseFile.version={android:\"1.3.2\",ios:\"1.3.2\",pc:\"1.3.6\"};export default function chooseFile(o){return invoker.invoke(\"chooseFile\",o)}", "import invoker from\"./invoker\";invoker.registerAPI(\"chooseHalfDayWithCalendar\",{mini:!0,mobile:!0}),chooseHalfDayWithCalendar.version={android:\"1.3.10\",ios:\"1.3.10\"};export default function chooseHalfDayWithCalendar(o){return invoker.invoke(\"chooseHalfDayWithCalendar\",o)}", "import _extends from\"@babel/runtime/helpers/extends\";import invoker from\"./invoker\";export var ImageType;!function(e){e[e.image=0]=\"image\",e[e.video=1]=\"video\"}(ImageType||(ImageType={})),invoker.registerAPI(\"dgChooseImage\",{mini:!0,mobile:!0,pc:function(e,o){window.dingtalk.platform.invokeAPI(o.msgId,\"biz.util.dgChooseImage\",e)}}),chooseImage.version={android:\"1.3.2\",ios:\"1.3.2\",pc:\"1.3.6\"};export default function chooseImage(e){return invoker.invoke(\"dgChooseImage\",_extends({},e,{_apiName:\"chooseImage\"}))}", "import invoker from\"./invoker\";invoker.registerAPI(\"chooseInterconnectionChat\",{mini:!0,mobile:!0,pc:function(o,n){window.dingtalk.platform.invokeAPI(n.msgId,\"chooseInterconnectionChat\",o)}}),chooseContact.version={pc:\"2.9.0\",ios:\"2.9.0\",android:\"2.9.0\"};export default function chooseContact(o){return invoker.invoke(\"chooseInterconnectionChat\",o)}", "import _extends from\"@babel/runtime/helpers/extends\";import invoker from\"./invoker\";invoker.registerAPI(\"chooseImage\",{mini:!0}),chooseLocalImage.version={android:\"1.6.2\",ios:\"1.6.2\"};export default function chooseLocalImage(e){return new Promise((function(o,n){my.chooseImage(_extends({},e,{success:function(e){o(e)},fail:function(e){n(e)}}))}))}", "import invoker from\"./invoker\";invoker.registerAPI(\"chooseSpaceDir\",{mini:!0,mobile:!0,pc:function(o,i){void 0===o&&(o={}),window.dingtalk.platform.invokeAPI(i.msgId,\"biz.util.chooseSpaceDir\",o)}}),chooseSpaceDir.version={android:\"1.6.0\",ios:\"1.6.0\",pc:\"2.6.0\"};export default function chooseSpaceDir(){return invoker.invoke(\"chooseSpaceDir\")}", "import invoker from\"./invoker\";invoker.registerAPI(\"chooseTimeWithCalendar\",{mini:!0,mobile:!0}),chooseTimeWithCalendar.version={android:\"1.3.10\",ios:\"1.3.10\"};export default function chooseTimeWithCalendar(e){return invoker.invoke(\"chooseTimeWithCalendar\",e)}", "import invoker from\"./invoker\";invoker.registerAPI(\"chooseVideo\",{mini:!0,mobile:!0}),chooseVideo.version={android:\"1.6.2\",ios:\"1.6.2\"};export default function chooseVideo(o){return invoker.invoke(\"chooseVideo\",o)}", "import _extends from\"@babel/runtime/helpers/extends\";import invoker,{API_INVOKER_TYPE,PLATFORM_TYPE_ENUM}from\"./invoker\";function popWindow(e,n){var o=n.resolve,i=n.reject,a=n.context,r=n.containerType,s=n.appType,t=n.platformType;if(r){var l=function(e){invoker.handleBridgeResponse(e,o,i)},p=function(e){invoker.handleBridgeResponse(e,o,i)};s===API_INVOKER_TYPE.MINI_APP?a&&a({m:\"biz.navigation.close\",args:e,onSuccess:l,onFail:p}):t===PLATFORM_TYPE_ENUM.ANDROID?a&&a(l,p,\"biz.navigation\",\"close\",e):t===PLATFORM_TYPE_ENUM.IOS&&a.callHandler(\"biz.navigation.close\",Object.assign({},e),(function(e){invoker.handleBridgeResponse(e,o,i)}))}else a&&a.call(\"popWindow\",e,(function(e){invoker.handleBridgeResponse(e,o,i)}))}invoker.registerAPI(\"closePage\",{mini:popWindow,mobile:popWindow,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,\"biz.navigation.quit\",e)}}),closePage.version={android:\"1.2.0\",ios:\"1.2.0\",pc:\"1.2.0\"};export default function closePage(e){return invoker.invoke(\"closePage\",_extends({},e,{_apiName:\"closePage\"}))}", "import invoker from\"./invoker\";export var EmployeeKeyTypeEnum;!function(e){e.CODE=\"code\",e.ACCOUNTID=\"accountId\"}(EmployeeKeyTypeEnum||(EmployeeKeyTypeEnum={}));export var KeyTypeEnum;!function(e){e.CODE=\"code\",e.id=\"id\"}(KeyTypeEnum||(KeyTypeEnum={})),invoker.registerAPI(\"complexPickerAdmin\",{pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,\"biz.contact.complexPickerAdmin\",e)}}),complexPickerAdmin.version={pc:\"2.8.0\"};export default function complexPickerAdmin(e){return invoker.invoke(\"complexPickerAdmin\",e)}", "import invoker,{Container_Type_Enum,PLATFORM_TYPE_ENUM,BRIDGE_ERROR_CODE}from\"./invoker\";invoker.registerAPI(\"confirm\",{mini:function(e,n){var o=n.resolve,r=n.reject,t=n.context,i=n.containerType,s={message:e.message,title:e.title,okButton:e.buttonLabels[0],cancelButton:e.buttonLabels[1]};i===Container_Type_Enum.isDingTalk?t({m:\"taurus.common.confirm\",args:s,onSuccess:function(e){var n={errorCode:BRIDGE_ERROR_CODE.SUCCESS,result:{buttonIndex:e.ok?0:1}};invoker.handleBridgeResponse(n,o,r)},onFail:function(e){invoker.handleBridgeResponse(e,o,r)}}):t&&t.call(\"confirm\",s,(function(e){var n={errorCode:BRIDGE_ERROR_CODE.SUCCESS,result:{buttonIndex:e.ok?0:1}};invoker.handleBridgeResponse(n,o,r)}))},mobile:function(e,n){var o=n.resolve,r=n.reject,t=n.context,i=n.containerType,s=n.platformType,a={message:e.message,title:e.title,okButton:e.buttonLabels[0],cancelButton:e.buttonLabels[1]};if(i){s===PLATFORM_TYPE_ENUM.ANDROID?t&&t((function(e){var n={errorCode:BRIDGE_ERROR_CODE.SUCCESS,result:{buttonIndex:e.ok?0:1}};invoker.handleBridgeResponse(n,o,r)}),(function(e){invoker.handleBridgeResponse(e,o,r)}),\"taurus.common\",\"confirm\",a):s===PLATFORM_TYPE_ENUM.IOS&&t.callHandler(\"taurus.common.confirm\",Object.assign({},a),(function(e){invoker.handleBridgeResponse(e,o,r)}))}else t&&t.call(\"confirm\",a,(function(e){var n={errorCode:BRIDGE_ERROR_CODE.SUCCESS,result:{buttonIndex:e.ok?0:1}};invoker.handleBridgeResponse(n,o,r)}))},pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,\"device.notification.confirm\",e)}}),confirm.version={android:\"1.3.2\",ios:\"1.3.2\",pc:\"1.3.2\"};export default function confirm(e){return invoker.invoke(\"confirm\",e)}", "import invoker from\"./invoker\";invoker.registerAPI(\"copyToClipboard\",{mini:!0,mobile:!0}),copyToClipboard.version={android:\"1.3.2\",ios:\"1.3.2\"};export default function copyToClipboard(o){return invoker.invoke(\"copyToClipboard\",o)}", "import invoker from\"./invoker\";invoker.registerAPI(\"createChatGroup\",{mini:!0,mobile:!0}),createChatGroup.version={android:\"1.3.0\",ios:\"1.3.0\",pc:\"1.3.0\"};export default function createChatGroup(r){return invoker.invoke(\"createChatGroup\",r)}", "import invoker from\"./invoker\";invoker.registerAPI(\"createDing\",{mini:!0,mobile:!0,pc:function(i,e){window.dingtalk.platform.invokeAPI(e.msgId,\"biz.ding.create\",i)}}),createDing.version={android:\"1.3.9\",ios:\"1.3.9\",pc:\"1.3.9\"};export default function createDing(i){return invoker.invoke(\"createDing\",i)}", "import invoker from\"./invoker\";invoker.registerAPI(\"createDingV2\",{mini:!0,mobile:!0,pc:function(i,e){window.dingtalk.platform.invokeAPI(e.msgId,\"biz.ding.createV2\",i)}}),createDingV2.version={android:\"2.7.0\",ios:\"2.7.0\",pc:\"2.7.0\"};export default function createDingV2(i){return invoker.invoke(\"createDingV2\",i)}", "import _extends from\"@babel/runtime/helpers/extends\";import invoker,{API_INVOKER_TYPE,PLATFORM_TYPE_ENUM}from\"./invoker\";function createVideoConfHandler(e,n){void 0===e&&(e={});var o=n.resolve,i=n.reject,r=n.context,c=n.containerType,t=n.appType,a=n.platformType;if(c){var d=function(e){invoker.handleBridgeResponse(e,o,i)},f=function(e){invoker.handleBridgeResponse(e,o,i)};t===API_INVOKER_TYPE.MINI_APP?r&&r({m:\"biz.conference.createVideoConf\",args:e,onSuccess:d,onFail:f}):a===PLATFORM_TYPE_ENUM.ANDROID?r&&r(d,f,\"biz.conference\",\"createVideoConf\",e):a===PLATFORM_TYPE_ENUM.IOS&&r.callHandler(\"biz.conference.createVideoConf\",Object.assign({},e),(function(e){invoker.handleBridgeResponse(e,o,i)}))}else r&&r.call(\"createVideoConf\",e,(function(){o()}))}invoker.registerAPI(\"createVideoConf\",{mini:createVideoConfHandler,mobile:createVideoConfHandler,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,\"biz.conference.createVideoConf\",_extends({},e))}}),createVideoConf.version={android:\"3.7.5\",ios:\"3.7.5\",pc:\"3.7.5\"};export default function createVideoConf(e){return invoker.invoke(\"createVideoConf\",e)}", "import _extends from\"@babel/runtime/helpers/extends\";import invoker from\"./invoker\";invoker.registerAPI(\"createVideoMeeting\",{mini:!0,mobile:!0,pc:function(e,i){window.dingtalk.platform.invokeAPI(i.msgId,\"biz.meeting.create\",_extends({isVideoConference:!0},e))}}),createVideoMeeting.version={android:\"1.3.1.1\",ios:\"1.3.1.1\",pc:\"1.9.4\"};export default function createVideoMeeting(e){return invoker.invoke(\"createVideoMeeting\",e)}", "import invoker from\"./invoker\";invoker.registerAPI(\"dealWithBackAction\",{mobile:!0}),dealWithBackAction.version={android:\"1.2.0.10\"};export default function dealWithBackAction(i){return invoker.invoke(\"dealWithBackAction\",i)}", "import invoker from\"./invoker\";invoker.registerAPI(\"disableClosePage\",{mini:!0,mobile:!0,pc:function(e,i){window.dingtalk.platform.invokeAPI(i.msgId,\"biz.util.disableClosePage\",{})}}),disableClosePage.version={pc:\"3.4.0\"};export default function disableClosePage(){return invoker.invoke(\"disableClosePage\")}", "import invoker,{PLATFORM_TYPE_ENUM}from\"./invoker\";invoker.registerAPI(\"disablePullToRefresh\",{mobile:function(e,l){var i=l.resolve,o=l.reject,r=l.context,n=l.containerType,s=l.platformType;if(n){s===PLATFORM_TYPE_ENUM.ANDROID?r&&r((function(){i()}),(function(){o()}),\"ui.pullToRefresh\",\"disable\",{}):s===PLATFORM_TYPE_ENUM.IOS&&r.callHandler(\"ui.pullToRefresh.disable\",Object.assign({},{}),(function(e){i(e)}))}else r&&r.call(\"pullRefresh\",{pullRefresh:!1},(function(){i()}))}}),disablePullToRefresh.version={android:\"1.3.0\",ios:\"1.3.0\"};export default function disablePullToRefresh(){return invoker.invoke(\"disablePullToRefresh\",{_apiName:\"disablePullToRefresh\"})}", "import invoker,{PLATFORM_TYPE_ENUM}from\"./invoker\";invoker.registerAPI(\"disableWebviewBounce\",{mobile:function(e,i){var n=i.resolve,o=i.reject,c=i.context,a=i.containerType,b=i.platformType;if(a){b===PLATFORM_TYPE_ENUM.ANDROID?c&&c((function(){n()}),(function(){o()}),\"ui.webViewBounce\",\"disable\",{}):b===PLATFORM_TYPE_ENUM.IOS&&c.callHandler(\"ui.webViewBounce.disable\",Object.assign({},{}),(function(e){n(e)}))}else c&&c.call(\"bounce\",{enable:!1},(function(e){n(e)}))}}),disableWebviewBounce.version={ios:\"1.3.0\"};export default function disableWebviewBounce(){return invoker.invoke(\"disableWebviewBounce\",{_apiName:\"disableWebviewBounce\"})}", "import invoker from\"./invoker\";invoker.registerAPI(\"downloadAudio\",{mini:!0,mobile:!0}),downloadAudio.version={android:\"1.3.0\",ios:\"1.3.0\"};export default function downloadAudio(o){return invoker.invoke(\"downloadAudio\",o)}", "import invoker,{Container_Type_Enum}from\"./invoker\";var TASK_COMPLETE=1;function downloadFile(e){return invoker.invoke(\"downloadFile\",e)}invoker.registerAPI(\"downloadFile\",{mini:function(e,n){var i=n.resolve,o=n.reject,r=n.containerType,t=n.context;if(r===Container_Type_Enum.isDingTalk){t&&t({m:\"taurus.common.downloadFile\",args:e,onSuccess:function(e){invoker.handleBridgeResponse(e,i,o)},onFail:function(e){invoker.handleBridgeResponse(e,i,o)}})}else t&&t.call(\"downloadFile\",e,(function(e){e.error?o(e):i(e)}))},pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,\"biz.util.downloadFile\",e),invoker.registerContinuesEvent(n.msgId,(function(i,o){\"im.fileTask.addNewTask\"===i&&(invoker.removeContinuesEvent(n.msgId),invoker.registerContinuesEvent(o.taskId,(function(n,i){if(\"im.fileTask.updateTask\"===n){var o=i.doneSize,r=i.fileName,t=i.filePath,a=i.fileSize,l=i.speed;e.onProgress({doneSize:o,fileName:r,filePath:t,fileSize:a,speed:l}),i.status===TASK_COMPLETE&&invoker.removeContinuesEvent(i.taskId)}})))}))}}),downloadFile.version={pc:\"1.3.5\"};export default downloadFile;", "import invoker,{PLATFORM_TYPE_ENUM}from\"./invoker\";invoker.registerAPI(\"enablePullToRefresh\",{mobile:function(e,l){var n=l.resolve,o=l.reject,r=l.context,i=l.containerType,f=l.platformType;if(i){f===PLATFORM_TYPE_ENUM.ANDROID?r&&r((function(){n()}),(function(){o()}),\"ui.pullToRefresh\",\"enable\",{}):f===PLATFORM_TYPE_ENUM.IOS&&r.callHandler(\"ui.pullToRefresh.enable\",Object.assign({},{}),(function(){n()}))}else r&&r.call(\"pullRefresh\",{pullRefresh:!0},(function(){n()}))}}),enablePullToRefresh.version={android:\"1.3.0\",ios:\"1.3.0\"};export default function enablePullToRefresh(){return invoker.invoke(\"enablePullToRefresh\",{_apiName:\"enablePullToRefresh\"})}", "import invoker from\"./invoker\";invoker.registerAPI(\"enableVpn\",{mini:!0,mobile:!0}),enableVpn.version={android:\"1.1.0\",ios:\"1.1.0\"};export default function enableVpn(){return invoker.invoke(\"enableVpn\")}", "import invoker,{PLATFORM_TYPE_ENUM}from\"./invoker\";invoker.registerAPI(\"enableWebviewBounce\",{mobile:function(e,n){var o=n.resolve,i=n.reject,c=n.context,a=n.containerType,r=n.platformType;if(a){r===PLATFORM_TYPE_ENUM.ANDROID?c&&c((function(){o()}),(function(){i()}),\"taurus.common\",\"bounce\",{enable:!0}):r===PLATFORM_TYPE_ENUM.IOS&&c.callHandler(\"taurus.common.bounce\",Object.assign({},{enable:!0}),(function(e){o(e)}))}else c&&c.call(\"bounce\",{enable:!0},(function(e){o(e)}))}}),enableWebviewBounce.version={ios:\"1.3.0\"};export default function enableWebviewBounce(){return invoker.invoke(\"enableWebviewBounce\",{_apiName:\"enableWebviewBounce\"})}", "import invoker from\"./invoker\";invoker.registerAPI(\"exclusiveInvoke\",{mini:!0,mobile:!0}),exclusiveInvoke.version={ios:\"1.9.5\",android:\"1.9.5\"};export default function exclusiveInvoke(e){return invoker.invoke(\"exclusiveInvoke\",e)}", "import invoker from\"./invoker\";invoker.registerAPI(\"faceComparison\",{mobile:!0,mini:!0}),faceComparison.version={android:\"2.4.0\",ios:\"2.4.0\"};export default function faceComparison(o){return invoker.invoke(\"faceComparison\",o)}", "import invoker from\"./invoker\";export var ImageTypeEnum;!function(e){e.PNG=\"png\",e.JPG=\"jpg\"}(ImageTypeEnum||(ImageTypeEnum={})),invoker.registerAPI(\"faceRecognition\",{mobile:!0,mini:!0}),faceRecognition.version={android:\"2.4.0\",ios:\"2.4.0\"};export default function faceRecognition(e){return invoker.invoke(\"faceRecognition\",e)}", "import invoker from\"./invoker\";invoker.registerAPI(\"getAppInstallStatus\",{mini:!0,mobile:!0}),getAppInstallStatus.version={android:\"2.1.10\",ios:\"2.1.10\"};export default function getAppInstallStatus(t){return invoker.invoke(\"getAppInstallStatus\",t)}", "import invoker from\"./invoker\";invoker.registerAPI(\"getAuthCode\",{pc:function(e,o){window.dingtalk.platform.invokeAPI(o.msgId,\"runtime.permission.requestAuthCode\",e)},mobile:!0,mini:!0}),getAuthCode.version={android:\"1.0.0\",ios:\"1.0.0\",pc:\"1.0.0\"};export default function getAuthCode(e){return invoker.invoke(\"getAuthCode\",e)}", "import invoker from\"./invoker\";invoker.registerAPI(\"getConfig\",{mobile:!0,mini:!0,pc:function(i,o){window.dingtalk.platform.invokeAPI(o.msgId,\"getConfig\",i)}}),getConfig.version={android:\"1.3.2\",ios:\"1.3.2\",pc:\"1.3.2\"};export default function getConfig(){return invoker.invoke(\"getConfig\",{})}", "import invoker from\"./invoker\";export{Container_Type_Enum}from\"./invoker\";export default function getContainerType(){return invoker.getContainerType()}", "import invoker from\"./invoker\";invoker.registerAPI(\"getDeviceId\",{mobile:!0,mini:!0}),getDeviceId.version={android:\"2.5.0\",ios:\"2.5.0\"};export default function getDeviceId(){return invoker.invoke(\"getDeviceId\",{})}", "import invoker from\"./invoker\";invoker.registerAPI(\"getFromClipboard\",{mini:!0,mobile:!0,pc:function(o,i){window.dingtalk.platform.invokeAPI(i.msgId,\"util.clipboardData.getData\",o)}}),getFromClipboard.version={android:\"2.3.1\",ios:\"2.3.1\",pc:\"2.6.10\"};export default function getFromClipboard(){return invoker.invoke(\"getFromClipboard\")}", "import invoker from\"./invoker\";invoker.registerAPI(\"getGeolocation\",{mini:!0,mobile:!0}),getGeolocation.version={android:\"1.2.0\",ios:\"1.2.0\"};export default function getGeolocation(o){return invoker.invoke(\"getGeolocation\",o)}", "import invoker from\"./invoker\";invoker.registerAPI(\"getGeolocationStatus\",{mobile:!0,mini:!0}),getGeolocationStatus.version={android:\"1.6.2\",ios:\"1.6.2\"};export default function getGeolocationStatus(o){return invoker.invoke(\"getGeolocationStatus\",o)}", "import invoker from\"./invoker\";invoker.registerAPI(\"getHotspotInfo\",{mobile:!0,mini:!0}),getHotspotInfo.version={android:\"1.3.5\",ios:\"1.3.5\"};export default function getHotspotInfo(){return invoker.invoke(\"getHotspotInfo\")}", "import invoker from\"./invoker\";invoker.registerAPI(\"getLanguageSetting\",{mobile:!0,mini:!0,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,\"getLanguageSetting\",e)}}),getLanguageSetting.version={android:\"1.4.0\",ios:\"1.4.0\",pc:\"1.4.0\"};export default function getLanguageSetting(){return invoker.invoke(\"getLanguageSetting\")}", "import invoker from\"./invoker\";invoker.registerAPI(\"getLoginUser\",{mobile:!0,mini:!0}),getLoginUser.version={android:\"1.1.0\",ios:\"1.1.0\"};export default function getLoginUser(){return invoker.invoke(\"getLoginUser\")}", "import invoker from\"./invoker\";invoker.registerAPI(\"getNetworkType\",{mobile:!0,mini:!0}),getNetworkType.version={android:\"1.3.0\",ios:\"1.3.0\"};export default function getNetworkType(){return invoker.invoke(\"getNetworkType\")}", "import invoker from\"./invoker\";invoker.registerAPI(\"getPhoneInfo\",{mini:!0,mobile:!0}),getPhoneInfo.version={android:\"1.3.5\",ios:\"1.3.5\"};export default function getPhoneInfo(){return invoker.invoke(\"getPhoneInfo\")}", "import invoker from\"./invoker\";export var MODE_ENUM;!function(o){o.SOCKS5=\"SOCKS5\",o.HTTP=\"HTTP\"}(MODE_ENUM||(MODE_ENUM={})),invoker.registerAPI(\"getProxyInfo\",{pc:function(o,n){void 0===o&&(o={}),window.dingtalk.platform.invokeAPI(n.msgId,\"net.util.getProxyInfo\",o)}}),getProxyInfo.version={pc:\"2.10.0\"};export default function getProxyInfo(){return invoker.invoke(\"getProxyInfo\",{})}", "import invoker from\"./invoker\";invoker.registerAPI(\"getStorageItem\",{mobile:!0,mini:!0}),getStorageItem.version={android:\"1.3.1.1\",ios:\"1.3.1.1\"};export default function getStorageItem(e){return invoker.invoke(\"getStorageItem\",e)}", "import invoker from\"./invoker\";invoker.registerAPI(\"getTraceStatus\",{mobile:!0}),getTraceStatus.version={android:\"1.3.4\",ios:\"1.3.4\"};export default function getTraceStatus(e){return invoker.invoke(\"getTraceStatus\",e)}", "import invoker from\"./invoker\";invoker.registerAPI(\"getUUID\",{mobile:!0,mini:!0}),getUUID.version={android:\"1.3.5\",ios:\"1.3.5\"};export default function getUUID(){return invoker.invoke(\"getUUID\")}", "import invoker,{API_INVOKER_TYPE}from\"./invoker\";var RGE_CLIENT_INFO=/TaurusApp\\((\\S*)\\/(\\S*)\\)/;function getUserAgentInternal(){if(window&&window.navigator){var e=window.navigator.userAgent;if(e){var r=e.match(RGE_CLIENT_INFO);return Promise.resolve({group:\"TaurusApp\",name:r[1],version:r[2]})}return Promise.reject(\"调用错误：无法检测到当下环境的 userAgent，请确保在政务钉钉客户端 H5 容器下调用。\")}}invoker.registerAPI(\"getUserAgent\",{mobile:!0,mini:!0,pc:!0}),getUserAgent.version={android:\"1.6.2\",ios:\"1.6.2\",pc:\"1.6.2\"};export default function getUserAgent(){var e=invoker.getAppType();return e===API_INVOKER_TYPE.PC||e===API_INVOKER_TYPE.MOBILE?getUserAgentInternal():e===API_INVOKER_TYPE.MINI_APP?invoker.invoke(\"getUserAgent\",{}):void 0}", "import invoker from\"../invoker\";var TargetPageConfigEnum,WatermarkStatusEnum,ContentType;!function(n){n.off=\"0\",n.on=\"1\"}(TargetPageConfigEnum||(TargetPageConfigEnum={})),function(n){n[n.off=0]=\"off\",n[n.on=1]=\"on\"}(WatermarkStatusEnum||(WatermarkStatusEnum={})),function(n){n[n.name=1]=\"name\",n[n.id=2]=\"id\",n[n.custom=3]=\"custom\"}(ContentType||(ContentType={})),invoker.registerAPI(\"getWaterMarkConfig\",{pc:function(n,t){window.dingtalk.platform.invokeAPI(t.msgId,\"getWaterMarkConfig\",n)},mini:!0,mobile:!0});export default function getWaterMarkConfig(n){return invoker.invoke(\"getWaterMarkConfig\",n)}", "import _extends from\"@babel/runtime/helpers/extends\";import{isUndef}from\"../../utils/common\";var systemInfo,H5_PAGE=\"h5Page\",MEETING_DETAIL=\"meetingDetail\",DOC_PREVIEW=\"docPreview\",targetPageArr=[H5_PAGE,MEETING_DETAIL,DOC_PREVIEW],isMiniProgram=!isUndef(typeof my)&&null!==my&&!isUndef(typeof my.alert);isMiniProgram&&(systemInfo=my.getSystemInfoSync());var userAgent=isMiniProgram?systemInfo.platform:navigator.userAgent,screenWidth=isMiniProgram?systemInfo.screenWidth:window.screen.width,pixelRatio=(isMiniProgram?systemInfo.pixelRatio:window.devicePixelRatio)||2,emptyWatermark=isMiniProgram?Promise.resolve(\"\"):\"\",WaterMark=function(){function t(t){void 0===t&&(t={}),this.options=_extends({texts:[\"\"],width:50,height:50,textRotate:-10,textColor:\"#000000\",textFont:\"PingFangSC-Regular,system-ui,sans-serif\",fontStyle:\"normal\",opacity:90,canvas:[],fontSize:14},t),this.options.width*=this.options.fontSize/12,this.options.height*=this.options.fontSize/12,this.options.deg=this.options.textRotate*Math.PI/180,this.options.cosDeg=Math.cos(this.options.deg),this.options.absSinDeg=Math.abs(Math.sin(this.options.deg))}var e=t.prototype;return e.init=function(){var t=this,e=null,i=null;isMiniProgram?i=my.createCanvasContext(\"canvasBg\"):(e=this.createCanvas(),i=e.getContext(\"2d\")),this.calcTextSize();var n=this.options,o=n.allItemsWidth,a=n.drawItems,r=n.height,s=n.containerComp,h=Math.ceil(screenWidth/o),l=new Array(h).fill(a).reduce((function(t,e){return t.concat(e)}),[]),f=function(){t.setCanvasStyle(i),t.drawText(i,l),i.translate(0,r),t.drawText(i,l.reverse(),!0)};if(isMiniProgram)return new Promise((function(t){s.setState({width:o*h,height:2*r},(function(){setTimeout((function(){f(),i.draw(),t(i.toDataURL(\"image/png\"))}),0)}))}));e.width=o*h,e.height=2*r,e.style.display=\"none\",f();var m=e.toDataURL(\"image/png\");return this.destroy(),m},e.calcTextSize=function(){var t=0,e=0,i=this.options;i.drawItems=[].map.call(i.texts,(function(n){var o,a,r,s;if(isMiniProgram){for(var h=0,l=0;l<n.length;l+=1)h+=/[\\uff00-\\uffff]/.test(n[l])?1:.5;o=1.1*i.fontSize*h,a=1.2*i.fontSize}else{var f=(r='<span style=\"font:'+i.fontSize+\"px \"+i.textFont+';visibility:hidden;\">'+n+\"</span>\",(s=document.createElement(\"div\")).innerHTML=r.trim(),s.firstChild);document.body.appendChild(f),o=f.offsetWidth,a=f.offsetHeight,document.body.removeChild(f)}return t=Math.max(t,o),i.fontHeight||(i.fontHeight=a),e+=Math.ceil(i.cosDeg*(i.width<o?o:i.width)),{txt:n,width:o,height:a}})),t>i.width&&(i.width=t);var n=t*i.absSinDeg+i.fontHeight*i.cosDeg;n>i.height&&(i.height=n),i.maxItemWidth=t,i.allItemsWidth=e},e.setCanvasStyle=function(t){var e=this.options,i=e.deg,n=e.absSinDeg,o=e.height,a=e.fontHeight,r=e.fontStyle,s=e.fontSize,h=e.textFont,l=e.textColor,f=e.opacity;t.rotate(i);var m=n*(o-a);t.translate(-m,0),t.font=r+\" \"+s+\"px \"+h,t.fillStyle=l,t.textAlign=\"left\",t.textBaseline=\"bottom\",t.globalAlpha=f},e.drawText=function(t,e,i){void 0===i&&(i=!1);var n=this.options,o=n.maxItemWidth,a=n.width,r=n.height,s=n.deg,h=n.cosDeg,l=n.absSinDeg;e.forEach((function(e,n){var f=h*(o-e.width)/2,m=a*h*n,c=Math.abs(m*Math.tan(s))+r;t.fillText(e.txt,m+(i?h*(a-e.width)/2:f),c+(i?l*(a-e.width)/2:0))}))},e.createCanvas=function(){var t=document.createElement(\"canvas\");return this.options.canvas.push(t),t},e.destroy=function(){this.options.canvas.forEach((function(t){t.remove(),t=null}))},t}();function drawWatermark(t,e){var i=JSON.parse(t),n=i.watermark||i;if(!n||\"0\"===String(n.watermarkStatus))return emptyWatermark;if(!Array.isArray(n.targetPages)||!n.targetPages.some((function(t){return t.name===e&&\"1\"===String(t.value)})))return emptyWatermark;var o=[];if(Array.isArray(n.contentType)){var a=\"\";n.contentType.includes(1)&&(a+=n.userName+\" \"),n.contentType.includes(2)&&(a+=(n.account||\"\").slice(-4)),a&&o.push(a),n.contentType.includes(0)&&n.contentCustom&&o.push(n.contentCustom)}if(!o.length)return emptyWatermark;var r,s,h=/Android|Adr|SymbianOS|Windows\\s*Phone|Mobile/.test(userAgent),l=/iPhone|iPad|iPod|Mac\\s*OS.*Mobile|iOS/.test(userAgent),f=\"0\"===String(n.watermarkShowDensity);l?f?(r=114,s=66):(r=86,s=45):h?f?(r=47*pixelRatio,s=40*pixelRatio):(r=25*pixelRatio,s=25*pixelRatio):f?(r=300,s=126):(r=194,s=106);return new WaterMark({containerComp:this,texts:o,width:r,height:s,textRotate:-10,textColor:{0:\"#FF0000\",1:\"#000000\",2:\"#0000FF\"}[n.fontColor]||\"#000000\",textFont:\"PingFangSC-Regular,system-ui,sans-serif\",fontStyle:\"0\"===String(n.fontStyle)?\"normal\":\"bold\",opacity:(120-parseInt(n.fontDiaphaneity,10))/100,fontSize:{0:12,1:16,2:28}[n.fontSize]||16}).init()}export function generateWatermark(t,e){if(void 0===t&&(t={}),void 0===e&&(e=H5_PAGE),!targetPageArr.includes(e))throw new Error(\"第二个可选参数，仅能为“h5Page”或“meetingDetail”\");try{return drawWatermark.call(this,JSON.stringify(t),e)}catch(t){throw t}}", "import getWaterMarkConfig from\"./utils/getWaterMarkConfig\";import{generateWatermark}from\"./packages/h5-watermark/watermark\";getWaterMark.version={android:\"1.1.0\",ios:\"1.1.0\",pc:\"1.1.0\"};export default function getWaterMark(r,e){return void 0===r&&(r=\"\"),new Promise((function(t,a){getWaterMarkConfig({pageInfo:r}).then((function(r){try{var n=generateWatermark(r,e);t(n)}catch(r){a(r)}}))}))}", "import invoker from\"./invoker\";export var EnableEnum;!function(n){n[n.ENABLE=1]=\"ENABLE\",n[n.DISABLE=0]=\"DISABLE\"}(EnableEnum||(EnableEnum={})),invoker.registerAPI(\"getWaterMarkConfigV2\",{mobile:!0,mini:!0,pc:function(n,e){window.dingtalk.platform.invokeAPI(e.msgId,\"getWaterMarkConfigV2\",n)}}),getWaterMarkConfigV2.version={android:\"2.8.0\",ios:\"2.8.0\",pc:\"2.8.0\"};export default function getWaterMarkConfigV2(n){return invoker.invoke(\"getWaterMarkConfigV2\",n)}", "export var EnableEnum;!function(t){t[t.DISABLE=0]=\"DISABLE\",t[t.ENABLE=1]=\"ENABLE\"}(EnableEnum||(EnableEnum={}));export var PageInfoEnum;!function(t){t.IMSESSIONLIST=\"imSessionList\",t.DOCPREVIEW=\"docPreview\",t.H5PAGEOTHER=\"h5PageOther\",t.MEETINGDETAIL=\"meetingDetail\",t.H5PAGEBASIC=\"h5PageBasic\",t.SELECTIONCOMPONENT=\"selectionComponent\",t.CONTACTLIST=\"contactList\",t.CONTACTDETAIL=\"contactDetail\",t.CHAT=\"chat\",t.SECRETCHAT=\"secretChat\",t.CAMERA=\"camera\"}(PageInfoEnum||(PageInfoEnum={}));var ShowDensityEnum,emptyWatermark=\"\",fontStyleMap={1:\"normal\",2:\"bold\",3:\"italic\"};!function(t){t[t.LOOSE=0]=\"LOOSE\",t[t.NORMAL=1]=\"NORMAL\",t[t.DENSE=2]=\"DENSE\"}(ShowDensityEnum||(ShowDensityEnum={}));export var HorizontalTypeEnum;!function(t){t[t.RIGHT=0]=\"RIGHT\",t[t.LEFT=1]=\"LEFT\"}(HorizontalTypeEnum||(HorizontalTypeEnum={}));var DEFAULT_CANVAS_WIDTH=749,DEFAULT_CANVAS_HEIGHT=326,DEFAULT_TEXT_WIDTH=200,DEFAULT_TEXT_HEIGHT=16,DEFAULT_RATIO=1.3,WaterMark=function(){function t(t){this.options=Object.assign({texts:\"\",width:50,height:50,tiltAngle:-15,fontColor:\"#171A1D\",textFont:\"PingFangSC-Regular,system-ui,sans-serif\",transparency:90,canvas:[],fontSize:13,tWidth:0,tHeight:0,deg:-15},t,{width:t.leftAndRightSpacing,height:t.upAndDownSpacing}),this.options.deg=this.options.tiltAngle*Math.PI/180}var n=t.prototype;return n.init=function(){var t,n,e,i,o,a,l,r,u,E=null;return u=(E=this.createCanvas()).getContext(\"2d\"),E.width=(null===(t=window)||void 0===t||null===(n=t.screen)||void 0===n?void 0:n.width)||(null===(e=document)||void 0===e||null===(i=e.documentElement)||void 0===i?void 0:i.clientWidth)||DEFAULT_CANVAS_WIDTH,E.height=(null===(o=window)||void 0===o||null===(a=o.screen)||void 0===a?void 0:a.height)||(null===(l=document)||void 0===l||null===(r=l.documentElement)||void 0===r?void 0:r.clientHeight)||DEFAULT_CANVAS_HEIGHT,this.calcTextSize(),this.setCanvasStyle(u),this.drawText(u),E.toDataURL(\"image/png\")},n.calcTextSize=function(){var t,n,e=this.options,i=\"exclusiveDingTalkWaterMarkCustomClass\"+100*Math.random(),o=(t='<span id=\"'+i+'\" style=\"font:'+e.fontSize+\"px \"+e.textFont+';visibility:hidden;display:inline-block;\">'+e.texts+\"</span>\",(n=document.createElement(\"div\")).innerHTML=t.trim(),n.firstChild);document.body.appendChild(o);var a=document.getElementById(i),l=Math.max(a.clientWidth,e.texts.length*e.fontSize*DEFAULT_RATIO)||DEFAULT_TEXT_WIDTH,r=Math.min(a.clientHeight,e.fontSize*DEFAULT_RATIO)||DEFAULT_TEXT_HEIGHT;e.tWidth=l,e.tHeight=r,document.body.removeChild(o)},n.setCanvasStyle=function(t){var n=this.options,e=n.deg,i=n.fontStyle,o=n.fontSize,a=n.textFont,l=n.fontColor,r=n.transparency;t.rotate(e),t.font=i+\" \"+o+\"px \"+a,t.fillStyle=l,t.textAlign=\"left\",t.textBaseline=\"bottom\",t.globalAlpha=(100-r)/100},n.fillContent=function(t,n){for(var e=this.options,i=e.width,o=e.height,a=e.texts,l=e.tWidth,r=e.tHeight,u=0;u<40;u++)for(var E=u*o+r,d=0;d<40;d++){var s=void 0;s=u%2==0?t===HorizontalTypeEnum.RIGHT?(l+i)*d:(l+i)*d+l+i:t===HorizontalTypeEnum.RIGHT?(l+i)*d+i:(l+i)*d+l,n.fillText(a,t===HorizontalTypeEnum.RIGHT?s:-s,E)}},n.drawText=function(t){this.fillContent(HorizontalTypeEnum.RIGHT,t),this.fillContent(HorizontalTypeEnum.LEFT,t)},n.createCanvas=function(){var t=document.createElement(\"canvas\");return this.options.canvas.push(t),t},t}();function drawWatermark(t,n){var e,i,o,a,l,r,u,E;void 0===n&&(n=PageInfoEnum.H5PAGEOTHER);var d=null;try{d=JSON.parse(t)}catch(t){d={}}var s=null===(e=d)||void 0===e||null===(i=e.watermark)||void 0===i?void 0:i.ruleContent,m=null===(o=d)||void 0===o?void 0:o.userInfo;if((null==s?void 0:s.enable)===EnableEnum.DISABLE||(null==s?void 0:s.enable)===EnableEnum.ENABLE&&(null==s||null===(a=s.effectPage)||void 0===a?void 0:a[n])!==EnableEnum.ENABLE)return emptyWatermark;var T,c=\"\";((null==s||null===(l=s.watermarkContent)||void 0===l?void 0:l.enableUsername)===EnableEnum.ENABLE&&(c+=null==m?void 0:m.userName),(null==s||null===(r=s.watermarkContent)||void 0===r?void 0:r.enablePhoneNumber)===EnableEnum.ENABLE&&(c+=\" \"+(null==m?void 0:m.lastFourPhoneNo)),null!=s&&null!==(u=s.watermarkContent)&&void 0!==u&&u.customCopy)&&(c+=\" \"+(null==s||null===(T=s.watermarkContent)||void 0===T?void 0:T.customCopy));return c.length?new WaterMark(Object.assign({texts:c,textFont:\"PingFangSC-Regular,system-ui,sans-serif\"},null==s?void 0:s.watermarkStyle,{fontStyle:fontStyleMap[null==s||null===(E=s.watermarkStyle)||void 0===E?void 0:E.fontStyle]})).init():emptyWatermark}export default function generateWaterMarkV2(t,n){void 0===n&&(n=PageInfoEnum.H5PAGEOTHER);try{return drawWatermark.call(null,JSON.stringify(t),n)}catch(t){return\"\"}}", "import generateWaterMarkV2 from\"./packages/h5-watermark/generateWaterMarkV2\";import getWaterMarkConfig from\"./utils/getWaterMarkConfig\";import getWaterMarkConfigV2 from\"./getWaterMarkConfigV2\";import{generateWatermark}from\"./packages/h5-watermark/watermark\";import getVersion from\"./version\";import compareVersion from\"./utils/compareVersion\";export default function getWaterMarkV2(r){return new Promise((function(e,t){getVersion().then((function(a){var n=a.version;-1!==compareVersion(n,\"2.8.0\")?getWaterMarkConfigV2({pageInfo:r}).then((function(a){try{var n=generateWaterMarkV2(a,r);e(n)}catch(r){t(r)}})):getWaterMarkConfig({pageInfo:r}).then((function(a){try{var n=generateWatermark(a,r);e(n)}catch(r){t(r)}}))})).catch((function(){getWaterMarkConfig({pageInfo:r}).then((function(a){try{var n=generateWatermark(a,r);e(n)}catch(r){t(r)}}))}))}))}", "import invoker from\"./invoker\";invoker.registerAPI(\"getWifiStatus\",{mobile:!0,mini:!0}),getWifiStatus.version={android:\"1.3.5\",ios:\"1.3.5\"};export default function getWifiStatus(){return invoker.invoke(\"getWifiStatus\")}", "import invoker from\"./invoker\";invoker.registerAPI(\"getWorkbenchContext\",{mini:!0,mobile:!0,pc:function(e,o){window.dingtalk.platform.invokeAPI(o.msgId,\"getWorkbenchContext\",e)}}),getWorkbenchContext.version={android:\"2.1.10\",ios:\"2.1.10\"};export default function getWorkbenchContext(){return invoker.invoke(\"getWorkbenchContext\")}", "import invoker,{PLATFORM_TYPE_ENUM}from\"./invoker\";invoker.registerAPI(\"h5PageBack\",{mobile:function(e,n){var o=n.resolve,i=n.reject,a=n.context,r=n.containerType,c=n.platformType;if(r){c===PLATFORM_TYPE_ENUM.ANDROID?a&&a((function(e){invoker.handleBridgeResponse(e,o,i)}),(function(e){invoker.handleBridgeResponse(e,o,i)}),\"biz.navigation\",\"goBack\",e):c===PLATFORM_TYPE_ENUM.IOS&&a.callHandler(\"biz.navigation.goBack\",Object.assign({},e),(function(e){o(e)}))}else a&&a.call(\"h5PageBack\",{_apiName:\"goBack\"},(function(){o()}))}}),goBack.version={android:\"1.3.0\",ios:\"1.3.9\"};export default function goBack(){return invoker.invoke(\"h5PageBack\",{_apiName:\"goBack\"})}", "import invoker,{API_INVOKER_TYPE,PLATFORM_TYPE_ENUM}from\"./invoker\";function hideLoadingMenuHandler(e,i){var n=i.resolve,o=i.reject,d=i.context,r=i.containerType,a=i.appType,t=i.platformType;if(r){var c=function(e){invoker.handleBridgeResponse(e,n,o)},l=function(e){invoker.handleBridgeResponse(e,n,o)};a===API_INVOKER_TYPE.MINI_APP?d&&d({m:\"device.notification.hidePreloader\",args:e,onSuccess:c,onFail:l}):t===PLATFORM_TYPE_ENUM.ANDROID?d&&d(c,l,\"device.notification\",\"hidePreloader\",e):t===PLATFORM_TYPE_ENUM.IOS&&d.callHandler(\"device.notification.hidePreloader\",Object.assign({},e),(function(e){n(e)}))}else d&&d.call(\"hideLoading\",e,(function(){n()}))}invoker.registerAPI(\"hideLoading\",{mini:hideLoadingMenuHandler,mobile:hideLoadingMenuHandler}),hideLoading.version={android:\"1.3.2\",ios:\"1.3.2\"};export default function hideLoading(){return invoker.invoke(\"hideLoading\")}", "import invoker,{PLATFORM_TYPE_ENUM}from\"./invoker\";function hideOptionMenuHandler(e,n){var i=n.resolve,o=n.reject,t=n.context,r=n.containerType,a=(n.appType,n.platformType);if(r){var d={show:!1,control:!0,text:\"\"};a===PLATFORM_TYPE_ENUM.ANDROID?t&&t((function(e){invoker.handleBridgeResponse(e,i,o)}),(function(e){invoker.handleBridgeResponse(e,i,o)}),\"biz.navigation\",\"setRight\",d):a===PLATFORM_TYPE_ENUM.IOS&&t.callHandler(\"biz.navigation.setRight\",Object.assign({},d),(function(e){i(e)}))}else t&&t.call(\"hideOptionMenu\",e,(function(){i()}))}invoker.registerAPI(\"hideOptionMenu\",{mobile:hideOptionMenuHandler}),hideOptionMenu.version={android:\"1.1.0\",ios:\"1.1.0\"};export default function hideOptionMenu(){return invoker.invoke(\"hideOptionMenu\")}", "import invoker,{API_INVOKER_TYPE,PLATFORM_TYPE_ENUM}from\"./invoker\";function hideTitlebarHandler(i,e){var n=e.resolve,a=e.reject,r=e.containerType,o=e.platformType,t=e.appType,l=e.context,d=Object.assign(i,{hidden:!0});if(r){var c=function(){i.onSuccess&&i.onSuccess(),n()},T=function(){i.onFail&&i.onFail(),a()};t===API_INVOKER_TYPE.MINI_APP?l&&l({m:\"biz.navigation.hideBar\",args:d,onSuccess:c,onFail:T}):o===PLATFORM_TYPE_ENUM.ANDROID?l&&l(c,T,\"biz.navigation\",\"hideBar\",d):o===PLATFORM_TYPE_ENUM.IOS&&l.callHandler(\"biz.navigation.hideBar\",Object.assign({},d),(function(){n()}))}else l&&l.call(\"hideTitlebar\",d,(function(){n()}))}invoker.registerAPI(\"hideTitlebar\",{mini:hideTitlebarHandler,mobile:hideTitlebarHandler}),hideTitleBar.version={android:\"2.1.0\",ios:\"2.1.0\"};export default function hideTitleBar(){return invoker.invoke(\"hideTitlebar\")}", "import invoker from\"./invoker\";invoker.registerAPI(\"isDownloadFileExist\",{pc:function(i,o){window.dingtalk.platform.invokeAPI(o.msgId,\"biz.util.isLocalFileExist\",i)}}),isDownloadFileExist.version={pc:\"1.3.5\"};export default function isDownloadFileExist(i){return invoker.invoke(\"isDownloadFileExist\",i)}", "import _extends from\"@babel/runtime/helpers/extends\";import invoker,{API_INVOKER_TYPE,PLATFORM_TYPE_ENUM}from\"./invoker\";function joinScheduleConfHandler(e,n){void 0===e&&(e={});var o=n.resolve,i=n.reject,r=n.context,c=n.containerType,l=n.appType,d=n.platformType;if(c){var f=function(e){invoker.handleBridgeResponse(e,o,i)},t=function(e){invoker.handleBridgeResponse(e,o,i)};l===API_INVOKER_TYPE.MINI_APP?r&&r({m:\"biz.conference.joinScheduleConf\",args:e,onSuccess:f,onFail:t}):d===PLATFORM_TYPE_ENUM.ANDROID?r&&r(f,t,\"biz.conference\",\"joinScheduleConf\",e):d===PLATFORM_TYPE_ENUM.IOS&&r.callHandler(\"biz.conference.joinScheduleConf\",Object.assign({},e),(function(e){invoker.handleBridgeResponse(e,o,i)}))}else r&&r.call(\"joinScheduleConf\",e,(function(){o()}))}invoker.registerAPI(\"joinScheduleConf\",{mini:joinScheduleConfHandler,mobile:joinScheduleConfHandler,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,\"biz.conference.joinScheduleConf\",_extends({},e))}}),joinScheduleConf.version={android:\"3.7.5\",ios:\"3.7.5\",pc:\"3.7.5\"};export default function joinScheduleConf(e){return invoker.invoke(\"joinScheduleConf\",e)}", "import _extends from\"@babel/runtime/helpers/extends\";import invoker,{API_INVOKER_TYPE,PLATFORM_TYPE_ENUM}from\"./invoker\";function joinVideoConfHandler(n,e){void 0===n&&(n={});var o=e.resolve,i=e.reject,r=e.context,d=e.containerType,f=e.appType,c=e.platformType;if(d){var t=function(n){invoker.handleBridgeResponse(n,o,i)},a=function(n){invoker.handleBridgeResponse(n,o,i)};f===API_INVOKER_TYPE.MINI_APP?r&&r({m:\"biz.conference.joinVideoConf\",args:n,onSuccess:t,onFail:a}):c===PLATFORM_TYPE_ENUM.ANDROID?r&&r(t,a,\"biz.conference\",\"joinVideoConf\",n):c===PLATFORM_TYPE_ENUM.IOS&&r.callHandler(\"biz.conference.joinVideoConf\",Object.assign({},n),(function(n){invoker.handleBridgeResponse(n,o,i)}))}else r&&r.call(\"joinVideoConf\",n,(function(){o()}))}invoker.registerAPI(\"joinVideoConf\",{mini:joinVideoConfHandler,mobile:joinVideoConfHandler,pc:function(n,e){window.dingtalk.platform.invokeAPI(e.msgId,\"biz.conference.joinVideoConf\",_extends({},n))}}),joinVideoConf.version={android:\"3.7.5\",ios:\"3.7.5\",pc:\"3.7.5\"};export default function joinVideoConf(n){return invoker.invoke(\"joinVideoConf\",n)}", "import _extends from\"@babel/runtime/helpers/extends\";import invoker from\"./invoker\";invoker.registerAPI(\"joinVideoMeeting\",{mini:!0,mobile:!0,pc:function(e,i){window.dingtalk.platform.invokeAPI(i.msgId,\"biz.conference.joinVideoMeeting\",_extends({},e))}}),joinVideoMeeting.version={android:\"3.9.0\",ios:\"3.9.0\",pc:\"3.9.0\"};export default function joinVideoMeeting(e){return invoker.invoke(\"joinVideoMeeting\",e)}", "import invoker from\"./invoker\";invoker.registerAPI(\"locateOnMap\",{mobile:!0,mini:!0}),locateOnMap.version={android:\"1.3.0\",ios:\"1.3.0\"};export default function locateOnMap(o){return invoker.invoke(\"locateOnMap\",o)}", "import invoker from\"./invoker\";export default function on(r,e){return invoker.registerEvent(r,e)}", "import invoker from\"./invoker\";invoker.registerAPI(\"onAudioPlayEnd\",{mini:!0,mobile:!0}),onAudioPlayEnd.version={android:\"1.6.2\",ios:\"1.6.2\"};export default function onAudioPlayEnd(){return invoker.invoke(\"onAudioPlayEnd\")}", "import invoker from\"./invoker\";invoker.registerAPI(\"onRecordAudioEnd\",{mini:!0,mobile:!0}),onRecordAudioEnd.version={android:\"1.3.0\",ios:\"1.3.0\"};export default function onRecordAudioEnd(o){return invoker.invoke(\"onRecordAudioEnd\",o)}", "import invoker from\"./invoker\";invoker.registerAPI(\"openApiInvoker\",{mini:!0,mobile:!0,pc:function(o,n){window.dingtalk.platform.invokeAPI(n.msgId,\"openApiInvoker\",o)}}),openApiInvoker.version={ios:\"3.0.1\",android:\"3.0.1\",pc:\"3.0.1\"};export default function openApiInvoker(o){return invoker.invoke(\"openApiInvoker\",o)}", "import invoker from\"./invoker\";invoker.registerAPI(\"openApp\",{mini:!0,mobile:!0}),openApp.version={android:\"1.3.2\",ios:\"1.3.2\"};export default function openApp(o){return invoker.invoke(\"openApp\",o)}", "import invoker from\"./invoker\";invoker.registerAPI(\"openBrowser\",{mini:!0,mobile:!0}),openBrowser.version={android:\"1.2.3\"};export default function openBrowser(r){return invoker.invoke(\"openBrowser\",r)}", "import invoker from\"./invoker\";invoker.registerAPI(\"openChat\",{mini:!0,mobile:!0,pc:function(n,o){window.dingtalk.platform.invokeAPI(o.msgId,\"internal.chat.toConversation\",{cid:n.chatId})}}),openChat.version={android:\"1.2.0\",ios:\"1.2.0\",pc:\"1.2.0\"};export default function openChat(n){return invoker.invoke(\"openChat\",n)}", "import invoker from\"./invoker\";invoker.registerAPI(\"openDownloadFile\",{pc:function(o,n){window.dingtalk.platform.invokeAPI(n.msgId,\"biz.util.openLocalFile\",o)}}),openDownloadFile.version={pc:\"1.3.5\"};export default function openDownloadFile(o){return invoker.invoke(\"openDownloadFile\",o)}", "import invoker,{PLATFORM_TYPE_ENUM,API_INVOKER_TYPE}from\"./invoker\";import getVersion from\"./version\";import compareVersion from\"./utils/compareVersion\";function openLinkHandler(n,o){var e=o.resolve,i=o.reject,r=o.context,p=o.containerType,s=o.appType,t=o.platformType;getVersion().then((function(o){var a=o.version,u=-1!==compareVersion(a,\"1.6.2\");if(p){var c=function(n){invoker.handleBridgeResponse(n,e,i)},m=function(n){invoker.handleBridgeResponse(n,e,i)};s===API_INVOKER_TYPE.MINI_APP?r&&r({m:u?\"taurus.common.openLink\":\"taurus.common.pushWindow\",args:n,onSuccess:c,onFail:m}):t===PLATFORM_TYPE_ENUM.ANDROID?r&&r(c,m,\"taurus.common\",u?\"openLink\":\"pushWindow\",n):t===PLATFORM_TYPE_ENUM.IOS&&r.callHandler(u?\"taurus.common.openLink\":\"taurus.common.pushWindow\",Object.assign({},n),(function(n){invoker.handleBridgeResponse(n,e,i)}))}else r&&r.call(u?\"openLink\":\"pushWindow\",n,(function(n){invoker.handleBridgeResponse(n,e,i)}))}))}invoker.registerAPI(\"openLink\",{mini:openLinkHandler,mobile:openLinkHandler,pc:function(n,o){window.dingtalk.platform.invokeAPI(o.msgId,\"biz.util.openLink\",n)}}),openLink.version={android:\"1.2.0\",ios:\"1.2.0\",pc:\"1.2.0\"};export default function openLink(n){return invoker.invoke(\"openLink\",n)}", "import invoker from\"./invoker\";invoker.registerAPI(\"openPage\",{mini:!0,mobile:!0}),openPage.version={android:\"1.1.0\",ios:\"1.1.0\"};export default function openPage(e){return invoker.invoke(\"openPage\",e)}", "import _extends from\"@babel/runtime/helpers/extends\";import invoker from\"./invoker\";invoker.registerAPI(\"dgOpenApp\",{mobile:!0,mini:!0}),openSchemeUrl.version={android:\"1.3.1.1\",ios:\"1.3.1.1\"};export default function openSchemeUrl(e){return invoker.invoke(\"dgOpenApp\",_extends({},e,{_apiName:\"openSchemeUrl\"}))}", "import invoker from\"./invoker\";invoker.registerAPI(\"openSlidePanel\",{pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,\"biz.util.openSlidePanel\",e)}}),openSlidePanel.version={pc:\"1.3.5\"};export default function openSlidePanel(e){return invoker.invoke(\"openSlidePanel\",e)}", "import invoker from\"./invoker\";invoker.registerAPI(\"openWatermarkCamera\",{mobile:!0,mini:!0}),openWatermarkCamera.version={android:\"1.3.7\",ios:\"1.3.7\"};export default function openWatermarkCamera(){return invoker.invoke(\"openWatermarkCamera\")}", "import invoker from\"./invoker\";invoker.registerAPI(\"pauseAudio\",{mini:!0,mobile:!0}),pauseAudio.version={android:\"1.3.0\",ios:\"1.3.0\"};export default function pauseAudio(i){return invoker.invoke(\"pauseAudio\",i)}", "import invoker from\"./invoker\";invoker.registerAPI(\"pickChat\",{mini:!0,mobile:!0,pc:function(i,o){window.dingtalk.platform.invokeAPI(o.msgId,\"biz.chat.pickConversation\",i)}}),pickChat.version={android:\"1.2.0\",ios:\"1.2.0\",pc:\"2.9.0\"};export default function pickChat(i){return invoker.invoke(\"pickChat\",i)}", "import invoker from\"./invoker\";invoker.registerAPI(\"pickChatByCorpId\",{mini:!0,mobile:!0});export default function pickChatByCorpId(i){return invoker.invoke(\"pickChatByCorpId\",i)}", "import invoker from\"./invoker\";invoker.registerAPI(\"pickGroupChat\",{pc:function(o,i){window.dingtalk.platform.invokeAPI(i.msgId,\"biz.contact.pickGroupChat\",o)}}),pickGroupChat.version={pc:\"2.10.30\"};export default function pickGroupChat(o){return invoker.invoke(\"pickGroupChat\",o)}", "import invoker,{API_INVOKER_TYPE,PLATFORM_TYPE_ENUM}from\"./invoker\";function pickGroupConversationHandler(o,n){void 0===o&&(o={});var r=n.resolve,i=n.reject,e=n.context,a=n.containerType,t=n.appType,p=n.platformType;if(a){var c=function(o){invoker.handleBridgeResponse(o,r,i)},s=function(o){invoker.handleBridgeResponse(o,r,i)};t===API_INVOKER_TYPE.MINI_APP?e&&e({m:p===PLATFORM_TYPE_ENUM.ANDROID?\"taurus.common.pickGroupConversation\":\"internal.chat.pickGroupConversation\",args:o,onSuccess:c,onFail:s}):p===PLATFORM_TYPE_ENUM.ANDROID?e&&e(c,s,\"taurus.common\",\"pickGroupConversation\",o):p===PLATFORM_TYPE_ENUM.IOS&&e.callHandler(\"internal.chat.pickGroupConversation\",Object.assign({},o),(function(o){r(o)}))}else e&&e.call(\"pickGroupConversation\",o,(function(){r()}))}invoker.registerAPI(\"pickGroupConversation\",{mini:pickGroupConversationHandler,mobile:pickGroupConversationHandler}),pickGroupConversation.version={android:\"2.8.0\",ios:\"2.8.0\"};export default function pickGroupConversation(o){return void 0===o&&(o={owner:!1}),invoker.invoke(\"pickGroupConversation\",o)}", "import invoker from\"./invoker\";invoker.registerAPI(\"playAudio\",{mini:!0,mobile:!0}),playAudio.version={android:\"1.3.0\",ios:\"1.3.0\"};export default function playAudio(i){return invoker.invoke(\"playAudio\",i)}", "import invoker from\"./invoker\";invoker.registerAPI(\"previewDoc\",{mini:!0,mobile:!0}),previewDoc.version={android:\"1.1.0\",ios:\"1.1.0\"};export default function previewDoc(e){return invoker.invoke(\"previewDoc\",e)}", "import invoker from\"./invoker\";invoker.registerAPI(\"previewImage\",{mini:!0,mobile:!0,pc:function(e,i){window.dingtalk.platform.invokeAPI(i.msgId,\"biz.util.previewImage\",e)}}),previewImage.version={android:\"1.2.0\",ios:\"1.2.0\",pc:\"1.2.0\"};export default function previewImage(e){return invoker.invoke(\"previewImage\",e)}", "import invoker from\"./invoker\";invoker.registerAPI(\"printFile\",{mini:!0,mobile:!0}),printFile.version={android:\"2.2.10\"};export default function printFile(i){return invoker.invoke(\"printFile\",i)}", "import invoker from\"./invoker\";invoker.registerAPI(\"printNativeLog\",{mini:!0,mobile:!0}),printNativeLog.version={android:\"1.9.4\",ios:\"1.9.4\"};export default function printNativeLog(i){return invoker.invoke(\"printNativeLog\",i)}", "import invoker,{BRIDGE_ERROR_CODE,API_INVOKER_TYPE,PLATFORM_TYPE_ENUM}from\"./invoker\";function promptHandler(e,o){var n=o.resolve,r=o.reject,t=o.context,i=o.containerType,p=o.appType,a=o.platformType,s={message:e.message,title:e.title,okButton:e.button<PERSON>abels[0],cancelButton:e.buttonLabels[1]};if(i){var l=function(e){invoker.handleBridgeResponse(e,n,r)},m=function(e){invoker.handleBridgeResponse(e,n,r)};p===API_INVOKER_TYPE.MINI_APP?t&&t({m:\"taurus.common.prompt\",args:s,onSuccess:l,onFail:m}):a===PLATFORM_TYPE_ENUM.ANDROID?t&&t(l,m,\"taurus.common\",\"prompt\",s):a===PLATFORM_TYPE_ENUM.IOS&&t.callHandler(\"taurus.common.prompt\",Object.assign({},s),(function(e){invoker.handleBridgeResponse(e,n,r)}))}else t&&t.call(\"prompt\",s,(function(e){var o={errorCode:BRIDGE_ERROR_CODE.SUCCESS,result:{buttonIndex:e.ok?0:1,value:e.inputValue}};invoker.handleBridgeResponse(o,n,r)}))}invoker.registerAPI(\"prompt\",{mini:promptHandler,mobile:promptHandler,pc:function(e,o){window.dingtalk.platform.invokeAPI(o.msgId,\"device.notification.prompt\",e)}}),prompt.version={android:\"1.3.2\",ios:\"1.3.2\",pc:\"1.3.2\"};export default function prompt(e){return invoker.invoke(\"prompt\",e)}", "import invoker from\"./invoker\";invoker.registerAPI(\"pushWindow\",{mini:!0,mobile:!0}),pushWindow.version={android:\"2.9.7\",ios:\"2.9.7\"};export default function pushWindow(i){return invoker.invoke(\"pushWindow\",i)}", "import invoker from\"./invoker\";invoker.registerAPI(\"readImageToBase64\",{mini:!0,mobile:!0,pc:function(e,o){window.dingtalk.platform.invokeAPI(o.msgId,\"readImageToBase64\",e)}}),readImageToBase64.version={ios:\"2.1.0\",android:\"2.1.0\",pc:\"2.1.0\"};export default function readImageToBase64(e){return invoker.invoke(\"readImageToBase64\",e)}", "import invoker from\"./invoker\";export default function ready(o){\"function\"==typeof o?invoker.onReady(o):console.error(\"dd.ready's param must be function! \")}", "import invoker from\"./invoker\";export var COMPRESS_LEVEL;!function(e){e[e.ADJUST_BY_NET=0]=\"ADJUST_BY_NET\",e[e.LOW_QUALITY=1]=\"LOW_QUALITY\",e[e.MID_QUALITY=2]=\"MID_QUALITY\",e[e.HIGH_QUALITY=3]=\"HIGH_QUALITY\",e[e.NOT_COMPRESSED=4]=\"NOT_COMPRESSED\",e[e.CUSTOM=5]=\"CUSTOM\"}(COMPRESS_LEVEL||(COMPRESS_LEVEL={})),invoker.registerAPI(\"reduceImageSize\",{mini:!0,mobile:!0,pc:function(e,i){window.dingtalk.platform.invokeAPI(i.msgId,\"reduceImageSize\",e)}}),reduceImageSize.version={ios:\"2.1.0\",android:\"2.1.0\",pc:\"2.1.0\"};export default function reduceImageSize(e){return invoker.invoke(\"reduceImageSize\",e)}", "import invoker from\"./invoker\";invoker.registerAPI(\"removeStorageItem\",{mobile:!0,mini:!0}),removeStorageItem.version={android:\"1.3.1.1\",ios:\"1.3.1.1\"};export default function removeStorageItem(e){return invoker.invoke(\"removeStorageItem\",e)}", "import invoker,{API_INVOKER_TYPE,PLATFORM_TYPE_ENUM}from\"./invoker\";function replacePageHandler(e,a){var n=a.resolve,r=a.reject,o=a.containerType,i=a.platformType,c=a.appType,l=a.context;if(o){var P=function(){e.onSuccess&&e.onSuccess(),n()},t=function(){e.onFail&&e.onFail(),r()};c===API_INVOKER_TYPE.MINI_APP?l&&l({m:\"biz.navigation.replace\",args:e,onSuccess:P,onFail:t}):i===PLATFORM_TYPE_ENUM.ANDROID?l&&l(P,t,\"biz.navigation\",\"replace\",e):i===PLATFORM_TYPE_ENUM.IOS&&l.callHandler(\"taurus.common.replacePage\",Object.assign({},e),(function(){n()}))}else l&&l.call(\"replacePage\",e,(function(){n()}))}invoker.registerAPI(\"replacePage\",{mini:replacePageHandler,mobile:replacePageHandler}),replacePage.version={android:\"1.3.2\",ios:\"1.3.2\"};export default function replacePage(e){return invoker.invoke(\"replacePage\",e)}", "import invoker from\"./invoker\";invoker.registerAPI(\"resetView\",{mini:!0,mobile:!0}),resetView.version={android:\"1.3.0\",ios:\"1.3.0\"};export default function resetView(){return invoker.invoke(\"resetView\")}", "import invoker from\"./invoker\";invoker.registerAPI(\"resumeAudio\",{mini:!0,mobile:!0}),resumeAudio.version={android:\"1.3.0\",ios:\"1.3.0\"};export default function resumeAudio(e){return invoker.invoke(\"resumeAudio\",e)}", "import invoker from\"./invoker\";invoker.registerAPI(\"rotateView\",{mini:!0,mobile:!0}),rotateView.version={android:\"1.3.0\",ios:\"1.3.0\"};export default function rotateView(e){return invoker.invoke(\"rotateView\",e)}", "import invoker from\"./invoker\";invoker.registerAPI(\"scan\",{mini:!0,mobile:!0}),scan.version={android:\"1.3.2\",ios:\"1.3.2\"};export default function scan(n){return invoker.invoke(\"scan\",n)}", "import invoker from\"./invoker\";invoker.registerAPI(\"searchOnMap\",{mini:!0,mobile:!0}),searchOnMap.version={android:\"1.3.2\",ios:\"1.3.2\"};export default function searchOnMap(r){return invoker.invoke(\"searchOnMap\",r)}", "import _extends from\"@babel/runtime/helpers/extends\";import invoker from\"./invoker\";function fillDefaultProperties(e){return _extends({},e,{actionId:\"\",actionType:\"0\"})}function cardSendOutDataHandler(e,t){var n=t.resolve,a=t.context;a&&a.call(\"sendOutData\",fillDefaultProperties(e),(function(){n()}))}invoker.registerAPI(\"cardSendOutData\",{mini:cardSendOutDataHandler,mobile:cardSendOutDataHandler}),cardSendOutData.version={android:\"2.5.0\",ios:\"2.5.0\"};export default function cardSendOutData(e){return invoker.invoke(\"cardSendOutData\",e)}", "import invoker from\"./invoker\";export var POLICYENUM;!function(o){o.DEFAULT=\"0\",o.DISABLEALL=\"1\",o.ENABLEALL=\"2\"}(POLICYENUM||(POLICYENUM={})),invoker.registerAPI(\"setLocalScreenShotPolicy\",{mini:!0,mobile:!0}),setLocalScreenShotPolicy.version={android:\"2.12.12\",ios:\"2.12.12\"};export default function setLocalScreenShotPolicy(o){return invoker.invoke(\"setLocalScreenShotPolicy\",o)}", "import invoker,{PLATFORM_TYPE_ENUM}from\"./invoker\";function setNavIconHandler(n,e){var o=e.resolve,t=e.reject,i=e.containerType,c=e.platformType,a=e.context;if(i){c===PLATFORM_TYPE_ENUM.ANDROID?a&&a((function(e){n.onSuccess&&n.onSuccess(),o()}),(function(n){t()}),\"biz.navigation\",\"setIcon\",n):c===PLATFORM_TYPE_ENUM.IOS&&a.callHandler(\"biz.navigation.setIcon\",Object.assign({},n),(function(n){o()}))}else a&&a.call(\"setNavIcon\",n,(function(n){o()}))}invoker.registerAPI(\"setNavIcon\",{mobile:setNavIconHandler}),setNavIcon.version={android:\"1.3.0\",ios:\"1.3.0\"};export default function setNavIcon(n){return invoker.invoke(\"setNavIcon\",n)}", "import invoker,{API_INVOKER_TYPE,PLATFORM_TYPE_ENUM,Container_Type_Enum}from\"./invoker\";import getContainerType from\"./getContainerType\";function setNavLeftTextHandler(e,n){var t=n.resolve,i=n.reject,a=n.context,o=n.containerType,r=n.appType,s=n.platformType,T=n.watch;if(o){var c=function(n){e.onSuccess&&e.onSuccess(),invoker.handleBridgeResponse(n,t,i)},f=function(e){invoker.handleBridgeResponse(e,t,i)};r===API_INVOKER_TYPE.MINI_APP?a&&a({m:\"biz.navigation.setLeft\",args:e,onSuccess:c,onFail:f}):s===PLATFORM_TYPE_ENUM.ANDROID?a&&a(c,f,\"biz.navigation\",\"setLeft\",e):s===PLATFORM_TYPE_ENUM.IOS&&a.callHandler(\"biz.navigation.setLeft\",Object.assign({},e),(function(e){!T&&t(e)}))}else a&&a.call(\"setNavLeftText\",e,(function(){t()}))}invoker.registerAPI(\"setNavLeftText\",{mini:setNavLeftTextHandler,mobile:setNavLeftTextHandler,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,\"biz.navigation.setLeft\",e)}}),setNavLeftText.version={ios:\"1.2.0\",pc:\"1.2.0\"};export default function setNavLeftText(e){var n=getContainerType();return invoker.invoke(\"setNavLeftText\",n===Container_Type_Enum.isDingTalk?Object.assign({watch:!0,show:!0,control:!1,showIcon:!0,text:\"\"},e):e,{dingTalkAPIName:n===Container_Type_Enum.isDingTalk?\"biz.navigation.setLeft\":null})}", "import invoker,{Container_Type_Enum,PLATFORM_TYPE_ENUM}from\"./invoker\";import getContainerType from\"./getContainerType\";invoker.registerAPI(\"setOptionMenu\",{mobile:function(n,e){var i=e.resolve,o=e.reject,t=e.context,l=e.containerType,s=e.platformType;if(l){var a,r,u={text:n.title,show:void 0===n.show||n.show,control:void 0===n.control||n.control};if(s===PLATFORM_TYPE_ENUM.ANDROID)t&&t((function(e){n.onSuccess&&n.onSuccess(e),i(e)}),(function e(i){n.onFail&&n.onFail(i),o(e)}),\"biz.navigation\",(null==n||null===(a=n.menus)||void 0===a?void 0:a.length)>1?\"setMenu\":\"setRight\",(null==n||null===(r=n.menus)||void 0===r?void 0:r.length)>1?n:u);else if(s===PLATFORM_TYPE_ENUM.IOS){var v,c;t.callHandler((null==n||null===(v=n.menus)||void 0===v?void 0:v.length)>1?\"biz.navigation.setMenu\":\"biz.navigation.setRight\",Object.assign({},(null==n||null===(c=n.menus)||void 0===c?void 0:c.length)>1?n:u),(function(){i()}))}}else t&&t.call(\"setOptionMenu\",n,(function(){i()}))}}),setOptionMenu.version={android:\"1.1.0\",ios:\"1.1.0\"};export default function setOptionMenu(n){var e=getContainerType();return invoker.invoke(\"setOptionMenu\",e===Container_Type_Enum.isDingTalk?Object.assign({watch:!0,show:!0,control:!1,showIcon:!0,text:\"\"},n):n,e===Container_Type_Enum.isDingTalk?{dingTalkAPIName:\"biz.navigation.setRight\"}:null)}", "import invoker from\"./invoker\";invoker.registerAPI(\"setProxyInfo\",{pc:function(o,n){window.dingtalk.platform.invokeAPI(n.msgId,\"net.util.setProxyInfo\",o)}}),setProxyInfo.version={pc:\"2.10.0\"};export default function setProxyInfo(o){return invoker.invoke(\"setProxyInfo\",o)}", "import invoker from\"./invoker\";invoker.registerAPI(\"setStorageItem\",{mobile:!0,mini:!0}),setStorageItem.version={android:\"1.3.1.1\",ios:\"1.3.1.1\"};export default function setStorageItem(e){return invoker.invoke(\"setStorageItem\",e)}", "import invoker,{API_INVOKER_TYPE,PLATFORM_TYPE_ENUM}from\"./invoker\";export var goBackBtnEnum;function setTitleHandler(e,n){var i=n.resolve,t=n.reject,o=n.context,a=n.containerType,r=n.appType,l=n.platformType;if(a){var s=function(e){invoker.handleBridgeResponse(e,i,t)},T=function(e){invoker.handleBridgeResponse(e,i,t)};r===API_INVOKER_TYPE.MINI_APP?o&&o({m:\"biz.navigation.setTitle\",args:e,onSuccess:s,onFail:T}):l===PLATFORM_TYPE_ENUM.ANDROID?o&&o(s,T,\"biz.navigation\",\"setTitle\",e):l===PLATFORM_TYPE_ENUM.IOS&&o.callHandler(\"biz.navigation.setTitle\",Object.assign({},e),(function(e){invoker.handleBridgeResponse(e,i,t)}))}else o&&o.call(\"setTitle\",e,(function(){i()}))}!function(e){e.TRUE=\"true\",e.FALSE=\"false\"}(goBackBtnEnum||(goBackBtnEnum={})),invoker.registerAPI(\"setTitle\",{mini:setTitleHandler,mobile:setTitleHandler,pc:function(e,n){window.dingtalk.platform.invokeAPI(n.msgId,\"biz.navigation.setTitle\",e)}}),setTitle.version={android:\"1.2.0\",ios:\"1.2.0\",pc:\"1.2.0\"};export default function setTitle(e){return invoker.invoke(\"setTitle\",e)}", "import invoker from\"./invoker\";invoker.registerAPI(\"shareFileToMessage\",{mini:!0,mobile:!0,pc:function(e,i){window.dingtalk.platform.invokeAPI(i.msgId,\"shareFileToMessage\",e)}}),shareFileToMessage.version={android:\"1.8.2\",ios:\"1.8.2\",pc:\"1.8.2\"};export default function shareFileToMessage(e){return invoker.invoke(\"shareFileToMessage\",e)}", "import invoker from\"./invoker\";invoker.registerAPI(\"shareImageToMessage\",{mini:!0,mobile:!0,pc:function(e,o){window.dingtalk.platform.invokeAPI(o.msgId,\"shareImageToMessage\",e)}}),shareImageToMessage.version={android:\"1.8.2\",ios:\"1.8.2\",pc:\"1.8.2\"};export default function shareImageToMessage(e){return invoker.invoke(\"shareImageToMessage\",e)}", "import invoker from\"./invoker\";invoker.registerAPI(\"shareToMessage\",{mini:!0,mobile:!0,pc:function(e,o){window.dingtalk.platform.invokeAPI(o.msgId,\"biz.util.share\",e)}}),shareToMessage.version={android:\"1.3.5\",ios:\"1.3.5\",pc:\"1.3.5\"};export default function shareToMessage(e){return invoker.invoke(\"shareToMessage\",e)}", "import invoker from\"./invoker\";invoker.registerAPI(\"shootVideo\",{mini:!0,mobile:!0}),shootVideo.version={android:\"1.3.5\",ios:\"1.3.5\"};export default function shootVideo(){return invoker.invoke(\"shootVideo\")}", "import invoker from\"./invoker\";invoker.registerAPI(\"showActionSheet\",{mini:!0,mobile:!0,pc:function(o,i){window.dingtalk.platform.invokeAPI(i.msgId,\"device.notification.actionSheet\",o)}}),showActionSheet.version={android:\"1.3.2\",ios:\"1.3.2\",pc:\"1.3.2\"};export default function showActionSheet(o){return invoker.invoke(\"showActionSheet\",o)}", "import invoker from\"./invoker\";invoker.registerAPI(\"showCallMenu\",{mini:!0,mobile:!0}),showCallMenu.version={android:\"1.3.9\",ios:\"1.3.9\"};export default function showCallMenu(o){return invoker.invoke(\"showCallMenu\",o)}", "import invoker from\"./invoker\";invoker.registerAPI(\"showDatePicker\",{mobile:!0,mini:!0}),showDatePicker.version={android:\"1.3.0\",ios:\"1.3.0\"};export default function showDatePicker(e){return invoker.invoke(\"showDatePicker\",e)}", "import invoker from\"./invoker\";invoker.registerAPI(\"showDateTimePicker\",{mini:!0,mobile:!0}),showDateTimePicker.version={android:\"1.3.10\",ios:\"1.3.10\"};export default function showDateTimePicker(e){return invoker.invoke(\"showDateTimePicker\",e)}", "import invoker from\"./invoker\";invoker.registerAPI(\"showExtendModal\",{mini:!0,mobile:!0}),showExtendModal.version={android:\"1.3.5\",ios:\"1.3.5\"};export default function showExtendModal(o){return invoker.invoke(\"showExtendModal\",o)}", "import invoker from\"./invoker\";invoker.registerAPI(\"showHomeBottomTab\",{mobile:!0}),showHomeBottomTab.version={android:\"1.3.0\",ios:\"1.3.0\"};export default function showHomeBottomTab(o){return invoker.invoke(\"showHomeBottomTab\",o)}", "import invoker,{PLATFORM_TYPE_ENUM,API_INVOKER_TYPE}from\"./invoker\";function showLoadingMenuHandler(o,e){var n=e.resolve,i=e.reject,r=e.context,a=e.containerType,d=e.appType,s=e.platformType;if(a){var t=function(o){invoker.handleBridgeResponse(o,n,i)},c=function(o){invoker.handleBridgeResponse(o,n,i)};d===API_INVOKER_TYPE.MINI_APP?r&&r({m:\"device.notification.showPreloader\",args:o,onSuccess:t,onFail:c}):s===PLATFORM_TYPE_ENUM.ANDROID?r&&r(t,c,\"device.notification\",\"showPreloader\",o):s===PLATFORM_TYPE_ENUM.IOS&&r.callHandler(\"device.notification.showPreloader\",Object.assign({},o),(function(o){n(o)}))}else r&&r.call(\"showLoading\",o,(function(){n()}))}invoker.registerAPI(\"showLoading\",{mini:showLoadingMenuHandler,mobile:showLoadingMenuHandler}),showLoading.version={android:\"1.3.2\",ios:\"1.3.2\"};export default function showLoading(o){return invoker.invoke(\"showLoading\",o)}", "import invoker from\"./invoker\";invoker.registerAPI(\"showModal\",{mini:!0,mobile:!0,pc:function(o,i){window.dingtalk.platform.invokeAPI(i.msgId,\"biz.util.openModal\",o)}}),showModal.version={android:\"1.3.5\",ios:\"1.3.5\",pc:\"1.3.5\"};export default function showModal(o){return invoker.invoke(\"showModal\",o)}", "import invoker from\"./invoker\";invoker.registerAPI(\"showMultiSelect\",{mini:!0,mobile:!0}),showMultiSelect.version={android:\"1.3.10\",ios:\"1.3.10\"};export default function showMultiSelect(e){return invoker.invoke(\"showMultiSelect\",e)}", "import invoker from\"./invoker\";invoker.registerAPI(\"showOnMap\",{mini:!0,mobile:!0}),searchOnMap.version={android:\"1.3.2\",ios:\"1.3.2\"};export default function searchOnMap(n){return invoker.invoke(\"showOnMap\",n)}", "import invoker,{PLATFORM_TYPE_ENUM}from\"./invoker\";invoker.registerAPI(\"showOptionMenu\",{mobile:function(n,o){var e=o.resolve,i=o.reject,t=o.context,r=o.platformType;if(o.containerType){r===PLATFORM_TYPE_ENUM.ANDROID?t&&t((function(){e()}),(function(){i()}),\"taurus.common\",\"showOptionMenu\",n):r===PLATFORM_TYPE_ENUM.IOS&&t.callHandler(\"taurus.common.showOptionMenu\",Object.assign({},n),(function(){e()}))}else t&&t.call(\"showOptionMenu\",n,(function(){e()}))}}),showOptionMenu.version={android:\"1.1.0\",ios:\"1.1.0\"};export default function showOptionMenu(){return invoker.invoke(\"showOptionMenu\")}", "import invoker from\"./invoker\";invoker.registerAPI(\"showPlainInputUponKeyboard\",{mobile:!0,mini:!0}),showPlainInputUponKeyboard.version={android:\"1.3.0\",ios:\"1.3.0\"};export default function showPlainInputUponKeyboard(o){return invoker.invoke(\"showPlainInputUponKeyboard\",o)}", "import invoker from\"./invoker\";invoker.registerAPI(\"showQuickCallMenu\",{mini:!0,mobile:!0}),showQuickCallMenu.version={android:\"1.6.2\",ios:\"1.6.2\"};export default function showQuickCallMenu(i){return invoker.invoke(\"showQuickCallMenu\",i)}", "import invoker from\"./invoker\";invoker.registerAPI(\"showSelect\",{mini:!0,mobile:!0}),showSelect.version={android:\"1.3.2\",ios:\"1.3.2\"};export default function showSelect(e){return invoker.invoke(\"showSelect\",e)}", "import invoker from\"./invoker\";invoker.registerAPI(\"showSignature\",{mobile:!0}),showSignature.version={android:\"1.3.4\"};export default function showSignature(r){return invoker.invoke(\"showSignature\",r)}", "import invoker from\"./invoker\";invoker.registerAPI(\"showSocialShare\",{mini:!0,mobile:!0}),showSocialShare.version={android:\"1.2.0.10\",ios:\"1.2.0.10\"};export default function showSocialShare(o){return invoker.invoke(\"showSocialShare\",o)}", "import invoker from\"./invoker\";invoker.registerAPI(\"showTimePicker\",{mobile:!0,mini:!0}),showTimePicker.version={android:\"1.3.0\",ios:\"1.3.0\"};export default function showTimePicker(i){return invoker.invoke(\"showTimePicker\",i)}", "import invoker,{PLATFORM_TYPE_ENUM,API_INVOKER_TYPE}from\"./invoker\";function showTitlebarHandler(i,e){var n=e.resolve,o=e.reject,a=e.containerType,r=e.platformType,t=e.appType,l=e.context,s=Object.assign(i,{hidden:!1});if(a){var c=function(){i.onSuccess&&i.onSuccess(),n()},T=function(){i.onFail&&i.onFail(),o()};t===API_INVOKER_TYPE.MINI_APP?l&&l({m:\"biz.navigation.hideBar\",args:s,onSuccess:c,onFail:T}):r===PLATFORM_TYPE_ENUM.ANDROID?l&&l(c,T,\"biz.navigation\",\"hideBar\",s):r===PLATFORM_TYPE_ENUM.IOS&&l.callHandler(\"biz.navigation.hideBar\",Object.assign({},s),(function(){n()}))}else l&&l.call(\"showTitlebar\",s,(function(){n()}))}invoker.registerAPI(\"showTitlebar\",{mini:showTitlebarHandler,mobile:showTitlebarHandler}),showTitleBar.version={android:\"2.1.0\",ios:\"2.1.0\"};export default function showTitleBar(){return invoker.invoke(\"showTitlebar\")}", "import invoker from\"./invoker\";invoker.registerAPI(\"startFaceRecognition\",{mini:!0,mobile:!0}),startFaceRecognition.version={android:\"1.8.2\",ios:\"1.8.2\"};export default function startFaceRecognition(i){return invoker.invoke(\"startFaceRecognition\",i)}", "import invoker,{BRIDGE_ERROR_CODE,CONTINUOUS_EVENT_LIST,PLATFORM_TYPE_ENUM,API_INVOKER_TYPE}from\"./invoker\";function startGeolocationHandler(e,o){var n=o.resolve,t=o.reject,r=o.context,i=o.platformType,a=o.containerType,s=o.appType,c=invoker.registerEvent(CONTINUOUS_EVENT_LIST.UPDATE_LOCATION,(function(o){var n=o.data;n.errorCode!==BRIDGE_ERROR_CODE.SUCCESS?e.onFail&&e.onFail(n):e.onSuccess&&e.onSuccess(n.result)}));if(a){var l=function(o){invoker.registerContinuesEvent(e.sceneId,c),invoker.handleBridgeResponse(o,n,t)},E=function(o){invoker.registerContinuesEvent(e.sceneId,c),invoker.handleBridgeResponse(o,n,t)};s===API_INVOKER_TYPE.MINI_APP?(console.log(\"taurus.common.startGeolocation\",e),r&&r({m:\"taurus.common.startGeolocation\",args:e,onSuccess:l,onFail:E})):i===PLATFORM_TYPE_ENUM.ANDROID?r&&r(l,E,\"taurus.common\",\"startGeolocation\",e):i===PLATFORM_TYPE_ENUM.IOS&&r.callHandler(\"taurus.common.startGeolocation\",Object.assign({},e),(function(o){invoker.registerContinuesEvent(e.sceneId,c),invoker.handleBridgeResponse(o,n,t)}))}else r&&r.call(\"startGeolocation\",e,(function(o){invoker.registerContinuesEvent(e.sceneId,c),invoker.handleBridgeResponse(o,n,t)}))}invoker.registerAPI(\"startGeolocation\",{mobile:startGeolocationHandler,mini:startGeolocationHandler}),startGeolocation.version={android:\"1.3.2\",ios:\"1.3.2\"};export default function startGeolocation(e){return invoker.invoke(\"startGeolocation\",e)}", "import invoker,{BRIDGE_ERROR_CODE,CONTINUOUS_EVENT_LIST,PLATFORM_TYPE_ENUM,API_INVOKER_TYPE}from\"./invoker\";function startListenNetworkStatusHandler(e,t){var r=t.resolve,n=t.reject,s=t.context,o=t.containerType,i=t.appType,a=t.platformType,u=invoker.registerEvent(CONTINUOUS_EVENT_LIST.UPDATE_NETWORK_STATUS,(function(t){var r=t.data;r.errorCode!==BRIDGE_ERROR_CODE.SUCCESS?e.onFail&&e.onFail(r):e.onSuccess&&e.onSuccess(r.result)}));if(o){var E=function(e){invoker.registerContinuesEvent(e.result.requestId,u),invoker.handleBridgeResponse(e,r,n)},k=function(e){invoker.registerContinuesEvent(e.result.requestId,u),invoker.handleBridgeResponse(e,r,n)};i===API_INVOKER_TYPE.MINI_APP?s&&s({m:\"taurus.common.startListenNetworkStatus\",args:e,onSuccess:E,onFail:k}):a===PLATFORM_TYPE_ENUM.ANDROID?s&&s(E,k,\"taurus.common\",\"startListenNetworkStatus\",e):a===PLATFORM_TYPE_ENUM.IOS&&s.callHandler(\"taurus.common.startListenNetworkStatus\",Object.assign({},e),(function(e){invoker.registerContinuesEvent(e.result.requestId,u),invoker.handleBridgeResponse(e,r,n)}))}else s&&s.call(\"startListenNetworkStatus\",e,(function(e){invoker.registerContinuesEvent(e.result.requestId,u),invoker.handleBridgeResponse(e,r,n)}))}invoker.registerAPI(\"startListenNetworkStatus\",{mobile:startListenNetworkStatusHandler,mini:startListenNetworkStatusHandler}),startListenNetworkStatus.version={android:\"1.3.1.1\",ios:\"1.3.1.1\"};export default function startListenNetworkStatus(e){return invoker.invoke(\"startListenNetworkStatus\",e)}", "import invoker from\"./invoker\";invoker.registerAPI(\"startRecordAudio\",{mini:!0,mobile:!0}),startRecordAudio.version={android:\"1.3.0\",ios:\"1.3.0\"};export default function startRecordAudio(o){return invoker.invoke(\"startRecordAudio\",o)}", "import invoker,{BRIDGE_ERROR_CODE,CONTINUOUS_EVENT_LIST,PLATFORM_TYPE_ENUM,API_INVOKER_TYPE}from\"./invoker\";function startTraceReportHandler(e,r){var t=r.resolve,n=r.reject,o=r.context,a=r.containerType,i=r.platformType,s=r.appType,c=invoker.registerEvent(CONTINUOUS_EVENT_LIST.UPDATE_TRACE,(function(r){var t=r.data;t.errorCode&&t.errorCode!==BRIDGE_ERROR_CODE.SUCCESS?e.onFail&&e.onFail(t):e.onSuccess&&e.onSuccess(t.result||t)}));if(a){var R=function(r){invoker.registerContinuesEvent(e.traceId,c),invoker.handleBridgeResponse(r,t,n)},T=function(r){invoker.registerContinuesEvent(e.traceId,c),invoker.handleBridgeResponse(r,t,n)};s===API_INVOKER_TYPE.MINI_APP?o&&o({m:\"taurus.common.startTraceReport\",args:e,onSuccess:R,onFail:T}):i===PLATFORM_TYPE_ENUM.ANDROID?o&&o(R,T,\"taurus.common\",\"startTraceReport\",e):i===PLATFORM_TYPE_ENUM.IOS&&o.callHandler(\"taurus.common.startTraceReport\",Object.assign({},e),(function(r){invoker.registerContinuesEvent(e.traceId,c),invoker.handleBridgeResponse(r,t,n)}))}else o&&o.call(\"startTraceReport\",e,(function(r){invoker.registerContinuesEvent(e.traceId,c),invoker.handleBridgeResponse(r,t,n)}))}invoker.registerAPI(\"startTraceReport\",{mobile:startTraceReportHandler,mini:!0}),startTraceReport.version={android:\"1.3.4\",ios:\"1.3.4\"};export default function startTraceReport(e){return invoker.invoke(\"startTraceReport\",e)}", "import invoker from\"./invoker\";invoker.registerAPI(\"startVPNApp\",{mini:!0,mobile:!0}),startVPNApp.version={android:\"1.6.0\",ios:\"1.6.0\"};export default function startVPNApp(r){return invoker.invoke(\"startVPNApp\",r)}", "import invoker,{CONTINUOUS_EVENT_LIST,PLATFORM_TYPE_ENUM}from\"./invoker\";function startShakeHandler(e,t){var n=t.resolve,a=t.reject,r=t.context,o=t.containerType,s=t.platformType,c=invoker.registerEvent(CONTINUOUS_EVENT_LIST.ON_SHAKE,(function(){e.onSuccess&&e.onSuccess()}));if(o){s===PLATFORM_TYPE_ENUM.ANDROID?r&&r((function(t){e.onSuccess&&e.onSuccess(),n()}),(function(e){a()}),\"taurus.common\",\"startWatchShake\",e):s===PLATFORM_TYPE_ENUM.IOS&&r.callHandler(\"taurus.common.startWatchShake\",Object.assign({},e),(function(e){n()}))}else r&&r.call(\"startWatchShake\",e,(function(e){invoker.registerContinuesEvent(\"shake\",c),invoker.handleBridgeResponse(e,n,a)}))}invoker.registerAPI(\"startWatchShake\",{mobile:startShakeHandler}),startWatchShake.version={android:\"1.6.2\",ios:\"1.6.2\"};export default function startWatchShake(e){return invoker.invoke(\"startWatchShake\",e)}", "import invoker from\"./invoker\";invoker.registerAPI(\"stopAudio\",{mini:!0,mobile:!0}),stopAudio.version={android:\"1.3.0\",ios:\"1.3.0\"};export default function stopAudio(o){return invoker.invoke(\"stopAudio\",o)}", "import invoker,{PLATFORM_TYPE_ENUM,API_INVOKER_TYPE}from\"./invoker\";function stopGeolocationHandler(o,e){var n=e.resolve,i=e.reject,t=e.containerType,r=e.platformType,s=e.appType,a=e.context;if(t){var c=function(e){invoker.removeContinuesEvent(o.sceneId),invoker.handleBridgeResponse(e,n,i)},l=function(e){invoker.removeContinuesEvent(o.sceneId),invoker.handleBridgeResponse(e,n,i)};s===API_INVOKER_TYPE.MINI_APP?a&&a({m:\"taurus.common.stopGeolocation\",args:o,onSuccess:c,onFail:l}):r===PLATFORM_TYPE_ENUM.ANDROID?a&&a(c,l,\"taurus.common\",\"stopGeolocation\",o):r===PLATFORM_TYPE_ENUM.IOS&&a.callHandler(\"taurus.common.stopGeolocation\",Object.assign({},o),(function(e){invoker.removeContinuesEvent(o.sceneId),invoker.handleBridgeResponse(e,n,i)}))}else a&&a.call(\"stopGeolocation\",o,(function(e){invoker.removeContinuesEvent(o.sceneId),invoker.handleBridgeResponse(e,n,i)}))}invoker.registerAPI(\"stopGeolocation\",{mobile:stopGeolocationHandler,mini:stopGeolocationHandler}),stopGeolocation.version={android:\"1.3.2\",ios:\"1.3.2\"};export default function stopGeolocation(o){return invoker.invoke(\"stopGeolocation\",o)}", "import invoker,{API_INVOKER_TYPE,PLATFORM_TYPE_ENUM}from\"./invoker\";function stopListenNetworkStatusHandler(e,t){var n=t.resolve,o=t.reject,s=t.containerType,r=t.appType,i=t.platformType,a=t.context;if(s){var u=function(t){invoker.removeContinuesEvent(e.requestId),invoker.handleBridgeResponse(t,n,o)},v=function(t){invoker.removeContinuesEvent(e.requestId),invoker.handleBridgeResponse(t,n,o)};r===API_INVOKER_TYPE.MINI_APP?a&&a({m:\"taurus.common.stopListenNetworkStatus\",args:e,onSuccess:u,onFail:v}):i===PLATFORM_TYPE_ENUM.ANDROID?a&&a(u,v,\"taurus.common\",\"stopListenNetworkStatus\",e):i===PLATFORM_TYPE_ENUM.IOS&&a.callHandler(\"taurus.common.stopListenNetworkStatus\",Object.assign({},e),(function(t){invoker.removeContinuesEvent(e.requestId),invoker.handleBridgeResponse(t,n,o)}))}else a&&a.call(\"stopListenNetworkStatus\",e,(function(t){invoker.removeContinuesEvent(e.requestId),invoker.handleBridgeResponse(t,n,o)}))}invoker.registerAPI(\"stopListenNetworkStatus\",{mini:stopListenNetworkStatusHandler,mobile:stopListenNetworkStatusHandler}),stopListenNetworkStatus.version={android:\"1.3.1.1\",ios:\"1.3.1.1\"};export default function stopListenNetworkStatus(e){return invoker.invoke(\"stopListenNetworkStatus\",e)}", "import invoker,{PLATFORM_TYPE_ENUM}from\"./invoker\";invoker.registerAPI(\"stopPullToRefresh\",{mobile:function(e,o){var r=o.resolve,l=o.reject,n=o.context,t=o.containerType,i=o.platformType;if(t){i===PLATFORM_TYPE_ENUM.ANDROID?n&&n((function(){r()}),(function(){l()}),\"ui.pullToRefresh\",\"stop\",e):i===PLATFORM_TYPE_ENUM.IOS&&n.callHandler(\"ui.pullToRefresh.stop\",Object.assign({},e),(function(){r()}))}else n&&n.call(\"restorePullToRefresh\",e,(function(){r()}))}}),stopPullToRefresh.version={android:\"1.3.0\",ios:\"1.3.0\"};export default function stopPullToRefresh(){return invoker.invoke(\"stopPullToRefresh\",{_apiName:\"stopPullToRefresh\"})}", "import invoker from\"./invoker\";invoker.registerAPI(\"stopRecordAudio\",{mini:!0,mobile:!0}),stopRecordAudio.version={android:\"1.3.0\",ios:\"1.3.0\"};export default function stopRecordAudio(o){return invoker.invoke(\"stopRecordAudio\",o)}", "import invoker,{PLATFORM_TYPE_ENUM}from\"./invoker\";function stopTraceReportHandler(e,o){var r=o.resolve,n=o.reject,t=o.containerType,i=o.platformType,a=o.context;if(t){i===PLATFORM_TYPE_ENUM.ANDROID?a&&a((function(o){invoker.removeContinuesEvent(e.traceId),invoker.handleBridgeResponse(o,r,n)}),(function(o){invoker.removeContinuesEvent(e.traceId),invoker.handleBridgeResponse(o,r,n)}),\"taurus.common\",\"stopTraceReport\",e):i===PLATFORM_TYPE_ENUM.IOS&&a.callHandler(\"taurus.common.stopTraceReport\",Object.assign({},e),(function(o){invoker.removeContinuesEvent(e.traceId),invoker.handleBridgeResponse(o,r,n)}))}else a&&a.call(\"stopTraceReport\",e,(function(o){invoker.removeContinuesEvent(e.traceId),invoker.handleBridgeResponse(o,r,n)}))}invoker.registerAPI(\"stopTraceReport\",{mobile:stopTraceReportHandler}),stopTraceReport.version={android:\"1.3.4\",ios:\"1.3.4\"};export default function stopTraceReport(e){return invoker.invoke(\"stopTraceReport\",e)}", "import invoker from\"./invoker\";invoker.registerAPI(\"stopVPNApp\",{mini:!0,mobile:!0}),stopVPNApp.version={android:\"1.6.0\",ios:\"1.6.0\"};export default function stopVPNApp(o){return invoker.invoke(\"stopVPNApp\",o)}", "import invoker,{PLATFORM_TYPE_ENUM}from\"./invoker\";function stopWatchShakeHandler(e,o){var t=o.resolve,n=o.reject,a=o.containerType,c=o.platformType,r=o.context;if(a){c===PLATFORM_TYPE_ENUM.ANDROID?r&&r((function(o){e.onSuccess&&e.onSuccess(),t()}),(function(e){n()}),\"taurus.common\",\"stopWatchShake\",e):c===PLATFORM_TYPE_ENUM.IOS&&r.callHandler(\"taurus.common.stopWatchShake\",Object.assign({},e),(function(e){t()}))}else r&&r.call(\"stopWatchShake\",e,(function(e){invoker.removeContinuesEvent(\"shake\"),invoker.handleBridgeResponse(e,t,n)}))}invoker.registerAPI(\"stopWatchShake\",{mobile:stopWatchShakeHandler}),stopWatchShake.version={android:\"1.6.2\",ios:\"1.6.2\"};export default function stopWatchShake(){return invoker.invoke(\"stopWatchShake\")}", "import invoker,{PLATFORM_TYPE_ENUM}from\"./invoker\";invoker.registerAPI(\"subscribe\",{mobile:function(e,o){var n=o.resolve,s=o.reject,r=o.context,i=o.containerType,c=o.platformType,u=!1;if(i){c===PLATFORM_TYPE_ENUM.ANDROID?r&&r((function(o){u?(e.onSuccess||e.onFail)&&(\"0\"!==o.errorCode?e.onFail&&e.onFail(o):e.onSuccess&&e.onSuccess(o.result)):(u=!0,invoker.handleBridgeResponse(o,n,s))}),(function(o){u?e.onFail&&e.onFail(o):(u=!0,invoker.handleBridgeResponse(o,n,s))}),\"taurus.common\",\"subscribe\",e):c===PLATFORM_TYPE_ENUM.IOS&&r.callHandler(\"taurus.common.subscribe\",Object.assign({},e),(function(o){u?(e.onSuccess||e.onFail)&&(\"0\"!==o.errorCode?e.onFail&&e.onFail(o):e.onSuccess&&e.onSuccess(o.result)):(u=!0,invoker.handleBridgeResponse(o,n,s))}))}else r&&r.call(\"subscribe\",e,(function(o){u?(e.onSuccess||e.onFail)&&(\"0\"!==o.errorCode?e.onFail&&e.onFail(o):e.onSuccess&&e.onSuccess(o.result)):(u=!0,invoker.handleBridgeResponse(o,n,s))}))}}),subscribe.version={android:\"1.6.0\",ios:\"1.6.0\"};export default function subscribe(e){return invoker.invoke(\"subscribe\",e)}", "import invoker from\"./invoker\";invoker.registerAPI(\"takePhoto\",{mini:!0,mobile:!0}),takePhoto.version={android:\"1.3.5\",ios:\"1.3.5\"};export default function takePhoto(){return invoker.invoke(\"takePhoto\")}", "import invoker from\"./invoker\";invoker.registerAPI(\"testProxy\",{pc:function(t,o){void 0===t&&(t={}),window.dingtalk.platform.invokeAPI(o.msgId,\"net.util.testProxy\",t)}}),testProxy.version={pc:\"2.10.0\"};export default function testProxy(){return invoker.invoke(\"testProxy\",{})}", "import invoker,{BRIDGE_ERROR_CODE,PLATFORM_TYPE_ENUM,API_INVOKER_TYPE}from\"./invoker\";function toastHandler(e,o){var t=o.resolve,n=o.reject,r=o.context,a=o.containerType,i=o.platformType,s=o.appType,c={type:\"error\"===e.icon?\"fail\":\"success\"===e.icon?\"success\":\"none\",content:e.text,duration:1e3*e.duration,taurusToastStyle:e.taurusToastStyle};if(a){var u=function(){invoker.handleBridgeResponse({errorCode:BRIDGE_ERROR_CODE.SUCCESS,result:{}},t,n)},d=function(e){invoker.handleBridgeResponse(e,t,n)};s===API_INVOKER_TYPE.MINI_APP?r&&r({m:\"taurus.common.toast\",args:c,onSuccess:u,onFail:d}):i===PLATFORM_TYPE_ENUM.ANDROID?r&&r(u,d,\"taurus.common\",\"toast\",c):i===PLATFORM_TYPE_ENUM.IOS&&r.callHandler(\"taurus.common.toast\",Object.assign({},c),(function(){invoker.handleBridgeResponse({errorCode:BRIDGE_ERROR_CODE.SUCCESS,result:{}},t,n)}))}else r&&r.call(\"toast\",c,(function(){invoker.handleBridgeResponse({errorCode:BRIDGE_ERROR_CODE.SUCCESS,result:{}},t,n)}))}invoker.registerAPI(\"toast\",{mobile:toastHandler,mini:toastHandler,pc:function(e,o){var t=e.icon,n=e.text,r=e.duration,a=e.delay;window.dingtalk.platform.invokeAPI(o.msgId,\"device.notification.toast\",{type:t,text:n,duration:r,delay:a})}}),toast.version={android:\"1.3.2\",ios:\"1.3.2\"};export default function toast(e){return invoker.invoke(\"toast\",e)}", "import invoker from\"./invoker\";invoker.registerAPI(\"unlockWithSecurityVerification\",{mini:!0,mobile:!0}),unlockWithSecurityVerification.version={android:\"1.3.1.1\",ios:\"1.3.1.1\"};export default function unlockWithSecurityVerification(){return invoker.invoke(\"unlockWithSecurityVerification\")}", "import invoker from\"./invoker\";invoker.registerAPI(\"unsubscribe\",{mobile:!0}),unsubscribe.version={android:\"1.6.0\",ios:\"1.6.0\"};export default function unsubscribe(r){return invoker.invoke(\"unsubscribe\",r)}", "import _extends from\"@babel/runtime/helpers/extends\";import invoker from\"./invoker\";invoker.registerAPI(\"dgUploadFile\",{mini:!0,mobile:!0,pc:function(e,i){window.dingtalk.platform.invokeAPI(i.msgId,\"biz.util.dgUploadFile\",e)}}),uploadFile.version={android:\"1.3.2\",ios:\"1.3.2\",pc:\"1.3.6\"};export default function uploadFile(e){return invoker.invoke(\"dgUploadFile\",_extends({},e,{_apiName:\"uploadFile\"}))}", "import invoker from\"./invoker\";invoker.registerAPI(\"uploadFileByType\",{mini:!0,mobile:!0}),uploadFileByType.version={android:\"1.3.0\",ios:\"1.3.0\"};export default function uploadFileByType(e){return invoker.invoke(\"uploadFileByType\",e)}", "import _extends from\"@babel/runtime/helpers/extends\";import invoker from\"./invoker\";invoker.registerAPI(\"uploadFile\",{mini:!0}),uploadLocalFile.version={android:\"1.6.2\",ios:\"1.6.2\"};export default function uploadLocalFile(e){return new Promise((function(i,o){my.uploadFile(_extends({},e,{success:function(e){i(e)},fail:function(e){o(e)}}))}))}", "import invoker from\"./invoker\";invoker.registerAPI(\"uploadRemoteFileToDisk\",{mini:!0,mobile:!0,pc:function(o,i){window.dingtalk.platform.invokeAPI(i.msgId,\"biz.util.uploadRemoteFileToDisk\",o)}}),uploadRemoteFileToDisk.version={android:\"1.6.0\",ios:\"1.6.0\",pc:\"2.6.0\"};export default function uploadRemoteFileToDisk(o){return invoker.invoke(\"uploadRemoteFileToDisk\",o)}", "import invoker from\"./invoker\";invoker.registerAPI(\"ut\",{pc:function(i,t){window.dingtalk.platform.invokeAPI(t.msgId,\"biz.util.ut\",i)}}),ut.version={pc:\"1.3.10\"};export default function ut(i){return invoker.invoke(\"ut\",i)}", "import invoker from\"./invoker\";invoker.registerAPI(\"vibrate\",{mini:!0,mobile:!0}),vibrate.version={android:\"1.3.1\",ios:\"1.3.1\"};export default function vibrate(i){return invoker.invoke(\"vibrate\",i)}", "import invoker,{API_INVOKER_TYPE}from\"./invoker\";import{c2p}from\"./utils/transformReturn\";import alert from\"./alert\";import authConfig from\"./authConfig\";import bizContactDepartmentsPickerExternal from\"./bizContactDepartmentsPickerExternal\";import bizCustomContactChooseExternal from\"./bizCustomContactChooseExternal\";import bizCustomContactMultipleChooseExternal from\"./bizCustomContactMultipleChooseExternal\";import callPhone from\"./callPhone\";import canIUse from\"./canIUse\";import checkVPNAppInstalled from\"./checkVPNAppInstalled\";import checkVPNAppOnline from\"./checkVPNAppOnline\";import chooseContact from\"./chooseContact\";import chooseContactWithComplexPicker from\"./chooseContactWithComplexPicker\";import chooseDateRangeWithCalendar from\"./chooseDateRangeWithCalendar\";import chooseDayWithCalendar from\"./chooseDayWithCalendar\";import chooseDepartments from\"./chooseDepartments\";import chooseFile from\"./chooseFile\";import chooseHalfDayWithCalendar from\"./chooseHalfDayWithCalendar\";import chooseImage from\"./chooseImage\";import chooseInterconnectionChat from\"./chooseInterconnectionChat\";import chooseLocalImage from\"./chooseLocalImage\";import chooseSpaceDir from\"./chooseSpaceDir\";import chooseTimeWithCalendar from\"./chooseTimeWithCalendar\";import chooseVideo from\"./chooseVideo\";import closePage from\"./closePage\";import complexPickerAdmin from\"./complexPickerAdmin\";import confirm from\"./confirm\";import copyToClipboard from\"./copyToClipboard\";import createChatGroup from\"./createChatGroup\";import createDing from\"./createDing\";import createDingV2 from\"./createDingV2\";import createVideoConf from\"./createVideoConf\";import createVideoMeeting from\"./createVideoMeeting\";import dealWithBackAction from\"./dealWithBackAction\";import disableClosePage from\"./disableClosePage\";import disablePullToRefresh from\"./disablePullToRefresh\";import disableWebviewBounce from\"./disableWebviewBounce\";import downloadAudio from\"./downloadAudio\";import downloadFile from\"./downloadFile\";import enablePullToRefresh from\"./enablePullToRefresh\";import enableVpn from\"./enableVpn\";import enableWebviewBounce from\"./enableWebviewBounce\";import exclusiveInvoke from\"./exclusiveInvoke\";import faceComparison from\"./faceComparison\";import faceRecognition from\"./faceRecognition\";import getAppInstallStatus from\"./getAppInstallStatus\";import getAuthCode from\"./getAuthCode\";import getConfig from\"./getConfig\";import getContainerType from\"./getContainerType\";import getDeviceId from\"./getDeviceId\";import getFromClipboard from\"./getFromClipboard\";import getGeolocation from\"./getGeolocation\";import getGeolocationStatus from\"./getGeolocationStatus\";import getHotspotInfo from\"./getHotspotInfo\";import getLanguageSetting from\"./getLanguageSetting\";import getLoginUser from\"./getLoginUser\";import getNetworkType from\"./getNetworkType\";import getPhoneInfo from\"./getPhoneInfo\";import getProxyInfo from\"./getProxyInfo\";import getStorageItem from\"./getStorageItem\";import getTraceStatus from\"./getTraceStatus\";import getUUID from\"./getUUID\";import getUserAgent from\"./getUserAgent\";import getWaterMark from\"./getWaterMark\";import getWaterMarkConfigV2 from\"./getWaterMarkConfigV2\";import getWaterMarkV2 from\"./getWaterMarkV2\";import getWifiStatus from\"./getWifiStatus\";import getWorkbenchContext from\"./getWorkbenchContext\";import goBack from\"./goBack\";import hideLoading from\"./hideLoading\";import hideOptionMenu from\"./hideOptionMenu\";import hideTitleBar from\"./hideTitleBar\";import isDownloadFileExist from\"./isDownloadFileExist\";import joinScheduleConf from\"./joinScheduleConf\";import joinVideoConf from\"./joinVideoConf\";import joinVideoMeeting from\"./joinVideoMeeting\";import locateOnMap from\"./locateOnMap\";import on from\"./on\";import onAudioPlayEnd from\"./onAudioPlayEnd\";import onRecordAudioEnd from\"./onRecordAudioEnd\";import openApiInvoker from\"./openApiInvoker\";import openApp from\"./openApp\";import openBrowser from\"./openBrowser\";import openChat from\"./openChat\";import openDownloadFile from\"./openDownloadFile\";import openLink from\"./openLink\";import openPage from\"./openPage\";import openSchemeUrl from\"./openSchemeUrl\";import openSlidePanel from\"./openSlidePanel\";import openWatermarkCamera from\"./openWatermarkCamera\";import pauseAudio from\"./pauseAudio\";import pickChat from\"./pickChat\";import pickChatByCorpId from\"./pickChatByCorpId\";import pickGroupChat from\"./pickGroupChat\";import pickGroupConversation from\"./pickGroupConversation\";import playAudio from\"./playAudio\";import previewDoc from\"./previewDoc\";import previewImage from\"./previewImage\";import printFile from\"./printFile\";import printNativeLog from\"./printNativeLog\";import prompt from\"./prompt\";import pushWindow from\"./pushWindow\";import readImageToBase64 from\"./readImageToBase64\";import ready from\"./ready\";import reduceImageSize from\"./reduceImageSize\";import removeStorageItem from\"./removeStorageItem\";import replacePage from\"./replacePage\";import resetView from\"./resetView\";import resumeAudio from\"./resumeAudio\";import rotateView from\"./rotateView\";import scan from\"./scan\";import searchOnMap from\"./searchOnMap\";import sendOutData from\"./sendOutData\";import setLocalScreenShotPolicy from\"./setLocalScreenShotPolicy\";import setNavIcon from\"./setNavIcon\";import setNavLeftText from\"./setNavLeftText\";import setOptionMenu from\"./setOptionMenu\";import setProxyInfo from\"./setProxyInfo\";import setStorageItem from\"./setStorageItem\";import setTitle from\"./setTitle\";import shareFileToMessage from\"./shareFileToMessage\";import shareImageToMessage from\"./shareImageToMessage\";import shareToMessage from\"./shareToMessage\";import shootVideo from\"./shootVideo\";import showActionSheet from\"./showActionSheet\";import showCallMenu from\"./showCallMenu\";import showDatePicker from\"./showDatePicker\";import showDateTimePicker from\"./showDateTimePicker\";import showExtendModal from\"./showExtendModal\";import showHomeBottomTab from\"./showHomeBottomTab\";import showLoading from\"./showLoading\";import showModal from\"./showModal\";import showMultiSelect from\"./showMultiSelect\";import showOnMap from\"./showOnMap\";import showOptionMenu from\"./showOptionMenu\";import showPlainInputUponKeyboard from\"./showPlainInputUponKeyboard\";import showQuickCallMenu from\"./showQuickCallMenu\";import showSelect from\"./showSelect\";import showSignature from\"./showSignature\";import showSocialShare from\"./showSocialShare\";import showTimePicker from\"./showTimePicker\";import showTitleBar from\"./showTitleBar\";import startFaceRecognition from\"./startFaceRecognition\";import startGeolocation from\"./startGeolocation\";import startListenNetworkStatus from\"./startListenNetworkStatus\";import startRecordAudio from\"./startRecordAudio\";import startTraceReport from\"./startTraceReport\";import startVPNApp from\"./startVPNApp\";import startWatchShake from\"./startWatchShake\";import stopAudio from\"./stopAudio\";import stopGeolocation from\"./stopGeolocation\";import stopListenNetworkStatus from\"./stopListenNetworkStatus\";import stopPullToRefresh from\"./stopPullToRefresh\";import stopRecordAudio from\"./stopRecordAudio\";import stopTraceReport from\"./stopTraceReport\";import stopVPNApp from\"./stopVPNApp\";import stopWatchShake from\"./stopWatchShake\";import subscribe from\"./subscribe\";import takePhoto from\"./takePhoto\";import testProxy from\"./testProxy\";import toast from\"./toast\";import unlockWithSecurityVerification from\"./unlockWithSecurityVerification\";import unsubscribe from\"./unsubscribe\";import uploadFile from\"./uploadFile\";import uploadFileByType from\"./uploadFileByType\";import uploadLocalFile from\"./uploadLocalFile\";import uploadRemoteFileToDisk from\"./uploadRemoteFileToDisk\";import ut from\"./ut\";import version from\"./version\";import vibrate from\"./vibrate\";var dd={alert:alert,authConfig:authConfig,bizContactDepartmentsPickerExternal:bizContactDepartmentsPickerExternal,bizCustomContactChooseExternal:bizCustomContactChooseExternal,bizCustomContactMultipleChooseExternal:bizCustomContactMultipleChooseExternal,callPhone:callPhone,canIUse:canIUse,checkVPNAppInstalled:checkVPNAppInstalled,checkVPNAppOnline:checkVPNAppOnline,chooseContact:chooseContact,chooseContactWithComplexPicker:chooseContactWithComplexPicker,chooseDateRangeWithCalendar:chooseDateRangeWithCalendar,chooseDayWithCalendar:chooseDayWithCalendar,chooseDepartments:chooseDepartments,chooseFile:chooseFile,chooseHalfDayWithCalendar:chooseHalfDayWithCalendar,chooseImage:chooseImage,chooseInterconnectionChat:chooseInterconnectionChat,chooseLocalImage:chooseLocalImage,chooseSpaceDir:chooseSpaceDir,chooseTimeWithCalendar:chooseTimeWithCalendar,chooseVideo:chooseVideo,closePage:closePage,complexPickerAdmin:complexPickerAdmin,confirm:confirm,copyToClipboard:copyToClipboard,createChatGroup:createChatGroup,createDing:createDing,createDingV2:createDingV2,createVideoConf:createVideoConf,createVideoMeeting:createVideoMeeting,dealWithBackAction:dealWithBackAction,disableClosePage:disableClosePage,disablePullToRefresh:disablePullToRefresh,disableWebviewBounce:disableWebviewBounce,downloadAudio:downloadAudio,downloadFile:downloadFile,enablePullToRefresh:enablePullToRefresh,enableVpn:enableVpn,enableWebviewBounce:enableWebviewBounce,exclusiveInvoke:exclusiveInvoke,faceComparison:faceComparison,faceRecognition:faceRecognition,getAppInstallStatus:getAppInstallStatus,getAuthCode:getAuthCode,getConfig:getConfig,getContainerType:getContainerType,getDeviceId:getDeviceId,getFromClipboard:getFromClipboard,getGeolocation:getGeolocation,getGeolocationStatus:getGeolocationStatus,getHotspotInfo:getHotspotInfo,getLanguageSetting:getLanguageSetting,getLoginUser:getLoginUser,getNetworkType:getNetworkType,getPhoneInfo:getPhoneInfo,getProxyInfo:getProxyInfo,getStorageItem:getStorageItem,getTraceStatus:getTraceStatus,getUUID:getUUID,getUserAgent:getUserAgent,getWaterMark:getWaterMark,getWaterMarkConfigV2:getWaterMarkConfigV2,getWaterMarkV2:getWaterMarkV2,getWifiStatus:getWifiStatus,getWorkbenchContext:getWorkbenchContext,goBack:goBack,hideLoading:hideLoading,hideOptionMenu:hideOptionMenu,hideTitleBar:hideTitleBar,isDownloadFileExist:isDownloadFileExist,joinScheduleConf:joinScheduleConf,joinVideoConf:joinVideoConf,joinVideoMeeting:joinVideoMeeting,locateOnMap:locateOnMap,on:on,onAudioPlayEnd:onAudioPlayEnd,onRecordAudioEnd:onRecordAudioEnd,openApiInvoker:openApiInvoker,openApp:openApp,openBrowser:openBrowser,openChat:openChat,openDownloadFile:openDownloadFile,openLink:openLink,openPage:openPage,openSchemeUrl:openSchemeUrl,openSlidePanel:openSlidePanel,openWatermarkCamera:openWatermarkCamera,pauseAudio:pauseAudio,pickChat:pickChat,pickChatByCorpId:pickChatByCorpId,pickGroupChat:pickGroupChat,pickGroupConversation:pickGroupConversation,playAudio:playAudio,previewDoc:previewDoc,previewImage:previewImage,printFile:printFile,printNativeLog:printNativeLog,prompt:prompt,pushWindow:pushWindow,readImageToBase64:readImageToBase64,ready:ready,reduceImageSize:reduceImageSize,removeStorageItem:removeStorageItem,replacePage:replacePage,resetView:resetView,resumeAudio:resumeAudio,rotateView:rotateView,scan:scan,searchOnMap:searchOnMap,sendOutData:sendOutData,setLocalScreenShotPolicy:setLocalScreenShotPolicy,setNavIcon:setNavIcon,setNavLeftText:setNavLeftText,setOptionMenu:setOptionMenu,setProxyInfo:setProxyInfo,setStorageItem:setStorageItem,setTitle:setTitle,shareFileToMessage:shareFileToMessage,shareImageToMessage:shareImageToMessage,shareToMessage:shareToMessage,shootVideo:shootVideo,showActionSheet:showActionSheet,showCallMenu:showCallMenu,showDatePicker:showDatePicker,showDateTimePicker:showDateTimePicker,showExtendModal:showExtendModal,showHomeBottomTab:showHomeBottomTab,showLoading:showLoading,showModal:showModal,showMultiSelect:showMultiSelect,showOnMap:showOnMap,showOptionMenu:showOptionMenu,showPlainInputUponKeyboard:showPlainInputUponKeyboard,showQuickCallMenu:showQuickCallMenu,showSelect:showSelect,showSignature:showSignature,showSocialShare:showSocialShare,showTimePicker:showTimePicker,showTitleBar:showTitleBar,startFaceRecognition:startFaceRecognition,startGeolocation:startGeolocation,startListenNetworkStatus:startListenNetworkStatus,startRecordAudio:startRecordAudio,startTraceReport:startTraceReport,startVPNApp:startVPNApp,startWatchShake:startWatchShake,stopAudio:stopAudio,stopGeolocation:stopGeolocation,stopListenNetworkStatus:stopListenNetworkStatus,stopPullToRefresh:stopPullToRefresh,stopRecordAudio:stopRecordAudio,stopTraceReport:stopTraceReport,stopVPNApp:stopVPNApp,stopWatchShake:stopWatchShake,subscribe:subscribe,takePhoto:takePhoto,testProxy:testProxy,toast:toast,unlockWithSecurityVerification:unlockWithSecurityVerification,unsubscribe:unsubscribe,uploadFile:uploadFile,uploadFileByType:uploadFileByType,uploadLocalFile:uploadLocalFile,uploadRemoteFileToDisk:uploadRemoteFileToDisk,ut:ut,version:version,vibrate:vibrate};if(invoker.getAppType()===API_INVOKER_TYPE.MINI_APP)dd=new Proxy(dd,{get:function(o,e,t){return e in dd?Reflect.get(o,e,t):c2p(Reflect.get(my,e,t),e)}});else{window.dd&&console.warn(\"已经存在 window.dd 变量，引入 gdt-jsapi 会修改 window.dd 的值。\");try{Object.defineProperty(window,\"dd\",{value:dd,writable:!0})}catch(o){console.error(o)}window.gdt&&console.warn(\"已经存在 window.gdt 变量，引入 gdt-jsapi 会修改 window.gdt 的值。\");try{Object.defineProperty(window,\"gdt\",{value:dd,writable:!0})}catch(o){console.error(o)}}export default dd;"], "mappings": ";;;;;;AAAA;AAAA;AAAA,aAAS,eAAe,GAAG,GAAG;AAC5B,WAAK,IAAI,GAAG,KAAK,IAAI;AAAA,IACvB;AACA,WAAO,UAAU,gBAAgB,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO;AAAA;AAAA;;;ACHtG;AAAA;AAAA,aAAS,mBAAmB,GAAG,GAAG,GAAG,GAAG;AACtC,UAAI,IAAI,OAAO;AACf,UAAI;AACF,UAAE,CAAC,GAAG,IAAI,CAAC,CAAC;AAAA,MACd,SAASA,IAAP;AACA,YAAI;AAAA,MACN;AACA,aAAO,UAAU,qBAAqB,SAAS,kBAAkBA,IAAGC,IAAGC,IAAGC,IAAG;AAC3E,YAAIF;AAAG,cAAI,EAAED,IAAGC,IAAG;AAAA,YACjB,OAAOC;AAAA,YACP,YAAY,CAACC;AAAA,YACb,cAAc,CAACA;AAAA,YACf,UAAU,CAACA;AAAA,UACb,CAAC,IAAIH,GAAEC,MAAKC;AAAA,aAAO;AACjB,cAAI,IAAI,SAASE,GAAEH,IAAGC,IAAG;AACvB,+BAAmBF,IAAGC,IAAG,SAAUD,IAAG;AACpC,qBAAO,KAAK,QAAQC,IAAGC,IAAGF,EAAC;AAAA,YAC7B,CAAC;AAAA,UACH;AACA,YAAE,QAAQ,CAAC,GAAG,EAAE,SAAS,CAAC,GAAG,EAAE,UAAU,CAAC;AAAA,QAC5C;AAAA,MACF,GAAG,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO,SAAS,mBAAmB,GAAG,GAAG,GAAG,CAAC;AAAA,IAChH;AACA,WAAO,UAAU,oBAAoB,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO;AAAA;AAAA;;;ACvB1G;AAAA;AAAA,QAAI,oBAAoB;AACxB,aAAS,eAAe;AAEtB,UAAI,GACF,GACA,IAAI,cAAc,OAAO,SAAS,SAAS,CAAC,GAC5C,IAAI,EAAE,YAAY,cAClB,IAAI,EAAE,eAAe;AACvB,eAAS,EAAEK,IAAGC,IAAGC,IAAGC,IAAG;AACrB,YAAIC,KAAIH,MAAKA,GAAE,qBAAqB,YAAYA,KAAI,WAClDI,KAAI,OAAO,OAAOD,GAAE,SAAS;AAC/B,eAAO,kBAAkBC,IAAG,WAAW,SAAUL,IAAGC,IAAGC,IAAG;AACxD,cAAIC,IACFC,IACAC,IACAC,KAAI,GACJ,IAAIJ,MAAK,CAAC,GACV,IAAI,OACJ,IAAI;AAAA,YACF,GAAG;AAAA,YACH,GAAG;AAAA,YACH,GAAG;AAAA,YACH,GAAG;AAAA,YACH,GAAG,EAAE,KAAK,GAAG,CAAC;AAAA,YACd,GAAG,SAASK,GAAEC,IAAGR,IAAG;AAClB,qBAAOG,KAAIK,IAAGJ,KAAI,GAAGC,KAAI,GAAG,EAAE,IAAIL,IAAG;AAAA,YACvC;AAAA,UACF;AACF,mBAAS,EAAEA,IAAGC,IAAG;AACf,iBAAKG,KAAIJ,IAAGK,KAAIJ,IAAG,IAAI,GAAG,CAAC,KAAKK,MAAK,CAACJ,MAAK,IAAI,EAAE,QAAQ,KAAK;AAC5D,kBAAIA,IACFC,KAAI,EAAE,IACNI,KAAI,EAAE,GACN,IAAIJ,GAAE;AACR,cAAAH,KAAI,KAAKE,KAAI,MAAMD,QAAOI,KAAIF,IAAGC,KAAID,GAAE,MAAM,KAAKC,KAAI,GAAG,KAAKD,GAAE,KAAKA,GAAE,KAAK,KAAKA,GAAE,MAAMI,QAAOL,KAAIF,KAAI,KAAKO,KAAIJ,GAAE,OAAOC,KAAI,GAAG,EAAE,IAAIH,IAAG,EAAE,IAAIE,GAAE,MAAMI,KAAI,MAAML,KAAIF,KAAI,KAAKG,GAAE,KAAKF,MAAKA,KAAI,OAAOE,GAAE,KAAKH,IAAGG,GAAE,KAAKF,IAAG,EAAE,IAAI,GAAGG,KAAI;AAAA,YACzO;AACA,gBAAIF,MAAKF,KAAI;AAAG,qBAAO;AACvB,kBAAM,IAAI,MAAIC;AAAA,UAChB;AACA,iBAAO,SAAUC,IAAGO,IAAG,GAAG;AACxB,gBAAIH,KAAI;AAAG,oBAAM,UAAU,8BAA8B;AACzD,iBAAK,KAAK,MAAMG,MAAK,EAAEA,IAAG,CAAC,GAAGL,KAAIK,IAAGJ,KAAI,IAAI,IAAID,KAAI,IAAI,IAAIC,OAAM,CAAC,KAAI;AACtE,cAAAF,OAAMC,KAAIA,KAAI,KAAKA,KAAI,MAAM,EAAE,IAAI,KAAK,EAAEA,IAAGC,EAAC,KAAK,EAAE,IAAIA,KAAI,EAAE,IAAIA;AACnE,kBAAI;AACF,oBAAIC,KAAI,GAAGH,IAAG;AACZ,sBAAIC,OAAMF,KAAI,SAAS,IAAIC,GAAED,KAAI;AAC/B,wBAAI,EAAE,IAAI,EAAE,KAAKC,IAAGE,EAAC;AAAI,4BAAM,UAAU,kCAAkC;AAC3E,wBAAI,CAAC,EAAE;AAAM,6BAAO;AACpB,oBAAAA,KAAI,EAAE,OAAOD,KAAI,MAAMA,KAAI;AAAA,kBAC7B;AAAO,0BAAMA,OAAM,IAAID,GAAE,cAAc,EAAE,KAAKA,EAAC,GAAGC,KAAI,MAAMC,KAAI,UAAU,sCAAsCH,KAAI,UAAU,GAAGE,KAAI;AACrI,kBAAAD,KAAI;AAAA,gBACN,YAAY,KAAK,IAAI,EAAE,IAAI,KAAKE,KAAIL,GAAE,KAAKC,IAAG,CAAC,OAAO;AAAG;AAAA,cAC3D,SAASO,IAAP;AACA,gBAAAL,KAAI,GAAGC,KAAI,GAAGC,KAAIG;AAAA,cACpB,UAAE;AACA,gBAAAF,KAAI;AAAA,cACN;AAAA,YACF;AACA,mBAAO;AAAA,cACL,OAAO;AAAA,cACP,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF,EAAEN,IAAGE,IAAGC,EAAC,GAAG,IAAE,GAAGE;AAAA,MACnB;AACA,UAAI,IAAI,CAAC;AACT,eAAS,YAAY;AAAA,MAAC;AACtB,eAAS,oBAAoB;AAAA,MAAC;AAC9B,eAAS,6BAA6B;AAAA,MAAC;AACvC,UAAI,OAAO;AACX,UAAI,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,KAAK,kBAAkB,IAAI,CAAC,GAAG,GAAG,WAAY;AACtE,eAAO;AAAA,MACT,CAAC,GAAG,IACJA,KAAI,2BAA2B,YAAY,UAAU,YAAY,OAAO,OAAO,CAAC;AAClF,eAAS,EAAEK,IAAG;AACZ,eAAO,OAAO,iBAAiB,OAAO,eAAeA,IAAG,0BAA0B,KAAKA,GAAE,YAAY,4BAA4B,kBAAkBA,IAAG,GAAG,mBAAmB,IAAIA,GAAE,YAAY,OAAO,OAAOL,EAAC,GAAGK;AAAA,MAClN;AACA,aAAO,kBAAkB,YAAY,4BAA4B,kBAAkBL,IAAG,eAAe,0BAA0B,GAAG,kBAAkB,4BAA4B,eAAe,iBAAiB,GAAG,kBAAkB,cAAc,qBAAqB,kBAAkB,4BAA4B,GAAG,mBAAmB,GAAG,kBAAkBA,EAAC,GAAG,kBAAkBA,IAAG,GAAG,WAAW,GAAG,kBAAkBA,IAAG,GAAG,WAAY;AAC7a,eAAO;AAAA,MACT,CAAC,GAAG,kBAAkBA,IAAG,YAAY,WAAY;AAC/C,eAAO;AAAA,MACT,CAAC,IAAI,OAAO,UAAU,eAAe,SAASM,gBAAe;AAC3D,eAAO;AAAA,UACL,GAAG;AAAA,UACH,GAAG;AAAA,QACL;AAAA,MACF,GAAG,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO,SAAS;AAAA,IACnF;AACA,WAAO,UAAU,cAAc,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO;AAAA;AAAA;;;ACxFpG;AAAA;AAAA,QAAI,gBAAgB;AACpB,QAAI,oBAAoB;AACxB,aAAS,cAAc,GAAG,GAAG;AAC3B,eAAS,EAAEC,IAAG,GAAG,GAAG,GAAG;AACrB,YAAI;AACF,cAAI,IAAI,EAAEA,IAAG,CAAC,GACZC,KAAI,EAAE;AACR,iBAAOA,cAAa,gBAAgB,EAAE,QAAQA,GAAE,CAAC,EAAE,KAAK,SAAUC,IAAG;AACnE,cAAE,QAAQA,IAAG,GAAG,CAAC;AAAA,UACnB,GAAG,SAAUA,IAAG;AACd,cAAE,SAASA,IAAG,GAAG,CAAC;AAAA,UACpB,CAAC,IAAI,EAAE,QAAQD,EAAC,EAAE,KAAK,SAAUC,IAAG;AAClC,cAAE,QAAQA,IAAG,EAAE,CAAC;AAAA,UAClB,GAAG,SAAUA,IAAG;AACd,mBAAO,EAAE,SAASA,IAAG,GAAG,CAAC;AAAA,UAC3B,CAAC;AAAA,QACH,SAASA,IAAP;AACA,YAAEA,EAAC;AAAA,QACL;AAAA,MACF;AACA,UAAI;AACJ,WAAK,SAAS,kBAAkB,cAAc,SAAS,GAAG,kBAAkB,cAAc,WAAW,cAAc,OAAO,UAAU,OAAO,iBAAiB,kBAAkB,WAAY;AACxL,eAAO;AAAA,MACT,CAAC,IAAI,kBAAkB,MAAM,WAAW,SAAUA,IAAG,GAAG,GAAG;AACzD,iBAAS,IAAI;AACX,iBAAO,IAAI,EAAE,SAAUC,IAAGH,IAAG;AAC3B,cAAEE,IAAG,GAAGC,IAAGH,EAAC;AAAA,UACd,CAAC;AAAA,QACH;AACA,eAAO,IAAI,IAAI,EAAE,KAAK,GAAG,CAAC,IAAI,EAAE;AAAA,MAClC,GAAG,IAAE;AAAA,IACP;AACA,WAAO,UAAU,eAAe,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO;AAAA;AAAA;;;AChCrG;AAAA;AAAA,QAAI,cAAc;AAClB,QAAI,2BAA2B;AAC/B,aAAS,qBAAqB,GAAG,GAAG,GAAG,GAAG,GAAG;AAC3C,aAAO,IAAI,yBAAyB,YAAY,EAAE,EAAE,GAAG,GAAG,GAAG,CAAC,GAAG,KAAK,OAAO;AAAA,IAC/E;AACA,WAAO,UAAU,sBAAsB,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO;AAAA;AAAA;;;ACL5G;AAAA;AAAA,QAAI,sBAAsB;AAC1B,aAAS,kBAAkB,GAAG,GAAG,GAAG,GAAG,GAAG;AACxC,UAAI,IAAI,oBAAoB,GAAG,GAAG,GAAG,GAAG,CAAC;AACzC,aAAO,EAAE,KAAK,EAAE,KAAK,SAAUI,IAAG;AAChC,eAAOA,GAAE,OAAOA,GAAE,QAAQ,EAAE,KAAK;AAAA,MACnC,CAAC;AAAA,IACH;AACA,WAAO,UAAU,mBAAmB,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO;AAAA;AAAA;;;ACPzG;AAAA;AAAA,aAAS,iBAAiB,GAAG;AAC3B,UAAI,IAAI,OAAO,CAAC,GACd,IAAI,CAAC;AACP,eAAS,KAAK;AAAG,UAAE,QAAQ,CAAC;AAC5B,aAAO,SAASC,KAAI;AAClB,eAAO,EAAE;AAAS,eAAK,IAAI,EAAE,IAAI,MAAM;AAAG,mBAAOA,GAAE,QAAQ,GAAGA,GAAE,OAAO,OAAIA;AAC3E,eAAOA,GAAE,OAAO,MAAIA;AAAA,MACtB;AAAA,IACF;AACA,WAAO,UAAU,kBAAkB,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO;AAAA;AAAA;;;ACTxG;AAAA;AAAA,aAAS,QAAQ,GAAG;AAClB;AAEA,aAAO,OAAO,UAAU,UAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAUC,IAAG;AACjH,eAAO,OAAOA;AAAA,MAChB,IAAI,SAAUA,IAAG;AACf,eAAOA,MAAK,cAAc,OAAO,UAAUA,GAAE,gBAAgB,UAAUA,OAAM,OAAO,YAAY,WAAW,OAAOA;AAAA,MACpH,GAAG,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO,SAAS,QAAQ,CAAC;AAAA,IAC5F;AACA,WAAO,UAAU,SAAS,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO;AAAA;AAAA;;;ACT/F;AAAA;AAAA,QAAI,UAAU,iBAAuB;AACrC,aAAS,mBAAmB,GAAG;AAC7B,UAAI,QAAQ,GAAG;AACb,YAAI,IAAI,EAAE,cAAc,OAAO,UAAU,OAAO,YAAY,eAC1D,IAAI;AACN,YAAI;AAAG,iBAAO,EAAE,KAAK,CAAC;AACtB,YAAI,cAAc,OAAO,EAAE;AAAM,iBAAO;AACxC,YAAI,CAAC,MAAM,EAAE,MAAM;AAAG,iBAAO;AAAA,YAC3B,MAAM,SAAS,OAAO;AACpB,qBAAO,KAAK,KAAK,EAAE,WAAW,IAAI,SAAS;AAAA,gBACzC,OAAO,KAAK,EAAE;AAAA,gBACd,MAAM,CAAC;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA,MACF;AACA,YAAM,IAAI,UAAU,QAAQ,CAAC,IAAI,kBAAkB;AAAA,IACrD;AACA,WAAO,UAAU,oBAAoB,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO;AAAA;AAAA;;;AClB1G;AAAA;AAAA,QAAI,gBAAgB;AACpB,QAAI,cAAc;AAClB,QAAI,mBAAmB;AACvB,QAAI,sBAAsB;AAC1B,QAAI,2BAA2B;AAC/B,QAAI,kBAAkB;AACtB,QAAI,oBAAoB;AACxB,aAASC,uBAAsB;AAC7B;AAEA,UAAI,IAAI,YAAY,GAClB,IAAI,EAAE,EAAEA,oBAAmB,GAC3B,KAAK,OAAO,iBAAiB,OAAO,eAAe,CAAC,IAAI,EAAE,WAAW;AACvE,eAAS,EAAEC,IAAG;AACZ,YAAIC,KAAI,cAAc,OAAOD,MAAKA,GAAE;AACpC,eAAO,CAAC,CAACC,OAAMA,OAAM,KAAK,yBAAyBA,GAAE,eAAeA,GAAE;AAAA,MACxE;AACA,UAAI,IAAI;AAAA,QACN,SAAS;AAAA,QACT,UAAU;AAAA,QACV,SAAS;AAAA,QACT,YAAY;AAAA,MACd;AACA,eAAS,EAAED,IAAG;AACZ,YAAIC,IAAGC;AACP,eAAO,SAAUC,IAAG;AAClB,UAAAF,OAAMA,KAAI;AAAA,YACR,MAAM,SAAS,OAAO;AACpB,qBAAOC,GAAEC,GAAE,GAAG,CAAC;AAAA,YACjB;AAAA,YACA,SAAS,SAAS,SAAS;AACzB,qBAAOA,GAAE;AAAA,YACX;AAAA,YACA,QAAQ,SAAS,OAAOH,IAAGC,IAAG;AAC5B,qBAAOC,GAAEC,GAAE,GAAG,EAAEH,KAAIC,EAAC;AAAA,YACvB;AAAA,YACA,eAAe,SAAS,cAAcD,IAAGI,IAAGC,IAAG;AAC7C,qBAAOJ,GAAE,aAAaG,IAAGF,GAAEC,GAAE,GAAG,kBAAkBH,EAAC,GAAGK,EAAC;AAAA,YACzD;AAAA,YACA,QAAQ,SAAS,OAAOL,IAAG;AACzB,qBAAOE,GAAEC,GAAE,GAAGH,EAAC;AAAA,YACjB;AAAA,UACF,GAAGE,KAAI,SAASA,GAAEF,IAAG,IAAII,IAAG;AAC1B,YAAAD,GAAE,IAAIF,GAAE,MAAME,GAAE,IAAIF,GAAE;AACtB,gBAAI;AACF,qBAAOD,GAAE,IAAII,EAAC;AAAA,YAChB,UAAE;AACA,cAAAH,GAAE,OAAOE,GAAE;AAAA,YACb;AAAA,UACF,IAAIF,GAAE,eAAeA,GAAEA,GAAE,cAAcE,GAAE,GAAGF,GAAE,aAAa,SAASA,GAAE,OAAOE,GAAE,GAAGF,GAAE,OAAOE,GAAE;AAC7F,cAAI;AACF,mBAAOH,GAAE,KAAK,MAAMC,EAAC;AAAA,UACvB,UAAE;AACA,YAAAE,GAAE,IAAIF,GAAE,MAAME,GAAE,IAAIF,GAAE;AAAA,UACxB;AAAA,QACF;AAAA,MACF;AACA,cAAQ,OAAO,UAAUF,uBAAsB,SAASA,uBAAsB;AAC5E,eAAO;AAAA,UACL,MAAM,SAAS,KAAKE,IAAGC,IAAGC,IAAGC,IAAG;AAC9B,mBAAO,EAAE,EAAE,EAAEH,EAAC,GAAGC,IAAGC,IAAGC,MAAKA,GAAE,QAAQ,CAAC;AAAA,UACzC;AAAA,UACA,qBAAqB;AAAA,UACrB,MAAM,EAAE;AAAA,UACR,OAAO,SAAS,MAAMJ,IAAGC,IAAG;AAC1B,mBAAO,IAAI,cAAcD,IAAGC,EAAC;AAAA,UAC/B;AAAA,UACA,eAAe;AAAA,UACf,OAAO,SAAS,MAAMD,IAAGC,IAAGC,IAAGE,IAAGE,IAAG;AACnC,oBAAQ,EAAEL,EAAC,IAAI,sBAAsB,kBAAkB,EAAED,EAAC,GAAGC,IAAGC,IAAGE,IAAGE,EAAC;AAAA,UACzE;AAAA,UACA,MAAM;AAAA,UACN,QAAQ;AAAA,QACV;AAAA,MACF,GAAG,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO,SAAS;AAAA,IACnF;AACA,WAAO,UAAUP,sBAAqB,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,aAAa,OAAO;AAAA;AAAA;;;AC5E3G,IAAAQ,uBAAA;AAAA;AAEA,QAAI,UAAU,6BAAyC;AACvD,WAAO,UAAU;AAGjB,QAAI;AACF,2BAAqB;AAAA,IACvB,SAAS,sBAAP;AACA,UAAI,OAAO,eAAe,UAAU;AAClC,mBAAW,qBAAqB;AAAA,MAClC,OAAO;AACL,iBAAS,KAAK,wBAAwB,EAAE,OAAO;AAAA,MACjD;AAAA,IACF;AAAA;AAAA;;;ACdA,SAAS,WAAW;AAClB,SAAO,WAAW,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,GAAG;AACpE,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,IAAI,UAAU;AAClB,eAAS,KAAK;AAAG,SAAC,CAAC,GAAG,eAAe,KAAK,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE;AAAA,IAC/D;AACA,WAAO;AAAA,EACT,GAAG,SAAS,MAAM,MAAM,SAAS;AACnC;;;ACRA,SAAS,mBAAmB,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAC/C,MAAI;AACF,QAAI,IAAI,EAAE,GAAG,CAAC,GACZC,KAAI,EAAE;AAAA,EACV,SAASC,IAAP;AACA,WAAO,KAAK,EAAEA,EAAC;AAAA,EACjB;AACA,IAAE,OAAO,EAAED,EAAC,IAAI,QAAQ,QAAQA,EAAC,EAAE,KAAK,GAAG,CAAC;AAC9C;AACA,SAAS,kBAAkB,GAAG;AAC5B,SAAO,WAAY;AACjB,QAAI,IAAI,MACN,IAAI;AACN,WAAO,IAAI,QAAQ,SAAU,GAAG,GAAG;AACjC,UAAI,IAAI,EAAE,MAAM,GAAG,CAAC;AACpB,eAAS,MAAMC,IAAG;AAChB,2BAAmB,GAAG,GAAG,GAAG,OAAO,QAAQ,QAAQA,EAAC;AAAA,MACtD;AACA,eAAS,OAAOA,IAAG;AACjB,2BAAmB,GAAG,GAAG,GAAG,OAAO,QAAQ,SAASA,EAAC;AAAA,MACvD;AACA,YAAM,MAAM;AAAA,IACd,CAAC;AAAA,EACH;AACF;;;ACxB4H,yBAA+B;;;ACApJ,SAAS,QAAQ,GAAE;AAAC,SAAM,gBAAc;AAAC;AAAQ,SAAS,YAAW;AAAC,SAAM,CAAC,QAAQ,OAAO,EAAE,KAAG,SAAO,MAAI,CAAC,QAAQ,OAAO,GAAG,KAAK;AAAC;;;ADAyF,IAAI;AAAkB,CAAC,SAAS,GAAE;AAAC,IAAE,SAAO,MAAK,EAAE,UAAQ,KAAI,EAAE,gBAAc,KAAI,EAAE,iBAAe,KAAI,EAAE,gBAAc,KAAI,EAAE,oBAAkB,KAAI,EAAE,gBAAc,KAAI,EAAE,qBAAmB,KAAI,EAAE,mBAAiB,KAAI,EAAE,kBAAgB,KAAI,EAAE,wBAAsB,KAAI,EAAE,oBAAkB,MAAK,EAAE,cAAY,MAAK,EAAE,eAAa,MAAK,EAAE,kBAAgB,MAAK,EAAE,gBAAc,MAAK,EAAE,qCAAmC,MAAK,EAAE,4CAA0C,MAAK,EAAE,8BAA4B,MAAK,EAAE,sBAAoB,MAAK,EAAE,kCAAgC,MAAK,EAAE,gBAAc;AAAI,EAAE,sBAAoB,oBAAkB,CAAC,EAAE;AAAS,IAAI;AAAiB,CAAC,SAAS,GAAE;AAAC,IAAE,SAAO,UAAS,EAAE,KAAG,MAAK,EAAE,WAAS,QAAO,EAAE,UAAQ;AAAS,EAAE,qBAAmB,mBAAiB,CAAC,EAAE;AAAS,IAAI;AAAmB,CAAC,SAAS,GAAE;AAAC,IAAE,UAAQ,WAAU,EAAE,MAAI,OAAM,EAAE,SAAO;AAAQ,EAAE,uBAAqB,qBAAmB,CAAC,EAAE;AAAS,IAAI;AAAsB,CAAC,SAAS,GAAE;AAAC,IAAE,wBAAsB,mCAAkC,EAAE,kBAAgB,+BAA8B,EAAE,eAAa,wBAAuB,EAAE,WAAS;AAAS,EAAE,0BAAwB,wBAAsB,CAAC,EAAE;AAAS,IAAI;AAAoB,CAAC,SAAS,GAAE;AAAC,IAAE,aAAW,YAAW,EAAE,UAAQ,SAAQ,EAAE,WAAS;AAAQ,EAAE,wBAAsB,sBAAoB,CAAC,EAAE;AAAE,IAAI,KAAG,cAAY,UAAU,eAAa,UAAU,cAAY;AAAhE,IAAmE,UAAQ,WAAU;AAAC,WAAS,IAAG;AAAC,SAAK,eAAa,CAAC,GAAE,KAAK,4BAA0B,CAAC,GAAE,KAAK,UAAQ,CAAC,GAAE,KAAK,0BAAwB,CAAC,GAAE,KAAK,aAAW,MAAK,KAAK,UAAQ,MAAK,KAAK,eAAa,MAAK,KAAK,YAAU,UAAQ,OAAO,aAAW,OAAO,gBAAe,KAAK,UAAQ,OAAG,KAAK,KAAK,GAAE,QAAQ,KAAK,6IAAyC;AAAA,EAAC;AAAC,MAAI,IAAE,EAAE;AAAU,SAAO,EAAE,sBAAoB,WAAU;AAAC,QAAIC,KAAE;AAAK,SAAK,uBAAqB,IAAI,QAAS,SAASC,IAAE,GAAE;AAAC,UAAI,IAAE,WAAU;AAAC,YAAG;AAAC,iBAAO,iCAA+B,OAAO,QAAM,OAAO,KAAK,QAAQ,GAAED,GAAE,YAAY;AAAA,QAAC,SAAOA,IAAN;AAAA,QAAS;AAAA,MAAC;AAAE,aAAO,SAAO,WAAS,OAAO,KAAK,WAAS,OAAO,KAAK,WAAS,EAAE,KAAG,SAAS,iBAAiB,gBAAgB,WAAU;AAAC,UAAE;AAAA,MAAC,GAAG,KAAE,GAAE,SAAS,iBAAiB,iBAAiB,SAASE,IAAE;AAAC,YAAI,IAAEA,MAAGA,GAAE,UAAQ,EAAC,WAAU,kBAAkB,gBAAe,cAAa,iCAAgC;AAAE,QAAAF,GAAE,qBAAqB,GAAEC,IAAE,CAAC;AAAA,MAAC,GAAG,KAAE;AAAA,IAAE,CAAE;AAAA,EAAC,GAAE,EAAE,kBAAgB,WAAU;AAAC,QAAID,KAAE;AAAK,SAAK,uBAAqB,IAAI,QAAS,SAASC,IAAE,GAAE;AAAC,UAAG,eAAa,OAAO;AAAwB,YAAG;AAAC,kCAAwB,KAAM,SAASD,IAAEC,IAAE;AAAA,UAAC,CAAE,GAAED,GAAE,YAAY;AAAA,QAAC,SAAOA,IAAN;AAAA,QAAS;AAAA;AAAM,iBAAS,iBAAiB,gCAAgC,WAAU;AAAC,cAAG;AAAC,uCAAyB,wBAAwB,KAAM,SAASA,IAAEC,IAAE;AAAA,YAAC,CAAE,GAAED,GAAE,YAAY;AAAA,UAAC,SAAOA,IAAN;AAAA,UAAS;AAAA,QAAC,GAAG,KAAE;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,OAAK,WAAU;AAAC,QAAIA,KAAE,MAAKC,KAAE,KAAK,WAAW,GAAE,IAAE,KAAK,iBAAiB;AAAE,QAAGA,OAAI,iBAAiB,MAAI,OAAO,YAAU,CAAC,OAAO,SAAS,eAAa,OAAO,SAAS,aAAW,MAAG,OAAO,SAAS,gBAAc,CAAC,GAAE,OAAO,SAAS,MAAM,SAAU,SAASA,IAAEE,IAAE;AAAC,UAAGH,GAAE,wBAAwBC;AAAG,QAAAD,GAAE,wBAAwBC,IAAGE,EAAC;AAAA,eAAUA,IAAE;AAAC,YAAID,KAAE,KAAGC,GAAE;AAAM,iCAAuBF,MAAG,QAAQ,IAAI,2BAA0BE,IAAE,gBAAeD,EAAC,GAAE,OAAO,SAAS,cAAcA,QAAK,OAAO,SAAS,cAAcA,IAAGC,EAAC,GAAE,OAAO,OAAO,SAAS,cAAcD,QAAK,6BAA2BD,MAAG,6BAA2BA,MAAGE,GAAE,SAAOA,GAAE,WAAS,cAAY,OAAOH,GAAE,wBAAwBG,GAAE,SAAOA,GAAE,WAASH,GAAE,wBAAwBG,GAAE,SAAOA,GAAE,QAAQF,IAAEE,EAAC,IAAEH,GAAE,0BAA0BC,OAAID,GAAE,0BAA0BC,IAAG,QAAS,SAASA,IAAE;AAAC,UAAAA,GAAE,KAAKD,IAAEG,EAAC;AAAA,QAAC,CAAE;AAAA,MAAC;AAAA,IAAC,CAAE,IAAGF,OAAI,iBAAiB,QAAO;AAAC,UAAG,MAAI,oBAAoB;AAAW,aAAK,iBAAe,mBAAmB,UAAQ,CAAC,KAAK,wBAAsB,KAAK,oBAAoB,IAAE,KAAK,iBAAe,mBAAmB,OAAK,CAAC,KAAK,wBAAsB,KAAK,gBAAgB;AAAA,eAAU,MAAI,oBAAoB,WAASA,OAAI,iBAAiB;AAAO,YAAG,OAAO;AAAe,eAAK,YAAY;AAAA,aAAM;AAAC,cAAI,IAAE,WAAY,WAAU;AAAC,oBAAQ,KAAK,wGAAsCD,GAAE,SAAQ,OAAO,cAAc,GAAEA,GAAE,WAASA,GAAE,YAAY,KAAKA,EAAC;AAAA,UAAC,GAAG,IAAI;AAAE,mBAAS,iBAAiB,uBAAuB,WAAU;AAAC,YAAAA,GAAE,YAAU,aAAa,CAAC,GAAEA,GAAE,YAAY,KAAKA,EAAC;AAAA,UAAE,GAAG,KAAE;AAAA,QAAC;AAAA,IAAC;AAAM,iBAAY,WAAU;AAAC,QAAAA,GAAE,YAAY;AAAA,MAAC,CAAE;AAAA,EAAC,GAAE,EAAE,cAAY,WAAU;AAAC,SAAK,UAAQ;AAAG,aAAQA,KAAE,KAAK,aAAa,MAAM,GAAEA;AAAG,MAAAA,MAAGA,GAAE,IAAI,GAAEA,KAAE,KAAK,aAAa,MAAM;AAAA,EAAC,GAAE,EAAE,UAAQ,SAASA,IAAE;AAAC,SAAK,UAAQA,MAAGA,GAAE,IAAI,IAAE,KAAK,aAAa,KAAKA,EAAC;AAAA,EAAC,GAAE,EAAE,oBAAkB,SAASA,IAAE;AAAC,SAAK,iBAAeA;AAAA,EAAC,GAAE,EAAE,oBAAkB,WAAU;AAAC,WAAO,KAAK;AAAA,EAAc,GAAE,EAAE,YAAU,WAAU;AAAC,WAAO,KAAK;AAAA,EAAS,GAAE,EAAE,mBAAiB,WAAU;AAAC,WAAM,aAAa,KAAK,EAAE,IAAE,YAAY,KAAK,EAAE,IAAE,oBAAoB,aAAW,oBAAoB,UAAQ,YAAY,KAAK,EAAE,IAAE,oBAAoB,aAAW,eAAe,KAAK,EAAE,KAAG,UAAU,KAAK,EAAE,IAAE,oBAAoB,UAAQ,oBAAoB;AAAA,EAAQ,GAAE,EAAE,aAAW,WAAU;AAAC,WAAO,KAAK,YAAU,KAAK,SAAS,IAAE,KAAK,UAAQ,iBAAiB,SAAO,UAAQ,OAAO,aAAW,OAAO,UAAU,aAAW,OAAO,UAAU,UAAU,QAAQ,cAAc,KAAG,KAAG,OAAO,UAAU,UAAU,QAAQ,WAAW,KAAG,IAAE,KAAK,UAAQ,iBAAiB,KAAG,UAAU,IAAE,KAAK,UAAQ,iBAAiB,YAAU,QAAQ,KAAK,0KAAmC,GAAE,KAAK,UAAQ,iBAAiB,WAAU,KAAK;AAAA,EAAO,GAAE,EAAE,WAAS,WAAU;AAAC,QAAIA,KAAE,wBAAwB,KAAK,EAAE,GAAEC,KAAE,WAAW,KAAK,EAAE,GAAE,IAAE,UAAQ,OAAO,aAAW,OAAO,UAAU,aAAW;AAAG,WAAO,SAAO,KAAK,aAAW,KAAK,aAAW,KAAG,EAAE,QAAQ,cAAc,KAAG,KAAG,KAAK,aAAW,OAAG,SAAI,EAAE,CAAC,KAAG,EAAE,EAAE,SAAS,aAAa,KAAG,EAAE,SAAS,QAAQ,KAAG,EAAE,SAAS,UAAU,QAAM,KAAK,aAAW,MAAG,KAAK,eAAaD,KAAE,mBAAmB,MAAIC,KAAE,mBAAmB,UAAQ,mBAAmB,QAAO;AAAA,EAAG,GAAE,EAAE,gBAAc,SAASD,IAAEC,IAAE;AAAC,QAAI,IAAE;AAAK,QAAG,cAAY,OAAOA;AAAE,aAAO,KAAK,WAAW,MAAI,iBAAiB,MAAI,KAAK,0BAA0BD,QAAK,KAAK,0BAA0BA,MAAG,CAAC,IAAG,KAAK,0BAA0BA,IAAG,KAAKC,EAAC,GAAE,WAAU;AAAC,YAAI,IAAE,EAAE,0BAA0BD,IAAG,UAAW,SAASA,IAAE;AAAC,iBAAOA,OAAIC;AAAA,QAAC,CAAE;AAAE,UAAE,0BAA0BD,IAAG,OAAO,GAAE,CAAC;AAAA,MAAC,KAAG,KAAK,WAAW,MAAI,iBAAiB,UAAQ,SAAS,iBAAiBA,IAAEC,IAAE,KAAE,GAAE,WAAU;AAAC,iBAAS,oBAAoBD,IAAEC,EAAC;AAAA,MAAC,KAAG;AAAO,YAAQ,MAAM,qDAAkB;AAAA,EAAC,GAAE,EAAE,oBAAkB,SAASD,IAAEC,IAAE;AAAC,SAAK,QAAQD,MAAGC;AAAA,EAAC,GAAE,EAAE,cAAY,SAASD,IAAEC,IAAE;AAAC,SAAK,SAAS;AAAE,QAAG,YAAU,OAAOA,IAAE;AAAC,UAAI,IAAEA,IAAE,IAAE,KAAK,WAAW;AAAE,WAAK,kBAAkBD,IAAE,EAAE,EAAE;AAAA,IAAC;AAAM,WAAK,kBAAkBA,IAAEC,EAAC;AAAA,EAAC,GAAE,EAAE,gBAAc,WAAU;AAAC,QAAID,KAAE,kBAAkB,mBAAAI,QAAoB,KAAM,SAASJ,GAAEC,IAAE,GAAE;AAAC,UAAI,IAAE;AAAK,aAAO,mBAAAG,QAAoB,KAAM,SAASJ,IAAE;AAAC;AAAO,kBAAOA,GAAE,OAAKA,GAAE,MAAK;AAAA,YAAC,KAAK;AAAE,qBAAO,WAAS,MAAI,IAAE,CAAC,IAAGA,GAAE,OAAO,UAAS,IAAI,QAAS,SAASA,IAAE,GAAE;AAAC,oBAAE,SAAS,EAAC,UAASC,GAAC,GAAE,CAAC;AAAE,oBAAI,IAAE,EAAE,QAAQA,KAAG,IAAE,EAAE,iBAAiB;AAAE,oBAAG,CAAC;AAAE,yBAAO,QAAQ,KAAK,UAAQA,KAAE,0BAAM,GAAE,EAAE,UAAQA,KAAE,0BAAM;AAAE,oBAAG,MAAI,oBAAoB,SAAQ;AAAC,sBAAG,cAAY,OAAO;AAAE,2BAAO,KAAK,EAAE,KAAK,MAAK,GAAE,EAAC,SAAQ,IAAG,SAAQD,IAAE,QAAO,GAAE,YAAWC,GAAC,CAAC;AAAE,qBAAG,KAAKA,IAAE,GAAG,SAASA,IAAE;AAAC,sBAAE,qBAAqBA,IAAED,IAAE,CAAC;AAAA,kBAAC,CAAE;AAAA,gBAAC,WAAS,MAAI,oBAAoB,YAAW;AAAC,sBAAG,cAAY,OAAO;AAAE,2BAAO,KAAK,EAAE,KAAK,MAAK,GAAE,EAAC,SAAQ,GAAG,UAAS,SAAQA,IAAE,QAAO,GAAE,YAAWC,IAAE,eAAc,GAAE,SAAQ,iBAAiB,SAAQ,CAAC;AAAE,qBAAG,SAAS,EAAC,GAAE,mBAAiBA,IAAE,MAAK,GAAE,WAAU,SAASA,IAAE;AAAC,sBAAE,qBAAqBA,IAAED,IAAE,CAAC;AAAA,kBAAC,GAAE,QAAO,SAASC,IAAE;AAAC,sBAAE,qBAAqBA,IAAED,IAAE,CAAC;AAAA,kBAAC,EAAC,CAAC;AAAA,gBAAC;AAAA,cAAC,CAAE,CAAC;AAAA,YAAE,KAAK;AAAA,YAAE,KAAI;AAAM,qBAAOA,GAAE,KAAK;AAAA,UAAC;AAAA,MAAC,GAAGA,EAAC;AAAA,IAAC,CAAE,CAAC;AAAE,WAAO,SAASC,IAAE,GAAE;AAAC,aAAOD,GAAE,MAAM,MAAK,SAAS;AAAA,IAAC;AAAA,EAAC,EAAE,GAAE,EAAE,eAAa,WAAU;AAAC,QAAIA,KAAE,kBAAkB,mBAAAI,QAAoB,KAAM,SAASJ,GAAEC,IAAE,GAAE,GAAE;AAAC,UAAI,IAAE;AAAK,aAAO,mBAAAG,QAAoB,KAAM,SAASJ,IAAE;AAAC;AAAO,kBAAOA,GAAE,OAAKA,GAAE,MAAK;AAAA,YAAC,KAAK;AAAE,qBAAO,WAAS,MAAI,IAAE,CAAC,IAAGA,GAAE,OAAO,UAAS,IAAI,QAAS,SAASA,IAAE,GAAE;AAAC,oBAAE,SAAS,EAAC,UAASC,GAAC,GAAE,CAAC;AAAE,oBAAI,IAAE,EAAE,QAAQA,KAAG,IAAE,EAAE,iBAAiB;AAAE,oBAAG,CAAC;AAAE,yBAAO,QAAQ,KAAK,UAAQA,KAAE,0BAAM,GAAE,EAAE,UAAQA,KAAE,0BAAM;AAAE,oBAAG,MAAI,oBAAoB,YAAW;AAAC,sBAAG,EAAE,iBAAe,mBAAmB,KAAI;AAAC,wBAAI,IAAE,OAAO,OAAO,CAAC,GAAE,CAAC;AAAE,wBAAG,SAAK,EAAE,SAAO,eAAa,OAAO,2BAAyB,wBAAwB,gBAAgB,QAAM,KAAG,EAAE,kBAAgB,QAAM,IAAE,SAAO,EAAE,kBAAgB,mBAAiBA,IAAG,SAASD,IAAEC,IAAE;AAAC,oCAAY,OAAO,EAAE,aAAW,EAAE,UAAU,KAAK,MAAKD,EAAC,GAAEC,MAAGA,GAAE,EAAC,WAAU,KAAI,cAAa,UAAS,CAAC;AAAA,oBAAC,CAAE,GAAE,cAAY,OAAO;AAAE,6BAAO,KAAK,EAAE,KAAK,MAAK,GAAE,EAAC,SAAQ,OAAO,yBAAwB,SAAQD,IAAE,QAAO,GAAE,YAAWC,IAAE,eAAc,GAAE,SAAQ,iBAAiB,QAAO,cAAa,mBAAmB,KAAI,OAAM,EAAE,MAAK,CAAC;AAAE,+BAAS,OAAO,2BAAyB,OAAO,wBAAwB,YAAY,mBAAiBA,IAAE,OAAO,OAAO,CAAC,GAAE,CAAC,GAAG,SAASA,IAAE;AAAC,uBAAC,EAAE,SAAO,EAAE,qBAAqBA,MAAG,CAAC,GAAED,IAAE,CAAC;AAAA,oBAAC,CAAE;AAAA,kBAAC,WAAS,EAAE,iBAAe,mBAAmB,SAAQ;AAAC,wBAAIK,KAAEJ,GAAE,MAAM,GAAG,GAAE,IAAEI,GAAE,IAAI,KAAG,IAAG,IAAEA,GAAE,KAAK,GAAG,KAAG;AAAgB,wBAAG,cAAY,OAAO;AAAE,6BAAO,KAAK,EAAE,KAAK,MAAK,GAAE,EAAC,SAAQ,OAAO,gCAA+B,SAAQL,IAAE,QAAO,GAAE,YAAWC,IAAE,eAAc,GAAE,SAAQ,iBAAiB,QAAO,cAAa,mBAAmB,QAAO,CAAC;AAAE,kCAAY,OAAO,OAAO,kCAAgC,OAAO,+BAAgC,SAASA,IAAE;AAAC,wBAAE,qBAAqBA,IAAED,IAAE,CAAC;AAAA,oBAAC,GAAI,SAASC,IAAE;AAAC,wBAAE,qBAAqBA,IAAED,IAAE,CAAC;AAAA,oBAAC,GAAG,GAAE,GAAE,CAAC;AAAA,kBAAC;AAAA,gBAAC,WAAS,MAAI,oBAAoB,SAAQ;AAAC,sBAAG,cAAY,OAAO;AAAE,2BAAO,KAAK,EAAE,KAAK,MAAK,GAAE,EAAC,SAAQ,gBAAe,SAAQA,IAAE,QAAO,GAAE,YAAWC,GAAC,CAAC;AAAE,iCAAe,KAAKA,IAAE,GAAG,SAASA,IAAE;AAAC,sBAAE,qBAAqBA,IAAED,IAAE,CAAC;AAAA,kBAAC,CAAE;AAAA,gBAAC;AAAA,cAAC,CAAE,CAAC;AAAA,YAAE,KAAK;AAAA,YAAE,KAAI;AAAM,qBAAOA,GAAE,KAAK;AAAA,UAAC;AAAA,MAAC,GAAGA,EAAC;AAAA,IAAC,CAAE,CAAC;AAAE,WAAO,SAASC,IAAE,GAAE,GAAE;AAAC,aAAOD,GAAE,MAAM,MAAK,SAAS;AAAA,IAAC;AAAA,EAAC,EAAE,GAAE,EAAE,eAAa,SAASA,IAAE;AAAC,QAAIC,IAAE;AAAE,WAAO,UAAQA,KAAE,OAAO,aAAW,WAASA,MAAG,UAAQ,IAAEA,GAAE,kBAAgB,WAAS,KAAG,EAAED,MAAG,KAAK,aAAaA,KAAE,CAAC,IAAEA;AAAA,EAAC,GAAE,EAAE,WAAS,WAAU;AAAC,QAAIA,KAAE,kBAAkB,mBAAAI,QAAoB,KAAM,SAASJ,GAAEC,IAAE,GAAE,GAAE;AAAC,UAAI,IAAE;AAAK,aAAO,mBAAAG,QAAoB,KAAM,SAASJ,IAAE;AAAC;AAAO,kBAAOA,GAAE,OAAKA,GAAE,MAAK;AAAA,YAAC,KAAK;AAAE,qBAAO,WAAS,MAAI,IAAE,CAAC,IAAG,WAAS,MAAI,IAAE,EAAC,OAAM,EAAC,IAAGA,GAAE,OAAO,UAAS,IAAI,QAAS,SAASA,IAAE,GAAE;AAAC,oBAAG;AAAC,sBAAE,SAAS,EAAC,UAASC,GAAC,GAAE,CAAC;AAAE,sBAAI,IAAE,EAAE,aAAa,KAAK,IAAI,CAAC,GAAE,IAAE,EAAE,mBAAiBA;AAAE,sBAAG,EAAE,QAAM,GAAE,CAAC,OAAO;AAAS,2BAAO,QAAQ,OAAO,IAAI,MAAM,8DAAiB,CAAC;AAAE,oBAAE,QAAQA,MAAG,EAAE,QAAQA,IAAG,KAAK,MAAK,GAAE,CAAC,KAAG,QAAQ,KAAK,sBAAqB,GAAE,GAAE,CAAC,GAAE,OAAO,SAAS,SAAS,UAAU,GAAE,GAAE,CAAC,IAAG,OAAO,YAAU,OAAO,SAAS,cAAY,CAAC,OAAO,SAAS,kBAAgB,OAAO,SAAS,gBAAc,CAAC,IAAG,OAAO,SAAS,cAAc,KAAG,KAAG,SAASA,IAAE;AAAC,wBAAIE,KAAEF;AAAE,2BAAOE,GAAE,OAAKH,GAAEG,GAAE,IAAI,IAAEH,GAAEG,EAAC;AAAA,kBAAC;AAAA,gBAAC,SAAOH,IAAN;AAAS,oBAAEA,EAAC;AAAA,gBAAC;AAAA,cAAC,CAAE,CAAC;AAAA,YAAE,KAAK;AAAA,YAAE,KAAI;AAAM,qBAAOA,GAAE,KAAK;AAAA,UAAC;AAAA,MAAC,GAAGA,EAAC;AAAA,IAAC,CAAE,CAAC;AAAE,WAAO,SAASC,IAAE,GAAE,GAAE;AAAC,aAAOD,GAAE,MAAM,MAAK,SAAS;AAAA,IAAC;AAAA,EAAC,EAAE,GAAE,EAAE,uBAAqB,SAASA,IAAEC,IAAE,GAAE;AAAC,IAAAD,MAAGA,GAAE,YAAUA,GAAE,cAAY,kBAAkB,UAAQC,GAAED,GAAE,MAAM,KAAG,QAAQ,KAAK,gCAAWA,EAAC,GAAE,EAAEA,EAAC,KAAGA,MAAG,YAAUA,GAAE,UAAQ,EAAEA,EAAC,IAAEC,GAAED,EAAC;AAAA,EAAC,GAAE,EAAE,SAAO,WAAU;AAAC,QAAIA,KAAE,kBAAkB,mBAAAI,QAAoB,KAAM,SAASJ,GAAEC,IAAE,GAAE,GAAE;AAAC,UAAI;AAAE,aAAO,mBAAAG,QAAoB,KAAM,SAASJ,IAAE;AAAC;AAAO,kBAAOA,GAAE,OAAKA,GAAE,MAAK;AAAA,YAAC,KAAK;AAAE,kBAAG,WAAS,MAAI,IAAE,CAAC,KAAI,IAAE,KAAK,WAAW,OAAK,iBAAiB,QAAO;AAAC,gBAAAA,GAAE,OAAK;AAAE;AAAA,cAAK;AAAC,kBAAG,KAAK,SAAQ;AAAC,gBAAAA,GAAE,OAAK;AAAE;AAAA,cAAK;AAAC,qBAAOA,GAAE,OAAO,UAAS,QAAQ,OAAO,mIAAuCC,EAAC,CAAC;AAAA,YAAE,KAAK;AAAE,qBAAOD,GAAE,OAAO,UAAS,KAAK,aAAaC,IAAE,GAAE,CAAC,CAAC;AAAA,YAAE,KAAK;AAAE,kBAAG,MAAI,iBAAiB,IAAG;AAAC,gBAAAD,GAAE,OAAK;AAAG;AAAA,cAAK;AAAC,qBAAOA,GAAE,OAAO,UAAS,KAAK,SAASC,IAAE,GAAE,CAAC,CAAC;AAAA,YAAE,KAAK;AAAG,kBAAG,MAAI,iBAAiB,UAAS;AAAC,gBAAAD,GAAE,OAAK;AAAG;AAAA,cAAK;AAAC,qBAAOA,GAAE,OAAO,UAAS,KAAK,cAAcC,IAAE,CAAC,CAAC;AAAA,YAAE,KAAK;AAAG,qBAAOD,GAAE,OAAO,UAAS,QAAQ,OAAO,kKAAgC,CAAC;AAAA,YAAE,KAAK;AAAA,YAAG,KAAI;AAAM,qBAAOA,GAAE,KAAK;AAAA,UAAC;AAAA,MAAC,GAAGA,IAAE,IAAI;AAAA,IAAC,CAAE,CAAC;AAAE,WAAO,SAASC,IAAE,GAAE,GAAE;AAAC,aAAOD,GAAE,MAAM,MAAK,SAAS;AAAA,IAAC;AAAA,EAAC,EAAE,GAAE,EAAE,qBAAmB,SAASA,IAAE;AAAC,WAAM,CAAC,CAAC,KAAK,wBAAwBA;AAAA,EAAE,GAAE,EAAE,yBAAuB,SAASA,IAAEC,IAAE;AAAC,SAAK,wBAAwBD,MAAGC;AAAA,EAAC,GAAE,EAAE,uBAAqB,SAASD,IAAE;AAAC,SAAK,mBAAmBA,EAAC,MAAI,KAAK,wBAAwBA,IAAG,GAAE,OAAO,KAAK,wBAAwBA;AAAA,EAAG,GAAE;AAAC,EAAE;AAAE,UAAU,MAAI,OAAO,WAAS,OAAO,YAAU,IAAI;AAAS,IAAO,kBAAQ,UAAU,IAAE,IAAI,YAAQ,OAAO;;;AEAh0X,SAAS,IAAI,GAAE,GAAE;AAAC,MAAG;AAAE,WAAO,SAAS,GAAE;AAAC,aAAM,cAAY,OAAO,KAAG,EAAE,SAAS,MAAM,KAAG,EAAE,WAAW,QAAQ,IAAE,EAAE,CAAC,IAAE,IAAI,QAAS,SAASM,IAAE,GAAE;AAAC,UAAE,SAAS,CAAC,GAAE,GAAE,EAAC,SAAQ,SAASC,IAAE;AAAC,UAAAD,GAAEC,EAAC;AAAA,QAAC,GAAE,MAAK,SAASA,IAAE;AAAC,YAAEA,EAAC;AAAA,QAAC,EAAC,CAAC,CAAC;AAAA,MAAC,CAAE;AAAA,IAAC;AAAC;;;ACAhY,SAAS,aAAa,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,SAAQ,IAAE,EAAE,eAAc,IAAE,EAAE,SAAQ,IAAE,EAAE;AAAa,MAAG,GAAE;AAAC,QAAI,IAAE,SAASC,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,GAAE,IAAE,SAASA,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC;AAAE,UAAI,iBAAiB,WAAS,KAAG,EAAE,EAAC,GAAE,uBAAsB,MAAK,GAAE,WAAU,GAAE,QAAO,EAAC,CAAC,IAAE,MAAI,mBAAmB,UAAQ,KAAG,EAAE,GAAE,GAAE,iBAAgB,SAAQ,CAAC,IAAE,MAAI,mBAAmB,OAAK,EAAE,YAAY,uBAAsB,OAAO,OAAO,CAAC,GAAE,CAAC,GAAG,SAASA,IAAE;AAAC,QAAEA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAM,SAAG,EAAE,KAAK,SAAQ,GAAG,WAAU;AAAC,QAAE;AAAA,IAAC,CAAE;AAAC;AAAC,gBAAQ,YAAY,SAAQ,EAAC,MAAK,cAAa,QAAO,aAAY,CAAC,GAAE,MAAM,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,MAAuB,GAAE;AAAC,SAAO,gBAAQ,OAAO,SAAQ,CAAC;AAAC;;;ACA1uB,gBAAQ,YAAY,cAAa,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,WAAW,UAAQ,EAAC,SAAQ,SAAQ,KAAI,SAAQ,IAAG,QAAO;AAAiB,SAAR,WAA4B,GAAE;AAAC,SAAO,gBAAQ,OAAO,cAAa,CAAC;AAAC;;;ACAxJ,SAAS,qBAAqB,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,SAAQ,IAAE,EAAE,eAAc,IAAE,EAAE,SAAQ,IAAE,EAAE;AAAa,MAAG,GAAE;AAAC,QAAI,IAAE,SAASC,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,GAAE,IAAE,SAASA,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC;AAAE,UAAI,iBAAiB,WAAS,KAAG,EAAE,EAAC,GAAE,qDAAoD,MAAK,GAAE,WAAU,GAAE,QAAO,EAAC,CAAC,IAAE,MAAI,mBAAmB,UAAQ,KAAG,EAAE,GAAE,GAAE,iBAAgB,uCAAsC,CAAC,IAAE,MAAI,mBAAmB,OAAK,EAAE,YAAY,qDAAoD,OAAO,OAAO,CAAC,GAAE,CAAC,GAAG,SAASA,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAM,SAAG,EAAE,KAAK,uCAAsC,GAAG,SAASA,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,CAAE;AAAC;AAAC,gBAAQ,YAAY,uCAAsC,EAAC,MAAK,sBAAqB,QAAO,sBAAqB,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,mCAAkC,CAAC;AAAC,EAAC,CAAC,GAAE,oCAAoC,UAAQ,EAAC,SAAQ,SAAQ,KAAI,SAAQ,IAAG,QAAO;AAAiB,SAAR,oCAAqD,GAAE;AAAC,SAAO,gBAAQ,OAAO,uCAAsC,CAAC;AAAC;;;ACA1nC,SAASC,sBAAqB,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,SAAQ,IAAE,EAAE,eAAc,IAAE,EAAE,SAAQ,IAAE,EAAE;AAAa,MAAG,GAAE;AAAC,QAAI,IAAE,SAASC,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,GAAE,IAAE,SAASA,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC;AAAE,UAAI,iBAAiB,WAAS,KAAG,EAAE,EAAC,GAAE,gDAA+C,MAAK,GAAE,WAAU,GAAE,QAAO,EAAC,CAAC,IAAE,MAAI,mBAAmB,UAAQ,KAAG,EAAE,GAAE,GAAE,iBAAgB,kCAAiC,CAAC,IAAE,MAAI,mBAAmB,OAAK,EAAE,YAAY,gDAA+C,OAAO,OAAO,CAAC,GAAE,CAAC,GAAG,SAASA,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAM,SAAG,EAAE,KAAK,kCAAiC,GAAG,SAASA,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,CAAE;AAAC;AAAC,gBAAQ,YAAY,kCAAiC,EAAC,MAAKD,uBAAqB,QAAOA,uBAAqB,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,8BAA6B,CAAC;AAAC,EAAC,CAAC,GAAE,+BAA+B,UAAQ,EAAC,SAAQ,SAAQ,KAAI,SAAQ,IAAG,QAAO;AAAiB,SAAR,+BAAgD,GAAE;AAAC,SAAO,gBAAQ,OAAO,kCAAiC,CAAC;AAAC;;;ACA7kC,SAASE,sBAAqB,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,SAAQ,IAAE,EAAE,eAAc,IAAE,EAAE,SAAQ,IAAE,EAAE;AAAa,MAAG,GAAE;AAAC,QAAI,IAAE,SAASC,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,GAAE,IAAE,SAASA,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC;AAAE,UAAI,iBAAiB,WAAS,KAAG,EAAE,EAAC,GAAE,wDAAuD,MAAK,GAAE,WAAU,GAAE,QAAO,EAAC,CAAC,IAAE,MAAI,mBAAmB,UAAQ,KAAG,EAAE,GAAE,GAAE,iBAAgB,0CAAyC,CAAC,IAAE,MAAI,mBAAmB,OAAK,EAAE,YAAY,wDAAuD,OAAO,OAAO,CAAC,GAAE,CAAC,GAAG,SAASA,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAM,SAAG,EAAE,KAAK,0CAAyC,GAAG,SAASA,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,CAAE;AAAC;AAAC,gBAAQ,YAAY,0CAAyC,EAAC,MAAKD,uBAAqB,QAAOA,uBAAqB,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,sCAAqC,CAAC;AAAC,EAAC,CAAC,GAAE,uCAAuC,UAAQ,EAAC,SAAQ,SAAQ,KAAI,SAAQ,IAAG,QAAO;AAAiB,SAAR,uCAAwD,GAAE;AAAC,SAAO,gBAAQ,OAAO,0CAAyC,CAAC;AAAC;;;ACA1rC,gBAAQ,YAAY,aAAY,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,UAAU,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,UAA2B,GAAE;AAAC,SAAO,gBAAQ,OAAO,aAAY,CAAC;AAAC;;;ACAtI,IAAAE,sBAA+B;;;ACAvE,gBAAQ,YAAY,WAAU,EAAC,MAAK,MAAG,QAAO,MAAG,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,WAAU,CAAC,CAAC;AAAC,EAAC,CAAC;AAAiB,SAAR,UAA0B;AAAC,SAAO,gBAAQ,OAAO,SAAS;AAAC;;;ACA/N,IAAI,KAAG;AAAP,IAAS,KAAG;AAAZ,IAAe,KAAG;AAAE,SAAS,YAAY,GAAE,GAAE;AAAC,SAAM,YAAU,OAAO,MAAI,IAAE,IAAG,YAAU,OAAO,MAAI,IAAE,IAAG,IAAE,IAAE,KAAG,IAAE,IAAE,KAAG;AAAE;AAAC,SAAS,eAAe,GAAE,GAAE;AAAC,aAAS,MAAI,IAAE,KAAI,WAAS,MAAI,IAAE;AAAI,MAAI,IAAE;AAAoB,MAAG,CAAC,EAAE,KAAK,CAAC,KAAG,CAAC,EAAE,KAAK,CAAC;AAAE,UAAM,IAAI,MAAM,oEAAa;AAAE,WAAQ,KAAG,KAAG,GAAG,MAAM,GAAG,EAAE,IAAK,SAASC,IAAE;AAAC,WAAO,SAASA,IAAE,EAAE;AAAA,EAAC,CAAE,GAAE,KAAG,KAAG,GAAG,MAAM,GAAG,EAAE,IAAK,SAASA,IAAE;AAAC,WAAO,SAASA,IAAE,EAAE;AAAA,EAAC,CAAE,GAAE,IAAE,KAAK,IAAI,EAAE,QAAO,EAAE,MAAM,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE,MAAI,IAAE,YAAY,EAAE,IAAG,EAAE,EAAE,OAAK,IAAG;AAAI;AAAC,SAAO;AAAC;AAAC,IAAO,yBAAQ;;;AFA5Q,IAAI,IAAE,aAAW,UAAU,aAAW;AAAtC,IAAyC,YAAU,WAAU;AAAC,SAAO,EAAE,QAAQ,SAAS,IAAE,MAAI,EAAE,QAAQ,KAAK,IAAE;AAAE;AAAjH,IAAmH,QAAM,WAAU;AAAC,SAAM,CAAC,CAAC,EAAE,MAAM,+BAA+B;AAAC;AAApL,IAAsL,YAAU,WAAU;AAAC,SAAM,aAAa,KAAK,UAAU,SAAS;AAAC;AAAiB,SAAR,QAAyB,GAAE;AAAC,SAAO,SAAS,MAAM,MAAK,SAAS;AAAC;AAAC,SAAS,WAAU;AAAC,UAAO,WAAS,kBAAkB,oBAAAC,QAAoB,KAAM,SAAS,EAAE,GAAE;AAAC,QAAI,GAAE,GAAE,GAAE;AAAE,WAAO,oBAAAA,QAAoB,KAAM,SAASC,IAAE;AAAC;AAAO,gBAAOA,GAAE,OAAKA,GAAE,MAAK;AAAA,UAAC,KAAK;AAAE,gBAAG,WAAK,IAAG;AAAC,cAAAA,GAAE,OAAK;AAAE;AAAA,YAAK;AAAC,mBAAOA,GAAE,OAAO,UAAS,KAAE;AAAA,UAAE,KAAK;AAAE,mBAAOA,GAAE,OAAK,GAAE,QAAQ;AAAA,UAAE,KAAK;AAAE,mBAAO,IAAEA,GAAE,MAAK,IAAE,EAAE,SAAQ,IAAE,WAAK,GAAG,SAAQ,IAAE,UAAU,IAAE,YAAU,MAAM,IAAE,QAAM,UAAU,IAAE,OAAK,WAAUA,GAAE,OAAO,UAAS,EAAE,CAAC,KAAG,CAAC,EAAE,OAAK,uBAAe,GAAE,EAAE,EAAE,IAAE,CAAC;AAAA,UAAE,KAAK;AAAA,UAAE,KAAI;AAAM,mBAAOA,GAAE,KAAK;AAAA,QAAC;AAAA,IAAC,GAAG,CAAC;AAAA,EAAC,CAAE,CAAC,GAAG,MAAM,MAAK,SAAS;AAAC;;;AGAvhC,gBAAQ,YAAY,wBAAuB,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,qBAAqB,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,uBAAuC;AAAC,SAAO,gBAAQ,OAAO,sBAAsB;AAAC;;;ACAvN,gBAAQ,YAAY,qBAAoB,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,kBAAkB,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,oBAAoC;AAAC,SAAO,gBAAQ,OAAO,mBAAmB;AAAC;;;ACApM,IAAI;AAAkB,CAAC,SAAS,GAAE;AAAC,IAAE,EAAE,UAAQ,KAAG,WAAU,EAAE,EAAE,MAAI,KAAG;AAAK,EAAE,sBAAoB,oBAAkB,CAAC,EAAE;AAAS,IAAI;AAAc,CAAC,SAAS,GAAE;AAAC,IAAE,EAAE,aAAW,KAAG,cAAa,EAAE,EAAE,SAAO,KAAG,UAAS,EAAE,EAAE,QAAM,KAAG,SAAQ,EAAE,EAAE,YAAU,KAAG,aAAY,EAAE,EAAE,oBAAkB,KAAG,qBAAoB,EAAE,EAAE,oBAAkB,KAAG,qBAAoB,EAAE,EAAE,MAAI,KAAG;AAAK,EAAE,kBAAgB,gBAAc,CAAC,EAAE;AAAS,IAAI;AAAoB,CAAC,SAAS,GAAE;AAAC,IAAE,aAAW,cAAa,EAAE,eAAa,gBAAe,EAAE,0BAAwB,2BAA0B,EAAE,mBAAiB,oBAAmB,EAAE,qBAAmB,sBAAqB,EAAE,mBAAiB,oBAAmB,EAAE,mBAAiB;AAAkB,EAAE,wBAAsB,sBAAoB,CAAC,EAAE,GAAE,gBAAQ,YAAY,iBAAgB,EAAC,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,sBAAqB,CAAC;AAAC,EAAC,CAAC,GAAE,cAAc,UAAQ,EAAC,IAAG,QAAO;AAAiB,SAAR,cAA+B,GAAE;AAAC,SAAO,gBAAQ,OAAO,iBAAgB,CAAC;AAAC;;;ACA17B,SAASC,sBAAqB,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,SAAQ,IAAE,EAAE,eAAc,IAAE,EAAE,SAAQ,IAAE,EAAE;AAAa,MAAG,GAAE;AAAC,QAAI,IAAE,SAASC,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,GAAE,IAAE,SAASA,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC;AAAE,UAAI,iBAAiB,WAAS,KAAG,EAAE,EAAC,GAAE,gDAA+C,MAAK,GAAE,WAAU,GAAE,QAAO,EAAC,CAAC,IAAE,MAAI,mBAAmB,UAAQ,KAAG,EAAE,GAAE,GAAE,iBAAgB,kCAAiC,CAAC,IAAE,MAAI,mBAAmB,OAAK,EAAE,YAAY,gDAA+C,OAAO,OAAO,CAAC,GAAE,CAAC,GAAG,SAASA,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAM,SAAG,EAAE,KAAK,kCAAiC,GAAG,SAASC,IAAE;AAAC,MAAAA,GAAE,SAAOA,GAAE,MAAM,SAAS,MAAI,kBAAkB,gBAAc,EAAE,KAAK,iBAAgB,GAAG,SAASD,IAAE;AAAC,wBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,MAAC,CAAE,IAAE,gBAAQ,qBAAqBC,IAAE,GAAE,CAAC;AAAA,IAAC,CAAE;AAAC;AAAC,gBAAQ,YAAY,kCAAiC,EAAC,MAAKF,uBAAqB,QAAOA,uBAAqB,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,6BAA4B,CAAC;AAAC,EAAC,CAAC,GAAE,+BAA+B,UAAQ,EAAC,SAAQ,SAAQ,KAAI,SAAQ,IAAG,QAAO;AAAiB,SAAR,+BAAgD,GAAE;AAAC,SAAO,gBAAQ,OAAO,kCAAiC,CAAC;AAAC;;;ACA9wC,gBAAQ,YAAY,+BAA8B,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,4BAA4B,UAAQ,EAAC,SAAQ,UAAS,KAAI,SAAQ;AAAiB,SAAR,4BAA6C,GAAE;AAAC,SAAO,gBAAQ,OAAO,+BAA8B,CAAC;AAAC;;;ACAxP,gBAAQ,YAAY,yBAAwB,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,sBAAsB,UAAQ,EAAC,SAAQ,UAAS,KAAI,SAAQ;AAAiB,SAAR,sBAAuC,GAAE;AAAC,SAAO,gBAAQ,OAAO,yBAAwB,CAAC;AAAC;;;ACAhO,gBAAQ,YAAY,qBAAoB,EAAC,MAAK,MAAG,QAAO,MAAG,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,iCAAgC,CAAC;AAAC,EAAC,CAAC,GAAE,kBAAkB,UAAQ,EAAC,SAAQ,SAAQ,KAAI,SAAQ,IAAG,QAAO;AAAiB,SAAR,kBAAmC,GAAE;AAAC,SAAO,gBAAQ,OAAO,qBAAoB,CAAC;AAAC;;;ACAzT,gBAAQ,YAAY,cAAa,EAAC,MAAK,MAAG,QAAO,MAAG,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,uBAAsB,CAAC;AAAC,EAAC,CAAC,GAAE,WAAW,UAAQ,EAAC,SAAQ,SAAQ,KAAI,SAAQ,IAAG,QAAO;AAAiB,SAAR,WAA4B,GAAE;AAAC,SAAO,gBAAQ,OAAO,cAAa,CAAC;AAAC;;;ACAnR,gBAAQ,YAAY,6BAA4B,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,0BAA0B,UAAQ,EAAC,SAAQ,UAAS,KAAI,SAAQ;AAAiB,SAAR,0BAA2C,GAAE;AAAC,SAAO,gBAAQ,OAAO,6BAA4B,CAAC;AAAC;;;ACApL,IAAI;AAAU,CAAC,SAAS,GAAE;AAAC,IAAE,EAAE,QAAM,KAAG,SAAQ,EAAE,EAAE,QAAM,KAAG;AAAO,EAAE,cAAY,YAAU,CAAC,EAAE,GAAE,gBAAQ,YAAY,iBAAgB,EAAC,MAAK,MAAG,QAAO,MAAG,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,0BAAyB,CAAC;AAAC,EAAC,CAAC,GAAE,YAAY,UAAQ,EAAC,SAAQ,SAAQ,KAAI,SAAQ,IAAG,QAAO;AAAiB,SAAR,YAA6B,GAAE;AAAC,SAAO,gBAAQ,OAAO,iBAAgB,SAAS,CAAC,GAAE,GAAE,EAAC,UAAS,cAAa,CAAC,CAAC;AAAC;;;ACAje,gBAAQ,YAAY,6BAA4B,EAAC,MAAK,MAAG,QAAO,MAAG,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,6BAA4B,CAAC;AAAC,EAAC,CAAC,GAAEG,eAAc,UAAQ,EAAC,IAAG,SAAQ,KAAI,SAAQ,SAAQ,QAAO;AAAiB,SAARA,eAA+B,GAAE;AAAC,SAAO,gBAAQ,OAAO,6BAA4B,CAAC;AAAC;;;ACAxQ,gBAAQ,YAAY,eAAc,EAAC,MAAK,KAAE,CAAC,GAAE,iBAAiB,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,iBAAkC,GAAE;AAAC,SAAO,IAAI,QAAS,SAAS,GAAE,GAAE;AAAC,OAAG,YAAY,SAAS,CAAC,GAAE,GAAE,EAAC,SAAQ,SAASC,IAAE;AAAC,QAAEA,EAAC;AAAA,IAAC,GAAE,MAAK,SAASA,IAAE;AAAC,QAAEA,EAAC;AAAA,IAAC,EAAC,CAAC,CAAC;AAAA,EAAC,CAAE;AAAC;;;ACA3T,gBAAQ,YAAY,kBAAiB,EAAC,MAAK,MAAG,QAAO,MAAG,IAAG,SAAS,GAAE,GAAE;AAAC,aAAS,MAAI,IAAE,CAAC,IAAG,OAAO,SAAS,SAAS,UAAU,EAAE,OAAM,2BAA0B,CAAC;AAAC,EAAC,CAAC,GAAE,eAAe,UAAQ,EAAC,SAAQ,SAAQ,KAAI,SAAQ,IAAG,QAAO;AAAiB,SAAR,iBAAiC;AAAC,SAAO,gBAAQ,OAAO,gBAAgB;AAAC;;;ACAvT,gBAAQ,YAAY,0BAAyB,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,uBAAuB,UAAQ,EAAC,SAAQ,UAAS,KAAI,SAAQ;AAAiB,SAAR,uBAAwC,GAAE;AAAC,SAAO,gBAAQ,OAAO,0BAAyB,CAAC;AAAC;;;ACApO,gBAAQ,YAAY,eAAc,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,YAAY,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,YAA6B,GAAE;AAAC,SAAO,gBAAQ,OAAO,eAAc,CAAC;AAAC;;;ACA5F,SAAS,UAAU,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,SAAQ,IAAE,EAAE,eAAc,IAAE,EAAE,SAAQ,IAAE,EAAE;AAAa,MAAG,GAAE;AAAC,QAAI,IAAE,SAASC,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,GAAE,IAAE,SAASA,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC;AAAE,UAAI,iBAAiB,WAAS,KAAG,EAAE,EAAC,GAAE,wBAAuB,MAAK,GAAE,WAAU,GAAE,QAAO,EAAC,CAAC,IAAE,MAAI,mBAAmB,UAAQ,KAAG,EAAE,GAAE,GAAE,kBAAiB,SAAQ,CAAC,IAAE,MAAI,mBAAmB,OAAK,EAAE,YAAY,wBAAuB,OAAO,OAAO,CAAC,GAAE,CAAC,GAAG,SAASA,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAM,SAAG,EAAE,KAAK,aAAY,GAAG,SAASA,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,CAAE;AAAC;AAAC,gBAAQ,YAAY,aAAY,EAAC,MAAK,WAAU,QAAO,WAAU,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,uBAAsB,CAAC;AAAC,EAAC,CAAC,GAAE,UAAU,UAAQ,EAAC,SAAQ,SAAQ,KAAI,SAAQ,IAAG,QAAO;AAAiB,SAAR,UAA2B,GAAE;AAAC,SAAO,gBAAQ,OAAO,aAAY,SAAS,CAAC,GAAE,GAAE,EAAC,UAAS,YAAW,CAAC,CAAC;AAAC;;;ACA3+B,IAAI;AAAoB,CAAC,SAAS,GAAE;AAAC,IAAE,OAAK,QAAO,EAAE,YAAU;AAAW,EAAE,wBAAsB,sBAAoB,CAAC,EAAE;AAAS,IAAI;AAAY,CAAC,SAAS,GAAE;AAAC,IAAE,OAAK,QAAO,EAAE,KAAG;AAAI,EAAE,gBAAc,cAAY,CAAC,EAAE,GAAE,gBAAQ,YAAY,sBAAqB,EAAC,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,kCAAiC,CAAC;AAAC,EAAC,CAAC,GAAE,mBAAmB,UAAQ,EAAC,IAAG,QAAO;AAAiB,SAAR,mBAAoC,GAAE;AAAC,SAAO,gBAAQ,OAAO,sBAAqB,CAAC;AAAC;;;ACApb,gBAAQ,YAAY,WAAU,EAAC,MAAK,SAAS,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,SAAQ,IAAE,EAAE,eAAc,IAAE,EAAC,SAAQ,EAAE,SAAQ,OAAM,EAAE,OAAM,UAAS,EAAE,aAAa,IAAG,cAAa,EAAE,aAAa,GAAE;AAAE,QAAI,oBAAoB,aAAW,EAAE,EAAC,GAAE,yBAAwB,MAAK,GAAE,WAAU,SAASC,IAAE;AAAC,QAAIC,KAAE,EAAC,WAAU,kBAAkB,SAAQ,QAAO,EAAC,aAAYD,GAAE,KAAG,IAAE,EAAC,EAAC;AAAE,oBAAQ,qBAAqBC,IAAE,GAAE,CAAC;AAAA,EAAC,GAAE,QAAO,SAASD,IAAE;AAAC,oBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,EAAC,EAAC,CAAC,IAAE,KAAG,EAAE,KAAK,WAAU,GAAG,SAASA,IAAE;AAAC,QAAIC,KAAE,EAAC,WAAU,kBAAkB,SAAQ,QAAO,EAAC,aAAYD,GAAE,KAAG,IAAE,EAAC,EAAC;AAAE,oBAAQ,qBAAqBC,IAAE,GAAE,CAAC;AAAA,EAAC,CAAE;AAAC,GAAE,QAAO,SAAS,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,SAAQ,IAAE,EAAE,eAAc,IAAE,EAAE,cAAa,IAAE,EAAC,SAAQ,EAAE,SAAQ,OAAM,EAAE,OAAM,UAAS,EAAE,aAAa,IAAG,cAAa,EAAE,aAAa,GAAE;AAAE,MAAG,GAAE;AAAC,UAAI,mBAAmB,UAAQ,KAAG,EAAG,SAASD,IAAE;AAAC,UAAIC,KAAE,EAAC,WAAU,kBAAkB,SAAQ,QAAO,EAAC,aAAYD,GAAE,KAAG,IAAE,EAAC,EAAC;AAAE,sBAAQ,qBAAqBC,IAAE,GAAE,CAAC;AAAA,IAAC,GAAI,SAASD,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,GAAG,iBAAgB,WAAU,CAAC,IAAE,MAAI,mBAAmB,OAAK,EAAE,YAAY,yBAAwB,OAAO,OAAO,CAAC,GAAE,CAAC,GAAG,SAASA,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAM,SAAG,EAAE,KAAK,WAAU,GAAG,SAASA,IAAE;AAAC,UAAIC,KAAE,EAAC,WAAU,kBAAkB,SAAQ,QAAO,EAAC,aAAYD,GAAE,KAAG,IAAE,EAAC,EAAC;AAAE,sBAAQ,qBAAqBC,IAAE,GAAE,CAAC;AAAA,IAAC,CAAE;AAAC,GAAE,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,+BAA8B,CAAC;AAAC,EAAC,CAAC,GAAE,QAAQ,UAAQ,EAAC,SAAQ,SAAQ,KAAI,SAAQ,IAAG,QAAO;AAAiB,SAAR,QAAyB,GAAE;AAAC,SAAO,gBAAQ,OAAO,WAAU,CAAC;AAAC;;;ACAplD,gBAAQ,YAAY,mBAAkB,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,gBAAgB,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,gBAAiC,GAAE;AAAC,SAAO,gBAAQ,OAAO,mBAAkB,CAAC;AAAC;;;ACAtM,gBAAQ,YAAY,mBAAkB,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,gBAAgB,UAAQ,EAAC,SAAQ,SAAQ,KAAI,SAAQ,IAAG,QAAO;AAAiB,SAAR,gBAAiC,GAAE;AAAC,SAAO,gBAAQ,OAAO,mBAAkB,CAAC;AAAC;;;ACAjN,gBAAQ,YAAY,cAAa,EAAC,MAAK,MAAG,QAAO,MAAG,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,mBAAkB,CAAC;AAAC,EAAC,CAAC,GAAE,WAAW,UAAQ,EAAC,SAAQ,SAAQ,KAAI,SAAQ,IAAG,QAAO;AAAiB,SAAR,WAA4B,GAAE;AAAC,SAAO,gBAAQ,OAAO,cAAa,CAAC;AAAC;;;ACA/Q,gBAAQ,YAAY,gBAAe,EAAC,MAAK,MAAG,QAAO,MAAG,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,qBAAoB,CAAC;AAAC,EAAC,CAAC,GAAE,aAAa,UAAQ,EAAC,SAAQ,SAAQ,KAAI,SAAQ,IAAG,QAAO;AAAiB,SAAR,aAA8B,GAAE;AAAC,SAAO,gBAAQ,OAAO,gBAAe,CAAC;AAAC;;;ACA/L,SAAS,uBAAuB,GAAE,GAAE;AAAC,aAAS,MAAI,IAAE,CAAC;AAAG,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,SAAQ,IAAE,EAAE,eAAc,IAAE,EAAE,SAAQ,IAAE,EAAE;AAAa,MAAG,GAAE;AAAC,QAAI,IAAE,SAASC,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,GAAE,IAAE,SAASA,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC;AAAE,UAAI,iBAAiB,WAAS,KAAG,EAAE,EAAC,GAAE,kCAAiC,MAAK,GAAE,WAAU,GAAE,QAAO,EAAC,CAAC,IAAE,MAAI,mBAAmB,UAAQ,KAAG,EAAE,GAAE,GAAE,kBAAiB,mBAAkB,CAAC,IAAE,MAAI,mBAAmB,OAAK,EAAE,YAAY,kCAAiC,OAAO,OAAO,CAAC,GAAE,CAAC,GAAG,SAASA,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAM,SAAG,EAAE,KAAK,mBAAkB,GAAG,WAAU;AAAC,QAAE;AAAA,IAAC,CAAE;AAAC;AAAC,gBAAQ,YAAY,mBAAkB,EAAC,MAAK,wBAAuB,QAAO,wBAAuB,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,kCAAiC,SAAS,CAAC,GAAE,CAAC,CAAC;AAAC,EAAC,CAAC,GAAE,gBAAgB,UAAQ,EAAC,SAAQ,SAAQ,KAAI,SAAQ,IAAG,QAAO;AAAiB,SAAR,gBAAiC,GAAE;AAAC,SAAO,gBAAQ,OAAO,mBAAkB,CAAC;AAAC;;;ACAtgC,gBAAQ,YAAY,sBAAqB,EAAC,MAAK,MAAG,QAAO,MAAG,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,sBAAqB,SAAS,EAAC,mBAAkB,KAAE,GAAE,CAAC,CAAC;AAAC,EAAC,CAAC,GAAE,mBAAmB,UAAQ,EAAC,SAAQ,WAAU,KAAI,WAAU,IAAG,QAAO;AAAiB,SAAR,mBAAoC,GAAE;AAAC,SAAO,gBAAQ,OAAO,sBAAqB,CAAC;AAAC;;;ACA5Y,gBAAQ,YAAY,sBAAqB,EAAC,QAAO,KAAE,CAAC,GAAE,mBAAmB,UAAQ,EAAC,SAAQ,WAAU;AAAiB,SAAR,mBAAoC,GAAE;AAAC,SAAO,gBAAQ,OAAO,sBAAqB,CAAC;AAAC;;;ACAjM,gBAAQ,YAAY,oBAAmB,EAAC,MAAK,MAAG,QAAO,MAAG,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,6BAA4B,CAAC,CAAC;AAAC,EAAC,CAAC,GAAE,iBAAiB,UAAQ,EAAC,IAAG,QAAO;AAAiB,SAAR,mBAAmC;AAAC,SAAO,gBAAQ,OAAO,kBAAkB;AAAC;;;ACA/P,gBAAQ,YAAY,wBAAuB,EAAC,QAAO,SAAS,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,SAAQ,IAAE,EAAE,eAAc,IAAE,EAAE;AAAa,MAAG,GAAE;AAAC,UAAI,mBAAmB,UAAQ,KAAG,EAAG,WAAU;AAAC,QAAE;AAAA,IAAC,GAAI,WAAU;AAAC,QAAE;AAAA,IAAC,GAAG,oBAAmB,WAAU,CAAC,CAAC,IAAE,MAAI,mBAAmB,OAAK,EAAE,YAAY,4BAA2B,OAAO,OAAO,CAAC,GAAE,CAAC,CAAC,GAAG,SAASC,IAAE;AAAC,QAAEA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAM,SAAG,EAAE,KAAK,eAAc,EAAC,aAAY,MAAE,GAAG,WAAU;AAAC,QAAE;AAAA,IAAC,CAAE;AAAC,EAAC,CAAC,GAAE,qBAAqB,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,uBAAuC;AAAC,SAAO,gBAAQ,OAAO,wBAAuB,EAAC,UAAS,uBAAsB,CAAC;AAAC;;;ACAtmB,gBAAQ,YAAY,wBAAuB,EAAC,QAAO,SAAS,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,SAAQ,IAAE,EAAE,eAAc,IAAE,EAAE;AAAa,MAAG,GAAE;AAAC,UAAI,mBAAmB,UAAQ,KAAG,EAAG,WAAU;AAAC,QAAE;AAAA,IAAC,GAAI,WAAU;AAAC,QAAE;AAAA,IAAC,GAAG,oBAAmB,WAAU,CAAC,CAAC,IAAE,MAAI,mBAAmB,OAAK,EAAE,YAAY,4BAA2B,OAAO,OAAO,CAAC,GAAE,CAAC,CAAC,GAAG,SAASC,IAAE;AAAC,QAAEA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAM,SAAG,EAAE,KAAK,UAAS,EAAC,QAAO,MAAE,GAAG,SAASA,IAAE;AAAC,QAAEA,EAAC;AAAA,IAAC,CAAE;AAAC,EAAC,CAAC,GAAE,qBAAqB,UAAQ,EAAC,KAAI,QAAO;AAAiB,SAAR,uBAAuC;AAAC,SAAO,gBAAQ,OAAO,wBAAuB,EAAC,UAAS,uBAAsB,CAAC;AAAC;;;ACAlmB,gBAAQ,YAAY,iBAAgB,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,cAAc,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,cAA+B,GAAE;AAAC,SAAO,gBAAQ,OAAO,iBAAgB,CAAC;AAAC;;;ACAzK,IAAI,gBAAc;AAAE,SAAS,aAAa,GAAE;AAAC,SAAO,gBAAQ,OAAO,gBAAe,CAAC;AAAC;AAAC,gBAAQ,YAAY,gBAAe,EAAC,MAAK,SAAS,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,eAAc,IAAE,EAAE;AAAQ,MAAG,MAAI,oBAAoB,YAAW;AAAC,SAAG,EAAE,EAAC,GAAE,8BAA6B,MAAK,GAAE,WAAU,SAASC,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,GAAE,QAAO,SAASA,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,EAAC,CAAC;AAAA,EAAC;AAAM,SAAG,EAAE,KAAK,gBAAe,GAAG,SAASA,IAAE;AAAC,MAAAA,GAAE,QAAM,EAAEA,EAAC,IAAE,EAAEA,EAAC;AAAA,IAAC,CAAE;AAAC,GAAE,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,yBAAwB,CAAC,GAAE,gBAAQ,uBAAuB,EAAE,OAAO,SAAS,GAAE,GAAE;AAAC,iCAA2B,MAAI,gBAAQ,qBAAqB,EAAE,KAAK,GAAE,gBAAQ,uBAAuB,EAAE,QAAQ,SAASC,IAAEC,IAAE;AAAC,UAAG,6BAA2BD,IAAE;AAAC,YAAIE,KAAED,GAAE,UAAS,IAAEA,GAAE,UAAS,IAAEA,GAAE,UAAS,IAAEA,GAAE,UAAS,IAAEA,GAAE;AAAM,UAAE,WAAW,EAAC,UAASC,IAAE,UAAS,GAAE,UAAS,GAAE,UAAS,GAAE,OAAM,EAAC,CAAC,GAAED,GAAE,WAAS,iBAAe,gBAAQ,qBAAqBA,GAAE,MAAM;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAE,CAAE;AAAC,EAAC,CAAC,GAAE,aAAa,UAAQ,EAAC,IAAG,QAAO;AAAE,IAAO,uBAAQ;;;ACA9/B,gBAAQ,YAAY,uBAAsB,EAAC,QAAO,SAAS,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,SAAQ,IAAE,EAAE,eAAc,IAAE,EAAE;AAAa,MAAG,GAAE;AAAC,UAAI,mBAAmB,UAAQ,KAAG,EAAG,WAAU;AAAC,QAAE;AAAA,IAAC,GAAI,WAAU;AAAC,QAAE;AAAA,IAAC,GAAG,oBAAmB,UAAS,CAAC,CAAC,IAAE,MAAI,mBAAmB,OAAK,EAAE,YAAY,2BAA0B,OAAO,OAAO,CAAC,GAAE,CAAC,CAAC,GAAG,WAAU;AAAC,QAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAM,SAAG,EAAE,KAAK,eAAc,EAAC,aAAY,KAAE,GAAG,WAAU;AAAC,QAAE;AAAA,IAAC,CAAE;AAAC,EAAC,CAAC,GAAE,oBAAoB,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,sBAAsC;AAAC,SAAO,gBAAQ,OAAO,uBAAsB,EAAC,UAAS,sBAAqB,CAAC;AAAC;;;ACAjnB,gBAAQ,YAAY,aAAY,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,UAAU,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,YAA4B;AAAC,SAAO,gBAAQ,OAAO,WAAW;AAAC;;;ACAvJ,gBAAQ,YAAY,uBAAsB,EAAC,QAAO,SAAS,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,SAAQ,IAAE,EAAE,eAAc,IAAE,EAAE;AAAa,MAAG,GAAE;AAAC,UAAI,mBAAmB,UAAQ,KAAG,EAAG,WAAU;AAAC,QAAE;AAAA,IAAC,GAAI,WAAU;AAAC,QAAE;AAAA,IAAC,GAAG,iBAAgB,UAAS,EAAC,QAAO,KAAE,CAAC,IAAE,MAAI,mBAAmB,OAAK,EAAE,YAAY,wBAAuB,OAAO,OAAO,CAAC,GAAE,EAAC,QAAO,KAAE,CAAC,GAAG,SAASE,IAAE;AAAC,QAAEA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAM,SAAG,EAAE,KAAK,UAAS,EAAC,QAAO,KAAE,GAAG,SAASA,IAAE;AAAC,QAAEA,EAAC;AAAA,IAAC,CAAE;AAAC,EAAC,CAAC,GAAE,oBAAoB,UAAQ,EAAC,KAAI,QAAO;AAAiB,SAAR,sBAAsC;AAAC,SAAO,gBAAQ,OAAO,uBAAsB,EAAC,UAAS,sBAAqB,CAAC;AAAC;;;ACAvmB,gBAAQ,YAAY,mBAAkB,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,gBAAgB,UAAQ,EAAC,KAAI,SAAQ,SAAQ,QAAO;AAAiB,SAAR,gBAAiC,GAAE;AAAC,SAAO,gBAAQ,OAAO,mBAAkB,CAAC;AAAC;;;ACAtM,gBAAQ,YAAY,kBAAiB,EAAC,QAAO,MAAG,MAAK,KAAE,CAAC,GAAE,eAAe,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,eAAgC,GAAE;AAAC,SAAO,gBAAQ,OAAO,kBAAiB,CAAC;AAAC;;;ACA3L,IAAI;AAAc,CAAC,SAAS,GAAE;AAAC,IAAE,MAAI,OAAM,EAAE,MAAI;AAAK,EAAE,kBAAgB,gBAAc,CAAC,EAAE,GAAE,gBAAQ,YAAY,mBAAkB,EAAC,QAAO,MAAG,MAAK,KAAE,CAAC,GAAE,gBAAgB,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,gBAAiC,GAAE;AAAC,SAAO,gBAAQ,OAAO,mBAAkB,CAAC;AAAC;;;ACAxS,gBAAQ,YAAY,uBAAsB,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,oBAAoB,UAAQ,EAAC,SAAQ,UAAS,KAAI,SAAQ;AAAiB,SAAR,oBAAqC,GAAE;AAAC,SAAO,gBAAQ,OAAO,uBAAsB,CAAC;AAAC;;;ACAxN,gBAAQ,YAAY,eAAc,EAAC,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,sCAAqC,CAAC;AAAC,GAAE,QAAO,MAAG,MAAK,KAAE,CAAC,GAAE,YAAY,UAAQ,EAAC,SAAQ,SAAQ,KAAI,SAAQ,IAAG,QAAO;AAAiB,SAAR,YAA6B,GAAE;AAAC,SAAO,gBAAQ,OAAO,eAAc,CAAC;AAAC;;;ACAtS,gBAAQ,YAAY,aAAY,EAAC,QAAO,MAAG,MAAK,MAAG,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,aAAY,CAAC;AAAC,EAAC,CAAC,GAAE,UAAU,UAAQ,EAAC,SAAQ,SAAQ,KAAI,SAAQ,IAAG,QAAO;AAAiB,SAAR,YAA4B;AAAC,SAAO,gBAAQ,OAAO,aAAY,CAAC,CAAC;AAAC;;;ACA3M,SAAR,mBAAmC;AAAC,SAAO,gBAAQ,iBAAiB;AAAC;;;ACAvH,gBAAQ,YAAY,eAAc,EAAC,QAAO,MAAG,MAAK,KAAE,CAAC,GAAE,YAAY,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,cAA8B;AAAC,SAAO,gBAAQ,OAAO,eAAc,CAAC,CAAC;AAAC;;;ACAtL,gBAAQ,YAAY,oBAAmB,EAAC,MAAK,MAAG,QAAO,MAAG,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,8BAA6B,CAAC;AAAC,EAAC,CAAC,GAAE,iBAAiB,UAAQ,EAAC,SAAQ,SAAQ,KAAI,SAAQ,IAAG,SAAQ;AAAiB,SAAR,mBAAmC;AAAC,SAAO,gBAAQ,OAAO,kBAAkB;AAAC;;;ACAhT,gBAAQ,YAAY,kBAAiB,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,eAAe,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,eAAgC,GAAE;AAAC,SAAO,gBAAQ,OAAO,kBAAiB,CAAC;AAAC;;;ACAlM,gBAAQ,YAAY,wBAAuB,EAAC,QAAO,MAAG,MAAK,KAAE,CAAC,GAAE,qBAAqB,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,qBAAsC,GAAE;AAAC,SAAO,gBAAQ,OAAO,wBAAuB,CAAC;AAAC;;;ACA1N,gBAAQ,YAAY,kBAAiB,EAAC,QAAO,MAAG,MAAK,KAAE,CAAC,GAAE,eAAe,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,iBAAiC;AAAC,SAAO,gBAAQ,OAAO,gBAAgB;AAAC;;;ACA/L,gBAAQ,YAAY,sBAAqB,EAAC,QAAO,MAAG,MAAK,MAAG,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,sBAAqB,CAAC;AAAC,EAAC,CAAC,GAAE,mBAAmB,UAAQ,EAAC,SAAQ,SAAQ,KAAI,SAAQ,IAAG,QAAO;AAAiB,SAAR,qBAAqC;AAAC,SAAO,gBAAQ,OAAO,oBAAoB;AAAC;;;ACA/S,gBAAQ,YAAY,gBAAe,EAAC,QAAO,MAAG,MAAK,KAAE,CAAC,GAAE,aAAa,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,eAA+B;AAAC,SAAO,gBAAQ,OAAO,cAAc;AAAC;;;ACAvL,gBAAQ,YAAY,kBAAiB,EAAC,QAAO,MAAG,MAAK,KAAE,CAAC,GAAE,eAAe,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,iBAAiC;AAAC,SAAO,gBAAQ,OAAO,gBAAgB;AAAC;;;ACA/L,gBAAQ,YAAY,gBAAe,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,aAAa,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,eAA+B;AAAC,SAAO,gBAAQ,OAAO,cAAc;AAAC;;;ACAhL,IAAI;AAAU,CAAC,SAAS,GAAE;AAAC,IAAE,SAAO,UAAS,EAAE,OAAK;AAAM,EAAE,cAAY,YAAU,CAAC,EAAE,GAAE,gBAAQ,YAAY,gBAAe,EAAC,IAAG,SAAS,GAAE,GAAE;AAAC,aAAS,MAAI,IAAE,CAAC,IAAG,OAAO,SAAS,SAAS,UAAU,EAAE,OAAM,yBAAwB,CAAC;AAAC,EAAC,CAAC,GAAE,aAAa,UAAQ,EAAC,IAAG,SAAQ;AAAiB,SAAR,eAA+B;AAAC,SAAO,gBAAQ,OAAO,gBAAe,CAAC,CAAC;AAAC;;;ACAjW,gBAAQ,YAAY,kBAAiB,EAAC,QAAO,MAAG,MAAK,KAAE,CAAC,GAAE,eAAe,UAAQ,EAAC,SAAQ,WAAU,KAAI,UAAS;AAAiB,SAAR,eAAgC,GAAE;AAAC,SAAO,gBAAQ,OAAO,kBAAiB,CAAC;AAAC;;;ACAtM,gBAAQ,YAAY,kBAAiB,EAAC,QAAO,KAAE,CAAC,GAAE,eAAe,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,eAAgC,GAAE;AAAC,SAAO,gBAAQ,OAAO,kBAAiB,CAAC;AAAC;;;ACA1L,gBAAQ,YAAY,WAAU,EAAC,QAAO,MAAG,MAAK,KAAE,CAAC,GAAE,QAAQ,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,UAA0B;AAAC,SAAO,gBAAQ,OAAO,SAAS;AAAC;;;ACAjJ,IAAI,kBAAgB;AAA4B,SAAS,uBAAsB;AAAC,MAAG,UAAQ,OAAO,WAAU;AAAC,QAAI,IAAE,OAAO,UAAU;AAAU,QAAG,GAAE;AAAC,UAAI,IAAE,EAAE,MAAM,eAAe;AAAE,aAAO,QAAQ,QAAQ,EAAC,OAAM,aAAY,MAAK,EAAE,IAAG,SAAQ,EAAE,GAAE,CAAC;AAAA,IAAC;AAAC,WAAO,QAAQ,OAAO,sNAAiD;AAAA,EAAC;AAAC;AAAC,gBAAQ,YAAY,gBAAe,EAAC,QAAO,MAAG,MAAK,MAAG,IAAG,KAAE,CAAC,GAAE,aAAa,UAAQ,EAAC,SAAQ,SAAQ,KAAI,SAAQ,IAAG,QAAO;AAAiB,SAAR,eAA+B;AAAC,MAAI,IAAE,gBAAQ,WAAW;AAAE,SAAO,MAAI,iBAAiB,MAAI,MAAI,iBAAiB,SAAO,qBAAqB,IAAE,MAAI,iBAAiB,WAAS,gBAAQ,OAAO,gBAAe,CAAC,CAAC,IAAE;AAAM;;;ACAxqB,IAAI;AAAJ,IAAyB;AAAzB,IAA6C;AAAY,CAAC,SAAS,GAAE;AAAC,IAAE,MAAI,KAAI,EAAE,KAAG;AAAG,EAAE,yBAAuB,uBAAqB,CAAC,EAAE,GAAE,SAAS,GAAE;AAAC,IAAE,EAAE,MAAI,KAAG,OAAM,EAAE,EAAE,KAAG,KAAG;AAAI,EAAE,wBAAsB,sBAAoB,CAAC,EAAE,GAAE,SAAS,GAAE;AAAC,IAAE,EAAE,OAAK,KAAG,QAAO,EAAE,EAAE,KAAG,KAAG,MAAK,EAAE,EAAE,SAAO,KAAG;AAAQ,EAAE,gBAAc,cAAY,CAAC,EAAE,GAAE,gBAAQ,YAAY,sBAAqB,EAAC,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,sBAAqB,CAAC;AAAC,GAAE,MAAK,MAAG,QAAO,KAAE,CAAC;AAAiB,SAAR,mBAAoC,GAAE;AAAC,SAAO,gBAAQ,OAAO,sBAAqB,CAAC;AAAC;;;ACA7f,IAAI;AAAJ,IAAe,UAAQ;AAAvB,IAAgC,iBAAe;AAA/C,IAA+D,cAAY;AAA3E,IAAwF,gBAAc,CAAC,SAAQ,gBAAe,WAAW;AAAzI,IAA2I,gBAAc,CAAC,QAAQ,OAAO,EAAE,KAAG,SAAO,MAAI,CAAC,QAAQ,OAAO,GAAG,KAAK;AAAE,kBAAgB,aAAW,GAAG,kBAAkB;AAAG,IAAI,YAAU,gBAAc,WAAW,WAAS,UAAU;AAA1D,IAAoE,cAAY,gBAAc,WAAW,cAAY,OAAO,OAAO;AAAnI,IAAyI,cAAY,gBAAc,WAAW,aAAW,OAAO,qBAAmB;AAAnN,IAAqN,iBAAe,gBAAc,QAAQ,QAAQ,EAAE,IAAE;AAAtQ,IAAyQ,YAAU,WAAU;AAAC,WAAS,EAAEC,IAAE;AAAC,eAASA,OAAIA,KAAE,CAAC,IAAG,KAAK,UAAQ,SAAS,EAAC,OAAM,CAAC,EAAE,GAAE,OAAM,IAAG,QAAO,IAAG,YAAW,KAAI,WAAU,WAAU,UAAS,2CAA0C,WAAU,UAAS,SAAQ,IAAG,QAAO,CAAC,GAAE,UAAS,GAAE,GAAEA,EAAC,GAAE,KAAK,QAAQ,SAAO,KAAK,QAAQ,WAAS,IAAG,KAAK,QAAQ,UAAQ,KAAK,QAAQ,WAAS,IAAG,KAAK,QAAQ,MAAI,KAAK,QAAQ,aAAW,KAAK,KAAG,KAAI,KAAK,QAAQ,SAAO,KAAK,IAAI,KAAK,QAAQ,GAAG,GAAE,KAAK,QAAQ,YAAU,KAAK,IAAI,KAAK,IAAI,KAAK,QAAQ,GAAG,CAAC;AAAA,EAAC;AAAC,MAAI,IAAE,EAAE;AAAU,SAAO,EAAE,OAAK,WAAU;AAAC,QAAIA,KAAE,MAAKC,KAAE,MAAK,IAAE;AAAK,oBAAc,IAAE,GAAG,oBAAoB,UAAU,KAAGA,KAAE,KAAK,aAAa,GAAE,IAAEA,GAAE,WAAW,IAAI,IAAG,KAAK,aAAa;AAAE,QAAI,IAAE,KAAK,SAAQ,IAAE,EAAE,eAAc,IAAE,EAAE,WAAU,IAAE,EAAE,QAAO,IAAE,EAAE,eAAc,IAAE,KAAK,KAAK,cAAY,CAAC,GAAE,IAAE,IAAI,MAAM,CAAC,EAAE,KAAK,CAAC,EAAE,OAAQ,SAASD,IAAEC,IAAE;AAAC,aAAOD,GAAE,OAAOC,EAAC;AAAA,IAAC,GAAG,CAAC,CAAC,GAAE,IAAE,WAAU;AAAC,MAAAD,GAAE,eAAe,CAAC,GAAEA,GAAE,SAAS,GAAE,CAAC,GAAE,EAAE,UAAU,GAAE,CAAC,GAAEA,GAAE,SAAS,GAAE,EAAE,QAAQ,GAAE,IAAE;AAAA,IAAC;AAAE,QAAG;AAAc,aAAO,IAAI,QAAS,SAASA,IAAE;AAAC,UAAE,SAAS,EAAC,OAAM,IAAE,GAAE,QAAO,IAAE,EAAC,GAAG,WAAU;AAAC,qBAAY,WAAU;AAAC,cAAE,GAAE,EAAE,KAAK,GAAEA,GAAE,EAAE,UAAU,WAAW,CAAC;AAAA,UAAC,GAAG,CAAC;AAAA,QAAC,CAAE;AAAA,MAAC,CAAE;AAAE,IAAAC,GAAE,QAAM,IAAE,GAAEA,GAAE,SAAO,IAAE,GAAEA,GAAE,MAAM,UAAQ,QAAO,EAAE;AAAE,QAAI,IAAEA,GAAE,UAAU,WAAW;AAAE,WAAO,KAAK,QAAQ,GAAE;AAAA,EAAC,GAAE,EAAE,eAAa,WAAU;AAAC,QAAID,KAAE,GAAEC,KAAE,GAAE,IAAE,KAAK;AAAQ,MAAE,YAAU,CAAC,EAAE,IAAI,KAAK,EAAE,OAAO,SAASC,IAAE;AAAC,UAAI,GAAE,GAAE,GAAE;AAAE,UAAG,eAAc;AAAC,iBAAQ,IAAE,GAAE,IAAE,GAAE,IAAEA,GAAE,QAAO,KAAG;AAAE,eAAG,kBAAkB,KAAKA,GAAE,EAAE,IAAE,IAAE;AAAG,YAAE,MAAI,EAAE,WAAS,GAAE,IAAE,MAAI,EAAE;AAAA,MAAQ,OAAK;AAAC,YAAI,KAAG,IAAE,uBAAqB,EAAE,WAAS,QAAM,EAAE,WAAS,0BAAwBA,KAAE,YAAW,IAAE,SAAS,cAAc,KAAK,GAAG,YAAU,EAAE,KAAK,GAAE,EAAE;AAAY,iBAAS,KAAK,YAAY,CAAC,GAAE,IAAE,EAAE,aAAY,IAAE,EAAE,cAAa,SAAS,KAAK,YAAY,CAAC;AAAA,MAAC;AAAC,aAAOF,KAAE,KAAK,IAAIA,IAAE,CAAC,GAAE,EAAE,eAAa,EAAE,aAAW,IAAGC,MAAG,KAAK,KAAK,EAAE,UAAQ,EAAE,QAAM,IAAE,IAAE,EAAE,MAAM,GAAE,EAAC,KAAIC,IAAE,OAAM,GAAE,QAAO,EAAC;AAAA,IAAC,CAAE,GAAEF,KAAE,EAAE,UAAQ,EAAE,QAAMA;AAAG,QAAI,IAAEA,KAAE,EAAE,YAAU,EAAE,aAAW,EAAE;AAAO,QAAE,EAAE,WAAS,EAAE,SAAO,IAAG,EAAE,eAAaA,IAAE,EAAE,gBAAcC;AAAA,EAAC,GAAE,EAAE,iBAAe,SAASD,IAAE;AAAC,QAAIC,KAAE,KAAK,SAAQ,IAAEA,GAAE,KAAI,IAAEA,GAAE,WAAU,IAAEA,GAAE,QAAO,IAAEA,GAAE,YAAW,IAAEA,GAAE,WAAU,IAAEA,GAAE,UAAS,IAAEA,GAAE,UAAS,IAAEA,GAAE,WAAU,IAAEA,GAAE;AAAQ,IAAAD,GAAE,OAAO,CAAC;AAAE,QAAI,IAAE,KAAG,IAAE;AAAG,IAAAA,GAAE,UAAU,CAAC,GAAE,CAAC,GAAEA,GAAE,OAAK,IAAE,MAAI,IAAE,QAAM,GAAEA,GAAE,YAAU,GAAEA,GAAE,YAAU,QAAOA,GAAE,eAAa,UAASA,GAAE,cAAY;AAAA,EAAC,GAAE,EAAE,WAAS,SAASA,IAAEC,IAAE,GAAE;AAAC,eAAS,MAAI,IAAE;AAAI,QAAI,IAAE,KAAK,SAAQ,IAAE,EAAE,cAAa,IAAE,EAAE,OAAM,IAAE,EAAE,QAAO,IAAE,EAAE,KAAI,IAAE,EAAE,QAAO,IAAE,EAAE;AAAU,IAAAA,GAAE,QAAS,SAASA,IAAEC,IAAE;AAAC,UAAI,IAAE,KAAG,IAAED,GAAE,SAAO,GAAE,IAAE,IAAE,IAAEC,IAAE,IAAE,KAAK,IAAI,IAAE,KAAK,IAAI,CAAC,CAAC,IAAE;AAAE,MAAAF,GAAE,SAASC,GAAE,KAAI,KAAG,IAAE,KAAG,IAAEA,GAAE,SAAO,IAAE,IAAG,KAAG,IAAE,KAAG,IAAEA,GAAE,SAAO,IAAE,EAAE;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,eAAa,WAAU;AAAC,QAAID,KAAE,SAAS,cAAc,QAAQ;AAAE,WAAO,KAAK,QAAQ,OAAO,KAAKA,EAAC,GAAEA;AAAA,EAAC,GAAE,EAAE,UAAQ,WAAU;AAAC,SAAK,QAAQ,OAAO,QAAS,SAASA,IAAE;AAAC,MAAAA,GAAE,OAAO,GAAEA,KAAE;AAAA,IAAI,CAAE;AAAA,EAAC,GAAE;AAAC,EAAE;AAAE,SAAS,cAAc,GAAE,GAAE;AAAC,MAAI,IAAE,KAAK,MAAM,CAAC,GAAE,IAAE,EAAE,aAAW;AAAE,MAAG,CAAC,KAAG,QAAM,OAAO,EAAE,eAAe;AAAE,WAAO;AAAe,MAAG,CAAC,MAAM,QAAQ,EAAE,WAAW,KAAG,CAAC,EAAE,YAAY,KAAM,SAASA,IAAE;AAAC,WAAOA,GAAE,SAAO,KAAG,QAAM,OAAOA,GAAE,KAAK;AAAA,EAAC,CAAE;AAAE,WAAO;AAAe,MAAI,IAAE,CAAC;AAAE,MAAG,MAAM,QAAQ,EAAE,WAAW,GAAE;AAAC,QAAI,IAAE;AAAG,MAAE,YAAY,SAAS,CAAC,MAAI,KAAG,EAAE,WAAS,MAAK,EAAE,YAAY,SAAS,CAAC,MAAI,MAAI,EAAE,WAAS,IAAI,MAAM,EAAE,IAAG,KAAG,EAAE,KAAK,CAAC,GAAE,EAAE,YAAY,SAAS,CAAC,KAAG,EAAE,iBAAe,EAAE,KAAK,EAAE,aAAa;AAAA,EAAC;AAAC,MAAG,CAAC,EAAE;AAAO,WAAO;AAAe,MAAI,GAAE,GAAE,IAAE,+CAA+C,KAAK,SAAS,GAAE,IAAE,wCAAwC,KAAK,SAAS,GAAE,IAAE,QAAM,OAAO,EAAE,oBAAoB;AAAE,MAAE,KAAG,IAAE,KAAI,IAAE,OAAK,IAAE,IAAG,IAAE,MAAI,IAAE,KAAG,IAAE,KAAG,YAAW,IAAE,KAAG,eAAa,IAAE,KAAG,YAAW,IAAE,KAAG,cAAY,KAAG,IAAE,KAAI,IAAE,QAAM,IAAE,KAAI,IAAE;AAAK,SAAO,IAAI,UAAU,EAAC,eAAc,MAAK,OAAM,GAAE,OAAM,GAAE,QAAO,GAAE,YAAW,KAAI,WAAU,EAAC,GAAE,WAAU,GAAE,WAAU,GAAE,UAAS,EAAE,EAAE,cAAY,WAAU,UAAS,2CAA0C,WAAU,QAAM,OAAO,EAAE,SAAS,IAAE,WAAS,QAAO,UAAS,MAAI,SAAS,EAAE,iBAAgB,EAAE,KAAG,KAAI,UAAS,EAAC,GAAE,IAAG,GAAE,IAAG,GAAE,GAAE,EAAE,EAAE,aAAW,GAAE,CAAC,EAAE,KAAK;AAAC;AAAQ,SAAS,kBAAkB,GAAE,GAAE;AAAC,MAAG,WAAS,MAAI,IAAE,CAAC,IAAG,WAAS,MAAI,IAAE,UAAS,CAAC,cAAc,SAAS,CAAC;AAAE,UAAM,IAAI,MAAM,qHAAqC;AAAE,MAAG;AAAC,WAAO,cAAc,KAAK,MAAK,KAAK,UAAU,CAAC,GAAE,CAAC;AAAA,EAAC,SAAOA,IAAN;AAAS,UAAMA;AAAA,EAAC;AAAC;;;ACA9kJ,aAAa,UAAQ,EAAC,SAAQ,SAAQ,KAAI,SAAQ,IAAG,QAAO;AAAiB,SAAR,aAA8B,GAAE,GAAE;AAAC,SAAO,WAAS,MAAI,IAAE,KAAI,IAAI,QAAS,SAAS,GAAE,GAAE;AAAC,uBAAmB,EAAC,UAAS,EAAC,CAAC,EAAE,KAAM,SAASG,IAAE;AAAC,UAAG;AAAC,YAAI,IAAE,kBAAkBA,IAAE,CAAC;AAAE,UAAE,CAAC;AAAA,MAAC,SAAOA,IAAN;AAAS,UAAEA,EAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAC;;;ACAhW,IAAI;AAAW,CAAC,SAAS,GAAE;AAAC,IAAE,EAAE,SAAO,KAAG,UAAS,EAAE,EAAE,UAAQ,KAAG;AAAS,EAAE,eAAa,aAAW,CAAC,EAAE,GAAE,gBAAQ,YAAY,wBAAuB,EAAC,QAAO,MAAG,MAAK,MAAG,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,wBAAuB,CAAC;AAAC,EAAC,CAAC,GAAE,qBAAqB,UAAQ,EAAC,SAAQ,SAAQ,KAAI,SAAQ,IAAG,QAAO;AAAiB,SAAR,qBAAsC,GAAE;AAAC,SAAO,gBAAQ,OAAO,wBAAuB,CAAC;AAAC;;;ACArc,IAAIC;AAAW,CAAC,SAAS,GAAE;AAAC,IAAE,EAAE,UAAQ,KAAG,WAAU,EAAE,EAAE,SAAO,KAAG;AAAQ,EAAEA,gBAAaA,cAAW,CAAC,EAAE;AAAS,IAAI;AAAa,CAAC,SAAS,GAAE;AAAC,IAAE,gBAAc,iBAAgB,EAAE,aAAW,cAAa,EAAE,cAAY,eAAc,EAAE,gBAAc,iBAAgB,EAAE,cAAY,eAAc,EAAE,qBAAmB,sBAAqB,EAAE,cAAY,eAAc,EAAE,gBAAc,iBAAgB,EAAE,OAAK,QAAO,EAAE,aAAW,cAAa,EAAE,SAAO;AAAQ,EAAE,iBAAe,eAAa,CAAC,EAAE;AAAE,IAAI;AAAJ,IAAoBC,kBAAe;AAAnC,IAAsC,eAAa,EAAC,GAAE,UAAS,GAAE,QAAO,GAAE,SAAQ;AAAE,CAAC,SAAS,GAAE;AAAC,IAAE,EAAE,QAAM,KAAG,SAAQ,EAAE,EAAE,SAAO,KAAG,UAAS,EAAE,EAAE,QAAM,KAAG;AAAO,EAAE,oBAAkB,kBAAgB,CAAC,EAAE;AAAS,IAAI;AAAmB,CAAC,SAAS,GAAE;AAAC,IAAE,EAAE,QAAM,KAAG,SAAQ,EAAE,EAAE,OAAK,KAAG;AAAM,EAAE,uBAAqB,qBAAmB,CAAC,EAAE;AAAE,IAAI,uBAAqB;AAAzB,IAA6B,wBAAsB;AAAnD,IAAuD,qBAAmB;AAA1E,IAA8E,sBAAoB;AAAlG,IAAqG,gBAAc;AAAnH,IAAuHC,aAAU,WAAU;AAAC,WAAS,EAAEC,IAAE;AAAC,SAAK,UAAQ,OAAO,OAAO,EAAC,OAAM,IAAG,OAAM,IAAG,QAAO,IAAG,WAAU,KAAI,WAAU,WAAU,UAAS,2CAA0C,cAAa,IAAG,QAAO,CAAC,GAAE,UAAS,IAAG,QAAO,GAAE,SAAQ,GAAE,KAAI,IAAG,GAAEA,IAAE,EAAC,OAAMA,GAAE,qBAAoB,QAAOA,GAAE,iBAAgB,CAAC,GAAE,KAAK,QAAQ,MAAI,KAAK,QAAQ,YAAU,KAAK,KAAG;AAAA,EAAG;AAAC,MAAI,IAAE,EAAE;AAAU,SAAO,EAAE,OAAK,WAAU;AAAC,QAAIA,IAAEC,IAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAEC,IAAE,IAAE;AAAK,WAAOA,MAAG,IAAE,KAAK,aAAa,GAAG,WAAW,IAAI,GAAE,EAAE,SAAO,UAAQF,KAAE,WAAS,WAASA,MAAG,UAAQC,KAAED,GAAE,WAAS,WAASC,KAAE,SAAOA,GAAE,WAAS,UAAQ,IAAE,aAAW,WAAS,KAAG,UAAQ,IAAE,EAAE,oBAAkB,WAAS,IAAE,SAAO,EAAE,gBAAc,sBAAqB,EAAE,UAAQ,UAAQ,IAAE,WAAS,WAAS,KAAG,UAAQ,IAAE,EAAE,WAAS,WAAS,IAAE,SAAO,EAAE,YAAU,UAAQ,IAAE,aAAW,WAAS,KAAG,UAAQ,IAAE,EAAE,oBAAkB,WAAS,IAAE,SAAO,EAAE,iBAAe,uBAAsB,KAAK,aAAa,GAAE,KAAK,eAAeC,EAAC,GAAE,KAAK,SAASA,EAAC,GAAE,EAAE,UAAU,WAAW;AAAA,EAAC,GAAE,EAAE,eAAa,WAAU;AAAC,QAAIF,IAAEC,IAAE,IAAE,KAAK,SAAQ,IAAE,0CAAwC,MAAI,KAAK,OAAO,GAAE,KAAGD,KAAE,eAAa,IAAE,mBAAiB,EAAE,WAAS,QAAM,EAAE,WAAS,+CAA6C,EAAE,QAAM,YAAWC,KAAE,SAAS,cAAc,KAAK,GAAG,YAAUD,GAAE,KAAK,GAAEC,GAAE;AAAY,aAAS,KAAK,YAAY,CAAC;AAAE,QAAI,IAAE,SAAS,eAAe,CAAC,GAAE,IAAE,KAAK,IAAI,EAAE,aAAY,EAAE,MAAM,SAAO,EAAE,WAAS,aAAa,KAAG,oBAAmB,IAAE,KAAK,IAAI,EAAE,cAAa,EAAE,WAAS,aAAa,KAAG;AAAoB,MAAE,SAAO,GAAE,EAAE,UAAQ,GAAE,SAAS,KAAK,YAAY,CAAC;AAAA,EAAC,GAAE,EAAE,iBAAe,SAASD,IAAE;AAAC,QAAIC,KAAE,KAAK,SAAQ,IAAEA,GAAE,KAAI,IAAEA,GAAE,WAAU,IAAEA,GAAE,UAAS,IAAEA,GAAE,UAAS,IAAEA,GAAE,WAAU,IAAEA,GAAE;AAAa,IAAAD,GAAE,OAAO,CAAC,GAAEA,GAAE,OAAK,IAAE,MAAI,IAAE,QAAM,GAAEA,GAAE,YAAU,GAAEA,GAAE,YAAU,QAAOA,GAAE,eAAa,UAASA,GAAE,eAAa,MAAI,KAAG;AAAA,EAAG,GAAE,EAAE,cAAY,SAASA,IAAEC,IAAE;AAAC,aAAQ,IAAE,KAAK,SAAQ,IAAE,EAAE,OAAM,IAAE,EAAE,QAAO,IAAE,EAAE,OAAM,IAAE,EAAE,QAAO,IAAE,EAAE,SAAQC,KAAE,GAAEA,KAAE,IAAGA;AAAI,eAAQ,IAAEA,KAAE,IAAE,GAAE,IAAE,GAAE,IAAE,IAAG,KAAI;AAAC,YAAI,IAAE;AAAO,YAAEA,KAAE,KAAG,IAAEF,OAAI,mBAAmB,SAAO,IAAE,KAAG,KAAG,IAAE,KAAG,IAAE,IAAE,IAAEA,OAAI,mBAAmB,SAAO,IAAE,KAAG,IAAE,KAAG,IAAE,KAAG,IAAE,GAAEC,GAAE,SAAS,GAAED,OAAI,mBAAmB,QAAM,IAAE,CAAC,GAAE,CAAC;AAAA,MAAC;AAAA,EAAC,GAAE,EAAE,WAAS,SAASA,IAAE;AAAC,SAAK,YAAY,mBAAmB,OAAMA,EAAC,GAAE,KAAK,YAAY,mBAAmB,MAAKA,EAAC;AAAA,EAAC,GAAE,EAAE,eAAa,WAAU;AAAC,QAAIA,KAAE,SAAS,cAAc,QAAQ;AAAE,WAAO,KAAK,QAAQ,OAAO,KAAKA,EAAC,GAAEA;AAAA,EAAC,GAAE;AAAC,EAAE;AAAE,SAASG,eAAc,GAAE,GAAE;AAAC,MAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAED,IAAE;AAAE,aAAS,MAAI,IAAE,aAAa;AAAa,MAAI,IAAE;AAAK,MAAG;AAAC,QAAE,KAAK,MAAM,CAAC;AAAA,EAAC,SAAOF,IAAN;AAAS,QAAE,CAAC;AAAA,EAAC;AAAC,MAAI,IAAE,UAAQ,IAAE,MAAI,WAAS,KAAG,UAAQ,IAAE,EAAE,cAAY,WAAS,IAAE,SAAO,EAAE,aAAY,IAAE,UAAQ,IAAE,MAAI,WAAS,IAAE,SAAO,EAAE;AAAS,OAAI,QAAM,IAAE,SAAO,EAAE,YAAUH,YAAW,YAAU,QAAM,IAAE,SAAO,EAAE,YAAUA,YAAW,WAAS,QAAM,KAAG,UAAQ,IAAE,EAAE,eAAa,WAAS,IAAE,SAAO,EAAE,QAAMA,YAAW;AAAO,WAAOC;AAAe,MAAI,GAAE,IAAE;AAAG,IAAE,QAAM,KAAG,UAAQ,IAAE,EAAE,qBAAmB,WAAS,IAAE,SAAO,EAAE,oBAAkBD,YAAW,WAAS,KAAG,QAAM,IAAE,SAAO,EAAE,YAAW,QAAM,KAAG,UAAQ,IAAE,EAAE,qBAAmB,WAAS,IAAE,SAAO,EAAE,uBAAqBA,YAAW,WAAS,KAAG,OAAK,QAAM,IAAE,SAAO,EAAE,mBAAkB,QAAM,KAAG,UAAQK,KAAE,EAAE,qBAAmB,WAASA,MAAGA,GAAE,gBAAc,KAAG,OAAK,QAAM,KAAG,UAAQ,IAAE,EAAE,qBAAmB,WAAS,IAAE,SAAO,EAAE;AAAa,SAAO,EAAE,SAAO,IAAIH,WAAU,OAAO,OAAO,EAAC,OAAM,GAAE,UAAS,0CAAyC,GAAE,QAAM,IAAE,SAAO,EAAE,gBAAe,EAAC,WAAU,aAAa,QAAM,KAAG,UAAQ,IAAE,EAAE,mBAAiB,WAAS,IAAE,SAAO,EAAE,WAAU,CAAC,CAAC,EAAE,KAAK,IAAED;AAAc;AAAgB,SAAR,oBAAqC,GAAE,GAAE;AAAC,aAAS,MAAI,IAAE,aAAa;AAAa,MAAG;AAAC,WAAOK,eAAc,KAAK,MAAK,KAAK,UAAU,CAAC,GAAE,CAAC;AAAA,EAAC,SAAOH,IAAN;AAAS,WAAM;AAAA,EAAE;AAAC;;;ACAxpI,SAAR,eAAgC,GAAE;AAAC,SAAO,IAAI,QAAS,SAAS,GAAE,GAAE;AAAC,YAAW,EAAE,KAAM,SAAS,GAAE;AAAC,UAAI,IAAE,EAAE;AAAQ,aAAK,uBAAe,GAAE,OAAO,IAAE,qBAAqB,EAAC,UAAS,EAAC,CAAC,EAAE,KAAM,SAASI,IAAE;AAAC,YAAG;AAAC,cAAIC,KAAE,oBAAoBD,IAAE,CAAC;AAAE,YAAEC,EAAC;AAAA,QAAC,SAAOC,IAAN;AAAS,YAAEA,EAAC;AAAA,QAAC;AAAA,MAAC,CAAE,IAAE,mBAAmB,EAAC,UAAS,EAAC,CAAC,EAAE,KAAM,SAASF,IAAE;AAAC,YAAG;AAAC,cAAIC,KAAE,kBAAkBD,IAAE,CAAC;AAAE,YAAEC,EAAC;AAAA,QAAC,SAAOC,IAAN;AAAS,YAAEA,EAAC;AAAA,QAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE,EAAE,MAAO,WAAU;AAAC,yBAAmB,EAAC,UAAS,EAAC,CAAC,EAAE,KAAM,SAAS,GAAE;AAAC,YAAG;AAAC,cAAI,IAAE,kBAAkB,GAAE,CAAC;AAAE,YAAE,CAAC;AAAA,QAAC,SAAOA,IAAN;AAAS,YAAEA,EAAC;AAAA,QAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAC;;;ACAjzB,gBAAQ,YAAY,iBAAgB,EAAC,QAAO,MAAG,MAAK,KAAE,CAAC,GAAE,cAAc,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,gBAAgC;AAAC,SAAO,gBAAQ,OAAO,eAAe;AAAC;;;ACA3L,gBAAQ,YAAY,uBAAsB,EAAC,MAAK,MAAG,QAAO,MAAG,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,uBAAsB,CAAC;AAAC,EAAC,CAAC,GAAE,oBAAoB,UAAQ,EAAC,SAAQ,UAAS,KAAI,SAAQ;AAAiB,SAAR,sBAAsC;AAAC,SAAO,gBAAQ,OAAO,qBAAqB;AAAC;;;ACAvR,gBAAQ,YAAY,cAAa,EAAC,QAAO,SAAS,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,SAAQ,IAAE,EAAE,eAAc,IAAE,EAAE;AAAa,MAAG,GAAE;AAAC,UAAI,mBAAmB,UAAQ,KAAG,EAAG,SAASC,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,GAAI,SAASA,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,GAAG,kBAAiB,UAAS,CAAC,IAAE,MAAI,mBAAmB,OAAK,EAAE,YAAY,yBAAwB,OAAO,OAAO,CAAC,GAAE,CAAC,GAAG,SAASA,IAAE;AAAC,QAAEA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAM,SAAG,EAAE,KAAK,cAAa,EAAC,UAAS,SAAQ,GAAG,WAAU;AAAC,QAAE;AAAA,IAAC,CAAE;AAAC,EAAC,CAAC,GAAE,OAAO,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,SAAyB;AAAC,SAAO,gBAAQ,OAAO,cAAa,EAAC,UAAS,SAAQ,CAAC;AAAC;;;ACAnlB,SAAS,uBAAuB,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,SAAQ,IAAE,EAAE,eAAc,IAAE,EAAE,SAAQ,IAAE,EAAE;AAAa,MAAG,GAAE;AAAC,QAAI,IAAE,SAASC,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,GAAE,IAAE,SAASA,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC;AAAE,UAAI,iBAAiB,WAAS,KAAG,EAAE,EAAC,GAAE,qCAAoC,MAAK,GAAE,WAAU,GAAE,QAAO,EAAC,CAAC,IAAE,MAAI,mBAAmB,UAAQ,KAAG,EAAE,GAAE,GAAE,uBAAsB,iBAAgB,CAAC,IAAE,MAAI,mBAAmB,OAAK,EAAE,YAAY,qCAAoC,OAAO,OAAO,CAAC,GAAE,CAAC,GAAG,SAASA,IAAE;AAAC,QAAEA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAM,SAAG,EAAE,KAAK,eAAc,GAAG,WAAU;AAAC,QAAE;AAAA,IAAC,CAAE;AAAC;AAAC,gBAAQ,YAAY,eAAc,EAAC,MAAK,wBAAuB,QAAO,uBAAsB,CAAC,GAAE,YAAY,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,cAA8B;AAAC,SAAO,gBAAQ,OAAO,aAAa;AAAC;;;ACAzzB,SAAS,sBAAsB,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,SAAQ,IAAE,EAAE,eAAc,KAAG,EAAE,SAAQ,EAAE;AAAc,MAAG,GAAE;AAAC,QAAI,IAAE,EAAC,MAAK,OAAG,SAAQ,MAAG,MAAK,GAAE;AAAE,UAAI,mBAAmB,UAAQ,KAAG,EAAG,SAASC,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,GAAI,SAASA,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,GAAG,kBAAiB,YAAW,CAAC,IAAE,MAAI,mBAAmB,OAAK,EAAE,YAAY,2BAA0B,OAAO,OAAO,CAAC,GAAE,CAAC,GAAG,SAASA,IAAE;AAAC,QAAEA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAM,SAAG,EAAE,KAAK,kBAAiB,GAAG,WAAU;AAAC,QAAE;AAAA,IAAC,CAAE;AAAC;AAAC,gBAAQ,YAAY,kBAAiB,EAAC,QAAO,sBAAqB,CAAC,GAAE,eAAe,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,iBAAiC;AAAC,SAAO,gBAAQ,OAAO,gBAAgB;AAAC;;;ACAvqB,SAAS,oBAAoB,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,eAAc,IAAE,EAAE,cAAa,IAAE,EAAE,SAAQ,IAAE,EAAE,SAAQ,IAAE,OAAO,OAAO,GAAE,EAAC,QAAO,KAAE,CAAC;AAAE,MAAG,GAAE;AAAC,QAAI,IAAE,WAAU;AAAC,QAAE,aAAW,EAAE,UAAU,GAAE,EAAE;AAAA,IAAC,GAAE,IAAE,WAAU;AAAC,QAAE,UAAQ,EAAE,OAAO,GAAE,EAAE;AAAA,IAAC;AAAE,UAAI,iBAAiB,WAAS,KAAG,EAAE,EAAC,GAAE,0BAAyB,MAAK,GAAE,WAAU,GAAE,QAAO,EAAC,CAAC,IAAE,MAAI,mBAAmB,UAAQ,KAAG,EAAE,GAAE,GAAE,kBAAiB,WAAU,CAAC,IAAE,MAAI,mBAAmB,OAAK,EAAE,YAAY,0BAAyB,OAAO,OAAO,CAAC,GAAE,CAAC,GAAG,WAAU;AAAC,QAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAM,SAAG,EAAE,KAAK,gBAAe,GAAG,WAAU;AAAC,QAAE;AAAA,IAAC,CAAE;AAAC;AAAC,gBAAQ,YAAY,gBAAe,EAAC,MAAK,qBAAoB,QAAO,oBAAmB,CAAC,GAAE,aAAa,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,eAA+B;AAAC,SAAO,gBAAQ,OAAO,cAAc;AAAC;;;ACAnzB,gBAAQ,YAAY,uBAAsB,EAAC,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,6BAA4B,CAAC;AAAC,EAAC,CAAC,GAAE,oBAAoB,UAAQ,EAAC,IAAG,QAAO;AAAiB,SAAR,oBAAqC,GAAE;AAAC,SAAO,gBAAQ,OAAO,uBAAsB,CAAC;AAAC;;;ACArL,SAAS,wBAAwB,GAAE,GAAE;AAAC,aAAS,MAAI,IAAE,CAAC;AAAG,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,SAAQ,IAAE,EAAE,eAAc,IAAE,EAAE,SAAQ,IAAE,EAAE;AAAa,MAAG,GAAE;AAAC,QAAI,IAAE,SAASC,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,GAAE,IAAE,SAASA,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC;AAAE,UAAI,iBAAiB,WAAS,KAAG,EAAE,EAAC,GAAE,mCAAkC,MAAK,GAAE,WAAU,GAAE,QAAO,EAAC,CAAC,IAAE,MAAI,mBAAmB,UAAQ,KAAG,EAAE,GAAE,GAAE,kBAAiB,oBAAmB,CAAC,IAAE,MAAI,mBAAmB,OAAK,EAAE,YAAY,mCAAkC,OAAO,OAAO,CAAC,GAAE,CAAC,GAAG,SAASA,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAM,SAAG,EAAE,KAAK,oBAAmB,GAAG,WAAU;AAAC,QAAE;AAAA,IAAC,CAAE;AAAC;AAAC,gBAAQ,YAAY,oBAAmB,EAAC,MAAK,yBAAwB,QAAO,yBAAwB,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,mCAAkC,SAAS,CAAC,GAAE,CAAC,CAAC;AAAC,EAAC,CAAC,GAAE,iBAAiB,UAAQ,EAAC,SAAQ,SAAQ,KAAI,SAAQ,IAAG,QAAO;AAAiB,SAAR,iBAAkC,GAAE;AAAC,SAAO,gBAAQ,OAAO,oBAAmB,CAAC;AAAC;;;ACA7+B,SAAS,qBAAqB,GAAE,GAAE;AAAC,aAAS,MAAI,IAAE,CAAC;AAAG,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,SAAQ,IAAE,EAAE,eAAc,IAAE,EAAE,SAAQ,IAAE,EAAE;AAAa,MAAG,GAAE;AAAC,QAAI,IAAE,SAASC,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,GAAE,IAAE,SAASA,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC;AAAE,UAAI,iBAAiB,WAAS,KAAG,EAAE,EAAC,GAAE,gCAA+B,MAAK,GAAE,WAAU,GAAE,QAAO,EAAC,CAAC,IAAE,MAAI,mBAAmB,UAAQ,KAAG,EAAE,GAAE,GAAE,kBAAiB,iBAAgB,CAAC,IAAE,MAAI,mBAAmB,OAAK,EAAE,YAAY,gCAA+B,OAAO,OAAO,CAAC,GAAE,CAAC,GAAG,SAASA,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAM,SAAG,EAAE,KAAK,iBAAgB,GAAG,WAAU;AAAC,QAAE;AAAA,IAAC,CAAE;AAAC;AAAC,gBAAQ,YAAY,iBAAgB,EAAC,MAAK,sBAAqB,QAAO,sBAAqB,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,gCAA+B,SAAS,CAAC,GAAE,CAAC,CAAC;AAAC,EAAC,CAAC,GAAE,cAAc,UAAQ,EAAC,SAAQ,SAAQ,KAAI,SAAQ,IAAG,QAAO;AAAiB,SAAR,cAA+B,GAAE;AAAC,SAAO,gBAAQ,OAAO,iBAAgB,CAAC;AAAC;;;ACA9+B,gBAAQ,YAAY,oBAAmB,EAAC,MAAK,MAAG,QAAO,MAAG,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,mCAAkC,SAAS,CAAC,GAAE,CAAC,CAAC;AAAC,EAAC,CAAC,GAAE,iBAAiB,UAAQ,EAAC,SAAQ,SAAQ,KAAI,SAAQ,IAAG,QAAO;AAAiB,SAAR,iBAAkC,GAAE;AAAC,SAAO,gBAAQ,OAAO,oBAAmB,CAAC;AAAC;;;ACAzX,gBAAQ,YAAY,eAAc,EAAC,QAAO,MAAG,MAAK,KAAE,CAAC,GAAE,YAAY,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,YAA6B,GAAE;AAAC,SAAO,gBAAQ,OAAO,eAAc,CAAC;AAAC;;;ACAvK,SAAR,GAAoB,GAAE,GAAE;AAAC,SAAO,gBAAQ,cAAc,GAAE,CAAC;AAAC;;;ACAjE,gBAAQ,YAAY,kBAAiB,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,eAAe,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,iBAAiC;AAAC,SAAO,gBAAQ,OAAO,gBAAgB;AAAC;;;ACA/L,gBAAQ,YAAY,oBAAmB,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,iBAAiB,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,iBAAkC,GAAE;AAAC,SAAO,gBAAQ,OAAO,oBAAmB,CAAC;AAAC;;;ACA1M,gBAAQ,YAAY,kBAAiB,EAAC,MAAK,MAAG,QAAO,MAAG,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,kBAAiB,CAAC;AAAC,EAAC,CAAC,GAAE,eAAe,UAAQ,EAAC,KAAI,SAAQ,SAAQ,SAAQ,IAAG,QAAO;AAAiB,SAAR,eAAgC,GAAE;AAAC,SAAO,gBAAQ,OAAO,kBAAiB,CAAC;AAAC;;;ACA9R,gBAAQ,YAAY,WAAU,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,QAAQ,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,QAAyB,GAAE;AAAC,SAAO,gBAAQ,OAAO,WAAU,CAAC;AAAC;;;ACAtK,gBAAQ,YAAY,eAAc,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,YAAY,UAAQ,EAAC,SAAQ,QAAO;AAAiB,SAAR,YAA6B,GAAE;AAAC,SAAO,gBAAQ,OAAO,eAAc,CAAC;AAAC;;;ACA1K,gBAAQ,YAAY,YAAW,EAAC,MAAK,MAAG,QAAO,MAAG,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,gCAA+B,EAAC,KAAI,EAAE,OAAM,CAAC;AAAC,EAAC,CAAC,GAAE,SAAS,UAAQ,EAAC,SAAQ,SAAQ,KAAI,SAAQ,IAAG,QAAO;AAAiB,SAAR,SAA0B,GAAE;AAAC,SAAO,gBAAQ,OAAO,YAAW,CAAC;AAAC;;;ACAjS,gBAAQ,YAAY,oBAAmB,EAAC,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,0BAAyB,CAAC;AAAC,EAAC,CAAC,GAAE,iBAAiB,UAAQ,EAAC,IAAG,QAAO;AAAiB,SAAR,iBAAkC,GAAE;AAAC,SAAO,gBAAQ,OAAO,oBAAmB,CAAC;AAAC;;;ACAtI,SAAS,gBAAgB,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,SAAQ,IAAE,EAAE,eAAc,IAAE,EAAE,SAAQ,IAAE,EAAE;AAAa,UAAW,EAAE,KAAM,SAASC,IAAE;AAAC,QAAI,IAAEA,GAAE,SAAQC,KAAE,OAAK,uBAAe,GAAE,OAAO;AAAE,QAAG,GAAE;AAAC,UAAI,IAAE,SAASC,IAAE;AAAC,wBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,MAAC,GAAE,IAAE,SAASA,IAAE;AAAC,wBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,MAAC;AAAE,YAAI,iBAAiB,WAAS,KAAG,EAAE,EAAC,GAAED,KAAE,2BAAyB,4BAA2B,MAAK,GAAE,WAAU,GAAE,QAAO,EAAC,CAAC,IAAE,MAAI,mBAAmB,UAAQ,KAAG,EAAE,GAAE,GAAE,iBAAgBA,KAAE,aAAW,cAAa,CAAC,IAAE,MAAI,mBAAmB,OAAK,EAAE,YAAYA,KAAE,2BAAyB,4BAA2B,OAAO,OAAO,CAAC,GAAE,CAAC,GAAG,SAASC,IAAE;AAAC,wBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,MAAC,CAAE;AAAA,IAAC;AAAM,WAAG,EAAE,KAAKD,KAAE,aAAW,cAAa,GAAG,SAASC,IAAE;AAAC,wBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,MAAC,CAAE;AAAA,EAAC,CAAE;AAAC;AAAC,gBAAQ,YAAY,YAAW,EAAC,MAAK,iBAAgB,QAAO,iBAAgB,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,qBAAoB,CAAC;AAAC,EAAC,CAAC,GAAE,SAAS,UAAQ,EAAC,SAAQ,SAAQ,KAAI,SAAQ,IAAG,QAAO;AAAiB,SAAR,SAA0B,GAAE;AAAC,SAAO,gBAAQ,OAAO,YAAW,CAAC;AAAC;;;ACA1qC,gBAAQ,YAAY,YAAW,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,SAAS,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,SAA0B,GAAE;AAAC,SAAO,gBAAQ,OAAO,YAAW,CAAC;AAAC;;;ACArH,gBAAQ,YAAY,aAAY,EAAC,QAAO,MAAG,MAAK,KAAE,CAAC,GAAE,cAAc,UAAQ,EAAC,SAAQ,WAAU,KAAI,UAAS;AAAiB,SAAR,cAA+B,GAAE;AAAC,SAAO,gBAAQ,OAAO,aAAY,SAAS,CAAC,GAAE,GAAE,EAAC,UAAS,gBAAe,CAAC,CAAC;AAAC;;;ACAvR,gBAAQ,YAAY,kBAAiB,EAAC,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,2BAA0B,CAAC;AAAC,EAAC,CAAC,GAAE,eAAe,UAAQ,EAAC,IAAG,QAAO;AAAiB,SAAR,eAAgC,GAAE;AAAC,SAAO,gBAAQ,OAAO,kBAAiB,CAAC;AAAC;;;ACAzP,gBAAQ,YAAY,uBAAsB,EAAC,QAAO,MAAG,MAAK,KAAE,CAAC,GAAE,oBAAoB,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,sBAAsC;AAAC,SAAO,gBAAQ,OAAO,qBAAqB;AAAC;;;ACAnN,gBAAQ,YAAY,cAAa,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,WAAW,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,WAA4B,GAAE;AAAC,SAAO,gBAAQ,OAAO,cAAa,CAAC;AAAC;;;ACAlL,gBAAQ,YAAY,YAAW,EAAC,MAAK,MAAG,QAAO,MAAG,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,6BAA4B,CAAC;AAAC,EAAC,CAAC,GAAE,SAAS,UAAQ,EAAC,SAAQ,SAAQ,KAAI,SAAQ,IAAG,QAAO;AAAiB,SAAR,SAA0B,GAAE;AAAC,SAAO,gBAAQ,OAAO,YAAW,CAAC;AAAC;;;ACAjR,gBAAQ,YAAY,oBAAmB,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC;AAAiB,SAAR,iBAAkC,GAAE;AAAC,SAAO,gBAAQ,OAAO,oBAAmB,CAAC;AAAC;;;ACAnJ,gBAAQ,YAAY,iBAAgB,EAAC,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,6BAA4B,CAAC;AAAC,EAAC,CAAC,GAAE,cAAc,UAAQ,EAAC,IAAG,UAAS;AAAiB,SAAR,cAA+B,GAAE;AAAC,SAAO,gBAAQ,OAAO,iBAAgB,CAAC;AAAC;;;ACApN,SAAS,6BAA6B,GAAE,GAAE;AAAC,aAAS,MAAI,IAAE,CAAC;AAAG,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,SAAQ,IAAE,EAAE,eAAc,IAAE,EAAE,SAAQ,IAAE,EAAE;AAAa,MAAG,GAAE;AAAC,QAAI,IAAE,SAASC,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,GAAE,IAAE,SAASA,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC;AAAE,UAAI,iBAAiB,WAAS,KAAG,EAAE,EAAC,GAAE,MAAI,mBAAmB,UAAQ,wCAAsC,uCAAsC,MAAK,GAAE,WAAU,GAAE,QAAO,EAAC,CAAC,IAAE,MAAI,mBAAmB,UAAQ,KAAG,EAAE,GAAE,GAAE,iBAAgB,yBAAwB,CAAC,IAAE,MAAI,mBAAmB,OAAK,EAAE,YAAY,uCAAsC,OAAO,OAAO,CAAC,GAAE,CAAC,GAAG,SAASA,IAAE;AAAC,QAAEA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAM,SAAG,EAAE,KAAK,yBAAwB,GAAG,WAAU;AAAC,QAAE;AAAA,IAAC,CAAE;AAAC;AAAC,gBAAQ,YAAY,yBAAwB,EAAC,MAAK,8BAA6B,QAAO,6BAA4B,CAAC,GAAE,sBAAsB,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,sBAAuC,GAAE;AAAC,SAAO,WAAS,MAAI,IAAE,EAAC,OAAM,MAAE,IAAG,gBAAQ,OAAO,yBAAwB,CAAC;AAAC;;;ACA7gC,gBAAQ,YAAY,aAAY,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,UAAU,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,UAA2B,GAAE;AAAC,SAAO,gBAAQ,OAAO,aAAY,CAAC;AAAC;;;ACA9K,gBAAQ,YAAY,cAAa,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,WAAW,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,WAA4B,GAAE;AAAC,SAAO,gBAAQ,OAAO,cAAa,CAAC;AAAC;;;ACAlL,gBAAQ,YAAY,gBAAe,EAAC,MAAK,MAAG,QAAO,MAAG,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,yBAAwB,CAAC;AAAC,EAAC,CAAC,GAAE,aAAa,UAAQ,EAAC,SAAQ,SAAQ,KAAI,SAAQ,IAAG,QAAO;AAAiB,SAAR,aAA8B,GAAE;AAAC,SAAO,gBAAQ,OAAO,gBAAe,CAAC;AAAC;;;ACA7R,gBAAQ,YAAY,aAAY,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,UAAU,UAAQ,EAAC,SAAQ,SAAQ;AAAiB,SAAR,UAA2B,GAAE;AAAC,SAAO,gBAAQ,OAAO,aAAY,CAAC;AAAC;;;ACAnK,gBAAQ,YAAY,kBAAiB,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,eAAe,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,eAAgC,GAAE;AAAC,SAAO,gBAAQ,OAAO,kBAAiB,CAAC;AAAC;;;ACA3I,SAAS,cAAc,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,SAAQ,IAAE,EAAE,eAAc,IAAE,EAAE,SAAQ,IAAE,EAAE,cAAa,IAAE,EAAC,SAAQ,EAAE,SAAQ,OAAM,EAAE,OAAM,UAAS,EAAE,aAAa,IAAG,cAAa,EAAE,aAAa,GAAE;AAAE,MAAG,GAAE;AAAC,QAAI,IAAE,SAASC,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,GAAE,IAAE,SAASA,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC;AAAE,UAAI,iBAAiB,WAAS,KAAG,EAAE,EAAC,GAAE,wBAAuB,MAAK,GAAE,WAAU,GAAE,QAAO,EAAC,CAAC,IAAE,MAAI,mBAAmB,UAAQ,KAAG,EAAE,GAAE,GAAE,iBAAgB,UAAS,CAAC,IAAE,MAAI,mBAAmB,OAAK,EAAE,YAAY,wBAAuB,OAAO,OAAO,CAAC,GAAE,CAAC,GAAG,SAASA,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAM,SAAG,EAAE,KAAK,UAAS,GAAG,SAASA,IAAE;AAAC,UAAIC,KAAE,EAAC,WAAU,kBAAkB,SAAQ,QAAO,EAAC,aAAYD,GAAE,KAAG,IAAE,GAAE,OAAMA,GAAE,WAAU,EAAC;AAAE,sBAAQ,qBAAqBC,IAAE,GAAE,CAAC;AAAA,IAAC,CAAE;AAAC;AAAC,gBAAQ,YAAY,UAAS,EAAC,MAAK,eAAc,QAAO,eAAc,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,8BAA6B,CAAC;AAAC,EAAC,CAAC,GAAE,OAAO,UAAQ,EAAC,SAAQ,SAAQ,KAAI,SAAQ,IAAG,QAAO;AAAiB,SAAR,OAAwB,GAAE;AAAC,SAAO,gBAAQ,OAAO,UAAS,CAAC;AAAC;;;ACA1mC,gBAAQ,YAAY,cAAa,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,WAAW,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,WAA4B,GAAE;AAAC,SAAO,gBAAQ,OAAO,cAAa,CAAC;AAAC;;;ACAlL,gBAAQ,YAAY,qBAAoB,EAAC,MAAK,MAAG,QAAO,MAAG,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,qBAAoB,CAAC;AAAC,EAAC,CAAC,GAAE,kBAAkB,UAAQ,EAAC,KAAI,SAAQ,SAAQ,SAAQ,IAAG,QAAO;AAAiB,SAAR,kBAAmC,GAAE;AAAC,SAAO,gBAAQ,OAAO,qBAAoB,CAAC;AAAC;;;ACA9R,SAAR,MAAuB,GAAE;AAAC,gBAAY,OAAO,IAAE,gBAAQ,QAAQ,CAAC,IAAE,QAAQ,MAAM,qCAAqC;AAAC;;;ACAtH,IAAI;AAAe,CAAC,SAAS,GAAE;AAAC,IAAE,EAAE,gBAAc,KAAG,iBAAgB,EAAE,EAAE,cAAY,KAAG,eAAc,EAAE,EAAE,cAAY,KAAG,eAAc,EAAE,EAAE,eAAa,KAAG,gBAAe,EAAE,EAAE,iBAAe,KAAG,kBAAiB,EAAE,EAAE,SAAO,KAAG;AAAQ,EAAE,mBAAiB,iBAAe,CAAC,EAAE,GAAE,gBAAQ,YAAY,mBAAkB,EAAC,MAAK,MAAG,QAAO,MAAG,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,mBAAkB,CAAC;AAAC,EAAC,CAAC,GAAE,gBAAgB,UAAQ,EAAC,KAAI,SAAQ,SAAQ,SAAQ,IAAG,QAAO;AAAiB,SAAR,gBAAiC,GAAE;AAAC,SAAO,gBAAQ,OAAO,mBAAkB,CAAC;AAAC;;;ACAxjB,gBAAQ,YAAY,qBAAoB,EAAC,QAAO,MAAG,MAAK,KAAE,CAAC,GAAE,kBAAkB,UAAQ,EAAC,SAAQ,WAAU,KAAI,UAAS;AAAiB,SAAR,kBAAmC,GAAE;AAAC,SAAO,gBAAQ,OAAO,qBAAoB,CAAC;AAAC;;;ACA7K,SAAS,mBAAmB,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,eAAc,IAAE,EAAE,cAAa,IAAE,EAAE,SAAQ,IAAE,EAAE;AAAQ,MAAG,GAAE;AAAC,QAAI,IAAE,WAAU;AAAC,QAAE,aAAW,EAAE,UAAU,GAAE,EAAE;AAAA,IAAC,GAAE,IAAE,WAAU;AAAC,QAAE,UAAQ,EAAE,OAAO,GAAE,EAAE;AAAA,IAAC;AAAE,UAAI,iBAAiB,WAAS,KAAG,EAAE,EAAC,GAAE,0BAAyB,MAAK,GAAE,WAAU,GAAE,QAAO,EAAC,CAAC,IAAE,MAAI,mBAAmB,UAAQ,KAAG,EAAE,GAAE,GAAE,kBAAiB,WAAU,CAAC,IAAE,MAAI,mBAAmB,OAAK,EAAE,YAAY,6BAA4B,OAAO,OAAO,CAAC,GAAE,CAAC,GAAG,WAAU;AAAC,QAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAM,SAAG,EAAE,KAAK,eAAc,GAAG,WAAU;AAAC,QAAE;AAAA,IAAC,CAAE;AAAC;AAAC,gBAAQ,YAAY,eAAc,EAAC,MAAK,oBAAmB,QAAO,mBAAkB,CAAC,GAAE,YAAY,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,YAA6B,GAAE;AAAC,SAAO,gBAAQ,OAAO,eAAc,CAAC;AAAC;;;ACAlxB,gBAAQ,YAAY,aAAY,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,UAAU,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,YAA4B;AAAC,SAAO,gBAAQ,OAAO,WAAW;AAAC;;;ACA3K,gBAAQ,YAAY,eAAc,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,YAAY,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,YAA6B,GAAE;AAAC,SAAO,gBAAQ,OAAO,eAAc,CAAC;AAAC;;;ACAtL,gBAAQ,YAAY,cAAa,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,WAAW,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,WAA4B,GAAE;AAAC,SAAO,gBAAQ,OAAO,cAAa,CAAC;AAAC;;;ACAlL,gBAAQ,YAAY,QAAO,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,KAAK,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,KAAsB,GAAE;AAAC,SAAO,gBAAQ,OAAO,QAAO,CAAC;AAAC;;;ACA1J,gBAAQ,YAAY,eAAc,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,YAAY,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,YAA6B,GAAE;AAAC,SAAO,gBAAQ,OAAO,eAAc,CAAC;AAAC;;;ACAjI,SAAS,sBAAsB,GAAE;AAAC,SAAO,SAAS,CAAC,GAAE,GAAE,EAAC,UAAS,IAAG,YAAW,IAAG,CAAC;AAAC;AAAC,SAAS,uBAAuB,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE;AAAQ,OAAG,EAAE,KAAK,eAAc,sBAAsB,CAAC,GAAG,WAAU;AAAC,MAAE;AAAA,EAAC,CAAE;AAAC;AAAC,gBAAQ,YAAY,mBAAkB,EAAC,MAAK,wBAAuB,QAAO,uBAAsB,CAAC,GAAE,gBAAgB,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,gBAAiC,GAAE;AAAC,SAAO,gBAAQ,OAAO,mBAAkB,CAAC;AAAC;;;ACAtf,IAAI;AAAW,CAAC,SAAS,GAAE;AAAC,IAAE,UAAQ,KAAI,EAAE,aAAW,KAAI,EAAE,YAAU;AAAG,EAAE,eAAa,aAAW,CAAC,EAAE,GAAE,gBAAQ,YAAY,4BAA2B,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,yBAAyB,UAAQ,EAAC,SAAQ,WAAU,KAAI,UAAS;AAAiB,SAAR,yBAA0C,GAAE;AAAC,SAAO,gBAAQ,OAAO,4BAA2B,CAAC;AAAC;;;ACA1U,SAAS,kBAAkB,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,eAAc,IAAE,EAAE,cAAa,IAAE,EAAE;AAAQ,MAAG,GAAE;AAAC,UAAI,mBAAmB,UAAQ,KAAG,EAAG,SAASC,IAAE;AAAC,QAAE,aAAW,EAAE,UAAU,GAAE,EAAE;AAAA,IAAC,GAAI,SAASC,IAAE;AAAC,QAAE;AAAA,IAAC,GAAG,kBAAiB,WAAU,CAAC,IAAE,MAAI,mBAAmB,OAAK,EAAE,YAAY,0BAAyB,OAAO,OAAO,CAAC,GAAE,CAAC,GAAG,SAASA,IAAE;AAAC,QAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAM,SAAG,EAAE,KAAK,cAAa,GAAG,SAASA,IAAE;AAAC,QAAE;AAAA,IAAC,CAAE;AAAC;AAAC,gBAAQ,YAAY,cAAa,EAAC,QAAO,kBAAiB,CAAC,GAAE,WAAW,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,WAA4B,GAAE;AAAC,SAAO,gBAAQ,OAAO,cAAa,CAAC;AAAC;;;ACAnf,SAAS,sBAAsB,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,SAAQ,IAAE,EAAE,eAAc,IAAE,EAAE,SAAQ,IAAE,EAAE,cAAa,IAAE,EAAE;AAAM,MAAG,GAAE;AAAC,QAAI,IAAE,SAASC,IAAE;AAAC,QAAE,aAAW,EAAE,UAAU,GAAE,gBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,GAAE,IAAE,SAASC,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC;AAAE,UAAI,iBAAiB,WAAS,KAAG,EAAE,EAAC,GAAE,0BAAyB,MAAK,GAAE,WAAU,GAAE,QAAO,EAAC,CAAC,IAAE,MAAI,mBAAmB,UAAQ,KAAG,EAAE,GAAE,GAAE,kBAAiB,WAAU,CAAC,IAAE,MAAI,mBAAmB,OAAK,EAAE,YAAY,0BAAyB,OAAO,OAAO,CAAC,GAAE,CAAC,GAAG,SAASA,IAAE;AAAC,OAAC,KAAG,EAAEA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAM,SAAG,EAAE,KAAK,kBAAiB,GAAG,WAAU;AAAC,QAAE;AAAA,IAAC,CAAE;AAAC;AAAC,gBAAQ,YAAY,kBAAiB,EAAC,MAAK,uBAAsB,QAAO,uBAAsB,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,0BAAyB,CAAC;AAAC,EAAC,CAAC,GAAE,eAAe,UAAQ,EAAC,KAAI,SAAQ,IAAG,QAAO;AAAiB,SAAR,eAAgC,GAAE;AAAC,MAAI,IAAE,iBAAiB;AAAE,SAAO,gBAAQ,OAAO,kBAAiB,MAAI,oBAAoB,aAAW,OAAO,OAAO,EAAC,OAAM,MAAG,MAAK,MAAG,SAAQ,OAAG,UAAS,MAAG,MAAK,GAAE,GAAE,CAAC,IAAE,GAAE,EAAC,iBAAgB,MAAI,oBAAoB,aAAW,2BAAyB,KAAI,CAAC;AAAC;;;ACAtnC,gBAAQ,YAAY,iBAAgB,EAAC,QAAO,SAAS,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,SAAQ,IAAE,EAAE,eAAc,IAAE,EAAE;AAAa,MAAG,GAAE;AAAC,QAAI,GAAE,GAAEC,KAAE,EAAC,MAAK,EAAE,OAAM,MAAK,WAAS,EAAE,QAAM,EAAE,MAAK,SAAQ,WAAS,EAAE,WAAS,EAAE,QAAO;AAAE,QAAG,MAAI,mBAAmB;AAAQ,WAAG,EAAG,SAASC,IAAE;AAAC,UAAE,aAAW,EAAE,UAAUA,EAAC,GAAE,EAAEA,EAAC;AAAA,MAAC,GAAI,SAASA,GAAEC,IAAE;AAAC,UAAE,UAAQ,EAAE,OAAOA,EAAC,GAAE,EAAED,EAAC;AAAA,MAAC,GAAG,mBAAkB,QAAM,KAAG,UAAQ,IAAE,EAAE,UAAQ,WAAS,IAAE,SAAO,EAAE,UAAQ,IAAE,YAAU,aAAY,QAAM,KAAG,UAAQ,IAAE,EAAE,UAAQ,WAAS,IAAE,SAAO,EAAE,UAAQ,IAAE,IAAED,EAAC;AAAA,aAAU,MAAI,mBAAmB,KAAI;AAAC,UAAI,GAAE;AAAE,QAAE,aAAa,QAAM,KAAG,UAAQ,IAAE,EAAE,UAAQ,WAAS,IAAE,SAAO,EAAE,UAAQ,IAAE,2BAAyB,2BAA0B,OAAO,OAAO,CAAC,IAAG,QAAM,KAAG,UAAQ,IAAE,EAAE,UAAQ,WAAS,IAAE,SAAO,EAAE,UAAQ,IAAE,IAAEA,EAAC,GAAG,WAAU;AAAC,UAAE;AAAA,MAAC,CAAE;AAAA,IAAC;AAAA,EAAC;AAAM,SAAG,EAAE,KAAK,iBAAgB,GAAG,WAAU;AAAC,QAAE;AAAA,IAAC,CAAE;AAAC,EAAC,CAAC,GAAE,cAAc,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,cAA+B,GAAE;AAAC,MAAI,IAAE,iBAAiB;AAAE,SAAO,gBAAQ,OAAO,iBAAgB,MAAI,oBAAoB,aAAW,OAAO,OAAO,EAAC,OAAM,MAAG,MAAK,MAAG,SAAQ,OAAG,UAAS,MAAG,MAAK,GAAE,GAAE,CAAC,IAAE,GAAE,MAAI,oBAAoB,aAAW,EAAC,iBAAgB,0BAAyB,IAAE,IAAI;AAAC;;;ACAnwC,gBAAQ,YAAY,gBAAe,EAAC,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,yBAAwB,CAAC;AAAC,EAAC,CAAC,GAAE,aAAa,UAAQ,EAAC,IAAG,SAAQ;AAAiB,SAAR,aAA8B,GAAE;AAAC,SAAO,gBAAQ,OAAO,gBAAe,CAAC;AAAC;;;ACAhP,gBAAQ,YAAY,kBAAiB,EAAC,QAAO,MAAG,MAAK,KAAE,CAAC,GAAE,eAAe,UAAQ,EAAC,SAAQ,WAAU,KAAI,UAAS;AAAiB,SAAR,eAAgC,GAAE;AAAC,SAAO,gBAAQ,OAAO,kBAAiB,CAAC;AAAC;;;ACA1J,IAAI;AAAc,SAAS,gBAAgB,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,SAAQ,IAAE,EAAE,eAAc,IAAE,EAAE,SAAQ,IAAE,EAAE;AAAa,MAAG,GAAE;AAAC,QAAI,IAAE,SAASG,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,GAAE,IAAE,SAASA,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC;AAAE,UAAI,iBAAiB,WAAS,KAAG,EAAE,EAAC,GAAE,2BAA0B,MAAK,GAAE,WAAU,GAAE,QAAO,EAAC,CAAC,IAAE,MAAI,mBAAmB,UAAQ,KAAG,EAAE,GAAE,GAAE,kBAAiB,YAAW,CAAC,IAAE,MAAI,mBAAmB,OAAK,EAAE,YAAY,2BAA0B,OAAO,OAAO,CAAC,GAAE,CAAC,GAAG,SAASA,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAM,SAAG,EAAE,KAAK,YAAW,GAAG,WAAU;AAAC,QAAE;AAAA,IAAC,CAAE;AAAC;AAAC,CAAC,SAAS,GAAE;AAAC,IAAE,OAAK,QAAO,EAAE,QAAM;AAAO,EAAE,kBAAgB,gBAAc,CAAC,EAAE,GAAE,gBAAQ,YAAY,YAAW,EAAC,MAAK,iBAAgB,QAAO,iBAAgB,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,2BAA0B,CAAC;AAAC,EAAC,CAAC,GAAE,SAAS,UAAQ,EAAC,SAAQ,SAAQ,KAAI,SAAQ,IAAG,QAAO;AAAiB,SAAR,SAA0B,GAAE;AAAC,SAAO,gBAAQ,OAAO,YAAW,CAAC;AAAC;;;ACA1/B,gBAAQ,YAAY,sBAAqB,EAAC,MAAK,MAAG,QAAO,MAAG,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,sBAAqB,CAAC;AAAC,EAAC,CAAC,GAAE,mBAAmB,UAAQ,EAAC,SAAQ,SAAQ,KAAI,SAAQ,IAAG,QAAO;AAAiB,SAAR,mBAAoC,GAAE;AAAC,SAAO,gBAAQ,OAAO,sBAAqB,CAAC;AAAC;;;ACAlT,gBAAQ,YAAY,uBAAsB,EAAC,MAAK,MAAG,QAAO,MAAG,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,uBAAsB,CAAC;AAAC,EAAC,CAAC,GAAE,oBAAoB,UAAQ,EAAC,SAAQ,SAAQ,KAAI,SAAQ,IAAG,QAAO;AAAiB,SAAR,oBAAqC,GAAE;AAAC,SAAO,gBAAQ,OAAO,uBAAsB,CAAC;AAAC;;;ACAvT,gBAAQ,YAAY,kBAAiB,EAAC,MAAK,MAAG,QAAO,MAAG,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,kBAAiB,CAAC;AAAC,EAAC,CAAC,GAAE,eAAe,UAAQ,EAAC,SAAQ,SAAQ,KAAI,SAAQ,IAAG,QAAO;AAAiB,SAAR,eAAgC,GAAE;AAAC,SAAO,gBAAQ,OAAO,kBAAiB,CAAC;AAAC;;;ACA9R,gBAAQ,YAAY,cAAa,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,WAAW,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,aAA6B;AAAC,SAAO,gBAAQ,OAAO,YAAY;AAAC;;;ACA/K,gBAAQ,YAAY,mBAAkB,EAAC,MAAK,MAAG,QAAO,MAAG,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,mCAAkC,CAAC;AAAC,EAAC,CAAC,GAAE,gBAAgB,UAAQ,EAAC,SAAQ,SAAQ,KAAI,SAAQ,IAAG,QAAO;AAAiB,SAAR,gBAAiC,GAAE;AAAC,SAAO,gBAAQ,OAAO,mBAAkB,CAAC;AAAC;;;ACAnT,gBAAQ,YAAY,gBAAe,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,aAAa,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,aAA8B,GAAE;AAAC,SAAO,gBAAQ,OAAO,gBAAe,CAAC;AAAC;;;ACA1L,gBAAQ,YAAY,kBAAiB,EAAC,QAAO,MAAG,MAAK,KAAE,CAAC,GAAE,eAAe,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,eAAgC,GAAE;AAAC,SAAO,gBAAQ,OAAO,kBAAiB,CAAC;AAAC;;;ACAlM,gBAAQ,YAAY,sBAAqB,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,mBAAmB,UAAQ,EAAC,SAAQ,UAAS,KAAI,SAAQ;AAAiB,SAAR,mBAAoC,GAAE;AAAC,SAAO,gBAAQ,OAAO,sBAAqB,CAAC;AAAC;;;ACApN,gBAAQ,YAAY,mBAAkB,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,gBAAgB,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,gBAAiC,GAAE;AAAC,SAAO,gBAAQ,OAAO,mBAAkB,CAAC;AAAC;;;ACAtM,gBAAQ,YAAY,qBAAoB,EAAC,QAAO,KAAE,CAAC,GAAE,kBAAkB,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,kBAAmC,GAAE;AAAC,SAAO,gBAAQ,OAAO,qBAAoB,CAAC;AAAC;;;ACAjK,SAAS,uBAAuB,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,SAAQ,IAAE,EAAE,eAAc,IAAE,EAAE,SAAQ,IAAE,EAAE;AAAa,MAAG,GAAE;AAAC,QAAI,IAAE,SAASC,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,GAAE,IAAE,SAASA,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC;AAAE,UAAI,iBAAiB,WAAS,KAAG,EAAE,EAAC,GAAE,qCAAoC,MAAK,GAAE,WAAU,GAAE,QAAO,EAAC,CAAC,IAAE,MAAI,mBAAmB,UAAQ,KAAG,EAAE,GAAE,GAAE,uBAAsB,iBAAgB,CAAC,IAAE,MAAI,mBAAmB,OAAK,EAAE,YAAY,qCAAoC,OAAO,OAAO,CAAC,GAAE,CAAC,GAAG,SAASA,IAAE;AAAC,QAAEA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAM,SAAG,EAAE,KAAK,eAAc,GAAG,WAAU;AAAC,QAAE;AAAA,IAAC,CAAE;AAAC;AAAC,gBAAQ,YAAY,eAAc,EAAC,MAAK,wBAAuB,QAAO,uBAAsB,CAAC,GAAE,YAAY,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,YAA6B,GAAE;AAAC,SAAO,gBAAQ,OAAO,eAAc,CAAC;AAAC;;;ACAh1B,gBAAQ,YAAY,aAAY,EAAC,MAAK,MAAG,QAAO,MAAG,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,sBAAqB,CAAC;AAAC,EAAC,CAAC,GAAE,UAAU,UAAQ,EAAC,SAAQ,SAAQ,KAAI,SAAQ,IAAG,QAAO;AAAiB,SAAR,UAA2B,GAAE;AAAC,SAAO,gBAAQ,OAAO,aAAY,CAAC;AAAC;;;ACA9Q,gBAAQ,YAAY,mBAAkB,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,gBAAgB,UAAQ,EAAC,SAAQ,UAAS,KAAI,SAAQ;AAAiB,SAAR,gBAAiC,GAAE;AAAC,SAAO,gBAAQ,OAAO,mBAAkB,CAAC;AAAC;;;ACAxM,gBAAQ,YAAY,aAAY,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAEC,aAAY,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAARA,aAA6B,GAAE;AAAC,SAAO,gBAAQ,OAAO,aAAY,CAAC;AAAC;;;ACA9J,gBAAQ,YAAY,kBAAiB,EAAC,QAAO,SAAS,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,SAAQ,IAAE,EAAE;AAAa,MAAG,EAAE,eAAc;AAAC,UAAI,mBAAmB,UAAQ,KAAG,EAAG,WAAU;AAAC,QAAE;AAAA,IAAC,GAAI,WAAU;AAAC,QAAE;AAAA,IAAC,GAAG,iBAAgB,kBAAiB,CAAC,IAAE,MAAI,mBAAmB,OAAK,EAAE,YAAY,gCAA+B,OAAO,OAAO,CAAC,GAAE,CAAC,GAAG,WAAU;AAAC,QAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAM,SAAG,EAAE,KAAK,kBAAiB,GAAG,WAAU;AAAC,QAAE;AAAA,IAAC,CAAE;AAAC,EAAC,CAAC,GAAE,eAAe,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,iBAAiC;AAAC,SAAO,gBAAQ,OAAO,gBAAgB;AAAC;;;ACApjB,gBAAQ,YAAY,8BAA6B,EAAC,QAAO,MAAG,MAAK,KAAE,CAAC,GAAE,2BAA2B,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,2BAA4C,GAAE;AAAC,SAAO,gBAAQ,OAAO,8BAA6B,CAAC;AAAC;;;ACAlP,gBAAQ,YAAY,qBAAoB,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,kBAAkB,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,kBAAmC,GAAE;AAAC,SAAO,gBAAQ,OAAO,qBAAoB,CAAC;AAAC;;;ACA9M,gBAAQ,YAAY,cAAa,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,WAAW,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,WAA4B,GAAE;AAAC,SAAO,gBAAQ,OAAO,cAAa,CAAC;AAAC;;;ACAlL,gBAAQ,YAAY,iBAAgB,EAAC,QAAO,KAAE,CAAC,GAAE,cAAc,UAAQ,EAAC,SAAQ,QAAO;AAAiB,SAAR,cAA+B,GAAE;AAAC,SAAO,gBAAQ,OAAO,iBAAgB,CAAC;AAAC;;;ACA1K,gBAAQ,YAAY,mBAAkB,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,gBAAgB,UAAQ,EAAC,SAAQ,YAAW,KAAI,WAAU;AAAiB,SAAR,gBAAiC,GAAE;AAAC,SAAO,gBAAQ,OAAO,mBAAkB,CAAC;AAAC;;;ACA5M,gBAAQ,YAAY,kBAAiB,EAAC,QAAO,MAAG,MAAK,KAAE,CAAC,GAAE,eAAe,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,eAAgC,GAAE;AAAC,SAAO,gBAAQ,OAAO,kBAAiB,CAAC;AAAC;;;ACA7J,SAAS,oBAAoB,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,eAAc,IAAE,EAAE,cAAa,IAAE,EAAE,SAAQ,IAAE,EAAE,SAAQ,IAAE,OAAO,OAAO,GAAE,EAAC,QAAO,MAAE,CAAC;AAAE,MAAG,GAAE;AAAC,QAAI,IAAE,WAAU;AAAC,QAAE,aAAW,EAAE,UAAU,GAAE,EAAE;AAAA,IAAC,GAAE,IAAE,WAAU;AAAC,QAAE,UAAQ,EAAE,OAAO,GAAE,EAAE;AAAA,IAAC;AAAE,UAAI,iBAAiB,WAAS,KAAG,EAAE,EAAC,GAAE,0BAAyB,MAAK,GAAE,WAAU,GAAE,QAAO,EAAC,CAAC,IAAE,MAAI,mBAAmB,UAAQ,KAAG,EAAE,GAAE,GAAE,kBAAiB,WAAU,CAAC,IAAE,MAAI,mBAAmB,OAAK,EAAE,YAAY,0BAAyB,OAAO,OAAO,CAAC,GAAE,CAAC,GAAG,WAAU;AAAC,QAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAM,SAAG,EAAE,KAAK,gBAAe,GAAG,WAAU;AAAC,QAAE;AAAA,IAAC,CAAE;AAAC;AAAC,gBAAQ,YAAY,gBAAe,EAAC,MAAK,qBAAoB,QAAO,oBAAmB,CAAC,GAAE,aAAa,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,eAA+B;AAAC,SAAO,gBAAQ,OAAO,cAAc;AAAC;;;ACAnzB,gBAAQ,YAAY,wBAAuB,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,qBAAqB,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,qBAAsC,GAAE;AAAC,SAAO,gBAAQ,OAAO,wBAAuB,CAAC;AAAC;;;ACA7I,SAAS,wBAAwB,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,SAAQ,IAAE,EAAE,cAAa,IAAE,EAAE,eAAc,IAAE,EAAE,SAAQ,IAAE,gBAAQ,cAAc,sBAAsB,iBAAiB,SAASC,IAAE;AAAC,QAAIC,KAAED,GAAE;AAAK,IAAAC,GAAE,cAAY,kBAAkB,UAAQ,EAAE,UAAQ,EAAE,OAAOA,EAAC,IAAE,EAAE,aAAW,EAAE,UAAUA,GAAE,MAAM;AAAA,EAAC,CAAE;AAAE,MAAG,GAAE;AAAC,QAAI,IAAE,SAASD,IAAE;AAAC,sBAAQ,uBAAuB,EAAE,SAAQ,CAAC,GAAE,gBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,GAAE,IAAE,SAASA,IAAE;AAAC,sBAAQ,uBAAuB,EAAE,SAAQ,CAAC,GAAE,gBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC;AAAE,UAAI,iBAAiB,YAAU,QAAQ,IAAI,kCAAiC,CAAC,GAAE,KAAG,EAAE,EAAC,GAAE,kCAAiC,MAAK,GAAE,WAAU,GAAE,QAAO,EAAC,CAAC,KAAG,MAAI,mBAAmB,UAAQ,KAAG,EAAE,GAAE,GAAE,iBAAgB,oBAAmB,CAAC,IAAE,MAAI,mBAAmB,OAAK,EAAE,YAAY,kCAAiC,OAAO,OAAO,CAAC,GAAE,CAAC,GAAG,SAASA,IAAE;AAAC,sBAAQ,uBAAuB,EAAE,SAAQ,CAAC,GAAE,gBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAM,SAAG,EAAE,KAAK,oBAAmB,GAAG,SAASA,IAAE;AAAC,sBAAQ,uBAAuB,EAAE,SAAQ,CAAC,GAAE,gBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,CAAE;AAAC;AAAC,gBAAQ,YAAY,oBAAmB,EAAC,QAAO,yBAAwB,MAAK,wBAAuB,CAAC,GAAE,iBAAiB,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,iBAAkC,GAAE;AAAC,SAAO,gBAAQ,OAAO,oBAAmB,CAAC;AAAC;;;ACA5xC,SAAS,gCAAgC,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,SAAQ,IAAE,EAAE,eAAc,IAAE,EAAE,SAAQ,IAAE,EAAE,cAAaE,KAAE,gBAAQ,cAAc,sBAAsB,uBAAuB,SAASC,IAAE;AAAC,QAAIC,KAAED,GAAE;AAAK,IAAAC,GAAE,cAAY,kBAAkB,UAAQ,EAAE,UAAQ,EAAE,OAAOA,EAAC,IAAE,EAAE,aAAW,EAAE,UAAUA,GAAE,MAAM;AAAA,EAAC,CAAE;AAAE,MAAG,GAAE;AAAC,QAAI,IAAE,SAASC,IAAE;AAAC,sBAAQ,uBAAuBA,GAAE,OAAO,WAAUH,EAAC,GAAE,gBAAQ,qBAAqBG,IAAE,GAAE,CAAC;AAAA,IAAC,GAAE,IAAE,SAASA,IAAE;AAAC,sBAAQ,uBAAuBA,GAAE,OAAO,WAAUH,EAAC,GAAE,gBAAQ,qBAAqBG,IAAE,GAAE,CAAC;AAAA,IAAC;AAAE,UAAI,iBAAiB,WAAS,KAAG,EAAE,EAAC,GAAE,0CAAyC,MAAK,GAAE,WAAU,GAAE,QAAO,EAAC,CAAC,IAAE,MAAI,mBAAmB,UAAQ,KAAG,EAAE,GAAE,GAAE,iBAAgB,4BAA2B,CAAC,IAAE,MAAI,mBAAmB,OAAK,EAAE,YAAY,0CAAyC,OAAO,OAAO,CAAC,GAAE,CAAC,GAAG,SAASA,IAAE;AAAC,sBAAQ,uBAAuBA,GAAE,OAAO,WAAUH,EAAC,GAAE,gBAAQ,qBAAqBG,IAAE,GAAE,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAM,SAAG,EAAE,KAAK,4BAA2B,GAAG,SAASA,IAAE;AAAC,sBAAQ,uBAAuBA,GAAE,OAAO,WAAUH,EAAC,GAAE,gBAAQ,qBAAqBG,IAAE,GAAE,CAAC;AAAA,IAAC,CAAE;AAAC;AAAC,gBAAQ,YAAY,4BAA2B,EAAC,QAAO,iCAAgC,MAAK,gCAA+B,CAAC,GAAE,yBAAyB,UAAQ,EAAC,SAAQ,WAAU,KAAI,UAAS;AAAiB,SAAR,yBAA0C,GAAE;AAAC,SAAO,gBAAQ,OAAO,4BAA2B,CAAC;AAAC;;;ACA77C,gBAAQ,YAAY,oBAAmB,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,iBAAiB,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,iBAAkC,GAAE;AAAC,SAAO,gBAAQ,OAAO,oBAAmB,CAAC;AAAC;;;ACA7H,SAAS,wBAAwB,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,SAAQ,IAAE,EAAE,eAAc,IAAE,EAAE,cAAa,IAAE,EAAE,SAAQ,IAAE,gBAAQ,cAAc,sBAAsB,cAAc,SAASC,IAAE;AAAC,QAAIC,KAAED,GAAE;AAAK,IAAAC,GAAE,aAAWA,GAAE,cAAY,kBAAkB,UAAQ,EAAE,UAAQ,EAAE,OAAOA,EAAC,IAAE,EAAE,aAAW,EAAE,UAAUA,GAAE,UAAQA,EAAC;AAAA,EAAC,CAAE;AAAE,MAAG,GAAE;AAAC,QAAI,IAAE,SAASD,IAAE;AAAC,sBAAQ,uBAAuB,EAAE,SAAQ,CAAC,GAAE,gBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,GAAE,IAAE,SAASA,IAAE;AAAC,sBAAQ,uBAAuB,EAAE,SAAQ,CAAC,GAAE,gBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC;AAAE,UAAI,iBAAiB,WAAS,KAAG,EAAE,EAAC,GAAE,kCAAiC,MAAK,GAAE,WAAU,GAAE,QAAO,EAAC,CAAC,IAAE,MAAI,mBAAmB,UAAQ,KAAG,EAAE,GAAE,GAAE,iBAAgB,oBAAmB,CAAC,IAAE,MAAI,mBAAmB,OAAK,EAAE,YAAY,kCAAiC,OAAO,OAAO,CAAC,GAAE,CAAC,GAAG,SAASA,IAAE;AAAC,sBAAQ,uBAAuB,EAAE,SAAQ,CAAC,GAAE,gBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAM,SAAG,EAAE,KAAK,oBAAmB,GAAG,SAASA,IAAE;AAAC,sBAAQ,uBAAuB,EAAE,SAAQ,CAAC,GAAE,gBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,CAAE;AAAC;AAAC,gBAAQ,YAAY,oBAAmB,EAAC,QAAO,yBAAwB,MAAK,KAAE,CAAC,GAAE,iBAAiB,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,iBAAkC,GAAE;AAAC,SAAO,gBAAQ,OAAO,oBAAmB,CAAC;AAAC;;;ACA/yC,gBAAQ,YAAY,eAAc,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,YAAY,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,YAA6B,GAAE;AAAC,SAAO,gBAAQ,OAAO,eAAc,CAAC;AAAC;;;ACA5I,SAAS,kBAAkB,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,SAAQ,IAAE,EAAE,eAAc,IAAE,EAAE,cAAa,IAAE,gBAAQ,cAAc,sBAAsB,UAAU,WAAU;AAAC,MAAE,aAAW,EAAE,UAAU;AAAA,EAAC,CAAE;AAAE,MAAG,GAAE;AAAC,UAAI,mBAAmB,UAAQ,KAAG,EAAG,SAASE,IAAE;AAAC,QAAE,aAAW,EAAE,UAAU,GAAE,EAAE;AAAA,IAAC,GAAI,SAASC,IAAE;AAAC,QAAE;AAAA,IAAC,GAAG,iBAAgB,mBAAkB,CAAC,IAAE,MAAI,mBAAmB,OAAK,EAAE,YAAY,iCAAgC,OAAO,OAAO,CAAC,GAAE,CAAC,GAAG,SAASA,IAAE;AAAC,QAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAM,SAAG,EAAE,KAAK,mBAAkB,GAAG,SAASA,IAAE;AAAC,sBAAQ,uBAAuB,SAAQ,CAAC,GAAE,gBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,CAAE;AAAC;AAAC,gBAAQ,YAAY,mBAAkB,EAAC,QAAO,kBAAiB,CAAC,GAAE,gBAAgB,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,gBAAiC,GAAE;AAAC,SAAO,gBAAQ,OAAO,mBAAkB,CAAC;AAAC;;;ACAr0B,gBAAQ,YAAY,aAAY,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,UAAU,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,UAA2B,GAAE;AAAC,SAAO,gBAAQ,OAAO,aAAY,CAAC;AAAC;;;ACAzI,SAAS,uBAAuB,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,eAAc,IAAE,EAAE,cAAa,IAAE,EAAE,SAAQ,IAAE,EAAE;AAAQ,MAAG,GAAE;AAAC,QAAI,IAAE,SAASC,IAAE;AAAC,sBAAQ,qBAAqB,EAAE,OAAO,GAAE,gBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,GAAE,IAAE,SAASA,IAAE;AAAC,sBAAQ,qBAAqB,EAAE,OAAO,GAAE,gBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC;AAAE,UAAI,iBAAiB,WAAS,KAAG,EAAE,EAAC,GAAE,iCAAgC,MAAK,GAAE,WAAU,GAAE,QAAO,EAAC,CAAC,IAAE,MAAI,mBAAmB,UAAQ,KAAG,EAAE,GAAE,GAAE,iBAAgB,mBAAkB,CAAC,IAAE,MAAI,mBAAmB,OAAK,EAAE,YAAY,iCAAgC,OAAO,OAAO,CAAC,GAAE,CAAC,GAAG,SAASA,IAAE;AAAC,sBAAQ,qBAAqB,EAAE,OAAO,GAAE,gBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAM,SAAG,EAAE,KAAK,mBAAkB,GAAG,SAASA,IAAE;AAAC,sBAAQ,qBAAqB,EAAE,OAAO,GAAE,gBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,CAAE;AAAC;AAAC,gBAAQ,YAAY,mBAAkB,EAAC,QAAO,wBAAuB,MAAK,uBAAsB,CAAC,GAAE,gBAAgB,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,gBAAiC,GAAE;AAAC,SAAO,gBAAQ,OAAO,mBAAkB,CAAC;AAAC;;;ACAnhC,SAAS,+BAA+B,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,eAAc,IAAE,EAAE,SAAQ,IAAE,EAAE,cAAa,IAAE,EAAE;AAAQ,MAAG,GAAE;AAAC,QAAIC,KAAE,SAASC,IAAE;AAAC,sBAAQ,qBAAqB,EAAE,SAAS,GAAE,gBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,GAAE,IAAE,SAASA,IAAE;AAAC,sBAAQ,qBAAqB,EAAE,SAAS,GAAE,gBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC;AAAE,UAAI,iBAAiB,WAAS,KAAG,EAAE,EAAC,GAAE,yCAAwC,MAAK,GAAE,WAAUD,IAAE,QAAO,EAAC,CAAC,IAAE,MAAI,mBAAmB,UAAQ,KAAG,EAAEA,IAAE,GAAE,iBAAgB,2BAA0B,CAAC,IAAE,MAAI,mBAAmB,OAAK,EAAE,YAAY,yCAAwC,OAAO,OAAO,CAAC,GAAE,CAAC,GAAG,SAASC,IAAE;AAAC,sBAAQ,qBAAqB,EAAE,SAAS,GAAE,gBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAM,SAAG,EAAE,KAAK,2BAA0B,GAAG,SAASA,IAAE;AAAC,sBAAQ,qBAAqB,EAAE,SAAS,GAAE,gBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,CAAE;AAAC;AAAC,gBAAQ,YAAY,2BAA0B,EAAC,MAAK,gCAA+B,QAAO,+BAA8B,CAAC,GAAE,wBAAwB,UAAQ,EAAC,SAAQ,WAAU,KAAI,UAAS;AAAiB,SAAR,wBAAyC,GAAE;AAAC,SAAO,gBAAQ,OAAO,2BAA0B,CAAC;AAAC;;;ACAxoC,gBAAQ,YAAY,qBAAoB,EAAC,QAAO,SAAS,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,SAAQ,IAAE,EAAE,eAAc,IAAE,EAAE;AAAa,MAAG,GAAE;AAAC,UAAI,mBAAmB,UAAQ,KAAG,EAAG,WAAU;AAAC,QAAE;AAAA,IAAC,GAAI,WAAU;AAAC,QAAE;AAAA,IAAC,GAAG,oBAAmB,QAAO,CAAC,IAAE,MAAI,mBAAmB,OAAK,EAAE,YAAY,yBAAwB,OAAO,OAAO,CAAC,GAAE,CAAC,GAAG,WAAU;AAAC,QAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAM,SAAG,EAAE,KAAK,wBAAuB,GAAG,WAAU;AAAC,QAAE;AAAA,IAAC,CAAE;AAAC,EAAC,CAAC,GAAE,kBAAkB,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,oBAAoC;AAAC,SAAO,gBAAQ,OAAO,qBAAoB,EAAC,UAAS,oBAAmB,CAAC;AAAC;;;ACA3lB,gBAAQ,YAAY,mBAAkB,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,gBAAgB,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,gBAAiC,GAAE;AAAC,SAAO,gBAAQ,OAAO,mBAAkB,CAAC;AAAC;;;ACAlL,SAAS,uBAAuB,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,eAAc,IAAE,EAAE,cAAa,IAAE,EAAE;AAAQ,MAAG,GAAE;AAAC,UAAI,mBAAmB,UAAQ,KAAG,EAAG,SAASC,IAAE;AAAC,sBAAQ,qBAAqB,EAAE,OAAO,GAAE,gBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,GAAI,SAASA,IAAE;AAAC,sBAAQ,qBAAqB,EAAE,OAAO,GAAE,gBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,GAAG,iBAAgB,mBAAkB,CAAC,IAAE,MAAI,mBAAmB,OAAK,EAAE,YAAY,iCAAgC,OAAO,OAAO,CAAC,GAAE,CAAC,GAAG,SAASA,IAAE;AAAC,sBAAQ,qBAAqB,EAAE,OAAO,GAAE,gBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAM,SAAG,EAAE,KAAK,mBAAkB,GAAG,SAASA,IAAE;AAAC,sBAAQ,qBAAqB,EAAE,OAAO,GAAE,gBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,CAAE;AAAC;AAAC,gBAAQ,YAAY,mBAAkB,EAAC,QAAO,uBAAsB,CAAC,GAAE,gBAAgB,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,gBAAiC,GAAE;AAAC,SAAO,gBAAQ,OAAO,mBAAkB,CAAC;AAAC;;;ACAn5B,gBAAQ,YAAY,cAAa,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,WAAW,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,WAA4B,GAAE;AAAC,SAAO,gBAAQ,OAAO,cAAa,CAAC;AAAC;;;ACA9J,SAAS,sBAAsB,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,eAAc,IAAE,EAAE,cAAa,IAAE,EAAE;AAAQ,MAAG,GAAE;AAAC,UAAI,mBAAmB,UAAQ,KAAG,EAAG,SAASC,IAAE;AAAC,QAAE,aAAW,EAAE,UAAU,GAAE,EAAE;AAAA,IAAC,GAAI,SAASC,IAAE;AAAC,QAAE;AAAA,IAAC,GAAG,iBAAgB,kBAAiB,CAAC,IAAE,MAAI,mBAAmB,OAAK,EAAE,YAAY,gCAA+B,OAAO,OAAO,CAAC,GAAE,CAAC,GAAG,SAASA,IAAE;AAAC,QAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAM,SAAG,EAAE,KAAK,kBAAiB,GAAG,SAASA,IAAE;AAAC,sBAAQ,qBAAqB,OAAO,GAAE,gBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC,CAAE;AAAC;AAAC,gBAAQ,YAAY,kBAAiB,EAAC,QAAO,sBAAqB,CAAC,GAAE,eAAe,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,iBAAiC;AAAC,SAAO,gBAAQ,OAAO,gBAAgB;AAAC;;;ACAprB,gBAAQ,YAAY,aAAY,EAAC,QAAO,SAAS,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,SAAQ,IAAE,EAAE,eAAc,IAAE,EAAE,cAAaC,KAAE;AAAG,MAAG,GAAE;AAAC,UAAI,mBAAmB,UAAQ,KAAG,EAAG,SAASC,IAAE;AAAC,MAAAD,MAAG,EAAE,aAAW,EAAE,YAAU,QAAMC,GAAE,YAAU,EAAE,UAAQ,EAAE,OAAOA,EAAC,IAAE,EAAE,aAAW,EAAE,UAAUA,GAAE,MAAM,MAAID,KAAE,MAAG,gBAAQ,qBAAqBC,IAAE,GAAE,CAAC;AAAA,IAAE,GAAI,SAASA,IAAE;AAAC,MAAAD,KAAE,EAAE,UAAQ,EAAE,OAAOC,EAAC,KAAGD,KAAE,MAAG,gBAAQ,qBAAqBC,IAAE,GAAE,CAAC;AAAA,IAAE,GAAG,iBAAgB,aAAY,CAAC,IAAE,MAAI,mBAAmB,OAAK,EAAE,YAAY,2BAA0B,OAAO,OAAO,CAAC,GAAE,CAAC,GAAG,SAASA,IAAE;AAAC,MAAAD,MAAG,EAAE,aAAW,EAAE,YAAU,QAAMC,GAAE,YAAU,EAAE,UAAQ,EAAE,OAAOA,EAAC,IAAE,EAAE,aAAW,EAAE,UAAUA,GAAE,MAAM,MAAID,KAAE,MAAG,gBAAQ,qBAAqBC,IAAE,GAAE,CAAC;AAAA,IAAE,CAAE;AAAA,EAAC;AAAM,SAAG,EAAE,KAAK,aAAY,GAAG,SAASA,IAAE;AAAC,MAAAD,MAAG,EAAE,aAAW,EAAE,YAAU,QAAMC,GAAE,YAAU,EAAE,UAAQ,EAAE,OAAOA,EAAC,IAAE,EAAE,aAAW,EAAE,UAAUA,GAAE,MAAM,MAAID,KAAE,MAAG,gBAAQ,qBAAqBC,IAAE,GAAE,CAAC;AAAA,IAAE,CAAE;AAAC,EAAC,CAAC,GAAE,UAAU,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,UAA2B,GAAE;AAAC,SAAO,gBAAQ,OAAO,aAAY,CAAC;AAAC;;;ACA7gC,gBAAQ,YAAY,aAAY,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,UAAU,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,YAA4B;AAAC,SAAO,gBAAQ,OAAO,WAAW;AAAC;;;ACA3K,gBAAQ,YAAY,aAAY,EAAC,IAAG,SAAS,GAAE,GAAE;AAAC,aAAS,MAAI,IAAE,CAAC,IAAG,OAAO,SAAS,SAAS,UAAU,EAAE,OAAM,sBAAqB,CAAC;AAAC,EAAC,CAAC,GAAE,UAAU,UAAQ,EAAC,IAAG,SAAQ;AAAiB,SAAR,YAA4B;AAAC,SAAO,gBAAQ,OAAO,aAAY,CAAC,CAAC;AAAC;;;ACA7L,SAAS,aAAa,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,QAAO,IAAE,EAAE,SAAQ,IAAE,EAAE,eAAc,IAAE,EAAE,cAAa,IAAE,EAAE,SAAQ,IAAE,EAAC,MAAK,YAAU,EAAE,OAAK,SAAO,cAAY,EAAE,OAAK,YAAU,QAAO,SAAQ,EAAE,MAAK,UAAS,MAAI,EAAE,UAAS,kBAAiB,EAAE,iBAAgB;AAAE,MAAG,GAAE;AAAC,QAAIC,KAAE,WAAU;AAAC,sBAAQ,qBAAqB,EAAC,WAAU,kBAAkB,SAAQ,QAAO,CAAC,EAAC,GAAE,GAAE,CAAC;AAAA,IAAC,GAAE,IAAE,SAASC,IAAE;AAAC,sBAAQ,qBAAqBA,IAAE,GAAE,CAAC;AAAA,IAAC;AAAE,UAAI,iBAAiB,WAAS,KAAG,EAAE,EAAC,GAAE,uBAAsB,MAAK,GAAE,WAAUD,IAAE,QAAO,EAAC,CAAC,IAAE,MAAI,mBAAmB,UAAQ,KAAG,EAAEA,IAAE,GAAE,iBAAgB,SAAQ,CAAC,IAAE,MAAI,mBAAmB,OAAK,EAAE,YAAY,uBAAsB,OAAO,OAAO,CAAC,GAAE,CAAC,GAAG,WAAU;AAAC,sBAAQ,qBAAqB,EAAC,WAAU,kBAAkB,SAAQ,QAAO,CAAC,EAAC,GAAE,GAAE,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAM,SAAG,EAAE,KAAK,SAAQ,GAAG,WAAU;AAAC,sBAAQ,qBAAqB,EAAC,WAAU,kBAAkB,SAAQ,QAAO,CAAC,EAAC,GAAE,GAAE,CAAC;AAAA,IAAC,CAAE;AAAC;AAAC,gBAAQ,YAAY,SAAQ,EAAC,QAAO,cAAa,MAAK,cAAa,IAAG,SAAS,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,MAAK,IAAE,EAAE,MAAK,IAAE,EAAE,UAAS,IAAE,EAAE;AAAM,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,6BAA4B,EAAC,MAAK,GAAE,MAAK,GAAE,UAAS,GAAE,OAAM,EAAC,CAAC;AAAC,EAAC,CAAC,GAAE,MAAM,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,MAAuB,GAAE;AAAC,SAAO,gBAAQ,OAAO,SAAQ,CAAC;AAAC;;;ACA7vC,gBAAQ,YAAY,kCAAiC,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,+BAA+B,UAAQ,EAAC,SAAQ,WAAU,KAAI,UAAS;AAAiB,SAAR,iCAAiD;AAAC,SAAO,gBAAQ,OAAO,gCAAgC;AAAC;;;ACAnQ,gBAAQ,YAAY,eAAc,EAAC,QAAO,KAAE,CAAC,GAAE,YAAY,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,YAA6B,GAAE;AAAC,SAAO,gBAAQ,OAAO,eAAc,CAAC;AAAC;;;ACAzH,gBAAQ,YAAY,gBAAe,EAAC,MAAK,MAAG,QAAO,MAAG,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,yBAAwB,CAAC;AAAC,EAAC,CAAC,GAAE,WAAW,UAAQ,EAAC,SAAQ,SAAQ,KAAI,SAAQ,IAAG,QAAO;AAAiB,SAAR,WAA4B,GAAE;AAAC,SAAO,gBAAQ,OAAO,gBAAe,SAAS,CAAC,GAAE,GAAE,EAAC,UAAS,aAAY,CAAC,CAAC;AAAC;;;ACAnX,gBAAQ,YAAY,oBAAmB,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,iBAAiB,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,iBAAkC,GAAE;AAAC,SAAO,gBAAQ,OAAO,oBAAmB,CAAC;AAAC;;;ACArJ,gBAAQ,YAAY,cAAa,EAAC,MAAK,KAAE,CAAC,GAAE,gBAAgB,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,gBAAiC,GAAE;AAAC,SAAO,IAAI,QAAS,SAAS,GAAE,GAAE;AAAC,OAAG,WAAW,SAAS,CAAC,GAAE,GAAE,EAAC,SAAQ,SAASE,IAAE;AAAC,QAAEA,EAAC;AAAA,IAAC,GAAE,MAAK,SAASA,IAAE;AAAC,QAAEA,EAAC;AAAA,IAAC,EAAC,CAAC,CAAC;AAAA,EAAC,CAAE;AAAC;;;ACAvT,gBAAQ,YAAY,0BAAyB,EAAC,MAAK,MAAG,QAAO,MAAG,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,mCAAkC,CAAC;AAAC,EAAC,CAAC,GAAE,uBAAuB,UAAQ,EAAC,SAAQ,SAAQ,KAAI,SAAQ,IAAG,QAAO;AAAiB,SAAR,uBAAwC,GAAE;AAAC,SAAO,gBAAQ,OAAO,0BAAyB,CAAC;AAAC;;;ACA/U,gBAAQ,YAAY,MAAK,EAAC,IAAG,SAAS,GAAE,GAAE;AAAC,SAAO,SAAS,SAAS,UAAU,EAAE,OAAM,eAAc,CAAC;AAAC,EAAC,CAAC,GAAE,GAAG,UAAQ,EAAC,IAAG,SAAQ;AAAiB,SAAR,GAAoB,GAAE;AAAC,SAAO,gBAAQ,OAAO,MAAK,CAAC;AAAC;;;ACA9L,gBAAQ,YAAY,WAAU,EAAC,MAAK,MAAG,QAAO,KAAE,CAAC,GAAE,QAAQ,UAAQ,EAAC,SAAQ,SAAQ,KAAI,QAAO;AAAiB,SAAR,QAAyB,GAAE;AAAC,SAAO,gBAAQ,OAAO,WAAU,CAAC;AAAC;;;ACAq0O,IAAIC,MAAG,EAAC,OAAY,YAAsB,qCAAwE,gCAA8D,wCAA8E,WAAoB,SAAgB,sBAA0C,mBAAoC,eAA4B,gCAA8D,6BAAwD,uBAA4C,mBAAoC,YAAsB,2BAAoD,aAAwB,2BAA0BC,gBAA0B,kBAAkC,gBAA8B,wBAA8C,aAAwB,WAAoB,oBAAsC,SAAgB,iBAAgC,iBAAgC,YAAsB,cAA0B,iBAAgC,oBAAsC,oBAAsC,kBAAkC,sBAA0C,sBAA0C,eAA4B,cAAa,sBAAa,qBAAwC,WAAoB,qBAAwC,iBAAgC,gBAA8B,iBAAgC,qBAAwC,aAAwB,WAAoB,kBAAkC,aAAwB,kBAAkC,gBAA8B,sBAA0C,gBAA8B,oBAAsC,cAA0B,gBAA8B,cAA0B,cAA0B,gBAA8B,gBAA8B,SAAgB,cAA0B,cAA0B,sBAA0C,gBAA8B,eAA4B,qBAAwC,QAAc,aAAwB,gBAA8B,cAA0B,qBAAwC,kBAAkC,eAA4B,kBAAkC,aAAwB,IAAM,gBAA8B,kBAAkC,gBAA8B,SAAgB,aAAwB,UAAkB,kBAAkC,UAAkB,UAAkB,eAA4B,gBAA8B,qBAAwC,YAAsB,UAAkB,kBAAkC,eAA4B,uBAA4C,WAAoB,YAAsB,cAA0B,WAAoB,gBAA8B,QAAc,YAAsB,mBAAoC,OAAY,iBAAgC,mBAAoC,aAAwB,WAAoB,aAAwB,YAAsB,MAAU,aAAwB,aAAY,iBAAY,0BAAkD,YAAsB,gBAA8B,eAA4B,cAA0B,gBAA8B,UAAkB,oBAAsC,qBAAwC,gBAA8B,YAAsB,iBAAgC,cAA0B,gBAA8B,oBAAsC,iBAAgC,mBAAoC,aAAwB,WAAoB,iBAAgC,WAAUC,cAAU,gBAA8B,4BAAsD,mBAAoC,YAAsB,eAA4B,iBAAgC,gBAA8B,cAA0B,sBAA0C,kBAAkC,0BAAkD,kBAAkC,kBAAkC,aAAwB,iBAAgC,WAAoB,iBAAgC,yBAAgD,mBAAoC,iBAAgC,iBAAgC,YAAsB,gBAA8B,WAAoB,WAAoB,WAAoB,OAAY,gCAA8D,aAAwB,YAAsB,kBAAkC,iBAAgC,wBAA8C,IAAM,SAAgB,QAAe;AAAE,IAAG,gBAAQ,WAAW,MAAI,iBAAiB;AAAS,EAAAF,MAAG,IAAI,MAAMA,KAAG,EAAC,KAAI,SAAS,GAAE,GAAE,GAAE;AAAC,WAAO,KAAKA,MAAG,QAAQ,IAAI,GAAE,GAAE,CAAC,IAAE,IAAI,QAAQ,IAAI,IAAG,GAAE,CAAC,GAAE,CAAC;AAAA,EAAC,EAAC,CAAC;AAAA,KAAM;AAAC,SAAO,MAAI,QAAQ,KAAK,6HAAkD;AAAE,MAAG;AAAC,WAAO,eAAe,QAAO,MAAK,EAAC,OAAMA,KAAG,UAAS,KAAE,CAAC;AAAA,EAAC,SAAO,GAAN;AAAS,YAAQ,MAAM,CAAC;AAAA,EAAC;AAAC,SAAO,OAAK,QAAQ,KAAK,+HAAoD;AAAE,MAAG;AAAC,WAAO,eAAe,QAAO,OAAM,EAAC,OAAMA,KAAG,UAAS,KAAE,CAAC;AAAA,EAAC,SAAO,GAAN;AAAS,YAAQ,MAAM,CAAC;AAAA,EAAC;AAAC;AAAC,IAAO,aAAQA;", "names": ["e", "r", "n", "t", "o", "r", "n", "o", "i", "c", "u", "f", "d", "t", "p", "e", "_regenerator", "r", "u", "t", "e", "n", "e", "o", "_regeneratorRuntime", "r", "e", "t", "n", "o", "a", "u", "require_regenerator", "u", "n", "e", "n", "t", "i", "_regeneratorRuntime", "u", "t", "n", "e", "e", "chooseContactHandler", "o", "chooseContactHandler", "o", "import_regenerator", "r", "_regeneratorRuntime", "r", "chooseContactHandler", "o", "e", "chooseContact", "e", "e", "e", "n", "e", "e", "e", "e", "n", "i", "o", "e", "t", "e", "n", "r", "EnableEnum", "emptyWatermark", "WaterMark", "t", "n", "u", "drawWatermark", "a", "n", "r", "e", "e", "e", "e", "n", "o", "u", "n", "o", "e", "o", "e", "n", "n", "e", "u", "e", "i", "e", "o", "searchOnMap", "o", "n", "u", "t", "r", "e", "r", "t", "t", "e", "e", "u", "t", "o", "o", "e", "u", "o", "u", "e", "e", "dd", "chooseContact", "searchOnMap"]}