/*! 
 Build based on gin-vue-admin 
 Time : 1754834760000 */
import{_ as s,f as e,r as a,N as r,h as o,a as l,b as d,d as n,i as t,t as i,y as u,l as c,j as w,w as m,O as p,a6 as f,M as v}from"./index.0f69a27d.js";import{J as g}from"./index-browser-esm.c2d3b5c9.js";const y={style:{height:"100%"}},h={class:"person"},b={class:"person-main"},P={class:"info-row"},_={class:"info-col"},k={class:"info-col"},C={key:0,class:"info-col"},I={key:1,class:"info-col"},V={class:"info-col"},E={class:"info-row"},x={class:"info-col"},T={class:"modal-header"},j={class:"modal-body"},$={class:"modal-footer"},q=s(Object.assign({name:"Person"},{setup(s){const q=e(),A=a(null),U=a(!1),B=a({password:"",newPassword:"",confirmPassword:""}),F=()=>{try{return g("$..expireType",q.userInfo)[0]||""}catch(s){return""}},N=()=>{try{return g("$..expireEnd",q.userInfo)[0]||""}catch(s){return""}},O=()=>{try{return g("$..phone",q.userInfo)[0]||""}catch(s){return""}},z=()=>{U.value=!1,J()},J=()=>{B.value={password:"",newPassword:"",confirmPassword:""},A.value&&A.value.clearValidate()},M=async s=>{if(A.value)try{await A.value.validateField(s)}catch(e){logger.log("验证失败:",e)}},Z=()=>{setTimeout((async()=>{await M("newPassword")}),500)},D=()=>{setTimeout((async()=>{await M("confirmPassword")}),500)},G=r({password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:8,max:128,message:"密码长度最少8个字符至128个字符",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"},{validator:(s,e,a)=>{if(!e)return void a(new Error("请输入新密码"));if(e.length<8)return void a(new Error("密码长度不能少于8个字符"));if(e.length>128)return void a(new Error("密码长度不能超过128个字符"));const r=/\d/.test(e),o=/[a-zA-Z]/.test(e),l=/[!@#$%^&*(),.?":{}|<>]/.test(e);r&&o&&l?a():a(new Error("密码必须包含数字、字母和特殊字符"))},trigger:["blur","change"]}],confirmPassword:[{required:!0,message:"请输入确认密码",trigger:"blur"},{validator:(s,e,a)=>{e?e===B.value.newPassword?a():a(new Error("两次密码不一致")):a(new Error("请输入确认密码"))},trigger:"blur"}]}),H=async()=>{logger.log("修改密码");try{if(await A.value.validate()){const s=await f({password:B.value.password,newPassword:B.value.newPassword});if(200===s.status){if(s.data&&-1===s.data.code)return void v({type:"error",message:s.data.msg||"密码错误",showClose:!0});v({type:"success",message:"修改密码成功！",showClose:!0}),U.value=!1,J()}else v({type:"error",message:"修改密码失败",showClose:!0})}}catch(s){console.error("修改密码出错:",s),Array.isArray(s)?v({type:"error",message:s[0]||"表单验证失败",showClose:!0}):v({type:"error",message:"修改密码请求失败",showClose:!0})}};return(s,e)=>{const a=o("base-input"),r=o("base-form-item"),f=o("base-form"),v=o("base-button");return l(),d("div",y,[n("div",h,[e[11]||(e[11]=n("div",{class:"person-header"},[n("h1",{class:"page-title"},"基本信息")],-1)),n("div",b,[n("div",P,[n("div",_,[t(" 用户名："+i(u(q).userInfo.name)+"    ",1),"local"===u(q).userInfo.sourceType&&"password"===u(q).userInfo.authType?(l(),d("a",{key:0,class:"modify-password-link",onClick:e[0]||(e[0]=s=>U.value=!0)},"修改密码")):c("",!0)]),n("div",k,"所属组织： "+i(u(q).userInfo.groupName),1),"forever"===F()?(l(),d("div",C,"到期时间： 永久")):(l(),d("div",I,"到期时间： "+i(N()),1)),n("div",V,"手机号码： "+i(O()),1)]),n("div",E,[n("div",x,"邮箱： "+i(u(q).userInfo.email),1),e[9]||(e[9]=n("div",{class:"info-col"},null,-1)),e[10]||(e[10]=n("div",{class:"info-col"},null,-1))])])]),U.value?(l(),d("div",{key:0,class:"modal-overlay",onClick:z},[n("div",{class:"modal-dialog",onClick:e[8]||(e[8]=p((()=>{}),["stop"]))},[n("div",T,[e[12]||(e[12]=n("h3",null,"修改密码",-1)),n("button",{class:"modal-close",onClick:e[1]||(e[1]=s=>U.value=!1)},"×")]),n("div",j,[w(f,{ref_key:"modifyPwdForm",ref:A,model:B.value,rules:G,class:"password-form"},{default:m((()=>[w(r,{label:"原密码",prop:"password",class:"form-item"},{default:m((()=>[w(a,{modelValue:B.value.password,"onUpdate:modelValue":e[2]||(e[2]=s=>B.value.password=s),type:"password","show-password":"",placeholder:"请输入原密码",class:"form-input"},null,8,["modelValue"])])),_:1}),w(r,{label:"新密码",prop:"newPassword",class:"form-item"},{default:m((()=>[w(a,{modelValue:B.value.newPassword,"onUpdate:modelValue":e[3]||(e[3]=s=>B.value.newPassword=s),type:"password","show-password":"",placeholder:"请输入新密码",class:"form-input",onInput:Z,onBlur:e[4]||(e[4]=s=>M("newPassword"))},null,8,["modelValue"])])),_:1}),w(r,{label:"确认密码",prop:"confirmPassword",class:"form-item"},{default:m((()=>[w(a,{modelValue:B.value.confirmPassword,"onUpdate:modelValue":e[5]||(e[5]=s=>B.value.confirmPassword=s),type:"password","show-password":"",placeholder:"请再次输入新密码",class:"form-input",onInput:D,onBlur:e[6]||(e[6]=s=>M("confirmPassword"))},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])]),n("div",$,[w(v,{class:"cancel-btn",onClick:e[7]||(e[7]=s=>U.value=!1)},{default:m((()=>e[13]||(e[13]=[t(" 取 消 ")]))),_:1,__:[13]}),w(v,{type:"primary",class:"confirm-btn",onClick:H},{default:m((()=>e[14]||(e[14]=[t(" 确 定 ")]))),_:1,__:[14]})])])])):c("",!0)])}}}),[["__scopeId","data-v-d501ea84"]]);export{q as default};
