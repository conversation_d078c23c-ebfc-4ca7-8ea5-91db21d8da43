/*! 
 Build based on gin-vue-admin 
 Time : 1754836337000 */
import{_ as e,h as t,a as o,b as r,d as a,j as s,w as n,i,u as l,f as c,M as u,Q as h,C as d}from"./index.57c3624b.js";import{u as g}from"./secondaryAuth.7146041f.js";function p(e){let t=0;if(0===e.length)return t.toString(16).padStart(8,"0");for(let s=0;s<e.length;s++){t=(t<<5)-t+e.charCodeAt(s),t&=t}const o=Math.abs(t),r=Date.now(),a=Math.floor(65535*Math.random());return(o.toString(16)+r.toString(16)+a.toString(16)).padStart(64,"0").substring(0,64)}async function f(e){try{if("undefined"!=typeof window&&window.crypto&&window.crypto.subtle&&"function"==typeof window.crypto.subtle.digest){logger.log("使用Web Crypto API计算SHA-256");const t=(new TextEncoder).encode(e);return await window.crypto.subtle.digest("SHA-256",t)}{logger.log("QT环境：使用简化哈希算法");const t=p(e),o=new Uint8Array(32);for(let e=0;e<32;e++){const r=t.substring(2*e,2*e+2);o[e]=parseInt(r,16)}return o.buffer}}catch(t){console.error("SHA-256计算失败:",t),logger.log("使用最终回退方案");const o=p(e+"_"+Date.now()+"_"+Math.random()),r=new Uint8Array(32);for(let e=0;e<32;e++){const t=o.substring(2*e,2*e+2);r[e]=parseInt(t,16)}return r.buffer}}async function w(e){try{return function(e){const t=new Uint8Array(e),o=String.fromCharCode(...t);return btoa(o).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}(await f(e))}catch(t){console.error("生成code_challenge失败:",t),logger.log("使用最终回退方案生成code_challenge");const o=e+"_"+Date.now();return btoa(o).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}}const y=e=>{let t="";if(e&&e.includes("message =")){const o=/message\s*=\s*(.*?)(?=\s+metadata\s*=|$)/,r=e.match(o);r&&r[1]&&(t=r[1].trim())}return t},m={key:0,class:"sso-warpper"},b={class:"sso-title"},_={"aria-hidden":"true",class:"icon"},k=["xlink:href"],v={key:1,class:"sso-warpper"},A=["src"],I=["src"],S={props:{authId:{type:String,default:function(){return""}},authInfo:{type:Object,default:function(){return{}}}},data:()=>({iframeSrc:"",callbackIframeSrc:"",isListen:!1,isThirdBack:!1,isAutoLogin:!1,isListenShowApp:!1,route:l(),userStore:c(),secondaryAuth:g(),loading:!1}),computed:{isForceBrowser(){return"cas"===this.authInfo.authType?1===parseInt(this.authInfo.casOpenType):1===parseInt(this.authInfo.oauth2OpenType)},isCallbackMode(){return this.oauth_callbak||this.route.query.oauth_callbak}},watch:{authId:{handler(){this.init()},deep:!0,immediate:!0}},mounted(){this.isCallbackMode&&this.handleOAuth2Callback()},destroyed(){this.unListenGoBack(),this.clearLoading(),this.isListen&&this.removeEvent(window,"message",this.listenHandle)},methods:{isInIframe(){try{return window.self!==window.top}catch(e){return!0}},sendMessageToParent(e){try{logger.log("向父页面发送消息:",e),window.parent&&window.parent!==window&&(window.parent.postMessage(e,"*"),logger.log("已向parent发送消息")),window.top&&window.top!==window&&(window.top.postMessage(e,"*"),logger.log("已向top发送消息"))}catch(t){console.error("发送消息失败:",t)}},async handleOAuth2Callback(){try{if(logger.log("开始处理OAuth2回调"),this.isInIframe()){logger.log("在iframe中，发送消息给父窗口");const e={idp_id:this.route.query.idp_id,redirect:this.route.query.redirect,auth_token:this.route.query.auth_token,login_type:this.route.query.login_type,auth_error:this.route.query.auth_error,state:this.route.query.state};return void this.sendMessageToParent({type:"oauth2_auth_callback",code:e.auth_token,state:e.state,auth_error:e.auth_error})}await this.processOAuth2Callback()}catch(e){logger.log("OAuth2回调处理失败:",e),u({type:"error",message:"认证失败，请重试",showClose:!0})}},async processOAuth2Callback(){const e=this.route.query.auth_error;if(e){logger.log("OAuth2认证错误:",e);const t=y(e);return void u({type:"error",message:"认证失败: "+t,showClose:!0})}const t=this.route.query.auth_token;if(!t)return logger.log("缺少认证令牌"),void u({type:"error",message:"认证失败: 缺少认证令牌",showClose:!0});const o={clientId:"client_portal",grantType:"implicit",redirect_uri:"",idpId:this.authId,authWeb:{authWebToken:t}};logger.log("调用登录接口，参数:",o);const r=await this.userStore.LoginIn(o,"oauth2",this.authId);if(logger.log("登录结果:",r),!0===r||"object"==typeof r&&null!==r&&-1!==r.code){if(await this.secondaryAuth.handleSecondaryAuthResponse(r))return void logger.log("企业微信登录成功，进入双因子验证");logger.log("OAuth2登录成功")}else logger.log("OAuth2登录失败:",r)},init(){this.isForceBrowser||this.clickSubmit()},async clickSubmit(){const e=function(e=128){const t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~";let o="";if("undefined"!=typeof window&&window.crypto&&window.crypto.getRandomValues){const r=new Uint8Array(e);window.crypto.getRandomValues(r);for(let a=0;a<e;a++)o+=t[r[a]%66]}else for(let r=0;r<e;r++)o+=t[Math.floor(66*Math.random())];return o}();sessionStorage.setItem("oauth2_code_verifier",e);const t=await w(e);this.submit({code_challenge:encodeURIComponent(t),code_challenge_method:"S256"})},async submit(e){var t,o;let r=h()+"/auth/login/v1/callback/"+this.authId;if(e){const a=[];for(const t in e)a.push(t+"="+encodeURIComponent(e[t]));if(r+="?"+a.join("&"),null==(t=this.route.query)?void 0:t.redirect){const e=null==(o=this.route.query)?void 0:o.redirect;r+="&redirect=/"+encodeURIComponent(e)}}if(this.isForceBrowser)logger.log("强制浏览器授权认证URL:",r),d.isClient()?await d.openAsecPage(r):window.location.href=r;else{if(logger.log("iframe授权认证URL:",r),this.isListen)return logger.log("iframe授权监听:"),void(r.includes("code=")||r.includes("token=")||r.includes("auth_success=true")?(logger.log("iframe授权回调"),this.callbackIframeSrc=r):this.iframeSrc=r);this.iframeSrc=r,logger.log("iframe初始地址",this.iframeSrc),this.isListen=!0,this.addEvent(window,"message",this.listenHandle)}},async listenHandle(e){if(logger.log("sso触发监听：",e.data),"oauth2_auth_callback"===e.data.type)return void this.handleOAuth2Message(e.data.code,e.data.auth_error);const t=e.data.event;this.isThirdAppWakeup(t)?this.wakeupApp(e):e.data&&this.submit(e.data)},async handleOAuth2Message(e,t){try{if(logger.log("收到oauth2_result页面的消息:",{code:e}),!e){if(t&&t.includes("message =")){const e=y(t);u({type:"error",message:"认证失败: "+e,showClose:!0}),this.init()}return void logger.log("消息缺少必要参数:",{code:e})}const o={clientId:"client_portal",grantType:"implicit",redirect_uri:"",idpId:this.authId,authWeb:{authWebToken:e}};logger.log("调用登录接口，参数:",o);const r=await this.userStore.LoginIn(o,"oauth2",this.authId);if(logger.log("登录结果:",r),!0===r||"object"==typeof r&&null!==r&&-1!==r.code){if(await this.secondaryAuth.handleSecondaryAuthResponse(r))return void logger.log("企业微信登录成功，进入双因子验证");logger.log("OAuth2登录成功")}else logger.log("OAuth2登录失败:",r)}catch(o){logger.log("处理OAuth2消息失败:",o)}},addEvent(e,t,o){e.addEventListener?e.addEventListener(t,o,!1):e.attachEvent&&e.attachEvent("on"+t,(function(){o.call(e,window.event)}))},removeEvent(e,t,o){e.removeEventListener?e.removeEventListener(t,o):e.detachEvent&&e.detachEvent("on"+t,o)},isThirdAppWakeup:e=>"wakeup-app"===e,wakeupApp(e){const t=e.data.params.url;t&&(window.location.href=t)},clearLoading(){this.loading&&(this.loading.clear(),this.loading=!1)}}},C=e(Object.assign(S,{__name:"oauth2",setup:e=>(l,c)=>{const u=t("base-button");return o(),r("div",null,[l.isForceBrowser?(o(),r("div",m,[a("span",b,[(o(),r("svg",_,[a("use",{"xlink:href":"#icon-auth-"+e.authInfo.authType},null,8,k)]))]),s(u,{class:"login_submit_button",type:"primary",onClick:l.clickSubmit},{default:n((()=>c[0]||(c[0]=[i("授权登录")]))),_:1,__:[0]},8,["onClick"])])):(o(),r("div",v,[a("iframe",{src:l.iframeSrc,class:"sso-iframe",frameborder:"0"},null,8,A),a("iframe",{src:l.callbackIframeSrc,class:"sso-callback-iframe"},null,8,I)]))])}}),[["__scopeId","data-v-23638b1a"]]);export{C as default};
